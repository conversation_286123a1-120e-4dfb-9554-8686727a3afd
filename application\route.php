<?php

// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006~2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: liu21st <<EMAIL>>
// +----------------------------------------------------------------------

use think\Route;

// 首页相关接口
Route::get('api/index/index', 'api/IndexApi/index');
Route::get('api/index/stats', 'api/IndexApi/stats');
Route::get('api/index/banners', 'api/IndexApi/banners');

// 职位相关接口
Route::get('api/job/index', 'api/JobApi/index');
Route::get('api/job/detail', 'api/JobApi/detail');
Route::get('api/job/search', 'api/JobApi/search');
Route::get('api/job/categories', 'api/JobApi/categories');
Route::post('api/job/favorite', 'api/Job/favorite');
Route::post('api/job/unfavorite', 'api/Job/unfavorite');
Route::get('api/job/checkfavorite/:job_id', 'api/Job/checkFavorite');
Route::get('api/job/myfavorites', 'api/Job/myFavorites');

// 简历相关接口
Route::get('api/resume/getlist', 'api/ResumeApi/getList');
Route::get('api/resume/detail', 'api/ResumeApi/detail');
Route::post('api/resume/create', 'api/ResumeApi/create');
Route::post('api/resume/update', 'api/ResumeApi/update');
Route::post('api/resume/delete', 'api/ResumeApi/delete');
Route::post('api/resume/setdefault', 'api/ResumeApi/setdefault');
Route::post('api/resume/apply', 'api/ResumeApi/apply');
Route::get('api/resume/applies', 'api/ResumeApi/applies');
Route::post('api/resume/deleteapply', 'api/Resume/deleteapply');

// 面试相关接口
Route::get('api/interview/my_interviews', 'api/Interview/my_interviews');
Route::get('api/interview/detail/:id', 'api/Interview/detail');
Route::post('api/interview/confirm', 'api/Interview/confirm');
Route::post('api/interview/cancel', 'api/Interview/cancel');
Route::get('api/interview/stats', 'api/Interview/stats');

// 测试数据生成
Route::get('test/generate_interview_data', 'admin/Jobinterview/generate_test_data');

return [
    //别名配置,别名只能是映射到控制器且访问时必须加上请求的方法
    '__alias__'   => [
    ],
    //变量规则
    '__pattern__' => [
        'name' => '\w+',
    ],


    
//        域名绑定到模块
//        '__domain__'  => [
//            'admin' => 'admin',
//            'api'   => 'api',
//        ],
];

// 生成工作内容
Route::post('resume/generate_job_description', 'index/Resume/generateJobDescription');
