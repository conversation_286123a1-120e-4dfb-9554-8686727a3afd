<form id="edit-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label for="title" class="control-label col-xs-12 col-sm-2">{:__('Resource title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" class="form-control" id="title" name="row[title]" value="{$row.title|htmlentities}" data-rule="required" />
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Event key')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type='text' class="form-control" id="eventkey" name="row[eventkey]" value="{$row.eventkey|htmlentities}" data-rule="required" readonly />
        </div>
    </div>
    <div class="form-group">
        <label for="remark" class="control-label col-xs-12 col-sm-2">{:__('Memo')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea class="form-control" id="remark" name="row[remark]">{$row.remark|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Type')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[type]', ['text' => __('Text'), 'app' => __('App')], $row['type'])}
        </div>
    </div>
    <div id="expand">

    </div>
    <div class="form-group">
        <div class="col-xs-2"></div>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('Hidden')], $row['status'])}
        </div>
    </div>
    <div class="form-group hidden layer-footer">
        <div class="col-xs-2"></div>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
    <select name="applist" disabled="true" class="hidden">
        {foreach $applist as $k => $v}
            <option value="{$k|htmlentities}">{$v.name|htmlentities}</option>
        {/foreach}
    </select>
</form>
<script>
    var apps = {:json_encode($applist)};
    var datas = {$row.content};
</script>
