import { get, post, put, del } from './request';
const app = getApp();

/**
 * 获取我的简历列表
 */
export const getMyResumes = () => {
  return get('/resume/list', {}, { auth: true }).then(res => {
    if (res.code === 1 && res.data && res.data.resumeList) {
      // 确保每个简历项目都有 id 字段
      res.data.resumeList = res.data.resumeList.map(resume => {
        if (!resume.id) {
          console.error('简历数据缺少 id 字段:', resume);
          return null;
        }
        return resume;
      }).filter(resume => resume !== null);
    }
    return res;
  });
};

/**
 * 获取简历基本信息
 * @param {number} id - 简历ID
 * @returns {Promise} 返回Promise对象，包含简历的基本信息
 */
export const getResumeBasicInfo = (id) => {
  return get('/resume/basic_info', { id }, { auth: true });
};

/**
 * 获取简历详情
 * @param {number} id - 简历ID
 */
export const getResumeDetail = (id) => {
  return get(`/resume/view/${id}`, {
    id: id
  }, { auth: true }).then(res => {
    if (res.code === 1 && res.data) {
      // 处理图片URL
      const imageFields = [
        'avatar',
        'full_body_photo',
        'id_card_front',
        'id_card_back',
        'hk_macau_passport_front',
        'hk_macau_passport_back'
      ];

      imageFields.forEach(field => {
        if (res.data[field] && !res.data[field].startsWith('http')) {
          res.data[field] = app.globalData.baseApi.replace(/\/api\/?$/, '') + res.data[field];
        }
      });

      // 处理附加照片
      if (res.data.additional_photos) {
        try {
          const photos = res.data.additional_photos.split(',');
          res.data.additional_photos_array = photos.map(photo => {
            if (photo && !photo.startsWith('http')) {
              return app.globalData.baseApi.replace(/\/api\/?$/, '') + photo;
            }
            return photo;
          }).filter(photo => photo);
        } catch (e) {
          console.error('解析附加照片失败', e);
          res.data.additional_photos_array = [];
        }
      } else {
        res.data.additional_photos_array = [];
      }

      // 处理教育经历
      const educationFields = ['education_start', 'education_end', 'education_school', 'education_major', 'graduation_education'];
      let educationHistory = [];
      
      if (res.data.education_school) {
        const schools = res.data.education_school.split('|');
        const starts = (res.data.education_start || '').split('|');
        const ends = (res.data.education_end || '').split('|');
        const majors = (res.data.education_major || '').split('|');
        const degrees = (res.data.graduation_education || '').split('|');

        educationHistory = schools.map((school, index) => ({
          school: school,
          start_date: starts[index] || '',
          end_date: ends[index] || '',
          major: majors[index] || '',
          degree: degrees[index] || ''
        }));
      }
      res.data.education_history = educationHistory;

      // 处理工作经历
      const workFields = ['job_start', 'job_end', 'job_company', 'job_position', 'job_description'];
      let workHistory = [];
      
      if (res.data.job_company) {
        const companies = res.data.job_company.split('|');
        const starts = (res.data.job_start || '').split('|');
        const ends = (res.data.job_end || '').split('|');
        const positions = (res.data.job_position || '').split('|');
        const descriptions = (res.data.job_description || '').split('|');

        workHistory = companies.map((company, index) => ({
          company: company,
          start_date: starts[index] || '',
          end_date: ends[index] || '',
          position: positions[index] || '',
          description: descriptions[index] || ''
        }));
      }
      res.data.work_history = workHistory;

      // 处理联系人信息
      const contactFields = ['contact_name', 'contact_relation', 'contact_age', 'contact_job'];
      let contacts = [];
      
      if (res.data.contact_name) {
        const names = res.data.contact_name.split('|');
        const relations = (res.data.contact_relation || '').split('|');
        const ages = (res.data.contact_age || '').split('|');
        const jobs = (res.data.contact_job || '').split('|');

        contacts = names.map((name, index) => ({
          name: name,
          relation: relations[index] || '',
          age: ages[index] || '',
          job: jobs[index] || ''
        }));
      }
      res.data.contacts = contacts;
    }
    return res;
  });
};

/**
 * 创建简历
 * @param {Object} data - 简历数据
 */
export const createResume = (data) => {
  return post('/resume/create', data, { auth: true });
};

/**
 * 更新简历
 * @param {number} id - 简历ID
 * @param {Object} data - 简历数据
 */
export const updateResume = (id, data) => {
  return post(`/resume/update/${id}`, data, { auth: true });
};

/**
 * 删除简历
 * @param {number} id - 简历ID
 */
export const deleteResume = (id) => {
  return post(`/resume/delete/${id}`, { id: id }, { auth: true });
};

/**
 * 获取简历统计数据
 */
export const getResumeCount = () => {
  return get('/resume/count', {}, { auth: true });
};

/**
 * 扫描身份证
 * @param {string} filePath - 身份证图片本地路径
 */
export const scanIdCard = (filePath) => {
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: getApp().globalData.baseApi + '/resume/scanIdCard',
      filePath: filePath,
      name: 'id_card',
      header: {
        'Token': wx.getStorageSync('token')
      },
      success(res) {
        if (res.statusCode === 200) {
          const data = JSON.parse(res.data);
          resolve(data);
        } else {
          reject(new Error('上传失败'));
        }
      },
      fail(err) {
        reject(err);
      }
    });
  });
};

/**
 * 生成工作内容描述
 * @param {Object} data - 生成参数
 * @param {string} data.company - 公司名称
 * @param {string} data.position - 职位名称
 * @param {number} data.word_count - 字数要求
 */
export const generateJobDescription = (data) => {
  return post('/resume/generateJobDescription', data, { auth: true });
};

/**
 * 生成自我评价
 * @param {Object} data - 生成参数
 * @param {string} data.content - 原始内容
 * @param {number} data.word_count - 字数要求
 */
export const generateSelfEvaluation = (data) => {
  return post('/resume/generateSelfEvaluation', data, { auth: true });
};

/**
 * 生成兴趣爱好描述
 * @param {Object} data - 生成参数
 * @param {string} data.content - 原始内容
 * @param {number} data.word_count - 字数要求
 */
export const generateHobbies = (data) => {
  return post('/resume/generateHobbies', data, { auth: true });
};

/**
 * 投递简历（应聘职位）
 * @param {Object} data - 投递数据
 * @param {number} data.job_id - 职位ID
 * @param {number} data.resume_id - 简历ID
 * @param {string} data.job_code - 职位编号
 * @param {string} data.job_name - 职位名称
 * @returns {Promise} 返回Promise对象
 */
export const applyJob = (data) => {
  // 打印参数
  console.log('投递简历参数:', data);
  
  // 校验参数
  if (!data || !data.job_id || !data.resume_id) {
    console.error('投递简历参数错误:', data);
    return Promise.reject({
      code: -1,
      msg: '参数错误',
      message: '投递参数不完整，请确保包含职位ID和简历ID'
    });
  }
  
  // 确保job_id和resume_id是数字
  const requestData = {
    ...data,
    job_id: Number(data.job_id),
    resume_id: Number(data.resume_id)
  };
  
  // 发送请求
  return new Promise((resolve, reject) => {
    wx.request({
      url: getApp().globalData.baseApi + '/resume/apply',
      method: 'POST',
      data: requestData,
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'Token': wx.getStorageSync('token')
      },
      success(res) {
        console.log('投递简历响应:', res);
        
        // 检查HTTP状态
        if (res.statusCode !== 200) {
          console.error('投递简历HTTP错误:', res.statusCode);
          reject({
            code: res.statusCode,
            message: '网络请求错误，状态码: ' + res.statusCode
          });
          return;
        }
        
        // 检查响应数据格式
        if (!res.data) {
          console.error('投递简历响应为空');
          reject({
            code: -1,
            message: '服务器响应为空'
          });
          return;
        }
        
        // 检查业务逻辑状态
        if (res.data.code !== 1) {
          console.error('投递简历业务错误:', res.data);
          reject({
            code: res.data.code || 0,
            message: res.data.msg || '投递失败',
            data: res.data
          });
          return;
        }
        
        // 成功响应
        resolve(res.data);
      },
      fail(err) {
        console.error('投递简历请求失败:', err);
        reject({
          code: -1,
          message: err.errMsg || '网络请求失败'
        });
      }
    });
  });
};

/**
 * 获取我的投递记录
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 */
export const getMyApplications = (params = {}) => {
  return get('/resume/applications', params, { auth: true });
};

/**
 * 取消投递
 * @param {number} id - 投递记录ID
 */
export const cancelApplication = (id) => {
  console.log('取消投递请求，ID:', id);
  
  // 使用原生请求，以便更好地处理响应
  return new Promise((resolve, reject) => {
    wx.request({
      url: getApp().globalData.baseApi + '/resume/deleteapply',
      method: 'POST',
      data: { id: id },
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'Token': wx.getStorageSync('token')
      },
      success(res) {
        console.log('取消投递原始响应:', res);
        
        // 无论服务器返回什么状态，都返回响应数据
        // 让调用方自行决定如何处理
        resolve(res.data || { code: 0, msg: '服务器未返回数据' });
      },
      fail(err) {
        console.error('取消投递请求失败:', err);
        reject({
          code: -1,
          msg: err.errMsg || '网络请求失败',
          error: err
        });
      }
    });
  });
};

export default {
  getMyResumes,
  getResumeDetail,
  createResume,
  updateResume,
  deleteResume,
  getResumeCount,
  scanIdCard,
  generateJobDescription,
  generateSelfEvaluation,
  generateHobbies,
  applyJob,
  getMyApplications,
  cancelApplication
};
