<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 模板管理
 *
 * @icon fa fa-file-text
 */
class Template extends Backend
{
    /**
     * Template模型对象
     */
    protected $model = null;
    protected $searchFields = 'name';

    public function _initialize()
    {
        parent::_initialize();
        
        // 检查并安装菜单
        $this->installMenu();
    }
    
    /**
     * 安装菜单
     */
    protected function installMenu()
    {
        $menu = [
            [
                'name'    => 'template',
                'title'   => '模板管理',
                'icon'    => 'fa fa-file-text',
                'weigh'   => 100,
                'sublist' => [
                    [
                        'name'  => 'template/index',
                        'title' => '模板概览',
                        'icon'  => 'fa fa-list',
                        'weigh' => 10
                    ],
                    [
                        'name'  => 'template/excel',
                        'title' => 'Excel模板',
                        'icon'  => 'fa fa-file-excel-o',
                        'weigh' => 9
                    ],
                    [
                        'name'  => 'template/word',
                        'title' => 'Word模板',
                        'icon'  => 'fa fa-file-word-o',
                        'weigh' => 8
                    ]
                ]
            ]
        ];
        
        // 检查菜单是否已存在
        $existingMenu = Db::name('auth_rule')->where('name', 'template')->find();
        if (!$existingMenu) {
            \app\common\library\Menu::create($menu);
        }
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        
        return $this->view->fetch();
    }
    
    /**
     * Excel模板管理
     */
    public function excel()
    {
        return $this->view->fetch();
    }
    
    /**
     * Word模板管理
     */
    public function word()
    {
        return $this->view->fetch();
    }

    /**
     * 获取Excel模板列表
     */
    public function getExcelTemplateList()
    {
        // 添加缓存控制头，禁止浏览器缓存
        header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
        header('Cache-Control: post-check=0, pre-check=0', false);
        header('Pragma: no-cache');
        
        $templatesDir = ROOT_PATH . 'public' . DS . 'templates' . DS . 'resume';
        $templates = [];
        
        if (is_dir($templatesDir)) {
            if ($dh = opendir($templatesDir)) {
                while (($file = readdir($dh)) !== false) {
                    // 只处理Excel文件
                    $ext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                    if ($ext === 'xlsx' && $file != '.' && $file != '..') {
                        $filePath = $templatesDir . DS . $file;
                        $templates[] = [
                            'name' => $file,
                            'path' => str_replace(ROOT_PATH, '', $filePath), // 相对路径
                            'size' => filesize($filePath),
                            'modified' => date('Y-m-d H:i:s', filemtime($filePath))
                        ];
                    }
                }
                closedir($dh);
            }
        }
        
        // 按修改时间排序，最新的排在前面
        usort($templates, function($a, $b) {
            return strtotime($b['modified']) - strtotime($a['modified']);
        });
        
        return json(['code' => 1, 'msg' => '获取模板列表成功', 'data' => $templates]);
    }
    
    /**
     * 获取Word模板列表
     */
    public function getWordTemplateList()
    {
        // 添加缓存控制头，禁止浏览器缓存
        header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
        header('Cache-Control: post-check=0, pre-check=0', false);
        header('Pragma: no-cache');
        
        $templatesDir = ROOT_PATH . 'public' . DS . 'templates' . DS . 'resume';
        $templates = [];
        
        if (is_dir($templatesDir)) {
            if ($dh = opendir($templatesDir)) {
                while (($file = readdir($dh)) !== false) {
                    // 只处理Word文件
                    $ext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                    if ($ext === 'docx' && $file != '.' && $file != '..') {
                        $filePath = $templatesDir . DS . $file;
                        $templates[] = [
                            'name' => $file,
                            'path' => str_replace(ROOT_PATH, '', $filePath), // 相对路径
                            'size' => filesize($filePath),
                            'modified' => date('Y-m-d H:i:s', filemtime($filePath))
                        ];
                    }
                }
                closedir($dh);
            }
        }
        
        // 按修改时间排序，最新的排在前面
        usort($templates, function($a, $b) {
            return strtotime($b['modified']) - strtotime($a['modified']);
        });
        
        return json(['code' => 1, 'msg' => '获取Word模板列表成功', 'data' => $templates]);
    }

    /**
     * 上传模板文件（Excel或Word）
     */
    public function uploadTemplate()
    {
        try {
            // 获取上传类型（excel或word）
            $type = $this->request->get('type', 'excel');
            
            // 上传文件
            $file = $this->request->file('template_file');
            if (!$file) {
                return json(['code' => 0, 'msg' => '未上传文件或文件大小超出服务器限制']);
            }
            
            // 检查文件类型
            $ext = strtolower(pathinfo($file->getInfo('name'), PATHINFO_EXTENSION));
            if ($type == 'excel' && $ext != 'xlsx') {
                return json(['code' => 0, 'msg' => '请上传.xlsx格式的Excel文件']);
            } elseif ($type == 'word' && $ext != 'docx') {
                return json(['code' => 0, 'msg' => '请上传.docx格式的Word文件']);
            }
            
            // 确保模板目录存在
            $templatesDir = ROOT_PATH . 'public' . DS . 'templates' . DS . 'resume';
            if (!is_dir($templatesDir)) {
                if (!mkdir($templatesDir, 0777, true)) {
                    return json(['code' => 0, 'msg' => '创建模板目录失败，请检查目录权限']);
                }
            }
            
            // 获取原始文件名
            $originalName = $file->getInfo('name');
            
            // 直接使用自定义文件名
            $destFile = $templatesDir . DS . $originalName;
            
            // 如果文件已存在，先删除
            if (file_exists($destFile)) {
                @unlink($destFile);
            }
            
            // 使用PHP原生函数移动上传文件
            if (move_uploaded_file($file->getInfo('tmp_name'), $destFile)) {
                return json([
                    'code' => 1, 
                    'msg' => '模板上传成功', 
                    'data' => [
                        'filename' => $originalName,
                        'path' => 'public' . DS . 'templates' . DS . 'resume' . DS . $originalName
                    ]
                ]);
            } else {
                return json(['code' => 0, 'msg' => '文件保存失败，请检查目录权限']);
            }
        } catch (\Exception $e) {
            // 记录详细错误信息到日志
            \think\Log::record('模板上传异常: ' . $e->getMessage() . ' 位置: ' . $e->getFile() . ':' . $e->getLine(), 'error');
            return json(['code' => 0, 'msg' => '文件上传失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 删除模板文件
     */
    public function deleteTemplate()
    {
        if ($this->request->isPost()) {
            $templatePath = $this->request->post('template_path');
            $type = $this->request->post('type');
            
            if (empty($templatePath)) {
                return json(['code' => 0, 'msg' => '未指定要删除的模板文件']);
            }
            
            // 检查是否是合法的模板路径（必须在templates/resume目录下）
            $basePath = 'public' . DS . 'templates' . DS . 'resume';
            // 使用正规化路径并兼容不同系统的路径分隔符
            $normalizedPath = str_replace('\\', '/', $templatePath);
            $normalizedBasePath = str_replace('\\', '/', $basePath);
            
            if (strpos($normalizedPath, $normalizedBasePath) === false) {
                // 记录调试信息
                \think\Log::record('删除模板文件路径验证失败: 模板路径=' . $normalizedPath . ', 基础路径=' . $normalizedBasePath, 'debug');
                return json(['code' => 0, 'msg' => '非法的模板路径，路径必须在templates/resume目录下']);
            }
            
            // 获取完整文件路径
            $fullPath = ROOT_PATH . $templatePath;
            
            // 检查文件是否存在
            if (!file_exists($fullPath)) {
                return json(['code' => 0, 'msg' => '模板文件不存在，路径：' . $fullPath]);
            }
            
            // 检查文件类型
            $ext = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));
            if ($type == 'excel' && $ext != 'xlsx') {
                return json(['code' => 0, 'msg' => '非Excel模板文件']);
            } elseif ($type == 'word' && $ext != 'docx') {
                return json(['code' => 0, 'msg' => '非Word模板文件']);
            }
            
            // 尝试删除文件
            try {
                if (@unlink($fullPath)) {
                    return json(['code' => 1, 'msg' => '模板删除成功']);
                } else {
                    return json(['code' => 0, 'msg' => '删除失败，请检查文件权限']);
                }
            } catch (\Exception $e) {
                \think\Log::record('删除模板文件异常: ' . $e->getMessage() . ' 位置: ' . $e->getFile() . ':' . $e->getLine(), 'error');
                return json(['code' => 0, 'msg' => '删除失败: ' . $e->getMessage()]);
            }
        }
        
        return json(['code' => 0, 'msg' => '请求方式错误']);
    }
    
    /**
     * 预览模板文件
     */
    public function previewTemplate()
    {
        $templatePath = $this->request->get('path');
        $type = $this->request->get('type', 'excel');
        
        if (empty($templatePath)) {
            $this->error('未指定要预览的模板文件');
        }
        
        // 检查是否是合法的模板路径（必须在templates/resume目录下）
        $basePath = 'public' . DS . 'templates' . DS . 'resume';
        $normalizedPath = str_replace('\\', '/', $templatePath);
        $normalizedBasePath = str_replace('\\', '/', $basePath);
        
        if (strpos($normalizedPath, $normalizedBasePath) === false) {
            $this->error('非法的模板路径');
        }
        
        // 获取完整文件路径
        $fullPath = ROOT_PATH . $templatePath;
        
        // 检查文件是否存在
        if (!file_exists($fullPath)) {
            $this->error('模板文件不存在');
        }
        
        // 检查文件类型
        $ext = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));
        if ($type == 'excel' && $ext != 'xlsx') {
            $this->error('非Excel模板文件');
        } elseif ($type == 'word' && $ext != 'docx') {
            $this->error('非Word模板文件');
        }
        
        // 设置响应头并输出文件
        header('Content-Description: File Transfer');
        if ($type == 'excel') {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        } else {
            header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
        }
        header('Content-Disposition: inline; filename="' . basename($fullPath) . '"');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($fullPath));
        readfile($fullPath);
        exit;
    }
    
    /**
     * 下载模板文件
     */
    public function downloadTemplate()
    {
        $templatePath = $this->request->get('path');
        $type = $this->request->get('type', 'excel');
        
        if (empty($templatePath)) {
            $this->error('未指定要下载的模板文件');
        }
        
        // 检查是否是合法的模板路径（必须在templates/resume目录下）
        $basePath = 'public' . DS . 'templates' . DS . 'resume';
        $normalizedPath = str_replace('\\', '/', $templatePath);
        $normalizedBasePath = str_replace('\\', '/', $basePath);
        
        if (strpos($normalizedPath, $normalizedBasePath) === false) {
            $this->error('非法的模板路径');
        }
        
        // 获取完整文件路径
        $fullPath = ROOT_PATH . $templatePath;
        
        // 检查文件是否存在
        if (!file_exists($fullPath)) {
            $this->error('模板文件不存在');
        }
        
        // 检查文件类型
        $ext = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));
        if ($type == 'excel' && $ext != 'xlsx') {
            $this->error('非Excel模板文件');
        } elseif ($type == 'word' && $ext != 'docx') {
            $this->error('非Word模板文件');
        }
        
        // 设置响应头并输出文件
        header('Content-Description: File Transfer');
        if ($type == 'excel') {
            header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        } else {
            header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
        }
        header('Content-Disposition: attachment; filename="' . basename($fullPath) . '"');
        header('Content-Transfer-Encoding: binary');
        header('Expires: 0');
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Content-Length: ' . filesize($fullPath));
        readfile($fullPath);
        exit;
    }
    
    /**
     * 编辑Excel模板页面
     */
    public function ExcelTemplateDetail()
    {
        $templatePath = $this->request->get('path');
        
        if (empty($templatePath)) {
            $this->error('未指定要编辑的模板文件');
        }
        
        // 检查是否是合法的模板路径（必须在templates/resume目录下）
        $basePath = 'public' . DS . 'templates' . DS . 'resume';
        $normalizedPath = str_replace('\\', '/', $templatePath);
        $normalizedBasePath = str_replace('\\', '/', $basePath);
        
        if (strpos($normalizedPath, $normalizedBasePath) === false) {
            $this->error('非法的模板路径');
        }
        
        // 获取完整文件路径
        $fullPath = ROOT_PATH . $templatePath;
        
        // 检查文件是否存在
        if (!file_exists($fullPath)) {
            $this->error('模板文件不存在');
        }
        
        // 检查文件类型
        $ext = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));
        if ($ext != 'xlsx') {
            $this->error('非Excel模板文件');
        }
        
        // 获取模板文件信息
        $templateInfo = [
            'name' => basename($fullPath),
            'path' => $templatePath,
            'size' => filesize($fullPath),
            'modified' => date('Y-m-d H:i:s', filemtime($fullPath))
        ];
        
        $this->view->assign('templateInfo', $templateInfo);
        return $this->view->fetch();
    }
    
    /**
     * 编辑Word模板页面
     */
    public function WordTemplateDetail()
    {
        $templatePath = $this->request->get('path');
        
        if (empty($templatePath)) {
            $this->error('未指定要查看的模板文件');
        }
        
        // 检查是否是合法的模板路径（必须在templates/resume目录下）
        $basePath = 'public' . DS . 'templates' . DS . 'resume';
        $normalizedPath = str_replace('\\', '/', $templatePath);
        $normalizedBasePath = str_replace('\\', '/', $basePath);
        
        if (strpos($normalizedPath, $normalizedBasePath) === false) {
            $this->error('非法的模板路径');
        }
        
        // 获取完整文件路径
        $fullPath = ROOT_PATH . $templatePath;
        
        // 检查文件是否存在
        if (!file_exists($fullPath)) {
            $this->error('模板文件不存在');
        }
        
        // 检查文件类型
        $ext = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));
        if ($ext != 'docx') {
            $this->error('非Word模板文件');
        }
        
        // 获取模板文件信息
        $templateInfo = [
            'name' => basename($fullPath),
            'path' => $templatePath,
            'size' => filesize($fullPath),
            'modified' => date('Y-m-d H:i:s', filemtime($fullPath))
        ];
        
        $this->view->assign('templateInfo', $templateInfo);
        return $this->view->fetch('word_template_detail');
    }
    
    /**
     * 获取Excel模板的占位符列表
     */
    public function getExcelPlaceholders()
    {
        $templatePath = $this->request->get('path');
        
        if (empty($templatePath)) {
            return json(['code' => 0, 'msg' => '未指定模板文件']);
        }
        
        // 检查是否是合法的模板路径
        $basePath = 'public' . DS . 'templates' . DS . 'resume';
        $normalizedPath = str_replace('\\', '/', $templatePath);
        $normalizedBasePath = str_replace('\\', '/', $basePath);
        
        if (strpos($normalizedPath, $normalizedBasePath) === false) {
            return json(['code' => 0, 'msg' => '非法的模板路径']);
        }
        
        // 获取完整文件路径
        $fullPath = ROOT_PATH . $templatePath;
        
        // 检查文件是否存在
        if (!file_exists($fullPath)) {
            return json(['code' => 0, 'msg' => '模板文件不存在']);
        }
        
        try {
            // 尝试从模板文件中提取占位符
            $placeholders = $this->extractPlaceholdersFromExcel($fullPath);
            
            // 返回提取到的占位符，即使为空
            return json(['code' => 1, 'msg' => '获取占位符列表成功', 'data' => $placeholders]);
            
        } catch (\Exception $e) {
            \think\Log::record('获取Excel占位符异常: ' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => '读取Excel文件失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 获取Excel文件内容
     * 用于在线编辑Excel模板
     */
    public function getExcelContent()
    {
        $templatePath = $this->request->get('path');
        
        if (empty($templatePath)) {
            return json(['code' => 0, 'msg' => '未指定要编辑的模板文件']);
        }
        
        // 检查是否是合法的模板路径（必须在templates/resume目录下）
        $basePath = 'public' . DS . 'templates' . DS . 'resume';
        $normalizedPath = str_replace('\\', '/', $templatePath);
        $normalizedBasePath = str_replace('\\', '/', $basePath);
        
        if (strpos($normalizedPath, $normalizedBasePath) === false) {
            return json(['code' => 0, 'msg' => '非法的模板路径']);
        }
        
        // 获取完整文件路径
        $fullPath = ROOT_PATH . $templatePath;
        
        // 检查文件是否存在
        if (!file_exists($fullPath)) {
            return json(['code' => 0, 'msg' => '模板文件不存在']);
        }
        
        // 检查文件类型
        $ext = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));
        if ($ext != 'xlsx') {
            return json(['code' => 0, 'msg' => '非Excel模板文件']);
        }
        
        try {
            // 读取文件内容并转为base64
            $fileContent = file_get_contents($fullPath);
            
            // 确保文件内容正确读取
            if ($fileContent === false) {
                return json(['code' => 0, 'msg' => '读取文件内容失败']);
            }
            
            // 添加MIME类型前缀，使其成为标准的Data URL
            $base64Content = 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,' . base64_encode($fileContent);
            
            return json(['code' => 1, 'msg' => '获取Excel内容成功', 'data' => ['base64' => $base64Content]]);
        } catch (\Exception $e) {
            \think\Log::record('获取Excel内容异常: ' . $e->getMessage() . ' 位置: ' . $e->getFile() . ':' . $e->getLine(), 'error');
            return json(['code' => 0, 'msg' => '获取Excel内容失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 保存Excel文件内容
     * 用于在线编辑后保存Excel模板
     */
    public function saveExcelContent()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '请求方式错误']);
        }
        
        $templatePath = $this->request->post('path');
        $content = $this->request->post('content');
        
        if (empty($templatePath)) {
            return json(['code' => 0, 'msg' => '未指定要保存的模板文件']);
        }
        
        if (empty($content)) {
            return json(['code' => 0, 'msg' => '未提供文件内容']);
        }
        
        // 检查是否是合法的模板路径（必须在templates/resume目录下）
        $basePath = 'public' . DS . 'templates' . DS . 'resume';
        $normalizedPath = str_replace('\\', '/', $templatePath);
        $normalizedBasePath = str_replace('\\', '/', $basePath);
        
        if (strpos($normalizedPath, $normalizedBasePath) === false) {
            return json(['code' => 0, 'msg' => '非法的模板路径']);
        }
        
        // 获取完整文件路径
        $fullPath = ROOT_PATH . $templatePath;
        
        // 检查文件是否存在
        if (!file_exists($fullPath)) {
            return json(['code' => 0, 'msg' => '模板文件不存在']);
        }
        
        // 检查文件类型
        $ext = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));
        if ($ext != 'xlsx') {
            return json(['code' => 0, 'msg' => '非Excel模板文件']);
        }
        
        try {
            // 清理旧的备份文件，只保留最近1个备份
            $this->cleanupBackupFiles($fullPath, 1);
            
            // 创建临时文件用于备份
            $backupPath = $fullPath . '.bak.' . date('YmdHis');
            if (!copy($fullPath, $backupPath)) {
                return json(['code' => 0, 'msg' => '创建备份文件失败，请检查目录权限']);
            }
            
            // 保存前端发送的JSON数据，用于调试和记录
            $jsonFilePath = $fullPath . '.json';
            file_put_contents($jsonFilePath, $content);
            
            // 解析LuckySheet JSON数据
            $luckySheetData = json_decode($content, true);
            if (!$luckySheetData || !isset($luckySheetData['sheets']) || empty($luckySheetData['sheets'])) {
                return json(['code' => 0, 'msg' => 'Excel数据格式错误']);
            }
            
            // 检查PhpSpreadsheet库是否可用
            if (!class_exists('\\PhpOffice\\PhpSpreadsheet\\IOFactory')) {
                return json(['code' => 0, 'msg' => '服务器未安装PhpSpreadsheet库，无法处理Excel文件']);
            }
            
            // 加载原始Excel模板
            $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
            $reader->setReadDataOnly(false); // 确保读取所有格式信息
            $spreadsheet = $reader->load($fullPath);
            
            // 首先读取原始Excel的所有单元格值，用于后续比较
            $originalCellValues = [];
            foreach ($spreadsheet->getWorksheetIterator() as $worksheet) {
                $sheetName = $worksheet->getTitle();
                $originalCellValues[$sheetName] = [];
                
                foreach ($worksheet->getRowIterator() as $row) {
                    $rowIndex = $row->getRowIndex();
                    $cellIterator = $row->getCellIterator();
                    $cellIterator->setIterateOnlyExistingCells(true);
                    
                    foreach ($cellIterator as $cell) {
                        $columnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($cell->getColumn());
                        $value = $cell->getValue();
                        $originalCellValues[$sheetName][$rowIndex][$columnIndex] = $value;
                        
                        // 记录包含换行符的单元格
                        if (is_string($value) && (strpos($value, "\n") !== false || strpos($value, "\r") !== false)) {
                            \think\Log::record("检测到包含换行符的单元格: {$sheetName}!{$cell->getCoordinate()}, 值: " . json_encode($value), 'info');
                        }
                    }
                }
            }
            
            // 记录已更新的单元格数量
            $updatedCellCount = 0;
            $preservedNewlineCount = 0;
            
            // 遍历LuckySheet中的每个工作表
            foreach ($luckySheetData['sheets'] as $sheetData) {
                // 跳过隐藏的工作表
                if (isset($sheetData['hide']) && $sheetData['hide'] == 1) {
                    continue;
                }
                
                // 获取工作表名称
                $sheetName = isset($sheetData['name']) ? $sheetData['name'] : 'Sheet' . $sheetData['index'];
                
                // 检查工作表是否存在，不存在则跳过（保持原模板结构）
                if (!$spreadsheet->sheetNameExists($sheetName)) {
                    continue;
                }
                
                // 获取工作表
                $worksheet = $spreadsheet->getSheetByName($sheetName);
                
                // 预处理：标记所有包含换行符的单元格
                $newlineCells = [];
                if (isset($sheetData['celldata']) && is_array($sheetData['celldata'])) {
                    foreach ($sheetData['celldata'] as $cellData) {
                        if (isset($cellData['v']) && isset($cellData['v']['v']) && is_string($cellData['v']['v'])) {
                            $value = $cellData['v']['v'];
                            if (strpos($value, "\n") !== false || strpos($value, "\r") !== false) {
                                $rowIndex = $cellData['r'] + 1;
                                $columnIndex = $cellData['c'] + 1;
                                $key = $rowIndex . '_' . $columnIndex;
                                $newlineCells[$key] = true;
                                
                                \think\Log::record("前端数据中包含换行符的单元格: {$sheetName}!{$rowIndex}_{$columnIndex}, 值: " . json_encode($value), 'info');
                            }
                        }
                    }
                }
                
                // 处理单元格数据
                if (isset($sheetData['celldata']) && is_array($sheetData['celldata'])) {
                    foreach ($sheetData['celldata'] as $cellData) {
                        // 获取单元格位置
                        $rowIndex = $cellData['r'] + 1; // LuckySheet行索引从0开始，PhpSpreadsheet从1开始
                        $columnIndex = $cellData['c'] + 1; // 列同理
                        
                        // 将列索引转换为列字母（A, B, C...）
                        $columnLetter = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($columnIndex);
                        $cellCoordinate = $columnLetter . $rowIndex;
                        
                        // 检查是否是包含换行符的单元格
                        $cellKey = $rowIndex . '_' . $columnIndex;
                        $hasNewlines = isset($newlineCells[$cellKey]);
                        
                        // 获取单元格值
                        $newValue = null;
                        if (isset($cellData['v'])) {
                            if (isset($cellData['v']['v'])) {
                                $newValue = $cellData['v']['v'];
                                
                                // 如果是字符串且包含换行符，记录日志
                                if (is_string($newValue) && (strpos($newValue, "\n") !== false || strpos($newValue, "\r") !== false)) {
                                    \think\Log::record("处理包含换行符的单元格: {$sheetName}!{$cellCoordinate}, 值: " . json_encode($newValue), 'info');
                                    $hasNewlines = true;
                                }
                            } else {
                                // 单元格被清空的情况
                                $newValue = null;
                            }
                        }
                        
                        // 检查单元格值是否有变化
                        $originalValue = isset($originalCellValues[$sheetName][$rowIndex][$columnIndex]) 
                            ? $originalCellValues[$sheetName][$rowIndex][$columnIndex] 
                            : null;
                        
                        // 如果原值包含换行符，记录日志
                        if (is_string($originalValue) && (strpos($originalValue, "\n") !== false || strpos($originalValue, "\r") !== false)) {
                            \think\Log::record("原值包含换行符: {$sheetName}!{$cellCoordinate}, 值: " . json_encode($originalValue), 'info');
                            $hasNewlines = true;
                        }
                        
                        // 比较值，只更新有变化的单元格
                        $changed = $this->cellValueHasChanged($originalValue, $newValue);
                        
                        if ($changed) {
                            // 记录日志
                            \think\Log::record("检测到单元格变化: {$sheetName}!{$cellCoordinate}, 原值: " . json_encode($originalValue) . ", 新值: " . json_encode($newValue), 'info');
                            
                            // 如果原值包含换行符但新值为空，需要特殊处理
                            if ($hasNewlines && ($newValue === null || $newValue === '')) {
                                // 检查是否是真的要清空，还是因为换行符导致的误判
                                if (isset($cellData['hasNewlines']) && $cellData['hasNewlines'] === true) {
                                    \think\Log::record("保留带换行符的单元格: {$sheetName}!{$cellCoordinate}", 'info');
                                    $preservedNewlineCount++;
                                    continue; // 跳过此单元格，不更新
                                }
                                
                                // 检查原值是否只包含空白和换行符
                                if (is_string($originalValue)) {
                                    $trimmed = trim(str_replace(["\r\n", "\r", "\n"], '', $originalValue));
                                    if ($trimmed === '') {
                                        \think\Log::record("原值只包含空白和换行符，可以清空: {$sheetName}!{$cellCoordinate}", 'info');
                                    } else {
                                        \think\Log::record("原值包含实际内容，保留: {$sheetName}!{$cellCoordinate}", 'info');
                                        $preservedNewlineCount++;
                                        continue; // 跳过此单元格，不更新
                                    }
                                }
                            }
                            
                            // 设置单元格值
                            if ($newValue === null || $newValue === '') {
                                // 确保不是只包含换行符的情况
                                $isOnlyNewlines = false;
                                if (is_string($originalValue) && trim($originalValue) !== '') {
                                    $trimmed = trim(str_replace(["\r\n", "\r", "\n"], '', $originalValue));
                                    if ($trimmed === '') {
                                        $isOnlyNewlines = true;
                                    }
                                }
                                
                                // 如果原值包含实际内容（不只是换行符），则不清空
                                if ($hasNewlines && !$isOnlyNewlines) {
                                    \think\Log::record("保留包含换行符的单元格内容: {$sheetName}!{$cellCoordinate}", 'info');
                                    $preservedNewlineCount++;
                                } else {
                                    // 清空单元格
                                    $worksheet->getCell($cellCoordinate)->setValue(null);
                                    // 记录日志
                                    \think\Log::record("清空单元格: {$sheetName}!{$cellCoordinate}", 'info');
                                    $updatedCellCount++;
                                }
                            } else {
                                // 设置单元格值
                                $worksheet->setCellValue($cellCoordinate, $newValue);
                                
                                // 如果值包含换行符，确保正确设置格式
                                if (is_string($newValue) && (strpos($newValue, "\n") !== false || strpos($newValue, "\r") !== false)) {
                                    $worksheet->getStyle($cellCoordinate)->getAlignment()->setWrapText(true);
                                }
                                
                                $updatedCellCount++;
                            }
                        }
                    }
                }
            }
            
            // 保存Excel文件
            $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
            $writer->save($fullPath);
            
            // 清理临时JSON文件，只保留最新的一个
            $this->cleanupJsonFiles($fullPath, 1);
            
            $message = 'Excel文件保存成功，共更新了' . $updatedCellCount . '个单元格';
            if ($preservedNewlineCount > 0) {
                $message .= '，保留了' . $preservedNewlineCount . '个带换行符的单元格';
            }
            
            return json([
                'code' => 1, 
                'msg' => $message,
                'data' => [
                    'updated_cells' => $updatedCellCount,
                    'preserved_newline_cells' => $preservedNewlineCount
                ]
            ]);
            
        } catch (\Exception $e) {
            \think\Log::record('保存Excel内容异常: ' . $e->getMessage() . ' 位置: ' . $e->getFile() . ':' . $e->getLine(), 'error');
            return json(['code' => 0, 'msg' => '保存Excel内容失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 清理旧的备份文件
     * @param string $filePath 原始文件路径
     * @param int $keepCount 保留的备份文件数量
     */
    protected function cleanupBackupFiles($filePath, $keepCount = 5)
    {
        try {
            $dir = dirname($filePath);
            $filename = basename($filePath);
            $pattern = $filename . '.bak.*';
            
            // 获取所有匹配的备份文件
            $backupFiles = glob($dir . DS . $pattern);
            
            // 如果备份文件数量超过保留数量
            if (count($backupFiles) >= $keepCount) {
                // 按文件修改时间排序
                usort($backupFiles, function($a, $b) {
                    return filemtime($b) - filemtime($a); // 降序排列，最新的在前面
                });
                
                // 删除多余的旧备份
                for ($i = $keepCount; $i < count($backupFiles); $i++) {
                    if (file_exists($backupFiles[$i])) {
                        @unlink($backupFiles[$i]);
                        \think\Log::record('删除旧备份文件: ' . $backupFiles[$i], 'info');
                    }
                }
            }
        } catch (\Exception $e) {
            \think\Log::record('清理备份文件异常: ' . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 清理JSON临时文件
     * @param string $filePath 原始文件路径
     * @param int $keepCount 保留的JSON文件数量
     */
    protected function cleanupJsonFiles($filePath, $keepCount = 1)
    {
        try {
            $dir = dirname($filePath);
            $filename = basename($filePath);
            $pattern = $filename . '.json*';
            
            // 获取所有匹配的JSON文件
            $jsonFiles = glob($dir . DS . $pattern);
            
            // 如果JSON文件数量超过保留数量
            if (count($jsonFiles) > $keepCount) {
                // 按文件修改时间排序
                usort($jsonFiles, function($a, $b) {
                    return filemtime($b) - filemtime($a); // 降序排列，最新的在前面
                });
                
                // 删除多余的旧JSON文件
                for ($i = $keepCount; $i < count($jsonFiles); $i++) {
                    if (file_exists($jsonFiles[$i])) {
                        @unlink($jsonFiles[$i]);
                        \think\Log::record('删除旧JSON文件: ' . $jsonFiles[$i], 'info');
                    }
                }
            }
        } catch (\Exception $e) {
            \think\Log::record('清理JSON文件异常: ' . $e->getMessage(), 'error');
        }
    }
    
    /**
     * 比较单元格值是否发生变化
     * @param mixed $originalValue 原始值
     * @param mixed $newValue 新值
     * @return bool 是否有变化
     */
    protected function cellValueHasChanged($originalValue, $newValue)
    {
        // 特别处理换行符的情况
        if (is_string($originalValue) && is_string($newValue)) {
            // 标准化换行符，将所有换行符统一为\n
            $originalNormalized = str_replace(["\r\n", "\r"], "\n", $originalValue);
            $newNormalized = str_replace(["\r\n", "\r"], "\n", $newValue);
            
            // 如果标准化后相等，则没有变化
            if ($originalNormalized === $newNormalized) {
                return false;
            }
            
            // 处理只有空白字符和换行符的情况
            $originalTrimmed = trim(str_replace(["\n", "\r"], '', $originalNormalized));
            $newTrimmed = trim(str_replace(["\n", "\r"], '', $newNormalized));
            
            // 如果两者都是空白或只有换行符，则认为没有变化
            if ($originalTrimmed === '' && $newTrimmed === '') {
                return false;
            }
            
            // 如果原值包含实际内容和换行符，但新值为空，认为是误判，不应该清空
            if ($originalTrimmed !== '' && 
                (strpos($originalNormalized, "\n") !== false) && 
                ($newValue === null || $newValue === '' || $newTrimmed === '')) {
                \think\Log::record("原值包含实际内容和换行符，但新值为空，认为是误判: 原值=" . json_encode($originalValue), 'info');
                return false;
            }
        }
        
        // 处理空值情况 - 特别处理清空单元格的情况
        if ($originalValue !== null && ($newValue === null || $newValue === '')) {
            // 如果原值是字符串且包含换行符，需要特殊处理
            if (is_string($originalValue) && 
                (strpos($originalValue, "\n") !== false || strpos($originalValue, "\r") !== false)) {
                // 检查原值是否只包含空白和换行符
                $trimmed = trim(str_replace(["\r\n", "\r", "\n"], '', $originalValue));
                if ($trimmed !== '') {
                    // 原值包含实际内容，不应该被清空
                    \think\Log::record("原值包含实际内容和换行符，不应该被清空: " . json_encode($originalValue), 'info');
                    return false;
                }
            }
            
            // 原值不为空，新值为空，说明单元格被清空了
            \think\Log::record("检测到单元格被清空: 原值: " . json_encode($originalValue) . ", 新值: " . json_encode($newValue), 'info');
            return true;
        }
        
        // 如果原值为空字符串或null，新值也为空字符串或null，则认为没有变化
        if (($originalValue === null || $originalValue === '') && 
            ($newValue === null || $newValue === '')) {
            return false;
        }
        
        // 处理数字类型
        if (is_numeric($originalValue) && is_numeric($newValue)) {
            return (float)$originalValue !== (float)$newValue;
        }
        
        // 处理日期类型
        if ($originalValue instanceof \DateTime && is_string($newValue)) {
            return $originalValue->format('Y-m-d') !== $newValue;
        }
        
        // 处理字符串类型，特别注意处理换行符
        if (is_string($originalValue) && is_string($newValue)) {
            // 标准化换行符，将所有换行符统一为\n
            $originalNormalized = str_replace(["\r\n", "\r"], "\n", trim($originalValue));
            $newNormalized = str_replace(["\r\n", "\r"], "\n", trim($newValue));
            
            return $originalNormalized !== $newNormalized;
        }
        
        // 其他情况，直接比较
        return $originalValue !== $newValue;
    }
    
    /**
     * 从Excel文件中提取占位符
     * @param string $filePath Excel文件路径
     * @return array 占位符列表
     */
    protected function extractPlaceholdersFromExcel($filePath)
    {
        // 检查是否安装了PhpSpreadsheet或PHPExcel
        if (!class_exists('\\PhpOffice\\PhpSpreadsheet\\IOFactory') && !class_exists('\\PHPExcel_IOFactory')) {
            // 如果没有安装相关库，返回空数组，将使用默认占位符列表
            return [];
        }
        
        $placeholders = [];
        $placeholderPattern = '/\{\{([^}]+)\}\}/'; // 匹配{{xxx}}格式的占位符
        
        try {
            if (class_exists('\\PhpOffice\\PhpSpreadsheet\\IOFactory')) {
                // 使用PhpSpreadsheet库
                $reader = \PhpOffice\PhpSpreadsheet\IOFactory::createReader('Xlsx');
                $spreadsheet = $reader->load($filePath);
                
                foreach ($spreadsheet->getWorksheetIterator() as $worksheet) {
                    foreach ($worksheet->getRowIterator() as $row) {
                        $cellIterator = $row->getCellIterator();
                        $cellIterator->setIterateOnlyExistingCells(false);
                        
                        foreach ($cellIterator as $cell) {
                            $cellValue = $cell->getValue();
                            if (is_string($cellValue) && preg_match_all($placeholderPattern, $cellValue, $matches)) {
                                foreach ($matches[1] as $match) {
                                    $category = $this->getPlaceholderCategory($match);
                                    $placeholders[] = [
                                        'name' => '{{' . $match . '}}',
                                        'description' => $this->getPlaceholderDescription($match),
                                        'category' => $category
                                    ];
                                }
                            }
                        }
                    }
                }
            } elseif (class_exists('\\PHPExcel_IOFactory')) {
                // 使用PHPExcel库
                $reader = \PHPExcel_IOFactory::createReader('Excel2007');
                $excel = $reader->load($filePath);
                
                foreach ($excel->getWorksheetIterator() as $worksheet) {
                    foreach ($worksheet->getRowIterator() as $row) {
                        $cellIterator = $row->getCellIterator();
                        $cellIterator->setIterateOnlyExistingCells(false);
                        
                        foreach ($cellIterator as $cell) {
                            $cellValue = $cell->getValue();
                            if (is_string($cellValue) && preg_match_all($placeholderPattern, $cellValue, $matches)) {
                                foreach ($matches[1] as $match) {
                                    $category = $this->getPlaceholderCategory($match);
                                    $placeholders[] = [
                                        'name' => '{{' . $match . '}}',
                                        'description' => $this->getPlaceholderDescription($match),
                                        'category' => $category
                                    ];
                                }
                            }
                        }
                    }
                }
            }
            
            // 去重
            $uniquePlaceholders = [];
            $names = [];
            
            foreach ($placeholders as $placeholder) {
                if (!in_array($placeholder['name'], $names)) {
                    $names[] = $placeholder['name'];
                    $uniquePlaceholders[] = $placeholder;
                }
            }
            
            return $uniquePlaceholders;
            
        } catch (\Exception $e) {
            \think\Log::record('提取Excel占位符异常: ' . $e->getMessage(), 'error');
            return [];
        }
    }
    
    /**
     * 获取占位符所属分类
     * @param string $name 占位符名称
     * @return string 占位符分类
     */
    protected function getPlaceholderCategory($name)
    {
        // 基本信息字段
        $basicInfoFields = [
            '姓名', '意向岗位', '申请岗位', '民族', '身高', '体重', '身份证号', '性别', '年龄', 
            '出生日期', '手机号码', '婚姻状况', '户口所在地', '常住地址', '最高学历', 
            '粤语熟练度', '国语熟练度', '英语熟练度', '港澳通行证编号', '澳通行证到期时间',
            '特殊职业资格证', '海外工作经历', '海外工作地区', '兴趣爱好', '自我评价'
        ];
        
        // 图片字段
        $photoFields = [
            '头像', '全身照', '身份证正面', '身份证反面', '港澳通行证正面', '港澳通行证反面'
        ];
        
        // 其他字段
        $otherFields = ['对接人', '更新时间'];
        
        // 判断是否为联系人信息
        if (strpos($name, '联系人') !== false) {
            return '联系人信息';
        }
        
        // 判断是否为教育经历
        if (strpos($name, '教育') !== false || strpos($name, '学校') !== false || 
            strpos($name, '专业') !== false || strpos($name, '学历') !== false) {
            return '教育经历';
        }
        
        // 判断是否为工作经历
        if (strpos($name, '工作') !== false) {
            return '工作经历';
        }
        
        // 判断是否为照片/图片
        if (strpos($name, '照片') !== false || in_array($name, $photoFields)) {
            return '图片';
        }
        
        // 判断是否为基本信息
        if (in_array($name, $basicInfoFields)) {
            return '基本信息';
        }
        
        // 判断是否为其他信息
        if (in_array($name, $otherFields)) {
            return '其他';
        }
        
        // 默认归类为基本信息
        return '基本信息';
    }
    
    /**
     * 获取占位符描述
     * @param string $name 占位符名称
     * @return string 占位符描述
     */
    protected function getPlaceholderDescription($name)
    {
        // 根据占位符名称生成描述
        $descriptions = [
            '姓名' => '求职者姓名',
            '性别' => '求职者性别',
            '出生日期' => '出生日期，格式：YYYY年MM月DD日',
            '年龄' => '求职者年龄',
            '手机号码' => '联系电话',
            '邮箱' => '电子邮箱地址',
            '最高学历' => '最高学历',
            '头像' => '个人头像照片',
            '身份证号' => '身份证号码',
            '民族' => '民族信息',
            '政治面貌' => '政治面貌',
            '婚姻状况' => '婚姻状况',
            '户口所在地' => '户籍所在地',
            '常住地址' => '现居住地址',
            '意向岗位' => '求职意向岗位',
            '申请岗位' => '申请岗位',
            '期望薪资' => '期望薪资',
            '身高' => '身高(cm)',
            '体重' => '体重(kg)',
            '粤语熟练度' => '粤语熟练程度',
            '国语熟练度' => '国语熟练程度',
            '英语熟练度' => '英语熟练程度',
            '港澳通行证编号' => '港澳通行证号码',
            '澳通行证到期时间' => '港澳通行证到期时间，格式：YYYY年MM月DD日',
            '特殊职业资格证' => '特殊职业资格证书',
            '海外工作经历' => '海外工作经历',
            '海外工作地区' => '海外工作地区',
            '兴趣爱好' => '个人兴趣爱好',
            '自我评价' => '个人自我评价',
            '全身照' => '个人全身照片',
            '身份证正面' => '身份证正面照片',
            '身份证反面' => '身份证反面照片',
            '港澳通行证正面' => '港澳通行证正面照片',
            '港澳通行证反面' => '港澳通行证反面照片',
            '补充照片' => '补充照片',
            '对接人' => '对接人姓名',
            '更新时间' => '简历更新时间'
        ];
        
        // 处理带序号的字段
        // 联系人信息
        if (preg_match('/^联系人关系(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}位联系人关系";
        }
        
        if (preg_match('/^联系人姓名(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}位联系人姓名";
        }
        
        if (preg_match('/^联系人年龄(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}位联系人年龄";
        }
        
        if (preg_match('/^联系人工作(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}位联系人工作";
        }
        
        // 教育经历
        if (preg_match('/^教育开始(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段教育开始时间";
        }
        
        if (preg_match('/^教育结束(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段教育结束时间";
        }
        
        if (preg_match('/^学校(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段学校名称";
        }
        
        if (preg_match('/^专业(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段所学专业";
        }
        
        if (preg_match('/^学历(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段学历层次";
        }
        
        // 工作经历
        if (preg_match('/^工作开始(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段工作开始时间";
        }
        
        if (preg_match('/^工作结束(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段工作结束时间";
        }
        
        if (preg_match('/^工作单位(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段工作单位名称";
        }
        
        if (preg_match('/^工作岗位(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段工作岗位名称";
        }
        
        if (preg_match('/^工作内容(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段工作内容描述";
        }
        
        // 补充照片
        if (preg_match('/^补充照片(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}张补充照片";
        }
        
        // 处理旧格式的教育经历和工作经历
        if (preg_match('/^教育经历(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}条教育经历";
        }
        
        if (preg_match('/^工作经历(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}条工作经历";
        }
        
        // 返回默认描述或查找到的描述
        return isset($descriptions[$name]) ? $descriptions[$name] : $name;
    }
    
    
    /**
     * 获取Word模板的占位符列表
     */
    public function getWordPlaceholders()
    {
        $templatePath = $this->request->get('path');
        
        if (empty($templatePath)) {
            return json(['code' => 0, 'msg' => '未指定模板文件']);
        }
        
        // 检查是否是合法的模板路径
        $basePath = 'public' . DS . 'templates' . DS . 'resume';
        $normalizedPath = str_replace('\\', '/', $templatePath);
        $normalizedBasePath = str_replace('\\', '/', $basePath);
        
        if (strpos($normalizedPath, $normalizedBasePath) === false) {
            return json(['code' => 0, 'msg' => '非法的模板路径']);
        }
        
        // 获取完整文件路径
        $fullPath = ROOT_PATH . $templatePath;
        
        // 检查文件是否存在
        if (!file_exists($fullPath)) {
            return json(['code' => 0, 'msg' => '模板文件不存在']);
        }
        
        try {
            // 尝试从模板文件中提取占位符
            $placeholders = $this->extractPlaceholdersFromWord($fullPath);
            
            // 返回提取到的占位符，即使为空
            return json(['code' => 1, 'msg' => '获取占位符列表成功', 'data' => $placeholders]);
            
        } catch (\Exception $e) {
            \think\Log::record('获取Word占位符异常: ' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => '读取Word文件失败: ' . $e->getMessage()]);
        }
    }
    
    /**
     * 从Word文件中提取占位符
     * @param string $filePath Word文件路径
     * @return array 占位符列表
     */
    protected function extractPlaceholdersFromWord($filePath)
    {
        // 检查是否安装了PhpWord
        if (!class_exists('\\PhpOffice\\PhpWord\\PhpWord')) {
            return [];
        }
        
        $placeholders = [];
        $placeholderPattern = '/\$\{([^}]+)\}/'; // 匹配${xxx}格式的占位符
        
        try {
            // 使用ZipArchive读取docx文件中的document.xml
            $zip = new \ZipArchive();
            if ($zip->open($filePath) === true) {
                if (($index = $zip->locateName('word/document.xml')) !== false) {
                    $content = $zip->getFromIndex($index);
                    $zip->close();
                    
                    // 清理XML标记，只保留纯文本内容
                    $cleanedContent = $this->cleanWordXml($content);
                    
                    // 提取占位符
                    if (preg_match_all($placeholderPattern, $cleanedContent, $matches)) {
                        foreach ($matches[1] as $match) {
                            $fieldName = trim($match);
                            $category = $this->getWordPlaceholderCategory($fieldName);
                            $placeholders[] = [
                                'name' => '${' . $fieldName . '}',
                                'description' => $this->getWordPlaceholderDescription($fieldName),
                                'category' => $category
                            ];
                        }
                    }
                }
            }
            
            // 去重
            $uniquePlaceholders = [];
            $names = [];
            
            foreach ($placeholders as $placeholder) {
                if (!in_array($placeholder['name'], $names)) {
                    $names[] = $placeholder['name'];
                    $uniquePlaceholders[] = $placeholder;
                }
            }
            
            return $uniquePlaceholders;
            
        } catch (\Exception $e) {
            \think\Log::record('提取Word占位符异常: ' . $e->getMessage(), 'error');
            return [];
        }
    }
    
    /**
     * 清理Word XML标记，提取纯文本内容
     * @param string $xml Word XML内容
     * @return string 清理后的纯文本
     */
    protected function cleanWordXml($xml)
    {
        // 首先移除所有XML标签，但保留标签之间的文本内容
        $text = preg_replace('/<[^>]*>/', ' ', $xml);
        
        // 解码XML实体
        $text = html_entity_decode($text);
        
        // 移除多余的空格
        $text = preg_replace('/\s+/', ' ', $text);
        
        // 提取所有${...}格式的占位符
        $placeholders = [];
        if (preg_match_all('/\$\{\s*([^}]+?)\s*\}/', $text, $matches)) {
            foreach ($matches[1] as $match) {
                $fieldName = trim($match);
                $placeholders[] = '${' . $fieldName . '}';
            }
        }
        
        // 去重
        $placeholders = array_unique($placeholders);
        
        return implode(' ', $placeholders);
    }
    
    /**
     * 获取占位符所属分类
     * @param string $name 占位符名称
     * @return string 占位符分类
     */
    protected function getWordPlaceholderCategory($name)
    {
        // 基本信息字段
        $basicInfoFields = [
            '姓名', '意向岗位', '申请岗位', '民族', '身高', '体重', '身份证号', '性别', '年龄', 
            '出生日期', '手机号码', '婚姻状况', '户口所在地', '常住地址', '最高学历', 
            '粤语熟练度', '国语熟练度', '英语熟练度', '港澳通行证编号', '澳通行证到期时间',
            '特殊职业资格证', '海外工作经历', '海外工作地区', '兴趣爱好', '自我评价'
        ];
        
        // 图片字段
        $photoFields = [
            '头像', '全身照', '身份证正面', '身份证反面', '港澳通行证正面', '港澳通行证反面'
        ];
        
        // 其他字段
        $otherFields = ['对接人', '更新时间'];
        
        // 判断是否为联系人信息
        if (strpos($name, '联系人') !== false) {
            return '联系人信息';
        }
        
        // 判断是否为教育经历
        if (strpos($name, '教育') !== false || strpos($name, '学校') !== false || 
            strpos($name, '专业') !== false || strpos($name, '学历') !== false) {
            return '教育经历';
        }
        
        // 判断是否为工作经历
        if (strpos($name, '工作') !== false) {
            return '工作经历';
        }
        
        // 判断是否为照片/图片
        if (strpos($name, '照片') !== false || in_array($name, $photoFields)) {
            return '图片';
        }
        
        // 判断是否为基本信息
        if (in_array($name, $basicInfoFields)) {
            return '基本信息';
        }
        
        // 判断是否为其他信息
        if (in_array($name, $otherFields)) {
            return '其他';
        }
        
        // 默认归类为基本信息
        return '基本信息';
    }
    
    /**
     * 获取占位符描述
     * @param string $name 占位符名称
     * @return string 占位符描述
     */
    protected function getWordPlaceholderDescription($name)
    {
        // 根据占位符名称生成描述
        $descriptions = [
            '姓名' => '求职者姓名',
            '性别' => '求职者性别',
            '出生日期' => '出生日期，格式：YYYY年MM月DD日',
            '年龄' => '求职者年龄',
            '手机号码' => '联系电话',
            '邮箱' => '电子邮箱地址',
            '最高学历' => '最高学历',
            '头像' => '个人头像照片',
            '身份证号' => '身份证号码',
            '民族' => '民族信息',
            '政治面貌' => '政治面貌',
            '婚姻状况' => '婚姻状况',
            '户口所在地' => '户籍所在地',
            '常住地址' => '现居住地址',
            '意向岗位' => '求职意向岗位',
            '申请岗位' => '申请岗位',
            '期望薪资' => '期望薪资',
            '身高' => '身高(cm)',
            '体重' => '体重(kg)',
            '粤语熟练度' => '粤语熟练程度',
            '国语熟练度' => '国语熟练程度',
            '英语熟练度' => '英语熟练程度',
            '港澳通行证编号' => '港澳通行证号码',
            '澳通行证到期时间' => '港澳通行证到期时间，格式：YYYY年MM月DD日',
            '特殊职业资格证' => '特殊职业资格证书',
            '海外工作经历' => '海外工作经历',
            '海外工作地区' => '海外工作地区',
            '兴趣爱好' => '个人兴趣爱好',
            '自我评价' => '个人自我评价',
            '全身照' => '个人全身照片',
            '身份证正面' => '身份证正面照片',
            '身份证反面' => '身份证反面照片',
            '港澳通行证正面' => '港澳通行证正面照片',
            '港澳通行证反面' => '港澳通行证反面照片',
            '补充照片' => '补充照片',
            '对接人' => '对接人姓名',
            '更新时间' => '简历更新时间'
        ];
        
        // 处理带序号的字段
        // 联系人信息
        if (preg_match('/^联系人关系(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}位联系人关系";
        }
        
        if (preg_match('/^联系人姓名(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}位联系人姓名";
        }
        
        if (preg_match('/^联系人年龄(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}位联系人年龄";
        }
        
        if (preg_match('/^联系人工作(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}位联系人工作";
        }
        
        // 教育经历
        if (preg_match('/^教育开始(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段教育开始时间";
        }
        
        if (preg_match('/^教育结束(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段教育结束时间";
        }
        
        if (preg_match('/^学校(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段学校名称";
        }
        
        if (preg_match('/^专业(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段所学专业";
        }
        
        if (preg_match('/^学历(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段学历层次";
        }
        
        // 工作经历
        if (preg_match('/^工作开始(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段工作开始时间";
        }
        
        if (preg_match('/^工作结束(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段工作结束时间";
        }
        
        if (preg_match('/^工作单位(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段工作单位名称";
        }
        
        if (preg_match('/^工作岗位(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段工作岗位名称";
        }
        
        if (preg_match('/^工作内容(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}段工作内容描述";
        }
        
        // 补充照片
        if (preg_match('/^补充照片(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}张补充照片";
        }
        
        // 处理旧格式的教育经历和工作经历
        if (preg_match('/^教育经历(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}条教育经历";
        }
        
        if (preg_match('/^工作经历(\d+)$/', $name, $matches)) {
            return "第{$matches[1]}条工作经历";
        }
        
        // 返回默认描述或查找到的描述
        return isset($descriptions[$name]) ? $descriptions[$name] : $name;
    }
    
} 