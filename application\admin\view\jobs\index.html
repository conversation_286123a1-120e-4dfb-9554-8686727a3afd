<div class="panel panel-default panel-intro">
    
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="status">
            
            {foreach name="statusList" item="vo"}
            <li class="{:$Think.get.status === (string)$key ? 'active' : ''}"><a href="#t-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>
            {/foreach}
            <li class="{:$Think.get.status === null ? 'active' : ''}"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
        </ul>
    </div>
    

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {php}
                        $showBackButton = isset($_GET['from']) && $_GET['from'] === 'jobinterview';
                        {/php}
                        {if $showBackButton}
                        <a href="javascript:history.back();" class="btn btn-primary btn-bback" title="{:__('Back')}"><i class="fa fa-reply"></i> 返回面试管理</a>
                        {/if}
                        {:build_toolbar('refresh,add,edit,del')}
                                                <div class="dropdown btn-group {:$auth->check('jobcategory/multi')?'':'hide'}">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>
                            <ul class="dropdown-menu text-left" role="menu">
                                {foreach name="statusList" item="vo"}
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:" data-params="status={$key}">{:__('设置为' . $key)}</a></li>
                                {/foreach}
                            </ul>
                        </div>
                    </div>
                    
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('jobs/edit')}" 
                           data-operate-del="{:$auth->check('jobs/del')}"
                           width="100%">
                           
                    </table>
                    
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 自定义操作栏样式 */
.operate-col {
    background-color: #f9f9f9;
    padding: 8px !important;
    border-left: 1px solid #eee;
}

/* 美化按钮样式 */
.btn-copy {
    margin-right: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 鼠标悬停效果 */
.btn-copy:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0,0,0,0.15);
    transition: all 0.2s ease;
}
</style>
