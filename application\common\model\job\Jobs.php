<?php

namespace app\common\model\job;

use think\Model;


class Jobs extends Model
{

    

    

    // 表名
    protected $name = 'jobs';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = false;
    protected $updateTime = false;
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text'
    ];
    

    
    public function getStatusList()
    {
        return ['草稿' => __('草稿'), '上架' => __('上架'), '暂停' => __('暂停'), '下架' => __('下架')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }




    public function category()
    {
        return $this->belongsTo('Category', 'category_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }


    public function interview()
    {
        return $this->belongsTo('Interview', 'resume_count', 'id', [], 'LEFT')->setEagerlyType(0);
    }
}
