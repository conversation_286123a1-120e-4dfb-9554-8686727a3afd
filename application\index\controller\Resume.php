<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use app\common\model\Resume as ResumeModel;
use app\common\model\ResumeEducation;
use app\common\model\ResumeWork;
use app\common\model\ResumeContact;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;
use think\facade\Config;

class Resume extends Frontend
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new ResumeModel;
    }

    /**
     * 新增简历
     */
    public function add()
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录', url('index/user/login'));
        }

        // 添加调试信息
        \think\Log::write('OCR配置：' . json_encode([
            'baidu_ocr_enabled' => config('site.baidu_ocr_enabled'),
            'site_config' => config('site')
        ], JSON_UNESCAPED_UNICODE), 'debug');

        // 显式传递OCR配置到模板
        $this->assign('ocr_enabled', config('site.baidu_ocr_enabled'));

        if ($this->request->isPost()) {
            $params = $this->request->post();
            
            // 调试：打印提交的参数
            \think\Log::write('提交的参数：' . json_encode($params, JSON_UNESCAPED_UNICODE), 'debug');
            
            // 处理文件上传
            $files = $this->request->file();
            $uploadDir = '/uploads/resume/' . date('Ymd') . '/';
            
            // 确保上传目录存在
            if (!is_dir(ROOT_PATH . 'public' . $uploadDir)) {
                mkdir(ROOT_PATH . 'public' . $uploadDir, 0755, true);
            }

            $data = [
                'user_id' => $this->auth->id,
                'name' => $params['name'],
                'gender' => $params['gender'],
                'age' => $params['age'],
                'ethnicity' => $params['ethnicity'],
                'height' => $params['height'],
                'weight' => $params['weight'],
                'birth_date' => $params['birth_date'],
                'phone' => $params['phone'],
                'marital_status' => $params['marital_status'],
                'id_card' => $params['id_card'],
                'hk_macau_passport' => $params['hk_macau_passport'],
                'cantonese_level' => $params['cantonese_level'],
                'mandarin_level' => $params['mandarin_level'],
                'english_level' => $params['english_level'],
                'self_evaluation' => $params['self_evaluation'],
                'hobbies' => $params['hobbies'],
                'status' => '已发布',
                'create_time' => time(),
                'update_time' => time(),
                'intended_position' => $params['intended_position'],
                'applied_position' => $params['applied_position']
            ];

            // 处理户口所在地（省/市）
            if (isset($params['hukou_province']) && isset($params['hukou_city'])) {
                $hukou_location = [];
                // 获取省份名称
                $province = Db::name('area')->where('id', $params['hukou_province'])->value('name');
                // 获取城市名称
                $city = Db::name('area')->where('id', $params['hukou_city'])->value('name');
                
                if (!empty($province)) $hukou_location[] = $province;
                if (!empty($city)) $hukou_location[] = $city;
                $data['hukou_location'] = implode('/', $hukou_location);
            }

            // 处理常住地址（省/市/县）
            if (isset($params['residence_province']) && isset($params['residence_city']) && isset($params['residence_district'])) {
                $residence_address = [];
                // 获取省份名称
                $province = Db::name('area')->where('id', $params['residence_province'])->value('name');
                // 获取城市名称
                $city = Db::name('area')->where('id', $params['residence_city'])->value('name');
                // 获取区县名称
                $district = Db::name('area')->where('id', $params['residence_district'])->value('name');
                
                if (!empty($province)) $residence_address[] = $province;
                if (!empty($city)) $residence_address[] = $city;
                if (!empty($district)) $residence_address[] = $district;
                
                // 构建完整地址
                $data['residence_address'] = implode('/', $residence_address);
            }

            // 处理教育经历
            if (isset($params['graduation_education']) && is_array($params['graduation_education'])) {
                $highestEducation = '';
                $educationLevels = [
                    '高中' => 1,
                    '大专' => 2,
                    '本科' => 3,
                    '硕士' => 4,
                    '博士' => 5
                ];
                $maxLevel = 0;

                $starts = [];
                $ends = [];
                $schools = [];
                $majors = [];
                $degrees = [];

                foreach ($params['graduation_education'] as $key => $edu) {
                    if (!empty($edu)) {
                        $level = $educationLevels[$edu] ?? 0;
                        if ($level > $maxLevel) {
                            $maxLevel = $level;
                            $highestEducation = $edu;
                        }
                        
                        if (!empty($params['education_school'][$key])) {
                            $starts[] = $params['education_start'][$key] ?? '';
                            $ends[] = $params['education_end'][$key] ?? '';
                            $schools[] = $params['education_school'][$key];
                            $majors[] = $params['education_major'][$key] ?? '';
                            $degrees[] = $edu;
                        }
                    }
                }

                // 设置最高学历
                $data['highest_education'] = $highestEducation;
                
                // 保存教育经历数据
                $data['education_start'] = implode('|', $starts);
                $data['education_end'] = implode('|', $ends);
                $data['education_school'] = implode('|', $schools);
                $data['education_major'] = implode('|', $majors);
                $data['graduation_education'] = implode('|', $degrees);
            }

            // 处理工作经历
            if (isset($params['job_company']) && is_array($params['job_company'])) {
                $starts = [];
                $ends = [];
                $companies = [];
                $positions = [];
                $descriptions = [];

                foreach ($params['job_company'] as $key => $company) {
                    if (!empty($company)) {
                        $starts[] = $params['job_start'][$key] ?? '';
                        $ends[] = $params['job_end'][$key] ?? '';
                        $companies[] = $company;
                        $positions[] = $params['job_position'][$key] ?? '';
                        $descriptions[] = $params['job_description'][$key] ?? '';
                    }
                }
                
                // 保存工作经历数据
                $data['job_start'] = implode('|', $starts);
                $data['job_end'] = implode('|', $ends);
                $data['job_company'] = implode('|', $companies);
                $data['job_position'] = implode('|', $positions);
                $data['job_description'] = implode('|', $descriptions);
            }

            // 处理联系人信息
            if (isset($params['contact_name']) && is_array($params['contact_name'])) {
                $relations = [];
                $names = [];
                $ages = [];
                $jobs = [];

                foreach ($params['contact_name'] as $key => $name) {
                    if (!empty($name)) {
                        $relations[] = $params['contact_relation'][$key] ?? '';
                        $names[] = $name;
                        $ages[] = $params['contact_age'][$key] ?? '';
                        $jobs[] = $params['contact_job'][$key] ?? '';
                    }
                }
                
                // 保存联系人数据
                $data['contact_relation'] = implode('|', $relations);
                $data['contact_name'] = implode('|', $names);
                $data['contact_age'] = implode('|', $ages);
                $data['contact_job'] = implode('|', $jobs);
            }

            // 处理文件上传
            if (isset($files['avatar']) && $files['avatar']->isValid()) {
                $data['avatar'] = $this->uploadFile($files['avatar'], $uploadDir);
            }
            if (isset($files['full_body_photo']) && $files['full_body_photo']->isValid()) {
                $data['full_body_photo'] = $this->uploadFile($files['full_body_photo'], $uploadDir);
            }
            if (isset($files['id_card_front']) && $files['id_card_front']->isValid()) {
                $data['id_card_front'] = $this->uploadFile($files['id_card_front'], $uploadDir);
            }
            if (isset($files['id_card_back']) && $files['id_card_back']->isValid()) {
                $data['id_card_back'] = $this->uploadFile($files['id_card_back'], $uploadDir);
            }
            if (isset($files['additional_photos'])) {
                $additionalPhotos = [];
                foreach ($files['additional_photos'] as $photo) {
                    if ($photo->isValid()) {
                        $additionalPhotos[] = $this->uploadFile($photo, $uploadDir);
                    }
                }
                $data['additional_photos'] = implode(',', $additionalPhotos);
            }

            Db::startTrans();
            try {
                // 插入简历数据
                $resumeId = Db::name('resume')->insertGetId($data);
                
                // 调试：打印SQL语句
                \think\Log::write('SQL语句：' . Db::getLastSql(), 'debug');
                
                Db::commit();
                $this->success('简历创建成功', url('index/resume/list'));
            } catch (ValidateException $e) {
                Db::rollback();
                \think\Log::write('验证异常：' . $e->getMessage(), 'error');
                $this->error($e->getMessage());
            } catch (PDOException $e) {
                Db::rollback();
                \think\Log::write('数据库异常：' . $e->getMessage(), 'error');
                $this->error($e->getMessage());
            } catch (Exception $e) {
                Db::rollback();
                \think\Log::write('其他异常：' . $e->getMessage(), 'error');
                $this->error($e->getMessage());
            }
        }

        return $this->view->fetch();
    }

    /**
     * 申请职位
     */
    public function apply($job_id = null, $job_name = null)
    {
        if (!$job_id || !$job_name) {
            $this->error('参数错误');
        }

        // URL解码职位名称
        $job_name = urldecode($job_name);

        // 获取职位信息
        $job = Db::name('jobs')->where('id', $job_id)->find();
        if (!$job) {
            $this->error('职位不存在');
        }

        // 使用职位表中的title作为职位名称
        $job_name = $job['job_name'];

        // 显式传递OCR配置到模板
        $this->assign('ocr_enabled', config('site.baidu_ocr_enabled'));

        if ($this->request->isPost()) {
            $params = $this->request->post();
            
            // 调试：打印提交的参数
            \think\Log::write('提交的参数：' . json_encode($params, JSON_UNESCAPED_UNICODE), 'debug');
            
            // 处理文件上传
            $files = $this->request->file();
            $uploadDir = '/uploads/resume/' . date('Ymd') . '/';
            
            // 确保上传目录存在
            if (!is_dir(ROOT_PATH . 'public' . $uploadDir)) {
                mkdir(ROOT_PATH . 'public' . $uploadDir, 0755, true);
            }

            $data = [
                'user_id' => $this->auth->id,
                'intended_position' => $job_name,
                'applied_position' => $job_name,
                'name' => $params['name'],
                'gender' => $params['gender'],
                'age' => $params['age'],
                'ethnicity' => $params['ethnicity'],
                'height' => $params['height'],
                'weight' => $params['weight'],
                'birth_date' => $params['birth_date'],
                'phone' => $params['phone'],
                'marital_status' => $params['marital_status'],
                'id_card' => $params['id_card'],
                'hk_macau_passport' => $params['hk_macau_passport'],
                'cantonese_level' => $params['cantonese_level'],
                'mandarin_level' => $params['mandarin_level'],
                'english_level' => $params['english_level'],
                'self_evaluation' => $params['self_evaluation'],
                'hobbies' => $params['hobbies'],
                'status' => '官网提交',
                'contact_person' => '官网提交',
                'create_time' => time(),
                'update_time' => time()
            ];

            // 处理户口所在地（省/市）
            if (isset($params['hukou_province']) && isset($params['hukou_city'])) {
                $hukou_location = [];
                // 获取省份名称
                $province = Db::name('area')->where('id', $params['hukou_province'])->value('name');
                // 获取城市名称
                $city = Db::name('area')->where('id', $params['hukou_city'])->value('name');
                
                if (!empty($province)) $hukou_location[] = $province;
                if (!empty($city)) $hukou_location[] = $city;
                $data['hukou_location'] = implode('/', $hukou_location);
            }

            // 处理常住地址（省/市/县）
            if (isset($params['residence_province']) && isset($params['residence_city']) && isset($params['residence_district'])) {
                $residence_address = [];
                // 获取省份名称
                $province = Db::name('area')->where('id', $params['residence_province'])->value('name');
                // 获取城市名称
                $city = Db::name('area')->where('id', $params['residence_city'])->value('name');
                // 获取区县名称
                $district = Db::name('area')->where('id', $params['residence_district'])->value('name');
                
                if (!empty($province)) $residence_address[] = $province;
                if (!empty($city)) $residence_address[] = $city;
                if (!empty($district)) $residence_address[] = $district;
                
                // 构建完整地址
                $data['residence_address'] = implode('/', $residence_address);
            }

            // 处理教育经历
            if (isset($params['graduation_education']) && is_array($params['graduation_education'])) {
                $highestEducation = '';
                $educationLevels = [
                    '高中' => 1,
                    '大专' => 2,
                    '本科' => 3,
                    '硕士' => 4,
                    '博士' => 5
                ];
                $maxLevel = 0;

                $starts = [];
                $ends = [];
                $schools = [];
                $majors = [];
                $degrees = [];

                foreach ($params['graduation_education'] as $key => $edu) {
                    if (!empty($edu)) {
                        $level = $educationLevels[$edu] ?? 0;
                        if ($level > $maxLevel) {
                            $maxLevel = $level;
                            $highestEducation = $edu;
                        }
                        
                        if (!empty($params['education_school'][$key])) {
                            $starts[] = $params['education_start'][$key] ?? '';
                            $ends[] = $params['education_end'][$key] ?? '';
                            $schools[] = $params['education_school'][$key];
                            $majors[] = $params['education_major'][$key] ?? '';
                            $degrees[] = $edu;
                        }
                    }
                }

                // 设置最高学历
                $data['highest_education'] = $highestEducation;
                
                // 保存教育经历数据
                $data['education_start'] = implode('|', $starts);
                $data['education_end'] = implode('|', $ends);
                $data['education_school'] = implode('|', $schools);
                $data['education_major'] = implode('|', $majors);
                $data['graduation_education'] = implode('|', $degrees);
            }

            // 处理工作经历
            if (isset($params['job_company']) && is_array($params['job_company'])) {
                $starts = [];
                $ends = [];
                $companies = [];
                $positions = [];
                $descriptions = [];

                foreach ($params['job_company'] as $key => $company) {
                    if (!empty($company)) {
                        $starts[] = $params['job_start'][$key] ?? '';
                        $ends[] = $params['job_end'][$key] ?? '';
                        $companies[] = $company;
                        $positions[] = $params['job_position'][$key] ?? '';
                        $descriptions[] = $params['job_description'][$key] ?? '';
                    }
                }
                
                // 保存工作经历数据
                $data['job_start'] = implode('|', $starts);
                $data['job_end'] = implode('|', $ends);
                $data['job_company'] = implode('|', $companies);
                $data['job_position'] = implode('|', $positions);
                $data['job_description'] = implode('|', $descriptions);
            }

            // 处理联系人信息
            if (isset($params['contact_name']) && is_array($params['contact_name'])) {
                $relations = [];
                $names = [];
                $ages = [];
                $jobs = [];

                foreach ($params['contact_name'] as $key => $name) {
                    if (!empty($name)) {
                        $relations[] = $params['contact_relation'][$key] ?? '';
                        $names[] = $name;
                        $ages[] = $params['contact_age'][$key] ?? '';
                        $jobs[] = $params['contact_job'][$key] ?? '';
                    }
                }
                
                // 保存联系人数据
                $data['contact_relation'] = implode('|', $relations);
                $data['contact_name'] = implode('|', $names);
                $data['contact_age'] = implode('|', $ages);
                $data['contact_job'] = implode('|', $jobs);
            }

            // 处理文件上传
            if (isset($files['avatar']) && $files['avatar']->isValid()) {
                $data['avatar'] = $this->uploadFile($files['avatar'], $uploadDir);
            }
            if (isset($files['full_body_photo']) && $files['full_body_photo']->isValid()) {
                $data['full_body_photo'] = $this->uploadFile($files['full_body_photo'], $uploadDir);
            }
            if (isset($files['id_card_front']) && $files['id_card_front']->isValid()) {
                $data['id_card_front'] = $this->uploadFile($files['id_card_front'], $uploadDir);
            }
            if (isset($files['id_card_back']) && $files['id_card_back']->isValid()) {
                $data['id_card_back'] = $this->uploadFile($files['id_card_back'], $uploadDir);
            }
            if (isset($files['additional_photos'])) {
                $additionalPhotos = [];
                foreach ($files['additional_photos'] as $photo) {
                    if ($photo->isValid()) {
                        $additionalPhotos[] = $this->uploadFile($photo, $uploadDir);
                    }
                }
                $data['additional_photos'] = implode(',', $additionalPhotos);
            }

            Db::startTrans();
            try {
                // 插入简历数据
                $resumeId = Db::name('resume')->insertGetId($data);
                
                // 调试：打印SQL语句
                \think\Log::write('SQL语句：' . Db::getLastSql(), 'debug');
                
                // 更新职位申请人数
                Db::name('jobs')->where('id', $job_id)->setInc('resume_count');
                
                Db::commit();
                $this->success('申请提交成功');
            } catch (ValidateException $e) {
                Db::rollback();
                \think\Log::write('验证异常：' . $e->getMessage(), 'error');
                $this->error($e->getMessage());
            } catch (PDOException $e) {
                Db::rollback();
                \think\Log::write('数据库异常：' . $e->getMessage(), 'error');
                $this->error($e->getMessage());
            } catch (Exception $e) {
                Db::rollback();
                \think\Log::write('其他异常：' . $e->getMessage(), 'error');
                $this->error($e->getMessage());
            }
        }

        $this->assign('job', $job);
        return $this->view->fetch();
    }

    /**
     * 文件上传
     */
    protected function uploadFile($file, $dir)
    {
        try {
            $upload = new \app\common\library\Upload($file);
            $attachment = $upload->upload();
            return $attachment->url;
        } catch (\app\common\exception\UploadException $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 简历列表页面
     */
    public function list()
    {
        // 检查登录状态
        if (!$this->auth->isLogin()) {
            $this->error('请先登录', url('index/user/login'));
        }
        
        // 记录请求方法
        \think\Log::write('请求方法: ' . $this->request->method(), 'debug');
        
        // 获取当前登录用户信息
        $user = $this->auth->getUser();
        \think\Log::write('当前用户信息: ' . json_encode($user, JSON_UNESCAPED_UNICODE), 'debug');
        
        // 初始化两个结果集
        $resumeList = [];
        $otherResumeList = [];
        
        if ($user) {
            // 1. 按用户ID查询
            $userIdFilter = ['user_id' => $user->id];
            $resumeList = model('Resume')
                ->where($userIdFilter)
                ->order('create_time', 'desc')
                ->select();
                
                \think\Log::write('用户ID查询结果数量: ' . count($resumeList), 'debug');
            
            // 2. 按realname和mobile查询
            $namePhoneFilter = [];
            if (!empty($user->realname)) {
                $namePhoneFilter['name'] = ['like', "%{$user->realname}%"];
            }
            if (!empty($user->mobile)) {
                $namePhoneFilter['phone'] = ['like', "%{$user->mobile}%"];
            }
            
            if (!empty($namePhoneFilter)) {
                $otherResumeList = model('Resume')
                    ->where($namePhoneFilter)
                    ->where('user_id', '<>', $user->id) // 排除已通过user_id查询到的结果
                    ->order('create_time', 'desc')
                    ->select();
                    
                \think\Log::write('姓名电话查询结果数量: ' . count($otherResumeList), 'debug');
            }
        }
        
        // 合并结果并分页
        $allResumes = array_merge($resumeList, $otherResumeList);
        $pageSize = 10;
        $currentPage = input('page', 1);
        $total = count($allResumes);
        $start = ($currentPage - 1) * $pageSize;
        $paginatedResumes = array_slice($allResumes, $start, $pageSize);
        
        // 为每个简历添加匹配方式标记
        foreach ($paginatedResumes as &$resume) {
            $resume['match_type'] = in_array($resume['id'], array_column($resumeList, 'id')) ? 'user_id' : 'name_phone';
        }
        
        // 创建分页对象
        $page = \think\paginator\driver\Bootstrap::make($paginatedResumes, $pageSize, $currentPage, $total, false, [
            'path' => request()->baseUrl(),
            'query' => request()->get()
        ]);
        
        $this->assign('resumeList', $paginatedResumes);
        $this->assign('page', $page);
        $this->assign('totalCount', $total);
        $this->assign('userIdCount', count($resumeList));
        $this->assign('namePhoneCount', count($otherResumeList));
        
        return $this->view->fetch();
    }

    /**
     * 删除简历
     */
    public function delete($id = null)
    {
        if ($id) {
            $user = $this->auth->getUser();
            $resume = model('Resume')->where('id', $id)->find();
            
            if (!$resume) {
                $this->error('简历不存在');
            }
            
            // 验证权限：检查姓名和手机号是否匹配
            if ($resume->name != $user->realname || $resume->phone != $user->mobile) {
                $this->error('没有权限删除此简历');
            }
            
            // 删除简历相关文件
            $fields = ['avatar', 'full_body_photo', 'id_card_front', 'id_card_back', 
                       'hk_macau_passport_front', 'hk_macau_passport_back'];
            foreach ($fields as $field) {
                if ($resume[$field]) {
                    $attachment = \app\common\model\Attachment::where('url', $resume[$field])->find();
                    if ($attachment) {
                        $attachment->delete();
                    }
                }
            }
            
            // 删除附加照片
            if ($resume['additional_photos']) {
                $additionalPhotos = explode(',', $resume['additional_photos']);
                    foreach ($additionalPhotos as $photo) {
                    $attachment = \app\common\model\Attachment::where('url', $photo)->find();
                    if ($attachment) {
                        $attachment->delete();
                    }
                }
            }
            
            // 删除数据库记录
            if ($resume->delete()) {
                $this->success('删除成功');
            } else {
                $this->error('删除失败');
            }
        }
        $this->error('参数错误');
    }

    /**
     * 查看简历详情
     */
    public function view($id = null)
    {
        if (!$id) {
            $this->error('参数错误');
        }
        
        $resume = model('Resume')->where('id', $id)->find();
        if (!$resume) {
            $this->error('简历不存在');
        }
        
        // 检查权限：验证用户ID或姓名和手机号是否匹配
        $user = $this->auth->getUser();
        if (!$user || ($resume->user_id != $user->id && ($resume->name != $user->realname || $resume->phone != $user->mobile))) {
            $this->error('您没有权限查看此简历');
        }
        
        $this->assign('resume', $resume);
        return $this->view->fetch();
    }

    /**
     * 编辑简历
     */
    public function edit($id)
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录', url('index/user/login'));
        }

        $resume = model('Resume')->where('id', $id)->find();
        if (!$resume) {
            $this->error('简历不存在');
        }
        
        // 验证权限：检查用户ID或姓名和手机号是否匹配
        $user = $this->auth->getUser();
        if ($resume->user_id != $user->id && ($resume->name != $user->realname || $resume->phone != $user->mobile)) {
            $this->error('无权操作');
        }

        // 显式传递OCR配置到模板
        $this->assign('ocr_enabled', config('site.baidu_ocr_enabled'));

        if ($this->request->isPost()) {
            $data = $this->request->post();
            
            // 处理户口所在地
            if (isset($data['hukou_province']) && isset($data['hukou_city'])) {
                $hukou_location = [];
                // 获取省份名称
                $province = Db::name('area')->where('id', $data['hukou_province'])->value('name');
                // 获取城市名称
                $city = Db::name('area')->where('id', $data['hukou_city'])->value('name');
                
                if (!empty($province)) $hukou_location[] = $province;
                if (!empty($city)) $hukou_location[] = $city;
                $data['hukou_location'] = implode('/', $hukou_location);
            }

            // 处理常住地址
            if (isset($data['residence_province']) && isset($data['residence_city']) && isset($data['residence_district'])) {
                $residence_address = [];
                // 获取省份名称
                $province = Db::name('area')->where('id', $data['residence_province'])->value('name');
                // 获取城市名称
                $city = Db::name('area')->where('id', $data['residence_city'])->value('name');
                // 获取区县名称
                $district = Db::name('area')->where('id', $data['residence_district'])->value('name');
                
                if (!empty($province)) $residence_address[] = $province;
                if (!empty($city)) $residence_address[] = $city;
                if (!empty($district)) $residence_address[] = $district;
                
                $data['residence_address'] = implode('/', $residence_address);
            }
            
            // 处理文件上传
            $files = $this->request->file();
            $uploadDir = '/uploads/resume/' . date('Ymd') . '/';
            
            // 确保上传目录存在
            if (!is_dir(ROOT_PATH . 'public' . $uploadDir)) {
                mkdir(ROOT_PATH . 'public' . $uploadDir, 0755, true);
            }

            // 处理单个文件上传，如果没有上传新文件则保留原文件
            $fileFields = [
                'avatar' => 'old_avatar',
                'full_body_photo' => 'old_full_body_photo',
                'id_card_front' => 'old_id_card_front',
                'id_card_back' => 'old_id_card_back',
                'hk_macau_passport_front' => 'old_hk_macau_passport_front',
                'hk_macau_passport_back' => 'old_hk_macau_passport_back'
            ];
            
            foreach ($fileFields as $field => $oldField) {
                if (isset($files[$field]) && $files[$field]->isValid()) {
                    // 上传了新文件
                    $data[$field] = $this->uploadFile($files[$field], $uploadDir);
                    // 删除旧文件
                    if (!empty($resume[$field])) {
                        @unlink(ROOT_PATH . 'public' . $resume[$field]);
                    }
                } elseif (isset($data[$oldField])) {
                    // 检查是否要删除旧文件
                    if (empty($data[$oldField])) {
                        // 如果旧文件路径为空，说明要删除文件
                        if (!empty($resume[$field])) {
                            @unlink(ROOT_PATH . 'public' . $resume[$field]);
                        }
                        $data[$field] = ''; // 设置为空字符串
                    } else {
                        // 保留原文件路径
                        $data[$field] = $data[$oldField];
                    }
                }
                // 移除临时字段
                if (isset($data[$oldField])) {
                    unset($data[$oldField]);
                }
            }
            
            // 处理多文件上传
            if (isset($files['additional_photos']) && !empty($files['additional_photos'][0])) {
                $additionalPhotos = [];
                foreach ($files['additional_photos'] as $photo) {
                    if ($photo->isValid()) {
                        $additionalPhotos[] = $this->uploadFile($photo, $uploadDir);
                    }
                }
                $data['additional_photos'] = implode(',', $additionalPhotos);
            } elseif (isset($data['old_additional_photos']) && !empty($data['old_additional_photos'])) {
                // 没有上传新的附加照片，保留原来的
                $data['additional_photos'] = $data['old_additional_photos'];
            }
            // 移除临时字段
            if (isset($data['old_additional_photos'])) {
                unset($data['old_additional_photos']);
            }
            
            // 处理数组字段
            $arrayFields = ['contact_name', 'contact_relation', 'contact_age', 'contact_job', 
                           'graduation_education', 'education_school', 'education_major', 
                           'education_start', 'education_end', 'job_company', 'job_position', 
                           'job_start', 'job_end', 'job_description'];
            
            foreach ($arrayFields as $field) {
                if (isset($data[$field]) && is_array($data[$field])) {
                    $data[$field] = implode('|', $data[$field]);
                }
            }

            // 移除不存在的字段
            unset($data['hukou_province']);
            unset($data['hukou_city']);
            unset($data['residence_province']);
            unset($data['residence_city']);
            unset($data['residence_district']);
            
            $resume->save($data);
            $this->success('保存成功', url('index/resume/list'));
        }

        // 处理地址数据用于显示
        if ($resume->hukou_location) {
            $hukouParts = explode('/', $resume->hukou_location);
            if (count($hukouParts) >= 2) {
                // 查找省份ID
                $province = Db::name('area')->where('name', $hukouParts[0])->where('level', 1)->find();
                if ($province) {
                    $resume->hukou_province = $province['id'];
                    // 查找城市ID
                    $city = Db::name('area')->where('name', $hukouParts[1])->where('pid', $province['id'])->find();
                    if ($city) {
                        $resume->hukou_city = $city['id'];
                    }
                }
            }
        }

        if ($resume->residence_address) {
            $residenceParts = explode('/', $resume->residence_address);
            if (count($residenceParts) >= 3) {
                // 查找省份ID
                $province = Db::name('area')->where('name', $residenceParts[0])->where('level', 1)->find();
                if ($province) {
                    $resume->residence_province = $province['id'];
                    // 查找城市ID
                    $city = Db::name('area')->where('name', $residenceParts[1])->where('pid', $province['id'])->find();
                    if ($city) {
                        $resume->residence_city = $city['id'];
                        // 查找区县ID
                        $district = Db::name('area')->where('name', $residenceParts[2])->where('pid', $city['id'])->find();
                        if ($district) {
                            $resume->residence_district = $district['id'];
                        }
                    }
                }
            }
        }

        $this->assign('resume', $resume);
        return $this->fetch();
    }

    /**
     * 扫描身份证
     */
    public function scanIdCard()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }

        // 检查OCR功能是否启用
        if (!config('site.baidu_ocr_enabled')) {
            return json(['code' => 0, 'msg' => 'OCR功能未启用']);
        }

        $file = $this->request->file('id_card');
        if (!$file) {
            return json(['code' => 0, 'msg' => '请上传身份证图片']);
        }

        // 验证文件
        $info = $file->validate(['size'=>1024*1024*2, 'ext'=>'jpg,jpeg,png'])->move(ROOT_PATH . 'public' . DS . 'uploads' . DS . 'temp');
        if (!$info) {
            return json(['code' => 0, 'msg' => $file->getError()]);
        }

        $filePath = $info->getPathname();

        try {
            // 调用百度 OCR API 进行识别
            $result = $this->baiduOcr($filePath);
            
            // 删除临时文件
            @unlink($filePath);
            
            if ($result) {
                return json(['code' => 1, 'msg' => '识别成功', 'data' => $result]);
            } else {
                return json(['code' => 0, 'msg' => '识别失败']);
            }
        } catch (Exception $e) {
            // 删除临时文件
            @unlink($filePath);
            // 记录错误日志
            \think\Log::write('身份证识别错误：' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 调用百度 OCR API
     */
    protected function baiduOcr($imagePath)
    {
        // 百度 OCR API 配置
        $apiKey = config('site.baidu_ocr_api_key');
        $secretKey = config('site.baidu_ocr_secret_key');
        $enabled = config('site.baidu_ocr_enabled');
        
        \think\Log::write('OCR配置：' . json_encode([
            'enabled' => $enabled,
            'api_key' => $apiKey,
            'secret_key' => $secretKey
        ], JSON_UNESCAPED_UNICODE), 'debug');
        
        if (!$enabled) {
            throw new Exception('OCR功能未启用');
        }
        
        if (empty($apiKey) || empty($secretKey)) {
            throw new Exception('请先配置百度 OCR API 密钥');
        }

        // 获取 access token
        $tokenUrl = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={$apiKey}&client_secret={$secretKey}";
        
        // 记录请求信息
        \think\Log::write('请求百度 token URL：' . $tokenUrl, 'debug');
        
        $tokenResponse = file_get_contents($tokenUrl);
        if ($tokenResponse === false) {
            throw new Exception('获取 access token 失败：无法连接到百度服务器');
        }
        
        $tokenData = json_decode($tokenResponse, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('解析 access token 响应失败：' . json_last_error_msg());
        }
        
        if (!isset($tokenData['access_token'])) {
            throw new Exception('获取百度 OCR access token 失败：' . ($tokenData['error_description'] ?? '未知错误'));
        }

        $accessToken = $tokenData['access_token'];
        
        // 调用身份证识别接口
        $url = "https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token={$accessToken}";
        
        // 读取图片文件
        $image = file_get_contents($imagePath);
        if ($image === false) {
            throw new Exception('读取图片文件失败');
        }
        
        $base64 = base64_encode($image);
        
        // 发送请求
        $postData = [
            'image' => $base64,
            'id_card_side' => 'front' // 默认识别正面
        ];
        
        $options = [
            'http' => [
                'method' => 'POST',
                'header' => 'Content-Type: application/x-www-form-urlencoded',
                'content' => http_build_query($postData),
                'timeout' => 30 // 设置超时时间
            ]
        ];
        
        $context = stream_context_create($options);
        $response = file_get_contents($url, false, $context);
        
        if ($response === false) {
            throw new Exception('调用百度 OCR API 失败：无法连接到服务器');
        }
        
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('解析百度 OCR 响应失败：' . json_last_error_msg());
        }
        
        if (isset($result['error_code'])) {
            throw new Exception('身份证识别失败：' . ($result['error_msg'] ?? '未知错误'));
        }
        
        // 处理识别结果
        $data = [];
        if (isset($result['words_result'])) {
            $words = $result['words_result'];
            
            // 处理出生日期格式
            $birthDate = $words['出生']['words'] ?? '';
            if (!empty($birthDate)) {
                // 将"YYYY年MM月DD日"格式转换为"YYYY-MM-DD"格式
                $birthDate = str_replace(['年', '月', '日'], ['-', '-', ''], $birthDate);
            }
            
            // 提取信息
            $data = [
                'name' => $words['姓名']['words'] ?? '',
                'gender' => $words['性别']['words'] ?? '',
                'id_card' => $words['公民身份号码']['words'] ?? '',
                'birth_date' => $birthDate,
                'age' => $this->calculateAge($birthDate)
            ];
        }
        
        return $data;
    }

    /**
     * 计算年龄
     */
    protected function calculateAge($birthDate)
    {
        if (empty($birthDate)) {
            return '';
        }
        
        // 将出生日期转换为时间戳
        $birthTimestamp = strtotime($birthDate);
        if ($birthTimestamp === false) {
            return '';
        }
        
        // 计算年龄
        $age = date('Y') - date('Y', $birthTimestamp);
        if (date('md') < date('md', $birthTimestamp)) {
            $age--;
        }
        
        return $age;
    }

    /**
     * 生成工作内容
     */
    public function generateJobDescription()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }

        $data = $this->request->post();
        if (empty($data['company']) || empty($data['position']) || empty($data['word_count'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        try {
            // 构建提示词
            $prompt = "请根据以下工作信息生成一段工作内容描述：\n";
            $prompt .= "工作单位：{$data['company']}\n";
            $prompt .= "工作岗位：{$data['position']}\n";
            $prompt .= "要求：\n";
            $prompt .= "1. 只描述具体的工作内容和职责，不要包含工作单位和岗位名称\n";
            $prompt .= "2. 描述要专业、具体，突出工作职责和成就\n";
            $prompt .= "3. 字数控制在{$data['word_count']}字左右\n";
            $prompt .= "4. 语言要简洁、专业\n";
            $prompt .= "5. 直接输出工作内容，不要加任何前缀或说明\n";

            // 获取API配置
            $apiKey = config('site.openai_api_key');
            $apiUrl = config('site.openai_api_url');
            $model = config('site.openai_model');
            
            if (empty($apiKey) || empty($apiUrl) || empty($model)) {
                return json(['code' => 0, 'msg' => 'API配置不完整']);
            }

            $ch = curl_init($apiUrl . '/chat/completions');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ]);

            $postData = [
                'model' => $model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => '你是一个专业的简历顾问，擅长撰写工作内容描述。请直接输出工作内容，不要包含工作单位和岗位名称，也不要加任何前缀或说明。'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.7,
                'max_tokens' => 500,
                'stream' => false
            ];

            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_errno($ch)) {
                throw new Exception('API请求错误: ' . curl_error($ch));
            }
            
            curl_close($ch);

            if ($httpCode !== 200) {
                throw new Exception('API请求失败，状态码：' . $httpCode . '，响应：' . $response);
            }

            $result = json_decode($response, true);
            if (!isset($result['choices'][0]['message']['content'])) {
                throw new Exception('API返回数据格式错误：' . $response);
            }

            $content = trim($result['choices'][0]['message']['content']);
            return json(['code' => 1, 'msg' => '生成成功', 'data' => $content]);

        } catch (Exception $e) {
            \think\Log::write('生成工作内容失败：' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 生成自我评价
     */
    public function generateSelfEvaluation()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }

        $data = $this->request->post();
        if (empty($data['content']) || empty($data['word_count'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        try {
            // 构建提示词
            $prompt = "请根据以下内容生成一段自我评价：\n";
            $prompt .= "原始内容：{$data['content']}\n";
            $prompt .= "要求：\n";
            $prompt .= "1. 保持原意的同时，使表达更加专业、得体\n";
            $prompt .= "2. 突出个人优势和特点\n";
            $prompt .= "3. 字数控制在{$data['word_count']}字左右\n";
            $prompt .= "4. 语言要简洁、专业\n";
            $prompt .= "5. 直接输出润色后的内容，不要加任何前缀或说明\n";

            // 获取API配置
            $apiKey = config('site.openai_api_key');
            $apiUrl = config('site.openai_api_url');
            $model = config('site.openai_model');
            
            if (empty($apiKey) || empty($apiUrl) || empty($model)) {
                return json(['code' => 0, 'msg' => 'API配置不完整']);
            }

            $ch = curl_init($apiUrl . '/chat/completions');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ]);

            $postData = [
                'model' => $model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => '你是一个专业的简历顾问，擅长润色自我评价。请直接输出润色后的内容，不要加任何前缀或说明。'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.7,
                'max_tokens' => 500,
                'stream' => false
            ];

            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_errno($ch)) {
                throw new Exception('API请求错误: ' . curl_error($ch));
            }
            
            curl_close($ch);

            if ($httpCode !== 200) {
                throw new Exception('API请求失败，状态码：' . $httpCode . '，响应：' . $response);
            }

            $result = json_decode($response, true);
            if (!isset($result['choices'][0]['message']['content'])) {
                throw new Exception('API返回数据格式错误：' . $response);
            }

            $content = trim($result['choices'][0]['message']['content']);
            return json(['code' => 1, 'msg' => '生成成功', 'data' => $content]);

        } catch (Exception $e) {
            \think\Log::write('生成自我评价失败：' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 生成兴趣爱好
     */
    public function generateHobbies()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }

        $data = $this->request->post();
        if (empty($data['content']) || empty($data['word_count'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        try {
            // 构建提示词
            $prompt = "请根据以下内容生成一段兴趣爱好描述：\n";
            $prompt .= "原始内容：{$data['content']}\n";
            $prompt .= "要求：\n";
            $prompt .= "1. 保持原意的同时，使表达更加生动、有趣\n";
            $prompt .= "2. 突出个人特点和积极向上的态度\n";
            $prompt .= "3. 字数控制在{$data['word_count']}字左右\n";
            $prompt .= "4. 语言要简洁、自然\n";
            $prompt .= "5. 直接输出润色后的内容，不要加任何前缀或说明\n";

            // 获取API配置
            $apiKey = config('site.openai_api_key');
            $apiUrl = config('site.openai_api_url');
            $model = config('site.openai_model');
            
            if (empty($apiKey) || empty($apiUrl) || empty($model)) {
                return json(['code' => 0, 'msg' => 'API配置不完整']);
            }

            $ch = curl_init($apiUrl . '/chat/completions');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ]);

            $postData = [
                'model' => $model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => '你是一个专业的简历顾问，擅长润色兴趣爱好描述。请直接输出润色后的内容，不要加任何前缀或说明。'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.7,
                'max_tokens' => 500,
                'stream' => false
            ];

            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_errno($ch)) {
                throw new Exception('API请求错误: ' . curl_error($ch));
            }
            
            curl_close($ch);

            if ($httpCode !== 200) {
                throw new Exception('API请求失败，状态码：' . $httpCode . '，响应：' . $response);
            }

            $result = json_decode($response, true);
            if (!isset($result['choices'][0]['message']['content'])) {
                throw new Exception('API返回数据格式错误：' . $response);
            }

            $content = trim($result['choices'][0]['message']['content']);
            return json(['code' => 1, 'msg' => '生成成功', 'data' => $content]);

        } catch (Exception $e) {
            \think\Log::write('生成兴趣爱好失败：' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }
} 