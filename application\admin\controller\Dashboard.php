<?php

namespace app\admin\controller;

use app\admin\model\Admin;
use app\admin\model\User;
use app\common\controller\Backend;
use app\common\model\Attachment;
use fast\Date;
use think\Db;

/**
 * 控制台
 *
 * @icon   fa fa-dashboard
 * @remark 用于展示当前系统中的统计数据、统计报表及重要实时数据
 */
class Dashboard extends Backend
{
    protected $model = null;
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 查看
     */
    public function index()
    {
        try {
            \think\Db::execute("SET @@sql_mode='';");
        } catch (\Exception $e) {

        }
        $column = [];
        $starttime = Date::unixtime('day', -6);
        $endtime = Date::unixtime('day', 0, 'end');
        $joinlist = Db("user")->where('jointime', 'between time', [$starttime, $endtime])
            ->field('jointime, status, COUNT(*) AS nums, DATE_FORMAT(FROM_UNIXTIME(jointime), "%Y-%m-%d") AS join_date')
            ->group('join_date')
            ->select();
        for ($time = $starttime; $time <= $endtime;) {
            $column[] = date("Y-m-d", $time);
            $time += 86400;
        }
        $userlist = array_fill_keys($column, 0);
        foreach ($joinlist as $k => $v) {
            $userlist[$v['join_date']] = $v['nums'];
        }

        $dbTableList = Db::query("SHOW TABLE STATUS");
        $addonList = get_addon_list();
        $totalworkingaddon = 0;
        $totaladdon = count($addonList);
        foreach ($addonList as $index => $item) {
            if ($item['state']) {
                $totalworkingaddon += 1;
            }
        }
        $this->view->assign([
            'totaluser'         => User::count(),
            'totaladdon'        => $totaladdon,
            'totaladmin'        => Admin::count(),
            'totalcategory'     => \app\common\model\Category::count(),
            'todayusersignup'   => User::whereTime('jointime', 'today')->count(),
            'todayuserlogin'    => User::whereTime('logintime', 'today')->count(),
            'sevendau'          => User::whereTime('jointime|logintime|prevtime', '-7 days')->count(),
            'thirtydau'         => User::whereTime('jointime|logintime|prevtime', '-30 days')->count(),
            'threednu'          => User::whereTime('jointime', '-3 days')->count(),
            'sevendnu'          => User::whereTime('jointime', '-7 days')->count(),
            'dbtablenums'       => count($dbTableList),
            'dbsize'            => array_sum(array_map(function ($item) {
                return $item['Data_length'] + $item['Index_length'];
            }, $dbTableList)),
            'totalworkingaddon' => $totalworkingaddon,
            'attachmentnums'    => Attachment::count(),
            'attachmentsize'    => Attachment::sum('filesize'),
            'picturenums'       => Attachment::where('mimetype', 'like', 'image/%')->count(),
            'picturesize'       => Attachment::where('mimetype', 'like', 'image/%')->sum('filesize'),
        ]);

        $this->assignconfig('column', array_keys($userlist));
        $this->assignconfig('userdata', array_values($userlist));

        // 获取统计数据
        $resume_count = Db::name('resume')->count();
        $job_count = Db::name('jobs')->count();
        $interview_count = Db::name('job_interview')->count();
        $category_count = Db::name('job_category')->count();

        // 新增简历和岗位时间范围统计
        $days = $this->request->get('days', 7); // 默认7天
        $time_ranges = [3, 7, 15, 30]; // 所有时间范围
        $resume_trend_data = [];
        $job_trend_data = [];
        $interview_trend_data = [];
        
        foreach ($time_ranges as $range) {
            $starttime = Date::unixtime('day', -$range);
            $endtime = Date::unixtime('day', 0, 'end');
            
            // 简历趋势数据
            $resume_trend = Db::name('resume')
                ->where('create_time', 'between time', [$starttime, $endtime])
                ->field('create_time, COUNT(*) AS nums, DATE_FORMAT(FROM_UNIXTIME(create_time), "%Y-%m-%d") AS date')
                ->group('date')
                ->select();

            // 岗位趋势数据
            $job_trend = Db::name('jobs')
                ->where('create_time', 'between time', [$starttime, $endtime])
                ->field('create_time, COUNT(*) AS nums, DATE_FORMAT(FROM_UNIXTIME(create_time), "%Y-%m-%d") AS date')
                ->group('date')
                ->select();

            // 面试状态趋势数据
            $interview_trend = Db::name('job_interview')
                ->where('interview_time', 'between time', [$starttime, $endtime])
                ->field('status, COUNT(*) AS nums, DATE_FORMAT(interview_time, "%Y-%m-%d") AS date')
                ->group('status, date')
                ->select();

            // 生成日期数组
            $date_list = [];
            for ($time = $starttime; $time <= $endtime;) {
                $date_list[] = date("Y-m-d", $time);
                $time += 86400;
            }

            // 填充简历数据
            $resume_data = array_fill_keys($date_list, 0);
            foreach ($resume_trend as $item) {
                $resume_data[$item['date']] = $item['nums'];
            }

            // 填充岗位数据
            $job_data = array_fill_keys($date_list, 0);
            foreach ($job_trend as $item) {
                $job_data[$item['date']] = $item['nums'];
            }

            // 填充面试状态数据
            $interview_data = [];
            // 获取所有可能的状态
            $status_list = ['未开始', '进行中', '已结束', '已取消'];
            foreach ($status_list as $status) {
                $interview_data[$status] = array_fill_keys($date_list, 0);
            }
            foreach ($interview_trend as $item) {
                if (isset($interview_data[$item['status']])) {
                    $interview_data[$item['status']][$item['date']] = $item['nums'];
                }
            }

            $resume_trend_data[$range] = [
                'dates' => array_keys($resume_data),
                'values' => array_values($resume_data)
            ];

            $job_trend_data[$range] = [
                'dates' => array_keys($job_data),
                'values' => array_values($job_data)
            ];

            $interview_trend_data[$range] = [
                'dates' => $date_list,
                'status' => $status_list,
                'data' => $interview_data
            ];
        }

        // 使用assignconfig传递数据到前端
        $this->assignconfig('resume_trend_data', $resume_trend_data);
        $this->assignconfig('job_trend_data', $job_trend_data);
        $this->assignconfig('interview_trend_data', $interview_trend_data);
        $this->assignconfig('current_days', $days);

        // 简历性别分布
        $gender_data = Db::name('resume')
            ->field('gender, count(*) as count')
            ->group('gender')
            ->select();
        
        // 简历学历分布
        $education_data = Db::name('resume')
            ->field('highest_education, count(*) as count')
            ->group('highest_education')
            ->select();

        // 简历地区分布
        $location_data = Db::name('resume')
            ->field('hukou_location, count(*) as count')
            ->group('hukou_location')
            ->select();

        // 处理地区数据，提取省份
        $province_data = [];
        foreach ($location_data as $item) {
            // 提取省份（取第一个字符，如"北京市"取"北京"）
            $province = mb_substr($item['hukou_location'], 0, 2);
            if (!isset($province_data[$province])) {
                $province_data[$province] = 0;
            }
            $province_data[$province] += $item['count'];
        }

        // 转换为前端需要的格式
        $location_data = [];
        foreach ($province_data as $province => $count) {
            $location_data[] = [
                'hukou_location' => $province,
                'count' => $count
            ];
        }

        // 岗位分类分布
        $category_data = Db::name('jobs')
            ->field('category, count(*) as count')
            ->group('category')
            ->select();

        // 岗位薪资分布
        $salary_data = Db::name('jobs')
            ->field('salary_range, count(*) as count')
            ->group('salary_range')
            ->select();

        // 岗位状态分布
        $job_status_data = Db::name('jobs')
            ->field('status, count(*) as count')
            ->group('status')
            ->select();

        // 面试类型分布
        $interview_type_data = Db::name('job_interview')
            ->field('interview_type, count(*) as count')
            ->group('interview_type')
            ->select();

        // 面试状态分布
        $interview_status_data = Db::name('job_interview')
            ->field('status, count(*) as count')
            ->group('status')
            ->select();

        // 面试进度分布
        $progress_counts = [
            '未面试' => 0,
            '已面试' => 0,
            '未通过' => 0,
            '已通过' => 0,
            '已被录取' => 0
        ];
        
        $progress_records = Db::name('job_interview')
            ->field('progress')
            ->select();
            
        foreach ($progress_records as $record) {
            if (!empty($record['progress'])) {
                $statuses = explode(',', $record['progress']);
                foreach ($statuses as $status) {
                    if (strpos($status, ':') !== false) {
                        $status = trim(explode(':', $status)[1]);
                        if (isset($progress_counts[$status])) {
                            $progress_counts[$status]++;
                        }
                    }
                }
            }
        }
        
        $interview_progress_data = [];
        foreach ($progress_counts as $status => $count) {
            $interview_progress_data[] = [
                'progress' => $status,
                'count' => $count
            ];
        }

        // 使用assignconfig传递数据到前端
        $this->assignconfig('gender_data', $gender_data);
        $this->assignconfig('education_data', $education_data);
        $this->assignconfig('location_data', $location_data);
        $this->assignconfig('category_data', $category_data);
        $this->assignconfig('salary_data', $salary_data);
        $this->assignconfig('job_status_data', $job_status_data);
        $this->assignconfig('interview_type_data', $interview_type_data);
        $this->assignconfig('interview_status_data', $interview_status_data);
        $this->assignconfig('interview_progress_data', $interview_progress_data);

        // 使用view->assign传递统计数据
        $this->view->assign([
            'resume_count' => $resume_count,
            'job_count' => $job_count,
            'interview_count' => $interview_count,
            'category_count' => $category_count,
            'current_days' => $days
        ]);

        return $this->view->fetch();
    }

}
