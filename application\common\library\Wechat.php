<?php

namespace app\common\library;

use think\Config;

/**
 * 微信辅助类
 */
class Wechat
{
    /**
     * 获取配置信息
     *
     * @param string $name 配置名称
     * @param string $default 默认值
     * @return mixed
     */
    public static function config($name = '', $default = null)
    {
        // 优先从site.php获取配置
        $siteConfig = Config::get('site');
        $config = [];
        
        // 小程序配置
        if (isset($siteConfig['wechat_miniapp_appid'])) {
            $config['miniapp'] = [
                'appid' => $siteConfig['wechat_miniapp_appid'] ?? '',
                'appsecret' => $siteConfig['wechat_miniapp_appsecret'] ?? '',
                'name' => $siteConfig['wechat_miniapp_name'] ?? '',
                'version' => $siteConfig['wechat_miniapp_version'] ?? '',
            ];
        }
        
        // 公众号配置
        if (isset($siteConfig['wechat_mp_appid'])) {
            $config['mp'] = [
                'appid' => $siteConfig['wechat_mp_appid'] ?? '',
                'appsecret' => $siteConfig['wechat_mp_appsecret'] ?? '',
                'name' => $siteConfig['wechat_mp_name'] ?? '',
                'token' => $siteConfig['wechat_mp_token'] ?? '',
                'encodingaeskey' => $siteConfig['wechat_mp_encodingaeskey'] ?? '',
            ];
        }
        
        // 支付配置
        if (isset($siteConfig['wechat_pay_mch_id'])) {
            $config['pay'] = [
                'mch_id' => $siteConfig['wechat_pay_mch_id'] ?? '',
                'key' => $siteConfig['wechat_pay_key'] ?? '',
                'notify_url' => $siteConfig['wechat_pay_notify_url'] ?? '',
            ];
        }
        
        // 如果site.php中没有配置，则尝试从wechat.php获取
        if (empty($config)) {
            $config = Config::get('wechat') ?: [];
        }
        
        if ($name === '') {
            return $config;
        }
        
        $names = explode('.', $name);
        if (count($names) === 1) {
            return isset($config[$name]) ? $config[$name] : $default;
        }
        
        $result = $config;
        foreach ($names as $val) {
            if (isset($result[$val])) {
                $result = $result[$val];
            } else {
                return $default;
            }
        }
        
        return $result;
    }
} 