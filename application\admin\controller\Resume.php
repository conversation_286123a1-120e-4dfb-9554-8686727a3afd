<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Xlsx;
use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\TemplateProcessor;
use think\Db;
use think\Exception;
use PDOException;


/**
 * 简历管理
 *
 * @icon fa fa-circle-o
 */
class Resume extends Backend
{

    /**
     * Resume模型对象
     * @var \app\admin\model\Resume
     */
    protected $model = null;

    protected $searchFields = "id,name,intended_position,applied_position,height,weight,gender,age,hukou_location,highest_education,cantonese_level,english_level,special_certificate,self_evaluation,job_start,job_end,job_company,job_position,job_description,avatar,contact_person";

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\Resume;
        $this->view->assign("genderList", $this->model->getGenderList());
        $this->view->assign("maritalStatusList", $this->model->getMaritalStatusList());
        $this->view->assign("cantoneseLevelList", $this->model->getCantoneseLevelList());
        $this->view->assign("mandarinLevelList", $this->model->getMandarinLevelList());
        $this->view->assign("englishLevelList", $this->model->getEnglishLevelList());
        $this->view->assign("overseasExperienceList", $this->model->getOverseasExperienceList());
        $this->view->assign("statusList", $this->model->getStatusList());
    }
    
    protected function processContactsData($params)
    {
        if (isset($params['contacts'])) {
            $relations = [];
            $names = [];
            $ages = [];
            $jobs = [];
            
            foreach ($params['contacts'] as $contact) {
                $relations[] = $contact['relation'] ?? '';
                $names[] = $contact['name'] ?? '';
                $ages[] = $contact['age'] ?? '';
                $jobs[] = $contact['job'] ?? '';
            }
            
            $params['row']['contact_relation'] = implode('|', $relations);
            $params['row']['contact_name'] = implode('|', $names);
            $params['row']['contact_age'] = implode('|', $ages);
            $params['row']['contact_job'] = implode('|', $jobs);
        }
        return $params;
    }
    
    protected function processEducationData($params)
    {
        if (isset($params['education'])) {
            $starts = [];
            $ends = [];
            $schools = [];
            $majors = [];
            $degrees = [];
            
            foreach ($params['education'] as $edu) {
                $starts[] = $edu['start'] ?? '';
                $ends[] = $edu['end'] ?? '';
                $schools[] = $edu['school'] ?? '';
                $majors[] = $edu['major'] ?? '';
                $degrees[] = $edu['degree'] ?? '';
            }
            
            $params['row']['education_start'] = implode('|', $starts);
            $params['row']['education_end'] = implode('|', $ends);
            $params['row']['education_school'] = implode('|', $schools);
            $params['row']['education_major'] = implode('|', $majors);
            $params['row']['graduation_education'] = implode('|', $degrees);
        }
        return $params;
    }
    
    protected function processJobData($params)
    {
        if (isset($params['jobs'])) {
            $starts = [];
            $ends = [];
            $companies = [];
            $positions = [];
            $descriptions = [];
            
            foreach ($params['jobs'] as $job) {
                $starts[] = $job['start'] ?? '';
                $ends[] = $job['end'] ?? '';
                $companies[] = $job['company'] ?? '';
                $positions[] = $job['position'] ?? '';
                $descriptions[] = $job['description'] ?? '';
            }
            
            $params['row']['job_start'] = implode('|', $starts);
            $params['row']['job_end'] = implode('|', $ends);
            $params['row']['job_company'] = implode('|', $companies);
            $params['row']['job_position'] = implode('|', $positions);
            $params['row']['job_description'] = implode('|', $descriptions);
        }
        return $params;
    }

    /**
     * 获取字段的中文名称
     * @param string $fieldName 英文字段名
     * @return string 中文字段名
     */
    protected function getFieldChineseName($fieldName)
    {
        $fieldMap = [
            'birth_date' => '出生日期',
            'hk_macau_passport_expiry' => '港澳通行证到期时间',
            'education_start' => '教育开始时间',
            'education_end' => '教育结束时间',
            'job_start' => '工作开始时间',
            'job_end' => '工作结束时间',
            'name' => '姓名',
            'intended_position' => '意向职位',
            'applied_position' => '应聘职位',
            'gender' => '性别',
            'ethnicity' => '民族',
            'height' => '身高',
            'weight' => '体重',
            'id_card' => '身份证号',
            'age' => '年龄',
            'phone' => '电话',
            'marital_status' => '婚姻状况',
            'hukou_location' => '户口所在地',
            'residence_address' => '居住地址',
            'highest_education' => '最高学历',
            'cantonese_level' => '粤语水平',
            'mandarin_level' => '普通话水平',
            'english_level' => '英语水平',
            'hk_macau_passport' => '港澳通行证编号',
            'overseas_experience' => '海外经历',
            'overseas_region' => '海外地区',
            'special_certificate' => '特殊证书',
            'hobbies' => '兴趣爱好',
            'self_evaluation' => '自我评价',
            'contact_person' => '联系人',
            'status' => '状态'
        ];

        return isset($fieldMap[$fieldName]) ? $fieldMap[$fieldName] : $fieldName;
    }

    /**
     * 转换日期格式
     * @param string $date 日期字符串
     * @param string $type 转换类型：full(年月日) 或 month(年月)
     * @param string $outputFormat 输出格式：dash(-) 或 slash(/)
     * @param array &$errorMessages 错误信息数组（引用传递）
     * @param string $fieldName 字段名称（用于错误信息）
     * @param int $arrayIndex 数组索引（用于多数组字段错误信息）
     * @return string
     */
    protected function convertDateFormat($date, $type = 'full', $outputFormat = 'dash', &$errorMessages = null, $fieldName = '', $arrayIndex = -1)
    {
        if (empty($date)) {
            return '';
        }
        
        // 处理Excel日期序列号
        if (is_numeric($date) && intval($date) == $date) {
            $date = intval($date);
            
            if ($date > 0 && $date < 2958466) {
                try {
                    if ($date > 60) {
                        $date -= 1;
                    }
                    
                    $timestamp = ($date - 1) * 86400;
                    $baseTimestamp = strtotime('1900-01-01');
                    $timestamp += $baseTimestamp;
                    
                    // 格式化日期
                    if ($type == 'month') {
                        return date('Y-m', $timestamp); // 返回Y-m格式
                    } elseif ($type == 'full') {
                        if ($outputFormat == 'slash') {
                            return date('Y/m/d', $timestamp);
                        }
                        return date('Y-m-d', $timestamp);
                    }
                } catch (\Exception $e) {
                    $errorMsg = "Excel日期转换异常: {$date}, " . $e->getMessage();
                    \think\Log::write($errorMsg, 'error');
                    if ($errorMessages !== null && !empty($fieldName)) {
                        $chineseFieldName = $this->getFieldChineseName($fieldName);
                        $arrayIndexText = $arrayIndex >= 0 ? " #" . ($arrayIndex + 1) : "";
                        $errorMessages[] = "字段 {$chineseFieldName}{$arrayIndexText} 的日期转换失败：{$date}";
                    }
                    return $date;
                }
            }
        }
        
        // 日期分隔符
        $separator = ($outputFormat == 'slash') ? '/' : '-';
        
        // 处理年月日格式 (Y年M月D日)
        if (preg_match('/^(\d{4})年(\d{1,2})月(\d{1,2})日$/', $date, $matches)) {
            if ($type == 'month') {
                return sprintf('%04d-%02d', $matches[1], $matches[2]); // 返回Y-m格式
            }
            return sprintf('%04d%s%02d%s%02d', $matches[1], $separator, $matches[2], $separator, $matches[3]);
        }
        
        // 处理年月格式 (Y年M月)
        if (preg_match('/^(\d{4})年(\d{1,2})月$/', $date, $matches)) {
            if ($type == 'month') {
                return sprintf('%04d-%02d', $matches[1], $matches[2]); // 返回Y-m格式
            }
            return sprintf('%04d%s%02d', $matches[1], $separator, $matches[2]);
        }
        
        // 处理仅有年份的格式 (Y年)
        if (preg_match('/^(\d{4})年$/', $date, $matches)) {
            if ($type == 'month') {
                return sprintf('%04d-01', $matches[1]); // 返回Y-m格式
            }
            return sprintf('%04d%s01', $matches[1], $separator);
        }
        
        // 处理斜杠格式 (Y/M 或 Y/M/D)
        if (preg_match('/^(\d{4})\/(\d{1,2})$/', $date, $matches)) {
            if ($type == 'month') {
                return sprintf('%04d-%02d', $matches[1], $matches[2]); // 返回Y-m格式
            }
            return sprintf('%04d%s%02d', $matches[1], $separator, $matches[2]);
        }
        
        if (preg_match('/^(\d{4})\/(\d{1,2})\/(\d{1,2})$/', $date, $matches)) {
            if ($type == 'month') {
                return sprintf('%04d-%02d', $matches[1], $matches[2]); // 返回Y-m格式
            }
            if ($outputFormat == 'slash') {
                return sprintf('%04d/%02d/%02d', $matches[1], $matches[2], $matches[3]);
            }
            return sprintf('%04d-%02d-%02d', $matches[1], $matches[2], $matches[3]);
        }
        
        // 处理点号格式 (Y.M 或 Y.M.D)
        if (preg_match('/^(\d{4})\.(\d{1,2})$/', $date, $matches)) {
            if ($type == 'month') {
                return sprintf('%04d-%02d', $matches[1], $matches[2]); // 返回Y-m格式
            }
            return sprintf('%04d%s%02d', $matches[1], $separator, $matches[2]);
        }

        if (preg_match('/^(\d{4})\.(\d{1,2})\.(\d{1,2})$/', $date, $matches)) {
            if ($type == 'month') {
                return sprintf('%04d-%02d', $matches[1], $matches[2]); // 返回Y-m格式
            }
            if ($outputFormat == 'slash') {
                return sprintf('%04d/%02d/%02d', $matches[1], $matches[2], $matches[3]);
            }
            return sprintf('%04d-%02d-%02d', $matches[1], $matches[2], $matches[3]);
        }

        // 处理横杠格式 (Y-M 或 Y-M-D)
        if (preg_match('/^(\d{4})-(\d{1,2})$/', $date, $matches)) {
            if ($type == 'month') {
                return sprintf('%04d-%02d', $matches[1], $matches[2]); // 返回Y-m格式
            }
            return sprintf('%04d%s%02d', $matches[1], $separator, $matches[2]);
        }

        if (preg_match('/^(\d{4})-(\d{1,2})-(\d{1,2})$/', $date, $matches)) {
            if ($type == 'month') {
                return sprintf('%04d-%02d', $matches[1], $matches[2]); // 返回Y-m格式
            }
            if ($outputFormat == 'slash') {
                return sprintf('%04d/%02d/%02d', $matches[1], $matches[2], $matches[3]);
            }
            return $date; // 已经是标准的短横线格式
        }
        
        // 处理纯数字格式 (YYYYMM 或 YYYYMMDD)
        if (preg_match('/^(\d{4})(\d{2})$/', $date, $matches)) {
            if ($type == 'month') {
                return sprintf('%04d-%02d', $matches[1], $matches[2]); // 返回Y-m格式
            }
            return sprintf('%04d%s%02d', $matches[1], $separator, $matches[2]);
        }
        
        if (preg_match('/^(\d{4})(\d{2})(\d{2})$/', $date, $matches)) {
            if ($type == 'month') {
                return sprintf('%04d-%02d', $matches[1], $matches[2]); // 返回Y-m格式
            }
            return sprintf('%04d%s%02d%s%02d', $matches[1], $separator, $matches[2], $separator, $matches[3]);
        }
        
        // 尝试使用strtotime解析日期
        $timestamp = strtotime($date);
        if ($timestamp !== false) {
            if ($type == 'month') {
                return date('Y-m', $timestamp); // 返回Y-m格式
            } elseif ($type == 'full') {
                if ($outputFormat == 'slash') {
                    return date('Y/m/d', $timestamp);
                }
                return date('Y-m-d', $timestamp);
            }
        }

        // 如果所有转换都失败，检查是否是有效的日期格式
        $originalDate = $date;

        // 如果返回的值与原始值相同，说明没有进行任何转换
        // 这时候检查是否是因为格式无法识别
        if ($originalDate === $date && $errorMessages !== null && !empty($fieldName)) {
            // 尝试用strtotime验证是否是有效日期
            if (strtotime($date) === false) {
                $chineseFieldName = $this->getFieldChineseName($fieldName);
                $arrayIndexText = $arrayIndex >= 0 ? " #" . ($arrayIndex + 1) : "";
                $errorMessages[] = "字段 {$chineseFieldName}{$arrayIndexText} 的日期格式无法识别：{$date}";
            }
        }

        return $date;
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    
    public function edit($ids = null)
    {
        $params = $this->request->post();
        $params = $this->processContactsData($params);
        $params = $this->processEducationData($params);
        $params = $this->processJobData($params);
        $this->request->post($params);
        return parent::edit($ids);
    }
    
    public function add()
    {
        $params = $this->request->post();
        $params = $this->processContactsData($params);
        $params = $this->processEducationData($params);
        $params = $this->processJobData($params);
        $this->request->post($params);
        return parent::add();
    }


    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                    
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            foreach ($list as $row) {
                $row->visible(['id','name','intended_position','applied_position','height','weight','gender','age','hukou_location','highest_education','cantonese_level','english_level','special_certificate','self_evaluation','job_start','job_end','job_company','job_position','job_description','avatar','contact_person','status']);
                
            }

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        
        // 如果没有指定状态，默认显示"已发布"状态
        if (!$this->request->get('status')) {
            $this->request->get(['status' => '已发布']);
        }
        
        return $this->view->fetch();
    }
    
    /**
     * 导出Excel进度API
     */
    public function exportProgress()
    {
        if (!$this->auth->check('resume/exportexcel')) {
            $this->error('没有权限');
        }

        $step = $this->request->get('step', 0);
        $total = $this->request->get('total', 0);

        // 模拟进度更新
        $progress = [
            'step' => $step,
            'total' => $total,
            'progress' => $total > 0 ? round(($step / $total) * 100) : 0,
            'message' => $this->getStepMessage($step),
            'timestamp' => time()
        ];

        $this->success('获取进度成功', null, $progress);
    }

    /**
     * 获取步骤消息
     */
    private function getStepMessage($step)
    {
        $messages = [
            0 => '初始化导出参数',
            1 => '验证用户权限',
            2 => '查询简历数据',
            3 => '处理基本信息',
            4 => '处理联系人信息',
            5 => '处理教育经历',
            6 => '处理工作经历',
            7 => '处理图片文件',
            8 => '生成Excel文件',
            9 => '设置文件格式',
            10 => '完成导出'
        ];

        return isset($messages[$step]) ? $messages[$step] : '处理中...';
    }

    /**
     * 导出Excel
     */
    public function exportexcel()
    {
        // 增加权限校验，防止无权限用户导出
        if (!$this->auth->check('resume/exportexcel')) {
            $this->error('没有权限');
        }
        $ids = $this->request->get('ids');
        if (empty($ids)) {
            $this->error('请选择要导出的数据');
        }
        
        $ids = explode(',', $ids);
        $list = $this->model->where('id', 'in', $ids)->select();
        
        // 设置表头
        $header = [
            'ID', '姓名', '意向岗位', '申请岗位', '性别', '民族', '身高(cm)', '体重(kg)','身份证号', '年龄', 
            '出生日期', '手机号码', '婚姻状况', '户口所在地', '常住地址', '最高学历', '粤语熟练度',
            '国语熟练度', '英语熟练度', '港澳通行证编号', '港澳通行证到期时间',
            '海外工作经历', '海外工作地区', '特殊职业资格证', '兴趣爱好', '自我评价', '第一联系人关系', '第一联系人姓名',
            '第一联系人年龄', '第一联系人工作', '读书开始时间', '读书结束时间', '学校名称', '选修专业',
            '毕业学历', '工作开始时间', '工作结束时间', '工作单位', '工作岗位', '工作内容', '对接人',
            '头像', '全身照', '身份证正面', '身份证反面', '港澳通行证正面', '港澳通行证反面', '补充照片',
            '状态', '创建时间', '更新时间'
        ];
        
        // 准备数据
        $data = [];
        foreach ($list as $row) {
            // 处理联系人数据
            $contactRelations = explode('|', $row['contact_relation'] ?? '');
            $contactNames = explode('|', $row['contact_name'] ?? '');
            $contactAges = explode('|', $row['contact_age'] ?? '');
            $contactJobs = explode('|', $row['contact_job'] ?? '');
            
            // 处理教育经历数据
            $eduStarts = explode('|', $row['education_start'] ?? '');
            $eduEnds = explode('|', $row['education_end'] ?? '');
            $eduSchools = explode('|', $row['education_school'] ?? '');
            $eduMajors = explode('|', $row['education_major'] ?? '');
            $eduDegrees = explode('|', $row['graduation_education'] ?? '');
            
            // 处理工作经历数据
            $jobStarts = explode('|', $row['job_start'] ?? '');
            $jobEnds = explode('|', $row['job_end'] ?? '');
            $jobCompanies = explode('|', $row['job_company'] ?? '');
            $jobPositions = explode('|', $row['job_position'] ?? '');
            $jobDescriptions = explode('|', $row['job_description'] ?? '');
            
            // 获取最大记录数
            $maxContacts = max(count($contactRelations), count($contactNames), count($contactAges), count($contactJobs));
            $maxEdu = max(count($eduStarts), count($eduEnds), count($eduSchools), count($eduMajors), count($eduDegrees));
            $maxJobs = max(count($jobStarts), count($jobEnds), count($jobCompanies), count($jobPositions), count($jobDescriptions));
            $maxRows = max($maxContacts, $maxEdu, $maxJobs, 1);
            
            // 为每个记录创建一行
            for ($i = 0; $i < $maxRows; $i++) {
                $data[] = [
                    $row['id'], $row['name'], $row['intended_position'], $row['applied_position'], $row['gender'],
                    $row['ethnicity'], $row['height'], $row['weight'], $this->formatIdCard($row['id_card']), $row['age'],
                    $row['birth_date'], $row['phone'], $row['marital_status'], $row['hukou_location'],
                    $row['residence_address'], $row['highest_education'], $row['cantonese_level'],
                    $row['mandarin_level'], $row['english_level'], $row['hk_macau_passport'],
                    $row['hk_macau_passport_expiry'], $row['overseas_experience'],
                    $row['overseas_region'], $row['special_certificate'], $row['hobbies'], $row['self_evaluation'], 
                    $contactRelations[$i] ?? '', $contactNames[$i] ?? '', $contactAges[$i] ?? '', $contactJobs[$i] ?? '',
                    $eduStarts[$i] ?? '', $eduEnds[$i] ?? '', $eduSchools[$i] ?? '', $eduMajors[$i] ?? '', $eduDegrees[$i] ?? '',
                    $jobStarts[$i] ?? '', $jobEnds[$i] ?? '', $jobCompanies[$i] ?? '', $jobPositions[$i] ?? '', $jobDescriptions[$i] ?? '',
                    $row['contact_person'], '', '', '', '', '', '', '', // 图片字段留空，后面会插入实际图片
                    $row['status'], 
                    date('Y-m-d H:i:s', $row['create_time']), // 转换创建时间
                    date('Y-m-d H:i:s', $row['update_time'])  // 转换更新时间
                ];
            }
        }
        
        // 导出Excel
        $filename = '简历数据_' . date('YmdHis');
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        
        // 设置表头
        $sheet->fromArray([$header], null, 'A1');
        
        // 设置数据
        if (!empty($data)) {
            $sheet->fromArray($data, null, 'A2');
            
            // 按每条简历数据独立处理合并
            $mergeColumns = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z','AO','AP','AQ','AR','AS','AT','AU','AV','AW','AX','AY'];
            $mergeColumns = array_diff($mergeColumns, ['AA','AB','AC','AD','AE','AF','AG','AH','AI','AJ','Ak','AL','AM','AN']); // 排除联系人、教育、工作经历列
            
            $currentRow = 2;
            foreach ($list as $rowData) {
                // 获取当前简历的记录数
                $contactCount = count(explode('|', $rowData['contact_relation'] ?? ''));
                $eduCount = count(explode('|', $rowData['education_start'] ?? ''));
                $jobCount = count(explode('|', $rowData['job_start'] ?? ''));
                $maxRows = max($contactCount, $eduCount, $jobCount, 1);
                
                // 合并当前简历的基本信息单元格
                foreach ($mergeColumns as $col) {
                    if ($maxRows > 1) {
                        $sheet->mergeCells($col.$currentRow.':'.$col.($currentRow + $maxRows - 1));
                    }
                }
                
                // 处理图片
                $imageColumns = [
                    'AP' => 'avatar',
                    'AQ' => 'full_body_photo',
                    'AR' => 'id_card_front',
                    'AS' => 'id_card_back',
                    'AT' => 'hk_macau_passport_front',
                    'AU' => 'hk_macau_passport_back',
                    'AV' => 'additional_photos'
                ];
                
                // 获取合并单元格的高度（像素）
                $mergedCellHeight = $maxRows * 20; // 假设每行高度为20像素
                
                foreach ($imageColumns as $col => $field) {
                    if (!empty($rowData[$field])) {
                        $imagePath = ROOT_PATH . 'public' . $rowData[$field];
                        if (file_exists($imagePath)) {
                            try {
                                // 获取图片原始尺寸
                                list($width, $height) = getimagesize($imagePath);
                                
                                // 使用合并单元格的高度作为目标高度
                                $targetHeight = $mergedCellHeight;
                                
                                // 按原始比例计算宽度
                                $scaledWidth = ($width / $height) * $targetHeight;
                                
                                // 创建图片对象
                                $drawing = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
                                $drawing->setName('Image');
                                $drawing->setDescription('Image');
                                $drawing->setPath($imagePath);
                                $drawing->setCoordinates($col . $currentRow);
                                
                                // 设置图片尺寸
                                $drawing->setWidth($scaledWidth);
                                $drawing->setHeight($targetHeight);
                                
                                // 设置图片位置偏移
                                $drawing->setOffsetX(2);
                                $drawing->setOffsetY(2);
                                
                                // 设置图片位置
                                $drawing->setWorksheet($sheet);
                                
                                // 调整单元格高度以适应图片
                                $rowHeight = $targetHeight * 0.75; // 将像素转换为Excel行高
                                for ($i = 0; $i < $maxRows; $i++) {
                                    $sheet->getRowDimension($currentRow + $i)->setRowHeight($rowHeight / $maxRows);
                                }
                            } catch (\Exception $e) {
                                // 图片处理失败时继续处理其他图片
                                continue;
                            }
                        }
                    }
                }
                
                $currentRow += $maxRows;
            }
        }

        // 设置自动列宽
        foreach(range('A','AX') as $columnID) {
            $spreadsheet->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
        }
        
        // 设置所有单元格水平垂直居中
        $sheet->getStyle($sheet->calculateWorksheetDimension())->getAlignment()
            ->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER)
            ->setVertical(\PhpOffice\PhpSpreadsheet\Style\Alignment::VERTICAL_CENTER);

        // 设置列宽
        $columnWidths = [
            'A' => 10,  // ID
            'B' => 15,  // 姓名
            'C' => 20,  // 意向岗位
            'D' => 20,  // 申请岗位
            'E' => 10,  // 性别
            'F' => 10,  // 民族
            'G' => 10,  // 身高
            'H' => 10,  // 体重
            'I' => 30,  // 身份证号
            'J' => 10,  // 年龄
            'K' => 15,  // 出生日期
            'L' => 15,  // 手机号码
            'M' => 10,  // 婚姻状况
            'N' => 20,  // 户口所在地
            'O' => 30,  // 常住地址
            'P' => 15,  // 最高学历
            'Q' => 15,  // 粤语熟练度
            'R' => 15,  // 国语熟练度
            'S' => 15,  // 英语熟练度
            'T' => 20,  // 港澳通行证编号
            'U' => 15,  // 港澳通行证到期时间
            'V' => 20,  // 海外工作经历
            'W' => 15,  // 海外工作地区
            'X' => 20,  // 特殊职业资格证
            'Y' => 20,  // 兴趣爱好
            'Z' => 30,  // 自我评价
            'AA' => 15, // 第一联系人关系
            'AB' => 15, // 第一联系人姓名
            'AC' => 10, // 第一联系人年龄
            'AD' => 20, // 第一联系人工作
            'AE' => 15, // 读书开始时间
            'AF' => 15, // 读书结束时间
            'AG' => 20, // 学校名称
            'AH' => 20, // 选修专业
            'AI' => 15, // 毕业学历
            'AJ' => 15, // 工作开始时间
            'AK' => 15, // 工作结束时间
            'AL' => 20, // 工作单位
            'AM' => 20, // 工作岗位
            'AN' => 30, // 工作内容
            'AO' => 15, // 对接人
            'AP' => 20, // 头像
            'AQ' => 20, // 全身照
            'AR' => 20, // 身份证正面
            'AS' => 20, // 身份证反面
            'AT' => 20, // 港澳通行证正面
            'AU' => 20, // 港澳通行证反面
            'AV' => 20, // 补充照片
            'AW' => 10, // 状态
            'AX' => 20, // 创建时间
            'AY' => 20  // 更新时间
        ];

        // 应用列宽设置
        foreach ($columnWidths as $column => $width) {
            $sheet->getColumnDimension($column)->setWidth($width);
        }

        // 输出Excel
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
        header('Cache-Control: max-age=0');

        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }



/**
 * 导入 Excel
 */
public function import()
{
    if ($this->request->isPost()) {
        // 检查是否有文件上传
        if (!$this->request->file('excel_file')) {
            $this->error('请选择要导入的 Excel 文件');
        }

        // 获取上传的文件
        $file = $this->request->file('excel_file');

        try {
            // 开启事务
            $this->model->startTrans();
            
            // 读取 Excel 文件
            $reader = new Xlsx();
            $spreadsheet = $reader->load($file->getRealPath());
            $sheet = $spreadsheet->getActiveSheet();
            $rows = $sheet->toArray();

            // 跳过表头
            array_shift($rows);

            $currentData = null;
            $contactRelations = [];
            $contactNames = [];
            $contactAges = [];
            $contactJobs = [];
            $eduStarts = [];
            $eduEnds = [];
            $eduSchools = [];
            $eduMajors = [];
            $eduDegrees = [];
            $jobStarts = [];
            $jobEnds = [];
            $jobCompanies = [];
            $jobPositions = [];
            $jobDescriptions = [];
            
            $successCount = 0;
            $errorCount = 0;
            $errorMessages = [];

            foreach ($rows as $index => $row) {
                try {
                    if (!empty($row[1]) && ($currentData === null || $currentData['name'] !== $row[1])) {
                        // 如果已有数据，先保存之前的简历
                        if ($currentData !== null) {
                            // 合并多行数据
                            $currentData['contact_relation'] = implode('|', array_filter($contactRelations));
                            $currentData['contact_name'] = implode('|', array_filter($contactNames));
                            $currentData['contact_age'] = implode('|', array_filter($contactAges));
                            $currentData['contact_job'] = implode('|', array_filter($contactJobs));
                            $currentData['education_start'] = implode('|', array_filter($eduStarts));
                            $currentData['education_end'] = implode('|', array_filter($eduEnds));
                            $currentData['education_school'] = implode('|', array_filter($eduSchools));
                            $currentData['education_major'] = implode('|', array_filter($eduMajors));
                            $currentData['graduation_education'] = implode('|', array_filter($eduDegrees));
                            $currentData['job_start'] = implode('|', array_filter($jobStarts));
                            $currentData['job_end'] = implode('|', array_filter($jobEnds));
                            $currentData['job_company'] = implode('|', array_filter($jobCompanies));
                            $currentData['job_position'] = implode('|', array_filter($jobPositions));
                            $currentData['job_description'] = implode('|', array_filter($jobDescriptions));

                            // 保存数据
                            $result = $this->model->create($currentData);
                            if ($result) {
                                $successCount++;
                            } else {
                                $errorCount++;
                                $errorMessages[] = "第" . ($index + 2) . "行数据保存失败";
                            }
                        }

                        // 重置数据数组
                        $contactRelations = [];
                        $contactNames = [];
                        $contactAges = [];
                        $contactJobs = [];
                        $eduStarts = [];
                        $eduEnds = [];
                        $eduSchools = [];
                        $eduMajors = [];
                        $eduDegrees = [];
                        $jobStarts = [];
                        $jobEnds = [];
                        $jobCompanies = [];
                        $jobPositions = [];
                        $jobDescriptions = [];

                        // 创建新的简历数据
                        $currentData = [
                            'name' => $row[1],
                            'intended_position' => $row[2],
                            'applied_position' => $row[3],
                            'gender' => $row[4],
                            'ethnicity' => $row[5],
                            'height' => $row[6],
                            'weight' => $row[7],
                            'id_card' => $row[8],
                            'age' => $row[9],
                            'birth_date' => $this->convertDateFormat($row[10], 'full'),
                            'phone' => $row[11],
                            'marital_status' => $row[12],
                            'hukou_location' => $row[13],
                            'residence_address' => $row[14],
                            'highest_education' => $row[15],
                            'cantonese_level' => $row[16],
                            'mandarin_level' => $row[17],
                            'english_level' => $row[18],
                            'hk_macau_passport' => $row[19],
                            'hk_macau_passport_expiry' => $this->convertDateFormat($row[20], 'full'),
                            'overseas_experience' => $row[21],
                            'overseas_region' => $row[22],
                            'special_certificate' => $row[23],
                            'hobbies' => $row[24],
                            'self_evaluation' => $row[25],
                            'contact_person' => $row[40],
                            'status' => empty($row[41]) ? '已发布' : $row[41]
                        ];
                    }

                    // 收集多行数据
                    if (!empty($row[26])) $contactRelations[] = $row[26];
                    if (!empty($row[27])) $contactNames[] = $row[27];
                    if (!empty($row[28])) $contactAges[] = $row[28];
                    if (!empty($row[29])) $contactJobs[] = $row[29];
                    if (!empty($row[30])) $eduStarts[] = $this->convertDateFormat($row[30], 'month');
                    if (!empty($row[31])) $eduEnds[] = $this->convertDateFormat($row[31], 'month');
                    if (!empty($row[32])) $eduSchools[] = $row[32];
                    if (!empty($row[33])) $eduMajors[] = $row[33];
                    if (!empty($row[34])) $eduDegrees[] = $row[34];
                    if (!empty($row[35])) $jobStarts[] = $this->convertDateFormat($row[35], 'month');
                    if (!empty($row[36])) $jobEnds[] = $this->convertDateFormat($row[36], 'month');
                    if (!empty($row[37])) $jobCompanies[] = $row[37];
                    if (!empty($row[38])) $jobPositions[] = $row[38];
                    if (!empty($row[39])) $jobDescriptions[] = $row[39];
                } catch (\Exception $e) {
                    $errorCount++;
                    $errorMessages[] = "第" . ($index + 2) . "行数据处理失败：" . $e->getMessage();
                }
            }

            // 保存最后一条数据
            if ($currentData !== null) {
                try {
                    $currentData['contact_relation'] = implode('|', array_filter($contactRelations));
                    $currentData['contact_name'] = implode('|', array_filter($contactNames));
                    $currentData['contact_age'] = implode('|', array_filter($contactAges));
                    $currentData['contact_job'] = implode('|', array_filter($contactJobs));
                    $currentData['education_start'] = implode('|', array_filter($eduStarts));
                    $currentData['education_end'] = implode('|', array_filter($eduEnds));
                    $currentData['education_school'] = implode('|', array_filter($eduSchools));
                    $currentData['education_major'] = implode('|', array_filter($eduMajors));
                    $currentData['graduation_education'] = implode('|', array_filter($eduDegrees));
                    $currentData['job_start'] = implode('|', array_filter($jobStarts));
                    $currentData['job_end'] = implode('|', array_filter($jobEnds));
                    $currentData['job_company'] = implode('|', array_filter($jobCompanies));
                    $currentData['job_position'] = implode('|', array_filter($jobPositions));
                    $currentData['job_description'] = implode('|', array_filter($jobDescriptions));

                    $result = $this->model->create($currentData);
                    if ($result) {
                        $successCount++;
                    } else {
                        $errorCount++;
                        $errorMessages[] = "最后一条数据保存失败";
                    }
                } catch (\Exception $e) {
                    $errorCount++;
                    $errorMessages[] = "最后一条数据处理失败：" . $e->getMessage();
                }
            }

            // 提交事务
            $this->model->commit();

            // 返回导入结果
            if ($errorCount > 0) {
                $message = "导入完成，成功导入 {$successCount} 条数据，失败 {$errorCount} 条。\n失败原因：\n" . implode("\n", $errorMessages);
                $this->result(['url' => url('index')], 0, $message);
            } else {
                $this->result(['url' => url('index')], 1, "成功导入 {$successCount} 条数据");
            }
        } catch (\Exception $e) {
            // 回滚事务
            $this->model->rollback();
            $this->result(['url' => url('index')], 0, "导入完成，成功导入 {$successCount} 条数据，失败 {$errorCount} 条。");
        }
    }

    return $this->view->fetch();
}

    /**
     * 获取Excel模板列表
     */
    public function getTemplateList()
    {
        $templatesDir = ROOT_PATH . 'public' . DS . 'templates' . DS . 'resume';
        $templates = [];
        
        if (is_dir($templatesDir)) {
            if ($dh = opendir($templatesDir)) {
                while (($file = readdir($dh)) !== false) {
                    // 只处理Excel文件
                    $ext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                    if ($ext === 'xlsx' && $file != '.' && $file != '..') {
                        $filePath = $templatesDir . DS . $file;
                        $templates[] = [
                            'name' => $file,
                            'path' => str_replace(ROOT_PATH, '', $filePath), // 相对路径
                            'size' => filesize($filePath),
                            'modified' => date('Y-m-d H:i:s', filemtime($filePath))
                        ];
                    }
                }
                closedir($dh);
            }
        }
        
        // 按修改时间排序，最新的排在前面
        usort($templates, function($a, $b) {
            return strtotime($b['modified']) - strtotime($a['modified']);
        });
        
        return json(['code' => 1, 'msg' => '获取模板列表成功', 'data' => $templates]);
    }
    
    /**
     * 获取Word模板列表
     */
    public function getWordTemplateList()
    {
        $templatesDir = ROOT_PATH . 'public' . DS . 'templates' . DS . 'resume';
        $templates = [];
        
        if (is_dir($templatesDir)) {
            if ($dh = opendir($templatesDir)) {
                while (($file = readdir($dh)) !== false) {
                    // 只处理Word文件
                    $ext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                    if ($ext === 'docx' && $file != '.' && $file != '..') {
                        $filePath = $templatesDir . DS . $file;
                        $templates[] = [
                            'name' => $file,
                            'path' => str_replace(ROOT_PATH, '', $filePath), // 相对路径
                            'size' => filesize($filePath),
                            'modified' => date('Y-m-d H:i:s', filemtime($filePath))
                        ];
                    }
                }
                closedir($dh);
            }
        }
        
        // 按修改时间排序，最新的排在前面
        usort($templates, function($a, $b) {
            return strtotime($b['modified']) - strtotime($a['modified']);
        });
        
        return json(['code' => 1, 'msg' => '获取Word模板列表成功', 'data' => $templates]);
    }
    
    /**
     * 导出到选定的Excel模板
     */
    public function exportToTemplate()
    {
        if ($this->request->isPost()) {
            // 增加内存限制
            ini_set('memory_limit', '1024M');
            
            $ids = $this->request->post('ids');
            $templatePath = $this->request->post('template');
            $filenameFields = $this->request->post('filename_fields');
            
            if (empty($ids)) {
                $this->error('请选择要导出的数据');
            }
            
            if (empty($templatePath)) {
                $this->error('请选择导出模板');
            }
            
            $fullTemplatePath = ROOT_PATH . $templatePath;
            if (!file_exists($fullTemplatePath)) {
                $this->error('模板文件不存在');
            }
            
            $ids = explode(',', $ids);
            $list = $this->model->where('id', 'in', $ids)->select();
            
            // 创建临时目录存放生成的文件 - 使用一致的目录分隔符
            $tempDir = ROOT_PATH . 'public' . DS . 'temp' . DS . date('YmdHis') . rand(1000, 9999);
            if (!is_dir($tempDir)) {
                if (!mkdir($tempDir, 0777, true)) {
                    $this->error('创建临时目录失败，请检查目录权限');
                }
            }
            
            $fileList = [];
            
            // 处理每条记录，生成单独的Excel文件
            foreach ($list as $index => $data) {
                try {
                    // 读取模板文件
                    $reader = IOFactory::createReader('Xlsx');
                    $spreadsheet = $reader->load($fullTemplatePath);
                    $sheet = $spreadsheet->getActiveSheet();
                    
                    // 查找并替换占位符
                    $allCells = $this->getAllCellValues($sheet);
                    
                    // 格式化日期字段
                    $birth_date = $this->formatDateToChineseYMD($data['birth_date']);
                    $hk_macau_passport_expiry = $this->formatDateToChineseYMD($data['hk_macau_passport_expiry']);
                    $update_time = $this->formatDateToChineseYMD(date('Y-m-d', $data['update_time']));
                    
                    // 格式化身高体重（不带单位，不保留小数点）
                    $height = !empty($data['height']) ? intval($data['height']) : '';
                    $weight = !empty($data['weight']) ? intval($data['weight']) : '';
                    
                    // 处理身份证号（不再添加单引号前缀）
                    $idCard = !empty($data['id_card']) ? $this->formatIdCard($data['id_card']) : '';
                    
                    // 定义占位符映射
                    $placeholderMap = [
                        '{{姓名}}' => $data['name'], 
                        '{{性别}}' => $data['gender'],
                        '{{年龄}}' => $data['age'],
                        '{{手机号}}' => $data['phone'],
                        '{{最高学历}}' => $data['highest_education'],
                        '{{意向岗位}}' => $data['intended_position'],
                        '{{申请岗位}}' => $data['applied_position'],
                        '{{出生日期}}' => $birth_date,
                        '{{身份证号}}' => $idCard,
                        '{{婚姻状况}}' => $data['marital_status'],
                        '{{户口所在地}}' => $data['hukou_location'],
                        '{{常住地址}}' => $data['residence_address'],
                        '{{身高}}' => $height,
                        '{{体重}}' => $weight,
                        '{{民族}}' => $data['ethnicity'],
                        '{{粤语水平}}' => $data['cantonese_level'],
                        '{{国语水平}}' => $data['mandarin_level'],
                        '{{英语水平}}' => $data['english_level'],
                        '{{港澳通行证}}' => $data['hk_macau_passport'],
                        '{{港澳通行证到期}}' => $hk_macau_passport_expiry,
                        '{{海外工作经历}}' => $data['overseas_experience'],
                        '{{海外工作地区}}' => $data['overseas_region'],
                        '{{特殊证书}}' => $data['special_certificate'],
                        '{{兴趣爱好}}' => $data['hobbies'],
                        '{{自我评价}}' => $data['self_evaluation'],
                        '{{更新时间}}' => $update_time,
                    ];
                    
                    // 处理复杂字段（联系人、教育、工作信息）
                    $contactRelations = explode('|', $data['contact_relation'] ?? '');
                    $contactNames = explode('|', $data['contact_name'] ?? '');
                    $contactAges = explode('|', $data['contact_age'] ?? '');
                    $contactJobs = explode('|', $data['contact_job'] ?? '');
                    
                    // 为联系人字段预设一定数量的占位符（假设最多5个联系人）
                    for ($i = 1; $i <= 5; $i++) {
                        $placeholderMap['{{联系人关系' . $i . '}}'] = isset($contactRelations[$i-1]) && !empty($contactRelations[$i-1]) ? $contactRelations[$i-1] : '';
                        $placeholderMap['{{联系人姓名' . $i . '}}'] = isset($contactNames[$i-1]) && !empty($contactNames[$i-1]) ? $contactNames[$i-1] : '';
                        $placeholderMap['{{联系人年龄' . $i . '}}'] = isset($contactAges[$i-1]) && !empty($contactAges[$i-1]) ? $contactAges[$i-1] : '';
                        $placeholderMap['{{联系人工作' . $i . '}}'] = isset($contactJobs[$i-1]) && !empty($contactJobs[$i-1]) ? $contactJobs[$i-1] : '';
                    }
                    
                    $eduStarts = explode('|', $data['education_start'] ?? '');
                    $eduEnds = explode('|', $data['education_end'] ?? '');
                    $eduSchools = explode('|', $data['education_school'] ?? '');
                    $eduMajors = explode('|', $data['education_major'] ?? '');
                    $eduDegrees = explode('|', $data['graduation_education'] ?? '');
                    
                    // 为教育经历字段预设一定数量的占位符（假设最多5段教育经历）
                    for ($i = 1; $i <= 5; $i++) {
                        $startDate = isset($eduStarts[$i-1]) && !empty($eduStarts[$i-1]) ? $this->formatDateToChineseYM($eduStarts[$i-1]) : '';
                        $endDate = isset($eduEnds[$i-1]) && !empty($eduEnds[$i-1]) ? $this->formatDateToChineseYM($eduEnds[$i-1]) : '';
                        
                        $placeholderMap['{{教育开始' . $i . '}}'] = $startDate;
                        $placeholderMap['{{教育结束' . $i . '}}'] = $endDate;
                        $placeholderMap['{{学校' . $i . '}}'] = isset($eduSchools[$i-1]) && !empty($eduSchools[$i-1]) ? $eduSchools[$i-1] : '';
                        $placeholderMap['{{专业' . $i . '}}'] = isset($eduMajors[$i-1]) && !empty($eduMajors[$i-1]) ? $eduMajors[$i-1] : '';
                        $placeholderMap['{{学历' . $i . '}}'] = isset($eduDegrees[$i-1]) && !empty($eduDegrees[$i-1]) ? $eduDegrees[$i-1] : '';
                    }
                    
                    $jobStarts = explode('|', $data['job_start'] ?? '');
                    $jobEnds = explode('|', $data['job_end'] ?? '');
                    $jobCompanies = explode('|', $data['job_company'] ?? '');
                    $jobPositions = explode('|', $data['job_position'] ?? '');
                    $jobDescriptions = explode('|', $data['job_description'] ?? '');
                    
                    // 为工作经历字段预设一定数量的占位符（假设最多5段工作经历）
                    for ($i = 1; $i <= 5; $i++) {
                        $startDate = isset($jobStarts[$i-1]) && !empty($jobStarts[$i-1]) ? $this->formatDateToChineseYM($jobStarts[$i-1]) : '';
                        $endDate = isset($jobEnds[$i-1]) && !empty($jobEnds[$i-1]) ? $this->formatDateToChineseYM($jobEnds[$i-1]) : '';
                        
                        $placeholderMap['{{工作开始' . $i . '}}'] = $startDate;
                        $placeholderMap['{{工作结束' . $i . '}}'] = $endDate;
                        $placeholderMap['{{工作单位' . $i . '}}'] = isset($jobCompanies[$i-1]) && !empty($jobCompanies[$i-1]) ? $jobCompanies[$i-1] : '';
                        $placeholderMap['{{工作岗位' . $i . '}}'] = isset($jobPositions[$i-1]) && !empty($jobPositions[$i-1]) ? $jobPositions[$i-1] : '';
                        $placeholderMap['{{工作内容' . $i . '}}'] = isset($jobDescriptions[$i-1]) && !empty($jobDescriptions[$i-1]) ? $jobDescriptions[$i-1] : '';
                    }
                    
                    // 处理图片字段
                    $imageFields = [
                        '{{头像}}' => $data['avatar'] ?? '',
                        '{{全身照}}' => $data['full_body_photo'] ?? '',
                        '{{身份证正面}}' => $data['id_card_front'] ?? '',
                        '{{身份证反面}}' => $data['id_card_back'] ?? '',
                        '{{港澳通行证正面}}' => $data['hk_macau_passport_front'] ?? '',
                        '{{港澳通行证反面}}' => $data['hk_macau_passport_back'] ?? '',
                    ];
                    
                    // 处理additional_photos多图片字段
                    $additionalPhotos = [];
                    if (!empty($data['additional_photos'])) {
                        $photoArray = explode(',', $data['additional_photos']);
                        foreach ($photoArray as $index => $photoPath) {
                            $additionalPhotos['{{补充照片' . ($index + 1) . '}}'] = $photoPath;
                        }
                    }
                    
                    // 预设一定数量的补充照片占位符（假设最多10张补充照片）
                    for ($i = 1; $i <= 10; $i++) {
                        if (!isset($additionalPhotos['{{补充照片' . $i . '}}'])) {
                            $additionalPhotos['{{补充照片' . $i . '}}'] = '';
                        }
                    }
                    
                    // 合并所有占位符
                    $placeholderMap = array_merge($placeholderMap, $imageFields, $additionalPhotos);
                    
                    // 遍历所有单元格，替换占位符
                    foreach ($allCells as $cellCoordinate => $cellValue) {
                        if (is_string($cellValue)) {
                            // 检查单元格是否包含占位符
                            $newValue = $cellValue;
                            foreach ($placeholderMap as $placeholder => $value) {
                                if (strpos($newValue, $placeholder) !== false) {
                                    $newValue = str_replace($placeholder, $value, $newValue);
                                }
                            }
                            
                            // 如果值已更改，更新单元格
                            if ($newValue !== $cellValue) {
                                $sheet->setCellValue($cellCoordinate, $newValue);
                            }
                        }
                    }
                    
                    // 为不同类型的图片设置固定高度
                    $imageHeightSettings = [
                        '{{头像}}' => 210,           
                        '{{全身照}}' => 480,           
                        '{{身份证正面}}' => 235,       
                        '{{身份证反面}}' => 235,       
                        '{{港澳通行证正面}}' => 235,  
                        '{{港澳通行证反面}}' => 235,   
                        '补充照片' => 235              
                    ];
                    
                    // 处理图片插入
                    // 先处理普通图片
                    foreach ($imageFields as $placeholder => $imagePath) {
                        if (!empty($imagePath)) {
                            // 查找包含此占位符的单元格
                            foreach ($allCells as $cellCoordinate => $cellValue) {
                                if (is_string($cellValue) && strpos($cellValue, $placeholder) !== false) {
                                    // 清除单元格内容
                                    $sheet->setCellValue($cellCoordinate, '');
                                    
                                    // 获取完整图片路径
                                    $fullImagePath = ROOT_PATH . 'public' . $imagePath;
                                    
                                    // 检查图片是否存在
                                    if (file_exists($fullImagePath)) {
                                        try {
                                            // 获取图片尺寸
                                            list($width, $height) = getimagesize($fullImagePath);
                                            
                                            // 获取该类型图片的指定高度
                                            $targetHeight = $imageHeightSettings[$placeholder];
                                            
                                            // 按原始比例计算宽度
                                            $scaledWidth = ($width / $height) * $targetHeight;
                                            $scaledHeight = $targetHeight;
                                            
                                            // 添加图片到单元格
                                            $drawing = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
                                            $drawing->setName('Image');
                                            $drawing->setDescription('Image');
                                            $drawing->setPath($fullImagePath);
                                            $drawing->setCoordinates($cellCoordinate);
                                            
                                            // 设置指定高度和按比例计算的宽度
                                            $drawing->setWidth($scaledWidth);
                                            $drawing->setHeight($scaledHeight);
                                            $drawing->setOffsetX(2);
                                            $drawing->setOffsetY(2);
                                            $drawing->setWorksheet($sheet);
                                        } catch (\Exception $e) {
                                            // 图片处理异常，继续处理其他单元格
                                            continue;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    // 处理补充照片
                    foreach ($additionalPhotos as $placeholder => $imagePath) {
                        if (!empty($imagePath)) {
                            // 查找包含此占位符的单元格
                            foreach ($allCells as $cellCoordinate => $cellValue) {
                                if (is_string($cellValue) && strpos($cellValue, $placeholder) !== false) {
                                    // 清除单元格内容
                                    $sheet->setCellValue($cellCoordinate, '');
                                    
                                    // 获取完整图片路径
                                    $fullImagePath = ROOT_PATH . 'public' . $imagePath;
                                    
                                    // 检查图片是否存在
                                    if (file_exists($fullImagePath)) {
                                        try {
                                            // 获取图片尺寸
                                            list($width, $height) = getimagesize($fullImagePath);
                                            
                                            // 获取补充照片的指定高度
                                            $targetHeight = $imageHeightSettings['补充照片'];
                                            
                                            // 按原始比例计算宽度
                                            $scaledWidth = ($width / $height) * $targetHeight;
                                            $scaledHeight = $targetHeight;
                                            
                                            // 添加图片到单元格
                                            $drawing = new \PhpOffice\PhpSpreadsheet\Worksheet\Drawing();
                                            $drawing->setName('Image');
                                            $drawing->setDescription('Image');
                                            $drawing->setPath($fullImagePath);
                                            $drawing->setCoordinates($cellCoordinate);
                                            
                                            // 设置指定高度和按比例计算的宽度
                                            $drawing->setWidth($scaledWidth);
                                            $drawing->setHeight($scaledHeight);
                                            $drawing->setOffsetX(2);
                                            $drawing->setOffsetY(2);
                                            $drawing->setWorksheet($sheet);
                                        } catch (\Exception $e) {
                                            // 图片处理异常，继续处理其他单元格
                                            continue;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    // 生成文件名
                    $filenameComponents = [];
                    $defaultFields = ['name', 'age', 'applied_position']; // 默认字段
                    
                    // 如果没有提供字段，使用默认字段
                    $selectedFields = !empty($filenameFields) ? explode(',', $filenameFields) : $defaultFields;
                    
                    foreach ($selectedFields as $field) {
                        if (isset($data[$field]) && !empty($data[$field])) {
                            // 获取字段值，进行必要的处理
                            $fieldValue = $data[$field];
                            
                            // 特殊字段处理
                            if ($field === 'age') {
                                // 对年龄字段特殊处理，添加"岁"
                                $fieldValue = $fieldValue . '岁';
                            } elseif ($field === 'hukou_location') {
                                // 处理户口所在地的"/"字符，直接移除
                                $fieldValue = str_replace(['/', '\\'], '', $fieldValue);
                            } elseif ($field === 'height') {
                                // 处理身高，添加单位
                                $fieldValue = $fieldValue . 'cm';
                            } elseif ($field === 'weight') {
                                // 处理体重，添加单位
                                $fieldValue = $fieldValue . 'kg';
                            }
                            
                            // 移除文件名中的非法字符
                            $fieldValue = preg_replace('/[\\\\\/\:\*\?\"\<\>\|]/', '', $fieldValue);
                            
                            $filenameComponents[] = $fieldValue;
                        }
                    }
                    
                    // 如果没有可用组件，使用姓名和时间戳
                    if (empty($filenameComponents)) {
                        $filenameComponents[] = $data['name'] ?: '未命名';
                        $filenameComponents[] = date('YmdHis') . '_' . ($index + 1);
                    }
                    
                    // 生成文件名 - 确保文件名中不包含非法字符
                    $filename = implode('-', $filenameComponents) . '.xlsx';
                    
                    // 使用一致的目录分隔符
                    $outputFile = $tempDir . DS . $filename;
                    
                    // 保存文件
                    $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
                    $writer->save($outputFile);
                    
                    $fileList[] = [
                        'path' => $outputFile,
                        'name' => $filename
                    ];
                } catch (\Exception $e) {
                    $this->error('导出失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
                }
            }
            
            // 如果只有一个文件，则直接下载
            if (count($fileList) === 1) {
                $file = $fileList[0];
                
                if(!file_exists($file['path'])) {
                    $this->error('导出失败: 生成的文件不存在: ' . $file['path']);
                }
                
                header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
                header('Content-Disposition: attachment;filename="' . $file['name'] . '"');
                header('Cache-Control: max-age=0');
                
                if(!readfile($file['path'])) {
                    $this->error('导出失败: 无法读取生成的文件');
                }
                
                // 删除临时文件
                @unlink($file['path']);
                if (is_dir($tempDir)) {
                    @rmdir($tempDir);
                }
                exit;
            }
            
            // 如果有多个文件，创建ZIP压缩包
            $zipPath = $tempDir . '.zip';
            $zip = new \ZipArchive();
            
            if ($zip->open($zipPath, \ZipArchive::CREATE) === TRUE) {
                $addedFiles = 0;
                foreach ($fileList as $file) {
                    if(file_exists($file['path'])) {
                        if($zip->addFile($file['path'], $file['name'])) {
                            $addedFiles++;
                        }
                    }
                }
                $zip->close();
                
                if($addedFiles == 0) {
                    $this->error('导出失败: 没有成功添加任何文件到ZIP包');
                }
                
                // 下载压缩包
                header('Content-Type: application/zip');
                header('Content-Disposition: attachment;filename="简历汇总_' . date('YmdHis') . '.zip"');
                header('Cache-Control: max-age=0');
                
                if(!readfile($zipPath)) {
                    $this->error('导出失败: 无法读取生成的ZIP文件');
                }
                
                // 删除临时文件
                foreach ($fileList as $file) {
                    @unlink($file['path']);
                }
                @unlink($zipPath);
                if (is_dir($tempDir)) {
                    @rmdir($tempDir);
                }
                exit;
            } else {
                $this->error('创建ZIP压缩包失败');
            }
        }
        
        $this->error('请求方式错误');
    }
    
    /**
     * 获取Excel表格中所有单元格值
     * @param \PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet
     * @return array
     */
    protected function getAllCellValues($sheet)
    {
        $cells = [];
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();
        
        // 转换列字母为索引
        $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);
        
        // 遍历所有单元格
        for ($row = 1; $row <= $highestRow; $row++) {
            for ($col = 1; $col <= $highestColumnIndex; $col++) {
                $cellCoordinate = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::stringFromColumnIndex($col) . $row;
                $cellValue = $sheet->getCell($cellCoordinate)->getValue();
                
                // 只记录非空且包含占位符的单元格
                if (!empty($cellValue) && is_string($cellValue) && strpos($cellValue, '{{') !== false) {
                    $cells[$cellCoordinate] = $cellValue;
                }
            }
        }
        
        return $cells;
    }
    
    /**
     * 格式化日期为中文年月日格式
     * @param string $date 日期字符串，格式为 Y-m-d
     * @return string 格式化后的日期，如 2023年5月20日
     */
    protected function formatDateToChineseYMD($date)
    {
        if (empty($date)) {
            return '';
        }
        
        // 尝试解析日期
        $timestamp = strtotime($date);
        if ($timestamp === false) {
            return $date;
        }
        
        // 转换为年月日格式
        return date('Y年m月d日', $timestamp);
    }
    
    /**
     * 格式化日期为中文年月格式
     * @param string $date 日期字符串，格式为 Y-m 或 Y-m-d
     * @return string 格式化后的日期，如 2023年5月
     */
    protected function formatDateToChineseYM($date)
    {
        if (empty($date)) {
            return '';
        }
        
        // 如果包含日，先移除日部分
        if (substr_count($date, '-') > 1) {
            $date = substr($date, 0, 7);
        }
        
        // 尝试解析日期
        $timestamp = strtotime($date);
        if ($timestamp === false) {
            return $date;
        }
        
        // 转换为年月格式
        return date('Y年m月', $timestamp);
    }

    /**
     * 导出到选定的Word模板
     */
    public function exportToWordTemplate()
    {
        if ($this->request->isPost()) {
            // 增加内存限制
            ini_set('memory_limit', '1024M');
            
            $ids = $this->request->post('ids');
            $templatePath = $this->request->post('template');
            $filenameFields = $this->request->post('filename_fields');
            
            if (empty($ids)) {
                $this->error('请选择要导出的数据');
            }
            
            if (empty($templatePath)) {
                $this->error('请选择导出模板');
            }
            
            $fullTemplatePath = ROOT_PATH . $templatePath;
            if (!file_exists($fullTemplatePath)) {
                $this->error('模板文件不存在');
            }
            
            $ids = explode(',', $ids);
            $list = $this->model->where('id', 'in', $ids)->select();
            
            // 创建临时目录存放生成的文件 - 使用一致的目录分隔符
            $tempDir = ROOT_PATH . 'public' . DS . 'temp' . DS . date('YmdHis') . rand(1000, 9999);
            if (!is_dir($tempDir)) {
                if (!mkdir($tempDir, 0777, true)) {
                    $this->error('创建临时目录失败，请检查目录权限');
                }
            }
            
            // 检查是否安装了PHPWord
            if (!class_exists('\PhpOffice\PhpWord\PhpWord')) {
                $this->error('未安装PhpWord扩展，请先安装PhpOffice/PhpWord');
            }
            
            // 引入PhpWord命名空间
            $fileList = [];
            
            // 处理每条记录，生成单独的Word文件
            foreach ($list as $index => $data) {
                try {
                    // 创建PHPWord实例
                    $phpWord = new \PhpOffice\PhpWord\PhpWord();
                    
                    // 加载模板文档
                    $template = new \PhpOffice\PhpWord\TemplateProcessor($fullTemplatePath);
                    
                    // 格式化日期字段
                    $birth_date = $this->formatDateToChineseYMD($data['birth_date']);
                    $hk_macau_passport_expiry = $this->formatDateToChineseYMD($data['hk_macau_passport_expiry']);
                    $update_time = $this->formatDateToChineseYMD(date('Y-m-d', $data['update_time']));
                    
                    // 格式化身高体重（不带单位，不保留小数点）
                    $height = !empty($data['height']) ? intval($data['height']) : '';
                    $weight = !empty($data['weight']) ? intval($data['weight']) : '';
                    
                    // 处理身份证号（不再添加单引号前缀）
                    $idCard = !empty($data['id_card']) ? $this->formatIdCard($data['id_card']) : '';
                    
                    // 安全设置值的辅助函数，确保数据为空时清除占位符
                    $safeSetValue = function($template, $placeholder, $value) {
                        if (empty($value) || $value === '') {
                            $template->setValue($placeholder, '');
                        } else {
                            $template->setValue($placeholder, $value);
                        }
                    };
                    
                    // 设置基本信息变量，使用安全设置函数
                    $safeSetValue($template, '姓名', $data['name'] ?? '');
                    $safeSetValue($template, '性别', $data['gender'] ?? '');
                    $safeSetValue($template, '年龄', $data['age'] ?? '');
                    $safeSetValue($template, '手机号', $data['phone'] ?? '');
                    $safeSetValue($template, '最高学历', $data['highest_education'] ?? '');
                    $safeSetValue($template, '意向岗位', $data['intended_position'] ?? '');
                    $safeSetValue($template, '申请岗位', $data['applied_position'] ?? '');
                    $safeSetValue($template, '出生日期', $birth_date);
                    $safeSetValue($template, '身份证号', $idCard);
                    $safeSetValue($template, '婚姻状况', $data['marital_status'] ?? '');
                    $safeSetValue($template, '户口所在地', $data['hukou_location'] ?? '');
                    $safeSetValue($template, '常住地址', $data['residence_address'] ?? '');
                    $safeSetValue($template, '身高', $height);
                    $safeSetValue($template, '体重', $weight);
                    $safeSetValue($template, '民族', $data['ethnicity'] ?? '');
                    $safeSetValue($template, '粤语水平', $data['cantonese_level'] ?? '');
                    $safeSetValue($template, '国语水平', $data['mandarin_level'] ?? '');
                    $safeSetValue($template, '英语水平', $data['english_level'] ?? '');
                    $safeSetValue($template, '港澳通行证', $data['hk_macau_passport'] ?? '');
                    $safeSetValue($template, '港澳通行证到期', $hk_macau_passport_expiry);
                    $safeSetValue($template, '海外工作经历', $data['overseas_experience'] ?? '');
                    $safeSetValue($template, '海外工作地区', $data['overseas_region'] ?? '');
                    $safeSetValue($template, '特殊证书', $data['special_certificate'] ?? '');
                    $safeSetValue($template, '兴趣爱好', $data['hobbies'] ?? '');
                    $safeSetValue($template, '自我评价', $data['self_evaluation'] ?? '');
                    $safeSetValue($template, '更新时间', $update_time);
                    
                    // 处理复杂字段（联系人、教育、工作信息）
                    $contactRelations = explode('|', $data['contact_relation'] ?? '');
                    $contactNames = explode('|', $data['contact_name'] ?? '');
                    $contactAges = explode('|', $data['contact_age'] ?? '');
                    $contactJobs = explode('|', $data['contact_job'] ?? '');
                    
                    // 为联系人字段设置值，使用安全设置函数
                    for ($i = 1; $i <= 5; $i++) {
                        $safeSetValue($template, '联系人关系' . $i, isset($contactRelations[$i-1]) && !empty($contactRelations[$i-1]) ? $contactRelations[$i-1] : '');
                        $safeSetValue($template, '联系人姓名' . $i, isset($contactNames[$i-1]) && !empty($contactNames[$i-1]) ? $contactNames[$i-1] : '');
                        $safeSetValue($template, '联系人年龄' . $i, isset($contactAges[$i-1]) && !empty($contactAges[$i-1]) ? $contactAges[$i-1] : '');
                        $safeSetValue($template, '联系人工作' . $i, isset($contactJobs[$i-1]) && !empty($contactJobs[$i-1]) ? $contactJobs[$i-1] : '');
                    }
                    
                    $eduStarts = explode('|', $data['education_start'] ?? '');
                    $eduEnds = explode('|', $data['education_end'] ?? '');
                    $eduSchools = explode('|', $data['education_school'] ?? '');
                    $eduMajors = explode('|', $data['education_major'] ?? '');
                    $eduDegrees = explode('|', $data['graduation_education'] ?? '');
                    
                    // 为教育经历字段设置值，使用安全设置函数
                    for ($i = 1; $i <= 5; $i++) {
                        $startDate = isset($eduStarts[$i-1]) && !empty($eduStarts[$i-1]) ? $this->formatDateToChineseYM($eduStarts[$i-1]) : '';
                        $endDate = isset($eduEnds[$i-1]) && !empty($eduEnds[$i-1]) ? $this->formatDateToChineseYM($eduEnds[$i-1]) : '';
                        
                        $safeSetValue($template, '教育开始' . $i, $startDate);
                        $safeSetValue($template, '教育结束' . $i, $endDate);
                        $safeSetValue($template, '学校' . $i, isset($eduSchools[$i-1]) && !empty($eduSchools[$i-1]) ? $eduSchools[$i-1] : '');
                        $safeSetValue($template, '专业' . $i, isset($eduMajors[$i-1]) && !empty($eduMajors[$i-1]) ? $eduMajors[$i-1] : '');
                        $safeSetValue($template, '学历' . $i, isset($eduDegrees[$i-1]) && !empty($eduDegrees[$i-1]) ? $eduDegrees[$i-1] : '');
                    }
                    
                    $jobStarts = explode('|', $data['job_start'] ?? '');
                    $jobEnds = explode('|', $data['job_end'] ?? '');
                    $jobCompanies = explode('|', $data['job_company'] ?? '');
                    $jobPositions = explode('|', $data['job_position'] ?? '');
                    $jobDescriptions = explode('|', $data['job_description'] ?? '');
                    
                    // 为工作经历字段设置值，使用安全设置函数
                    for ($i = 1; $i <= 5; $i++) {
                        $startDate = isset($jobStarts[$i-1]) && !empty($jobStarts[$i-1]) ? $this->formatDateToChineseYM($jobStarts[$i-1]) : '';
                        $endDate = isset($jobEnds[$i-1]) && !empty($jobEnds[$i-1]) ? $this->formatDateToChineseYM($jobEnds[$i-1]) : '';
                        
                        $safeSetValue($template, '工作开始' . $i, $startDate);
                        $safeSetValue($template, '工作结束' . $i, $endDate);
                        $safeSetValue($template, '工作单位' . $i, isset($jobCompanies[$i-1]) && !empty($jobCompanies[$i-1]) ? $jobCompanies[$i-1] : '');
                        $safeSetValue($template, '工作岗位' . $i, isset($jobPositions[$i-1]) && !empty($jobPositions[$i-1]) ? $jobPositions[$i-1] : '');
                        $safeSetValue($template, '工作内容' . $i, isset($jobDescriptions[$i-1]) && !empty($jobDescriptions[$i-1]) ? $jobDescriptions[$i-1] : '');
                    }
                    
                    // 定义不同类型图片的自定义高度（像素）
                    $imageHeightSettings = [
                        '头像' => 140,           // 头像高度
                        '全身照' => 300,         // 全身照高度
                        '身份证正面' => 180,     // 身份证正面高度
                        '身份证反面' => 180,     // 身份证反面高度
                        '港澳通行证正面' => 180, // 港澳通行证正面高度
                        '港澳通行证反面' => 180, // 港澳通行证反面高度
                        '补充照片' => 180        // 补充照片默认高度
                    ];
                    
                    // 计算等比例缩放尺寸的辅助函数
                    $calculateScaledSize = function($imagePath, $targetHeight) {
                        try {
                            // 获取图片原始尺寸
                            list($originalWidth, $originalHeight) = getimagesize($imagePath);
                            
                            // 按原始比例计算宽度
                            $scaledWidth = ($originalWidth / $originalHeight) * $targetHeight;
                            
                            return [
                                'width' => (int)$scaledWidth,
                                'height' => (int)$targetHeight
                            ];
                        } catch (\Exception $e) {
                            // 如果获取图片尺寸失败，返回默认尺寸
                            return [
                                'width' => 200,
                                'height' => (int)$targetHeight
                            ];
                        }
                    };
                    
                    // 在图像占位符位置插入图像
                    if (!empty($data['avatar'])) {
                        try {
                            // 头像字段
                            $avatarPath = ROOT_PATH . 'public' . DS . $data['avatar'];
                            if (file_exists($avatarPath)) {
                                // 获取头像的自定义高度
                                $targetHeight = $imageHeightSettings['头像'];
                                
                                // 计算等比例缩放的尺寸
                                $size = $calculateScaledSize($avatarPath, $targetHeight);
                                
                                // 尝试添加图像，根据Word模板中的标签
                                $template->setImageValue('头像', [
                                    'path' => $avatarPath, 
                                    'width' => $size['width'], 
                                    'height' => $size['height'], 
                                    'ratio' => false
                                ]);
                            } else {
                                // 如果图片不存在，清空占位符
                                $safeSetValue($template, '头像', '');
                            }
                        } catch (\Exception $e) {
                            // 图像处理失败时，清空占位符
                            $safeSetValue($template, '头像', '');
                        }
                    } else {
                        // 如果没有头像数据，清空占位符
                        $safeSetValue($template, '头像', '');
                    }
                    
                    // 其他图像字段的处理
                    $imageFields = [
                        '全身照' => $data['full_body_photo'] ?? '',
                        '身份证正面' => $data['id_card_front'] ?? '',
                        '身份证反面' => $data['id_card_back'] ?? '',
                        '港澳通行证正面' => $data['hk_macau_passport_front'] ?? '',
                        '港澳通行证反面' => $data['hk_macau_passport_back'] ?? ''
                    ];
                    
                    foreach ($imageFields as $placeholder => $path) {
                        if (!empty($path)) {
                            try {
                                $imagePath = ROOT_PATH . 'public' . DS . $path;
                                if (file_exists($imagePath)) {
                                    // 获取该类型图片的自定义高度
                                    $targetHeight = $imageHeightSettings[$placeholder];
                                    
                                    // 计算等比例缩放的尺寸
                                    $size = $calculateScaledSize($imagePath, $targetHeight);
                                    
                                    // 尝试添加图像，根据Word模板中的标签
                                    $template->setImageValue($placeholder, [
                                        'path' => $imagePath, 
                                        'width' => $size['width'], 
                                        'height' => $size['height'], 
                                        'ratio' => false
                                    ]);
                                } else {
                                    // 如果图片不存在，清空占位符
                                    $safeSetValue($template, $placeholder, '');
                                }
                            } catch (\Exception $e) {
                                // 图像处理失败时，清空占位符
                                $safeSetValue($template, $placeholder, '');
                            }
                        } else {
                            // 如果没有图片数据，清空占位符
                            $safeSetValue($template, $placeholder, '');
                        }
                    }
                    
                    // 处理多张补充照片
                    if (!empty($data['additional_photos'])) {
                        $photoArray = explode(',', $data['additional_photos']);
                        foreach ($photoArray as $index => $photoPath) {
                            $photoPlaceholder = '补充照片' . ($index + 1); // 照片占位符 补充照片1, 补充照片2, ...
                            try {
                                $imagePath = ROOT_PATH . 'public' . DS . $photoPath;
                                if (file_exists($imagePath)) {
                                    // 获取补充照片的自定义高度
                                    $targetHeight = $imageHeightSettings['补充照片'];
                                    
                                    // 计算等比例缩放的尺寸
                                    $size = $calculateScaledSize($imagePath, $targetHeight);
                                    
                                    // 尝试添加图像
                                    $template->setImageValue($photoPlaceholder, [
                                        'path' => $imagePath, 
                                        'width' => $size['width'], 
                                        'height' => $size['height'], 
                                        'ratio' => false
                                    ]);
                                } else {
                                    // 如果图片不存在，清空占位符
                                    $safeSetValue($template, $photoPlaceholder, '');
                                }
                            } catch (\Exception $e) {
                                // 图像处理失败时，清空占位符
                                $safeSetValue($template, $photoPlaceholder, '');
                            }
                        }
                    }
                    
                    // 默认清空未使用的补充照片占位符（假设最多有20个补充照片占位符）
                    $maxPhotoPlaceholders = 20;
                    $usedPhotoCount = !empty($data['additional_photos']) ? count(explode(',', $data['additional_photos'])) : 0;
                    
                    // 清空未使用的占位符
                    for ($i = $usedPhotoCount + 1; $i <= $maxPhotoPlaceholders; $i++) {
                        try {
                            $safeSetValue($template, '补充照片' . $i, '');
                        } catch (\Exception $e) {
                            // 忽略不存在的占位符错误
                        }
                    }
                    
                    // 根据选择的字段生成文件名
                    $filenameComponents = [];
                    $defaultFields = ['name', 'age', 'applied_position']; // 默认字段
                    
                    // 如果没有提供字段，使用默认字段
                    $selectedFields = !empty($filenameFields) ? explode(',', $filenameFields) : $defaultFields;
                    
                    foreach ($selectedFields as $field) {
                        if (isset($data[$field]) && !empty($data[$field])) {
                            // 获取字段值，进行必要的处理
                            $fieldValue = $data[$field];
                            
                            // 特殊字段处理
                            if ($field === 'age') {
                                // 对年龄字段特殊处理，添加"岁"
                                $fieldValue = $fieldValue . '岁';
                            } elseif ($field === 'hukou_location') {
                                // 处理户口所在地的"/"字符，直接移除
                                $fieldValue = str_replace(['/', '\\'], '', $fieldValue);
                            } elseif ($field === 'height') {
                                // 处理身高，添加单位
                                $fieldValue = $fieldValue . 'cm';
                            } elseif ($field === 'weight') {
                                // 处理体重，添加单位
                                $fieldValue = $fieldValue . 'kg';
                            }
                            
                            // 移除文件名中的非法字符
                            $fieldValue = preg_replace('/[\\\\\/\:\*\?\"\<\>\|]/', '', $fieldValue);
                            
                            $filenameComponents[] = $fieldValue;
                        }
                    }
                    
                    // 如果没有可用组件，使用姓名和时间戳
                    if (empty($filenameComponents)) {
                        $filenameComponents[] = $data['name'] ?: '未命名';
                        $filenameComponents[] = date('YmdHis') . '_' . ($index + 1);
                    }
                    
                    // 生成文件名 - 确保文件名中不包含非法字符
                    $filename = implode('-', $filenameComponents) . '.docx';
                    // 使用一致的目录分隔符
                    $outputFile = $tempDir . DS . $filename;
                    
                    // 保存生成的Word文档
                    $template->saveAs($outputFile);
                    
                    $fileList[] = [
                        'path' => $outputFile,
                        'name' => $filename
                    ];
                } catch (\Exception $e) {
                    $this->error('导出失败: ' . $e->getMessage() . "\n" . $e->getTraceAsString());
                }
            }
            
            // 如果只有一个文件，则直接下载
            if (count($fileList) === 1) {
                $file = $fileList[0];
                if(!file_exists($file['path'])) {
                    $this->error('导出失败: 生成的文件不存在: ' . $file['path']);
                }
                
                header('Content-Description: File Transfer');
                header('Content-Type: application/octet-stream');
                header('Content-Disposition: attachment; filename=' . urlencode($file['name']));
                header('Content-Transfer-Encoding: binary');
                header('Expires: 0');
                header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
                header('Pragma: public');
                header('Content-Length: ' . filesize($file['path']));
                
                if(!readfile($file['path'])) {
                    $this->error('导出失败: 无法读取生成的文件');
                }
                
                // 删除临时文件
                @unlink($file['path']);
                if (is_dir($tempDir)) {
                    @rmdir($tempDir);
                }
                exit;
            }
            
            // 如果有多个文件，则打包下载
            $zipFile = $tempDir . '.zip';
            $zip = new \ZipArchive();
            if ($zip->open($zipFile, \ZipArchive::CREATE | \ZipArchive::OVERWRITE) === TRUE) {
                $addedFiles = 0;
                foreach ($fileList as $file) {
                    if(file_exists($file['path'])) {
                        if($zip->addFile($file['path'], $file['name'])) {
                            $addedFiles++;
                        }
                    }
                }
                $zip->close();
                
                if($addedFiles == 0) {
                    $this->error('导出失败: 没有成功添加任何文件到ZIP包');
                }
                
                // 下载ZIP文件
                header('Content-Description: File Transfer');
                header('Content-Type: application/zip');
                header('Content-Disposition: attachment; filename=简历汇总_' . date('YmdHis') . '.zip');
                header('Content-Transfer-Encoding: binary');
                header('Expires: 0');
                header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
                header('Pragma: public');
                header('Content-Length: ' . filesize($zipFile));
                
                if(!readfile($zipFile)) {
                    $this->error('导出失败: 无法读取生成的ZIP文件');
                }
                
                // 删除临时文件
                foreach ($fileList as $file) {
                    @unlink($file['path']);
                }
                @unlink($zipFile);
                if (is_dir($tempDir)) {
                    @rmdir($tempDir);
                }
                exit;
            } else {
                $this->error('创建ZIP包失败');
            }
        }
    }

    /**
     * 上传模板文件（Excel或Word）
     */
    public function uploadTemplate()
    {
        try {
            // 获取上传类型（excel或word）
            $type = $this->request->get('type', 'excel');
            
            // 上传文件
            $file = $this->request->file('template_file');
            if (!$file) {
                return json(['code' => 0, 'msg' => '未上传文件或文件大小超出服务器限制']);
            }
            
            // 检查文件类型
            $ext = strtolower(pathinfo($file->getInfo('name'), PATHINFO_EXTENSION));
            if ($type == 'excel' && $ext != 'xlsx') {
                return json(['code' => 0, 'msg' => '请上传.xlsx格式的Excel文件']);
            } elseif ($type == 'word' && $ext != 'docx') {
                return json(['code' => 0, 'msg' => '请上传.docx格式的Word文件']);
            }
            
            // 确保模板目录存在
            $templatesDir = ROOT_PATH . 'public' . DS . 'templates' . DS . 'resume';
            if (!is_dir($templatesDir)) {
                if (!mkdir($templatesDir, 0777, true)) {
                    return json(['code' => 0, 'msg' => '创建模板目录失败，请检查目录权限']);
                }
            }
            
            // 获取原始文件名
            $originalName = $file->getInfo('name');
            
            // 直接使用自定义文件名
            $destFile = $templatesDir . DS . $originalName;
            
            // 如果文件已存在，先删除
            if (file_exists($destFile)) {
                @unlink($destFile);
            }
            
            // 使用PHP原生函数移动上传文件
            if (move_uploaded_file($file->getInfo('tmp_name'), $destFile)) {
                return json([
                    'code' => 1, 
                    'msg' => '模板上传成功', 
                    'data' => [
                        'filename' => $originalName,
                        'path' => 'public' . DS . 'templates' . DS . 'resume' . DS . $originalName
                    ]
                ]);
            } else {
                return json(['code' => 0, 'msg' => '文件保存失败，请检查目录权限']);
            }
        } catch (\Exception $e) {
            // 记录详细错误信息到日志
            \think\Log::record('模板上传异常: ' . $e->getMessage() . ' 位置: ' . $e->getFile() . ':' . $e->getLine(), 'error');
            return json(['code' => 0, 'msg' => '文件上传失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 删除模板文件
     */
    public function deleteTemplate()
    {
        if ($this->request->isPost()) {
            $template_path = $this->request->post('template_path');
            $type = $this->request->post('type');

            if (empty($template_path) || !in_array($type, ['excel', 'word'])) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }

            // 检查是否是合法的模板路径（必须在templates/resume目录下）
            $basePath = 'public' . DS . 'templates' . DS . 'resume';
            // 使用正规化路径并兼容不同系统的路径分隔符
            $normalizedPath = str_replace('\\', '/', $template_path);
            $normalizedBasePath = str_replace('\\', '/', $basePath);

            if (strpos($normalizedPath, $normalizedBasePath) === false) {
                // 记录调试信息
                \think\Log::record('删除模板文件路径验证失败: 模板路径=' . $normalizedPath . ', 基础路径=' . $normalizedBasePath, 'debug');
                return json(['code' => 0, 'msg' => '非法的模板路径，路径必须在templates/resume目录下']);
            }

            // 获取完整文件路径
            $fullPath = ROOT_PATH . $template_path;

            // 检查文件是否存在
            if (!file_exists($fullPath)) {
                return json(['code' => 0, 'msg' => '模板文件不存在，路径：' . $fullPath]);
            }

            // 检查文件类型
            $ext = strtolower(pathinfo($fullPath, PATHINFO_EXTENSION));
            if ($type == 'excel' && $ext != 'xlsx') {
                return json(['code' => 0, 'msg' => '非Excel模板文件']);
            } elseif ($type == 'word' && $ext != 'docx') {
                return json(['code' => 0, 'msg' => '非Word模板文件']);
            }

            // 删除文件
            if (unlink($fullPath)) {
                return json(['code' => 1, 'msg' => '模板删除成功']);
            } else {
                return json(['code' => 0, 'msg' => '删除失败，请检查文件权限']);
            }
        }

        return json(['code' => 0, 'msg' => '非法请求']);
    }
    
    /**
     * 批量复制简历
     */
    public function batchCopy()
    {
        if ($this->request->isPost()) {
            $resumeData = $this->request->post('resume_data');
            
            if (empty($resumeData)) {
                return json(['code' => 0, 'msg' => '参数错误']);
            }
            
            // 解析JSON数据
            $resumeList = json_decode($resumeData, true);
            if (empty($resumeList) || !is_array($resumeList)) {
                return json(['code' => 0, 'msg' => '无效的数据格式']);
            }
            
            // 使用事务来确保数据一致性
            $this->model->startTrans();
            
            try {
                $successCount = 0;
                
                foreach ($resumeList as $item) {
                    $id = $item['id'];
                    $position = $item['position'];
                    
                    // 获取原始简历数据
                    $resume = $this->model->where('id', $id)->find();
                    if (!$resume) {
                        continue;
                    }
                    
                    // 创建新记录数据
                    $newData = $resume->getData();
                    
                    // 移除id字段，让数据库自增
                    unset($newData['id']);
                    
                    // 修改申请岗位字段
                    $newData['applied_position'] = $position;
                    
                    // 插入新记录
                    $result = $this->model->insertGetId($newData);
                    
                    if ($result) {
                        $successCount++;
                    }
                }
                
                // 提交事务
                $this->model->commit();
                
                if ($successCount > 0) {
                    return json(['code' => 1, 'msg' => "成功复制了 {$successCount} 条简历记录"]);
                } else {
                    return json(['code' => 0, 'msg' => '复制失败，未能创建任何记录']);
                }
            } catch (\Exception $e) {
                // 回滚事务
                $this->model->rollback();
                return json(['code' => 0, 'msg' => '复制失败：' . $e->getMessage()]);
            }
        }
        
        return json(['code' => 0, 'msg' => '非法请求']);
    }
    
    /**
     * 下载导入模板
     */
    public function downloadTemplate()
    {
        // 模板文件路径
        $templatePath = ROOT_PATH . 'public' . DS . 'templates' . DS . 'template' . DS . '导入模版.xlsx';
        
        // 检查文件是否存在
        if (!file_exists($templatePath)) {
            $this->error('模板文件不存在');
        }
        
        // 获取文件信息
        $filename = '简历导入模板.xlsx';
        $filesize = filesize($templatePath);
        
        // 设置响应头，触发下载
        header("Content-Description: File Transfer");
        header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        header("Content-Disposition: attachment; filename=\"{$filename}\"");
        header("Content-Transfer-Encoding: binary");
        header("Expires: 0");
        header("Cache-Control: must-revalidate");
        header("Pragma: public");
        header("Content-Length: {$filesize}");
        
        // 读取文件并输出
        readfile($templatePath);
        exit;
    }

    /**
     * 简历详情
     */
    public function detail($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 生成工作内容
     */
    public function generateJobDescription()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }

        $data = $this->request->post();
        if (empty($data['company']) || empty($data['position']) || empty($data['word_count'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        try {
            // 构建提示词
            $prompt = "请根据以下工作信息生成一段工作内容描述：\n";
            $prompt .= "工作单位：{$data['company']}\n";
            $prompt .= "工作岗位：{$data['position']}\n";
            $prompt .= "要求：\n";
            $prompt .= "1. 只描述具体的工作内容和职责，不要包含工作单位和岗位名称\n";
            $prompt .= "2. 描述要专业、具体，突出工作职责和成就\n";
            $prompt .= "3. 字数控制在{$data['word_count']}字左右\n";
            $prompt .= "4. 语言要简洁、专业\n";
            $prompt .= "5. 直接输出工作内容，不要加任何前缀或说明\n";

            // 获取API配置
            $apiKey = config('site.openai_api_key');
            $apiUrl = config('site.openai_api_url');
            $model = config('site.openai_model');
            
            if (empty($apiKey) || empty($apiUrl) || empty($model)) {
                return json(['code' => 0, 'msg' => 'API配置不完整']);
            }

            $ch = curl_init($apiUrl . '/chat/completions');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ]);

            $postData = [
                'model' => $model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => '你是一个专业的简历顾问，擅长撰写工作内容描述。请直接输出工作内容，不要包含工作单位和岗位名称，也不要加任何前缀或说明。'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.7,
                'max_tokens' => 500,
                'stream' => false
            ];

            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_errno($ch)) {
                throw new \Exception('API请求错误: ' . curl_error($ch));
            }
            
            curl_close($ch);

            if ($httpCode !== 200) {
                throw new \Exception('API请求失败，状态码：' . $httpCode . '，响应：' . $response);
            }

            $result = json_decode($response, true);
            if (!isset($result['choices'][0]['message']['content'])) {
                throw new \Exception('API返回数据格式错误：' . $response);
            }

            $content = trim($result['choices'][0]['message']['content']);
            return json(['code' => 1, 'msg' => '生成成功', 'data' => $content]);

        } catch (\Exception $e) {
            \think\Log::write('生成工作内容失败：' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 生成自我评价
     */
    public function generateSelfEvaluation()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }

        $data = $this->request->post();
        if (empty($data['content']) || empty($data['word_count'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        try {
            // 构建提示词
            $prompt = "请根据以下内容生成一段自我评价：\n";
            $prompt .= "原始内容：{$data['content']}\n";
            $prompt .= "要求：\n";
            $prompt .= "1. 保持原意的同时，使表达更加专业、得体\n";
            $prompt .= "2. 突出个人优势和特点\n";
            $prompt .= "3. 字数控制在{$data['word_count']}字左右\n";
            $prompt .= "4. 语言要简洁、专业\n";
            $prompt .= "5. 直接输出润色后的内容，不要加任何前缀或说明\n";

            // 获取API配置
            $apiKey = config('site.openai_api_key');
            $apiUrl = config('site.openai_api_url');
            $model = config('site.openai_model');
            
            if (empty($apiKey) || empty($apiUrl) || empty($model)) {
                return json(['code' => 0, 'msg' => 'API配置不完整']);
            }

            $ch = curl_init($apiUrl . '/chat/completions');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ]);

            $postData = [
                'model' => $model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => '你是一个专业的简历顾问，擅长润色自我评价。请直接输出润色后的内容，不要加任何前缀或说明。'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.7,
                'max_tokens' => 500,
                'stream' => false
            ];

            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_errno($ch)) {
                throw new \Exception('API请求错误: ' . curl_error($ch));
            }
            
            curl_close($ch);

            if ($httpCode !== 200) {
                throw new \Exception('API请求失败，状态码：' . $httpCode . '，响应：' . $response);
            }

            $result = json_decode($response, true);
            if (!isset($result['choices'][0]['message']['content'])) {
                throw new \Exception('API返回数据格式错误：' . $response);
            }

            $content = trim($result['choices'][0]['message']['content']);
            return json(['code' => 1, 'msg' => '生成成功', 'data' => $content]);

        } catch (\Exception $e) {
            \think\Log::write('生成自我评价失败：' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 生成兴趣爱好
     */
    public function generateHobbies()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }

        $data = $this->request->post();
        if (empty($data['content']) || empty($data['word_count'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        try {
            // 构建提示词
            $prompt = "请根据以下内容生成一段兴趣爱好描述：\n";
            $prompt .= "原始内容：{$data['content']}\n";
            $prompt .= "要求：\n";
            $prompt .= "1. 保持原意的同时，使表达更加生动、有趣\n";
            $prompt .= "2. 突出个人特点和积极向上的态度\n";
            $prompt .= "3. 字数控制在{$data['word_count']}字左右\n";
            $prompt .= "4. 语言要简洁、自然\n";
            $prompt .= "5. 直接输出润色后的内容，不要加任何前缀或说明\n";

            // 获取API配置
            $apiKey = config('site.openai_api_key');
            $apiUrl = config('site.openai_api_url');
            $model = config('site.openai_model');
            
            if (empty($apiKey) || empty($apiUrl) || empty($model)) {
                return json(['code' => 0, 'msg' => 'API配置不完整']);
            }

            $ch = curl_init($apiUrl . '/chat/completions');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ]);

            $postData = [
                'model' => $model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => '你是一个专业的简历顾问，擅长润色兴趣爱好描述。请直接输出润色后的内容，不要加任何前缀或说明。'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.7,
                'max_tokens' => 500,
                'stream' => false
            ];

            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_errno($ch)) {
                throw new \Exception('API请求错误: ' . curl_error($ch));
            }
            
            curl_close($ch);

            if ($httpCode !== 200) {
                throw new \Exception('API请求失败，状态码：' . $httpCode . '，响应：' . $response);
            }

            $result = json_decode($response, true);
            if (!isset($result['choices'][0]['message']['content'])) {
                throw new \Exception('API返回数据格式错误：' . $response);
            }

            $content = trim($result['choices'][0]['message']['content']);
            return json(['code' => 1, 'msg' => '生成成功', 'data' => $content]);

        } catch (\Exception $e) {
            \think\Log::write('生成兴趣爱好失败：' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

    public function del($ids = null)
    {
        if (false === $this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ?: $this->request->post('ids');
        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }
        if ($ids) {
            $this->model->where($pk, 'in', $ids);
        }
        $count = 0;
        Db::startTrans();
        try {
            // 获取当前用户ID
            $currentUserId = $this->auth->id;
            
            // 获取要删除的简历列表
            $resumeList = $this->model->where($pk, 'in', $ids)->select();
            
            foreach ($resumeList as $resume) {
                // 检查是否有权限删除（只有管理员或简历所有者可以删除）
                if ($this->auth->isSuperAdmin() || $resume['user_id'] == $currentUserId) {
                    $count += $resume->delete();
                } else {
                    throw new \Exception('没有权限删除此简历');
                }
            }
            Db::commit();
        } catch (PDOException|Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        if ($count) {
            $this->success();
        }
        $this->error(__('No rows were deleted'));
    }

    /**
     * 格式化身份证号，确保在Excel中正确显示为文本
     * @param string $idCard 身份证号
     * @return string 格式化后的身份证号
     */
    protected function formatIdCard($idCard)
    {
        if (empty($idCard)) {
            return '';
        }
        
        // 在身份证号前加上单引号，使Excel将其识别为文本而非数字
        return "'" . $idCard;
    }

    /**
     * 基于模板导入Excel
     */
    public function importWithTemplate()
    {
        if ($this->request->isPost()) {
            // 检查是否有文件上传
            if (!$this->request->file('excel_file')) {
                $this->error('请选择要导入的 Excel 文件');
            }
            
            // 获取模板ID
            $templateId = $this->request->post('template_id');
            if (empty($templateId)) {
                $this->error('请选择导入模板');
            }
            
            // 获取模板配置
            $template = \app\admin\model\ResumeImportTemplate::get($templateId);
            if (!$template) {
                $this->error('导入模板不存在');
            }
            
            // 获取模板配置
            $config = $template->config;
            if (empty($config)) {
                $this->error('模板配置不完整');
            }

            // 记录模板配置信息用于调试
            \think\Log::write('使用的模板配置: ' . json_encode($config, JSON_UNESCAPED_UNICODE), 'debug');
            \think\Log::write('开始导入数据，模板ID: ' . $templateId, 'info');

            // 获取上传的文件
            $file = $this->request->file('excel_file');

            try {
                // 开启事务
                $this->model->startTrans();
                
                // 读取 Excel 文件
                $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
                $spreadsheet = $reader->load($file->getRealPath());
                $sheet = $spreadsheet->getActiveSheet();
                
                // 初始化数据和计数器
                $successCount = 0;
                $errorCount = 0;
                $errorMessages = [];
                
                // 解析Excel数据
                $data = $this->parseExcelWithTemplate($sheet, $config, $errorMessages);
                
                // 记录解析结果
                \think\Log::write('解析Excel数据结果: 共解析到 ' . count($data) . ' 条数据', 'debug');
                if (empty($data)) {
                    $errorMessages[] = "没有解析到任何数据，请检查Excel文件格式和模板配置";
                }

                // 保存数据
                foreach ($data as $index => $resumeData) {
                    try {
                        // 记录要保存的数据
                        \think\Log::write('准备保存第 ' . ($index + 1) . ' 条数据: ' . json_encode($resumeData, JSON_UNESCAPED_UNICODE), 'debug');

                        $result = $this->model->create($resumeData);
                        if ($result) {
                            $successCount++;
                            \think\Log::write('第 ' . ($index + 1) . ' 条数据保存成功，ID: ' . $result->id, 'debug');
                        } else {
                            $errorCount++;
                            $errorMessages[] = "第 " . ($index + 1) . " 条数据保存失败";
                            \think\Log::write('第 ' . ($index + 1) . ' 条数据保存失败', 'error');
                        }
                    } catch (\Exception $e) {
                        $errorCount++;
                        $errorMessages[] = "第 " . ($index + 1) . " 条数据处理失败：" . $e->getMessage();
                        \think\Log::write('第 ' . ($index + 1) . ' 条数据处理异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
                    }
                }

                // 提交事务
                $this->model->commit();

                // 返回导入结果
                if ($errorCount > 0) {
                    // 记录完整的错误信息到日志
                    $fullErrorMessage = "导入失败详情：\n" . implode("\n", $errorMessages);
                    \think\Log::write($fullErrorMessage, 'error');

                    // 限制显示的错误信息长度，避免页面显示问题
                    $displayErrors = array_slice($errorMessages, 0, 10); // 只显示前10个错误
                    $message = "导入完成，成功导入 {$successCount} 条数据，失败 {$errorCount} 条。\n\n失败原因：\n" . implode("\n", $displayErrors);
                    if (count($errorMessages) > 10) {
                        $message .= "\n\n... 还有 " . (count($errorMessages) - 10) . " 个错误，详情请查看日志文件";
                    }
                    $this->error($message);
                } else {
                    $this->success("🎉 导入成功！成功导入 {$successCount} 条简历数据", url('admin/resume/index'));
                }
            } catch (\think\exception\HttpResponseException $e) {
                // HttpResponseException 是框架正常的响应异常，直接重新抛出
                throw $e;
            } catch (\Exception $e) {
                // 回滚事务
                $this->model->rollback();

                // 记录异常详情到日志
                $fullException = '导入异常详情: ' . $e->getMessage() . "\n文件: " . $e->getFile() . "\n行号: " . $e->getLine() . "\n堆栈跟踪:\n" . $e->getTraceAsString();
                \think\Log::write($fullException, 'error');

                // 构建用户友好的错误信息
                $errorMessage = "导入失败！\n\n异常信息：" . $e->getMessage();

                // 添加文件和行号信息
                $errorMessage .= "\n错误位置：" . basename($e->getFile()) . " 第 " . $e->getLine() . " 行";

                // 添加其他错误信息
                if (!empty($errorMessages)) {
                    $errorMessage .= "\n\n数据处理错误：\n" . implode("\n", array_slice($errorMessages, 0, 3));
                    if (count($errorMessages) > 3) {
                        $errorMessage .= "\n... 还有 " . (count($errorMessages) - 3) . " 个错误";
                    }
                }

                $errorMessage .= "\n\n详细错误信息已记录到日志文件中。";

                $this->error($errorMessage);
            }
        }
        
        // 获取模板列表
        $templates = \app\admin\model\ResumeImportTemplate::select();
        $this->view->assign('templates', $templates);
        
        return $this->view->fetch();
    }

    /**
     * 提取Excel中的图片
     * @param \PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet Excel工作表
     * @param array $config 模板配置
     * @return array 图片数据数组
     */
    protected function extractImagesFromExcel($sheet, $config)
    {
        $images = [];
        $imageFields = ['avatar', 'full_body_photo', 'id_card_front', 'id_card_back',
                       'hk_macau_passport_front', 'hk_macau_passport_back', 'additional_photos'];

        try {
            // 获取工作表中的所有图片
            $drawings = $sheet->getDrawingCollection();

            if (empty($drawings)) {
                \think\Log::write('Excel中没有找到任何图片', 'debug');
                return $images;
            }

            \think\Log::write('Excel中找到 ' . count($drawings) . ' 个图片对象', 'debug');

            // 获取合并单元格信息
            $mergedCells = $sheet->getMergeCells();
            \think\Log::write('合并单元格信息: ' . json_encode($mergedCells), 'debug');

            foreach ($drawings as $index => $drawing) {
                \think\Log::write("处理第 " . ($index + 1) . " 个图片对象", 'debug');

                if ($drawing instanceof \PhpOffice\PhpSpreadsheet\Worksheet\MemoryDrawing) {
                    // 内存中的图片
                    $imageData = $this->processMemoryDrawing($drawing, $config, $imageFields, $mergedCells);
                    if ($imageData) {
                        $images[] = $imageData;
                    }
                } elseif ($drawing instanceof \PhpOffice\PhpSpreadsheet\Worksheet\Drawing) {
                    // 文件图片
                    $imageData = $this->processFileDrawing($drawing, $config, $imageFields, $mergedCells);
                    if ($imageData) {
                        $images[] = $imageData;
                    }
                } else {
                    \think\Log::write("未知的图片对象类型: " . get_class($drawing), 'warning');
                }
            }

            \think\Log::write('成功提取 ' . count($images) . ' 个图片', 'debug');

        } catch (\Exception $e) {
            \think\Log::write('提取Excel图片失败: ' . $e->getMessage(), 'error');
            \think\Log::write('错误堆栈: ' . $e->getTraceAsString(), 'error');
        }

        return $images;
    }

    /**
     * 处理内存图片
     */
    protected function processMemoryDrawing($drawing, $config, $imageFields, $mergedCells = [])
    {
        try {
            // 获取图片位置
            $coordinates = $drawing->getCoordinates();
            \think\Log::write("处理内存图片，位置: {$coordinates}", 'debug');

            // 获取图片的详细位置信息
            $offsetX = $drawing->getOffsetX();
            $offsetY = $drawing->getOffsetY();
            $width = $drawing->getWidth();
            $height = $drawing->getHeight();

            \think\Log::write("图片详细信息: 偏移X={$offsetX}, 偏移Y={$offsetY}, 宽度={$width}, 高度={$height}", 'debug');

            // 获取图片数据
            $imageResource = $drawing->getImageResource();
            $mimeType = $drawing->getMimeType();

            if (!$imageResource) {
                \think\Log::write('无法获取图片资源', 'error');
                return null;
            }

            // 确定文件扩展名
            $extension = 'png';
            if (strpos($mimeType, 'jpeg') !== false || strpos($mimeType, 'jpg') !== false) {
                $extension = 'jpg';
            } elseif (strpos($mimeType, 'gif') !== false) {
                $extension = 'gif';
            }

            // 生成唯一文件名
            $fileName = 'excel_image_' . uniqid() . '.' . $extension;
            $uploadPath = ROOT_PATH . 'public' . DS . 'uploads' . DS . 'images' . DS . date('Ymd') . DS;

            // 确保目录存在
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $fullPath = $uploadPath . $fileName;

            // 保存图片
            $saveResult = false;
            if ($extension === 'jpg') {
                $saveResult = imagejpeg($imageResource, $fullPath, 90);
            } elseif ($extension === 'gif') {
                $saveResult = imagegif($imageResource, $fullPath);
            } else {
                $saveResult = imagepng($imageResource, $fullPath);
            }

            if (!$saveResult) {
                \think\Log::write('保存图片失败', 'error');
                return null;
            }

            // 释放资源
            imagedestroy($imageResource);

            // 返回相对路径
            $relativePath = '/uploads/images/' . date('Ymd') . '/' . $fileName;

            // 匹配字段，考虑合并单元格
            $field = $this->matchImageFieldWithMerged($coordinates, $config, $imageFields, $mergedCells);

            \think\Log::write("成功保存图片: 位置 {$coordinates}, 路径 {$relativePath}, 匹配字段 {$field}", 'debug');

            return [
                'coordinates' => $coordinates,
                'path' => $relativePath,
                'field' => $field,
                'size' => filesize($fullPath),
                'type' => $mimeType,
                'offset_x' => $offsetX,
                'offset_y' => $offsetY,
                'width' => $width,
                'height' => $height
            ];

        } catch (\Exception $e) {
            \think\Log::write('处理内存图片失败: ' . $e->getMessage(), 'error');
            \think\Log::write('错误堆栈: ' . $e->getTraceAsString(), 'error');
            return null;
        }
    }

    /**
     * 处理文件图片
     */
    protected function processFileDrawing($drawing, $config, $imageFields, $mergedCells = [])
    {
        try {
            // 获取图片位置
            $coordinates = $drawing->getCoordinates();
            $imagePath = $drawing->getPath();

            \think\Log::write("处理文件图片，位置: {$coordinates}, 路径: {$imagePath}", 'debug');

            // 检查是否是zip路径（Excel内嵌图片）
            if (strpos($imagePath, 'zip://') === 0) {
                \think\Log::write("检测到Excel内嵌图片，尝试提取: {$imagePath}", 'debug');
                return $this->extractEmbeddedImage($drawing, $config, $imageFields, $mergedCells);
            }

            if (!file_exists($imagePath)) {
                \think\Log::write("图片文件不存在: {$imagePath}", 'warning');
                return null;
            }

            // 获取图片的详细位置信息
            $offsetX = $drawing->getOffsetX();
            $offsetY = $drawing->getOffsetY();
            $width = $drawing->getWidth();
            $height = $drawing->getHeight();

            \think\Log::write("图片详细信息: 偏移X={$offsetX}, 偏移Y={$offsetY}, 宽度={$width}, 高度={$height}", 'debug');

            // 复制到上传目录
            $fileName = 'excel_image_' . uniqid() . '.' . pathinfo($imagePath, PATHINFO_EXTENSION);
            $uploadPath = ROOT_PATH . 'public' . DS . 'uploads' . DS . 'images' . DS . date('Ymd') . DS;

            // 确保目录存在
            if (!is_dir($uploadPath)) {
                mkdir($uploadPath, 0755, true);
            }

            $fullPath = $uploadPath . $fileName;

            if (!copy($imagePath, $fullPath)) {
                \think\Log::write("复制图片文件失败: {$imagePath} -> {$fullPath}", 'error');
                return null;
            }

            // 返回相对路径
            $relativePath = '/uploads/images/' . date('Ymd') . '/' . $fileName;

            // 匹配字段，考虑合并单元格
            $field = $this->matchImageFieldWithMerged($coordinates, $config, $imageFields, $mergedCells);

            \think\Log::write("成功复制图片: 位置 {$coordinates}, 路径 {$relativePath}, 匹配字段 {$field}", 'debug');

            return [
                'coordinates' => $coordinates,
                'path' => $relativePath,
                'field' => $field,
                'size' => filesize($fullPath),
                'type' => mime_content_type($fullPath),
                'offset_x' => $offsetX,
                'offset_y' => $offsetY,
                'width' => $width,
                'height' => $height
            ];

        } catch (\Exception $e) {
            \think\Log::write('处理文件图片失败: ' . $e->getMessage(), 'error');
            \think\Log::write('错误堆栈: ' . $e->getTraceAsString(), 'error');
            return null;
        }
    }

    /**
     * 根据图片位置匹配对应的字段（考虑合并单元格）
     */
    protected function matchImageFieldWithMerged($coordinates, $config, $imageFields, $mergedCells)
    {
        \think\Log::write("尝试匹配图片位置: {$coordinates}", 'debug');
        \think\Log::write("可用的图片字段配置: " . json_encode(array_intersect_key($config, array_flip($imageFields)), JSON_UNESCAPED_UNICODE), 'debug');

        // 遍历图片字段配置，找到最接近的字段
        foreach ($imageFields as $field) {
            if (isset($config[$field]) && !empty($config[$field])) {
                $configCoord = $config[$field];
                \think\Log::write("检查字段 {$field} 配置坐标: {$configCoord}", 'debug');

                // 精确匹配
                if ($coordinates === $configCoord) {
                    \think\Log::write("精确匹配字段: {$field}", 'debug');
                    return $field;
                }

                // 检查是否在合并单元格范围内
                if ($this->isCoordinateInRange($coordinates, $configCoord)) {
                    \think\Log::write("范围匹配字段: {$field}", 'debug');
                    return $field;
                }

                // 检查是否在合并单元格内
                if ($this->isCoordinateInMergedCell($coordinates, $configCoord, $mergedCells)) {
                    \think\Log::write("合并单元格匹配字段: {$field}", 'debug');
                    return $field;
                }
            }
        }

        \think\Log::write("未找到匹配字段，使用默认: additional_photos", 'debug');
        // 如果没有精确匹配，返回通用字段
        return 'additional_photos';
    }

    /**
     * 检查坐标是否在合并单元格内
     */
    protected function isCoordinateInMergedCell($imageCoord, $configCoord, $mergedCells)
    {
        // 检查配置坐标是否在某个合并单元格内
        foreach ($mergedCells as $mergedRange) {
            if ($this->isCoordinateInRange($configCoord, $mergedRange)) {
                // 配置坐标在这个合并单元格内，检查图片坐标是否也在
                if ($this->isCoordinateInRange($imageCoord, $mergedRange)) {
                    \think\Log::write("图片和配置都在合并单元格 {$mergedRange} 内", 'debug');
                    return true;
                }
            }
        }

        // 检查图片坐标是否在某个包含配置坐标的合并单元格内
        foreach ($mergedCells as $mergedRange) {
            if ($this->isCoordinateInRange($imageCoord, $mergedRange) &&
                $this->isCoordinateInRange($configCoord, $mergedRange)) {
                \think\Log::write("图片和配置都在合并单元格 {$mergedRange} 内", 'debug');
                return true;
            }
        }

        return false;
    }

    /**
     * 提取Excel内嵌图片
     */
    protected function extractEmbeddedImage($drawing, $config, $imageFields, $mergedCells = [])
    {
        try {
            $coordinates = $drawing->getCoordinates();
            $imagePath = $drawing->getPath();

            \think\Log::write("提取Excel内嵌图片: {$imagePath}", 'debug');

            // 解析zip路径
            if (preg_match('/zip:\/\/(.+)#(.+)/', $imagePath, $matches)) {
                $zipFile = $matches[1];
                $internalPath = $matches[2];

                \think\Log::write("ZIP文件: {$zipFile}, 内部路径: {$internalPath}", 'debug');

                // 使用ZipArchive提取图片
                $zip = new \ZipArchive();
                if ($zip->open($zipFile) === TRUE) {
                    $imageContent = $zip->getFromName($internalPath);
                    $zip->close();

                    if ($imageContent !== false) {
                        // 确定文件扩展名
                        $extension = pathinfo($internalPath, PATHINFO_EXTENSION);
                        if (empty($extension)) {
                            $extension = 'png'; // 默认扩展名
                        }

                        // 生成唯一文件名
                        $fileName = 'excel_embedded_' . uniqid() . '.' . $extension;
                        $uploadPath = ROOT_PATH . 'public' . DS . 'uploads' . DS . 'images' . DS . date('Ymd') . DS;

                        // 确保目录存在
                        if (!is_dir($uploadPath)) {
                            mkdir($uploadPath, 0755, true);
                        }

                        $fullPath = $uploadPath . $fileName;

                        // 保存图片内容
                        if (file_put_contents($fullPath, $imageContent) !== false) {
                            $relativePath = '/uploads/images/' . date('Ymd') . '/' . $fileName;

                            // 获取图片的详细位置信息
                            $offsetX = $drawing->getOffsetX();
                            $offsetY = $drawing->getOffsetY();
                            $width = $drawing->getWidth();
                            $height = $drawing->getHeight();

                            // 匹配字段，考虑合并单元格
                            $field = $this->matchImageFieldWithMerged($coordinates, $config, $imageFields, $mergedCells);

                            \think\Log::write("成功提取内嵌图片: 位置 {$coordinates}, 路径 {$relativePath}, 匹配字段 {$field}", 'debug');

                            return [
                                'coordinates' => $coordinates,
                                'path' => $relativePath,
                                'field' => $field,
                                'size' => filesize($fullPath),
                                'type' => mime_content_type($fullPath),
                                'offset_x' => $offsetX,
                                'offset_y' => $offsetY,
                                'width' => $width,
                                'height' => $height,
                                'source' => 'embedded'
                            ];
                        } else {
                            \think\Log::write("保存内嵌图片失败: {$fullPath}", 'error');
                        }
                    } else {
                        \think\Log::write("从ZIP中读取图片内容失败: {$internalPath}", 'error');
                    }
                } else {
                    \think\Log::write("打开ZIP文件失败: {$zipFile}", 'error');
                }
            } else {
                \think\Log::write("无法解析ZIP路径: {$imagePath}", 'error');
            }

        } catch (\Exception $e) {
            \think\Log::write('提取Excel内嵌图片失败: ' . $e->getMessage(), 'error');
            \think\Log::write('错误堆栈: ' . $e->getTraceAsString(), 'error');
        }

        return null;
    }

    /**
     * 根据图片位置匹配对应的字段（旧方法，保持兼容性）
     */
    protected function matchImageField($coordinates, $config, $imageFields)
    {
        return $this->matchImageFieldWithMerged($coordinates, $config, $imageFields, []);
    }

    /**
     * 检查坐标是否在指定范围内（支持合并单元格）
     */
    protected function isCoordinateInRange($coordinate, $rangeOrCoordinate)
    {
        // 如果包含冒号，说明是范围（如A1:B2）
        if (strpos($rangeOrCoordinate, ':') !== false) {
            list($startCoord, $endCoord) = explode(':', $rangeOrCoordinate);
            return $this->isCoordinateBetween($coordinate, $startCoord, $endCoord);
        }

        // 单个坐标，直接比较
        return $coordinate === $rangeOrCoordinate;
    }

    /**
     * 检查坐标是否在两个坐标之间
     */
    protected function isCoordinateBetween($coordinate, $startCoord, $endCoord)
    {
        try {
            // 解析坐标
            $coord = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::coordinateFromString($coordinate);
            $start = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::coordinateFromString($startCoord);
            $end = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::coordinateFromString($endCoord);

            // 转换列字母为数字
            $coordCol = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($coord[0]);
            $startCol = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($start[0]);
            $endCol = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($end[0]);

            // 检查行和列是否都在范围内
            $rowInRange = $coord[1] >= $start[1] && $coord[1] <= $end[1];
            $colInRange = $coordCol >= $startCol && $coordCol <= $endCol;

            return $rowInRange && $colInRange;

        } catch (\Exception $e) {
            \think\Log::write("坐标范围检查失败: " . $e->getMessage(), 'error');
            return false;
        }
    }

    /**
     * 根据模板解析Excel数据
     * @param \PhpOffice\PhpSpreadsheet\Worksheet\Worksheet $sheet Excel工作表
     * @param array $config 模板配置
     * @param array &$errorMessages 错误信息
     * @return array 解析后的数据
     */
    protected function parseExcelWithTemplate($sheet, $config, &$errorMessages)
    {
        $result = [];
        $resumeData = [];
        $currentName = '';

        // 首先提取Excel中的图片
        $extractedImages = $this->extractImagesFromExcel($sheet, $config);

        // 详细记录图片提取结果
        \think\Log::write('图片提取完成，共提取到 ' . count($extractedImages) . ' 个图片', 'info');
        foreach ($extractedImages as $index => $image) {
            \think\Log::write("图片 " . ($index + 1) . ": 位置={$image['coordinates']}, 字段={$image['field']}, 路径={$image['path']}", 'info');
        }
        
        try {
            // 基本字段映射 - 添加缺失的字段
            $basicFields = [
                'name', 'intended_position', 'applied_position', 'gender', 'ethnicity', 
                'height', 'weight', 'id_card', 'age', 'birth_date', 'phone', 'marital_status', 
                'hukou_location', 'residence_address', 'highest_education', 'cantonese_level', 
                'mandarin_level', 'english_level', 'hk_macau_passport', 'hk_macau_passport_expiry',
                'overseas_experience', 'overseas_region', 'special_certificate', 'hobbies', 
                'self_evaluation', 'contact_person', 'status'
            ];
            
            // 字段长度限制 - 添加新字段的限制
            $fieldLengths = [
                'name' => 50, 
                'intended_position' => 100, 
                'applied_position' => 100, 
                'gender' => 10, 
                'ethnicity' => 20, 
                'height' => 10, 
                'weight' => 10, 
                'id_card' => 18, 
                'age' => 10, 
                'birth_date' => 20, 
                'phone' => 20, 
                'marital_status' => 10, 
                'hukou_location' => 500, 
                'residence_address' => 500, 
                'highest_education' => 20, 
                'cantonese_level' => 10, 
                'mandarin_level' => 10, 
                'english_level' => 10,
                'hk_macau_passport' => 50,
                'hk_macau_passport_expiry' => 20,
                'overseas_experience' => 10,
                'overseas_region' => 100,
                'special_certificate' => 255, 
                'hobbies' => 1000, 
                'self_evaluation' => 1000, 
                'contact_person' => 50, 
                'status' => 20
            ];
            
            // 枚举字段值映射（严格按照数据库定义）
            $enumFields = [
                'gender' => ['未知', '男', '女'],
                'marital_status' => ['未婚', '已婚', '离异'],
                'cantonese_level' => ['不会', '一般', '熟练'],
                'mandarin_level' => ['不会', '一般', '熟练'],
                'english_level' => ['不会', '一般', '熟练'],
                'overseas_experience' => ['无', '有'],
                'status' => ['草稿', '已发布', '官网提交']
            ];
            
            // 解析基本字段
            foreach ($basicFields as $field) {
                if (isset($config[$field]) && !empty($config[$field])) {
                    $cellCoord = $config[$field];
                    try {
                        // 获取单元格的原始值
                        $cell = $sheet->getCell($cellCoord);
                        $originalValue = $cell->getValue();
                        
                        // 检查是否是Excel日期格式
                        $isExcelDate = false;
                        if ($cell->getDataType() == \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_NUMERIC && 
                            \PhpOffice\PhpSpreadsheet\Shared\Date::isDateTime($cell)) {
                            $isExcelDate = true;
                            $value = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($originalValue)->format('Y-m-d');
                            \think\Log::write("检测到Excel日期格式: 字段 {$field}, 单元格 {$cellCoord}, 原始值: {$originalValue}, 转换后: {$value}", 'debug');
                        } else {
                            $value = trim($originalValue);
                        }
                        
                        // 特殊字段处理
                        if ($field == 'birth_date' || $field == 'hk_macau_passport_expiry') {
                            // 对出生日期和通行证到期时间使用斜杠格式(yyyy/mm/dd)
                            $originalDateValue = $value;
                            $value = $this->convertDateFormat($value, 'full', 'slash', $errorMessages, $field);
                            \think\Log::write("日期转换: 字段 {$field}, 原始值: {$originalDateValue}, 转换后: {$value}", 'debug');
                        } elseif (in_array($field, ['height', 'weight', 'age'])) {
                            // 确保数值字段为数字
                            $value = is_numeric($value) ? $value : '';
                        } elseif ($field == 'id_card') {
                            // 移除身份证中的单引号前缀
                            $value = ltrim($value, "'");
                        }
                        
                        // 检查枚举字段
                        if (isset($enumFields[$field])) {
                            if (empty($value)) {
                                // 如果值为空，使用默认值（第一个值）
                                $value = $enumFields[$field][0];
                                \think\Log::write("字段 {$field} 为空，使用默认值: {$value}", 'debug');
                            } elseif (!in_array($value, $enumFields[$field])) {
                                // 尝试匹配最相似的值
                                $bestMatch = '';
                                $bestSimilarity = 0;
                                foreach ($enumFields[$field] as $validValue) {
                                    $similarity = similar_text($value, $validValue, $percent);
                                    if ($percent > $bestSimilarity) {
                                        $bestSimilarity = $percent;
                                        $bestMatch = $validValue;
                                    }
                                }

                                if ($bestSimilarity > 70) {
                                    $value = $bestMatch;
                                    $chineseFieldName = $this->getFieldChineseName($field);
                                    $errorMessages[] = "字段 {$chineseFieldName} 的值 '{$sheet->getCell($cellCoord)->getValue()}' 自动更正为 '{$bestMatch}'";
                                } else {
                                    // 如果没有合适的匹配，使用默认值
                                    $value = $enumFields[$field][0];
                                    $chineseFieldName = $this->getFieldChineseName($field);
                                    $errorMessages[] = "字段 {$chineseFieldName} 的值 '{$sheet->getCell($cellCoord)->getValue()}' 不在允许范围内，已设置为默认值 '{$value}'";
                                }
                            }
                        }
                        
                        // 检查字段长度
                        if (isset($fieldLengths[$field]) && mb_strlen($value) > $fieldLengths[$field]) {
                            $value = mb_substr($value, 0, $fieldLengths[$field]);
                            $chineseFieldName = $this->getFieldChineseName($field);
                            $errorMessages[] = "字段 {$chineseFieldName} 的值过长，已截断为 {$fieldLengths[$field]} 个字符";
                        }
                        
                        $resumeData[$field] = $value;
                        
                        // 记录姓名，用于判断是否为新简历
                        if ($field == 'name') {
                            if (!empty($value)) {
                                $currentName = $value;
                                \think\Log::write("成功解析姓名字段: {$value}", 'debug');
                            } else {
                                \think\Log::write("姓名字段为空，单元格 {$cellCoord} 的原始值: " . var_export($originalValue, true), 'warning');
                            }
                        }
                    } catch (\Exception $e) {
                        $errorMessages[] = "读取字段 {$field} 单元格 {$cellCoord} 失败：" . $e->getMessage();
                    }
                }
            }
            
            // 多行数据字段
            $multiFields = [
                // 联系人信息
                'contact_relation' => [],
                'contact_name' => [],
                'contact_age' => [],
                'contact_job' => [],
                // 教育经历
                'education_start' => [],
                'education_end' => [],
                'education_school' => [],
                'education_major' => [],
                'graduation_education' => [],
                // 工作经历
                'job_start' => [],
                'job_end' => [],
                'job_company' => [],
                'job_position' => [],
                'job_description' => []
            ];
            
            // 多行字段长度限制
            $multiFieldLengths = [
                'contact_relation' => 50,
                'contact_name' => 50,
                'contact_age' => 10,
                'contact_job' => 100,
                'education_start' => 20,
                'education_end' => 20,
                'education_school' => 100,
                'education_major' => 100,
                'graduation_education' => 30,
                'job_start' => 20,
                'job_end' => 20,
                'job_company' => 100,
                'job_position' => 100,
                'job_description' => 500
            ];
            
            // 解析多行数据字段
            foreach ($multiFields as $field => $values) {
                if (isset($config[$field]) && !empty($config[$field])) {
                    // 处理可能的分隔符格式
                    $cellCoords = [];
                    if (is_string($config[$field])) {
                        if (strpos($config[$field], '|') !== false) {
                            $cellCoords = explode('|', $config[$field]);
                        } else if (strpos($config[$field], ',') !== false) {
                            $cellCoords = explode(',', $config[$field]);
                        } else {
                            $cellCoords = [$config[$field]];
                        }
                    } else if (is_array($config[$field])) {
                        $cellCoords = $config[$field];
                    }
                    
                    $values = [];

                    foreach ($cellCoords as $coordIndex => $coord) {
                        try {
                            $coord = trim($coord);
                            if (empty($coord)) continue;
                            
                            // 获取单元格的原始值
                            $cell = $sheet->getCell($coord);
                            $originalValue = $cell->getValue();
                            
                            // 检查是否是Excel日期格式
                            if ($cell->getDataType() == \PhpOffice\PhpSpreadsheet\Cell\DataType::TYPE_NUMERIC && 
                                \PhpOffice\PhpSpreadsheet\Shared\Date::isDateTime($cell)) {
                                $value = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($originalValue)->format('Y-m-d');
                                \think\Log::write("检测到Excel日期格式: 字段 {$field}, 单元格 {$coord}, 原始值: {$originalValue}, 转换后: {$value}", 'debug');
                            } else {
                                $value = trim($originalValue);
                            }
                            
                            if (!empty($value)) {
                                // 日期字段特殊处理
                                if (in_array($field, ['education_start', 'education_end', 'job_start', 'job_end'])) {
                                    // 使用convertDateFormat方法并指定为"month"类型
                                    $originalDateValue = $value;
                                    $value = $this->convertDateFormat($value, 'month', 'dash', $errorMessages, $field, $coordIndex); // 这里会返回Y-m格式
                                    // 记录原始值和转换后的值，用于调试
                                    \think\Log::write("日期转换: 字段 {$field}, 单元格 {$coord}, 原始值: {$originalDateValue}, 转换后: {$value}", 'debug');
                                }
                                
                                // 检查字段长度
                                if (isset($multiFieldLengths[$field]) && mb_strlen($value) > $multiFieldLengths[$field]) {
                                    $value = mb_substr($value, 0, $multiFieldLengths[$field]);
                                    $chineseFieldName = $this->getFieldChineseName($field);
                                    $arrayIndexText = " #" . ($coordIndex + 1);
                                    $errorMessages[] = "多行字段 {$chineseFieldName}{$arrayIndexText} 的值过长，已截断为 {$multiFieldLengths[$field]} 个字符";
                                }
                                
                                $values[] = $value;
                            }
                        } catch (\Exception $e) {
                            // 忽略空单元格错误
                            continue;
                        }
                    }
                    
                    // 合并为竖线分隔的字符串
                    if (!empty($values)) {
                        $resumeData[$field] = implode('|', $values);
                        
                        // 检查合并后的长度
                        $maxLength = 65535; // 数据库TEXT类型最大长度
                        if (mb_strlen($resumeData[$field]) > $maxLength) {
                            $resumeData[$field] = mb_substr($resumeData[$field], 0, $maxLength);
                            $chineseFieldName = $this->getFieldChineseName($field);
                            $errorMessages[] = "多行字段 {$chineseFieldName} 合并后的值过长，已截断为 {$maxLength} 个字符";
                        }
                    }
                }
            }
            
            // 设置默认状态
            if (empty($resumeData['status'])) {
                $resumeData['status'] = '已发布';
            }

            // 整合提取的图片数据
            if (!empty($extractedImages)) {
                foreach ($extractedImages as $imageData) {
                    $field = $imageData['field'];
                    if (in_array($field, ['avatar', 'full_body_photo', 'id_card_front', 'id_card_back',
                                         'hk_macau_passport_front', 'hk_macau_passport_back'])) {
                        // 单图片字段
                        $resumeData[$field] = $imageData['path'];
                        \think\Log::write("设置图片字段 {$field}: {$imageData['path']}", 'debug');
                    } elseif ($field === 'additional_photos') {
                        // 多图片字段
                        if (!isset($resumeData['additional_photos']) || empty($resumeData['additional_photos'])) {
                            $resumeData['additional_photos'] = [];
                        } else {
                            $resumeData['additional_photos'] = json_decode($resumeData['additional_photos'], true) ?: [];
                        }
                        $resumeData['additional_photos'][] = $imageData['path'];
                        $resumeData['additional_photos'] = json_encode($resumeData['additional_photos'], JSON_UNESCAPED_UNICODE);
                        \think\Log::write("添加补充照片: {$imageData['path']}", 'debug');
                    }
                }
            }

            // 如果有姓名，则添加到结果集
            if (!empty($currentName)) {
                $result[] = $resumeData;
                \think\Log::write('成功解析一条简历数据，姓名: ' . $currentName, 'debug');
            } else {
                \think\Log::write('跳过一条数据，原因：姓名为空。解析的数据: ' . json_encode($resumeData, JSON_UNESCAPED_UNICODE), 'warning');
                $errorMessages[] = "跳过一条数据：姓名字段为空或未正确解析";
            }
        } catch (\Exception $e) {
            $errorMessages[] = "解析Excel数据异常: " . $e->getMessage();
            // 记录详细日志
            \think\Log::write('解析Excel异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
        }
        
        return $result;
    }

    /**
     * 调试图片提取功能
     */
    public function debugImages()
    {
        try {
            // 检查是否有文件上传
            if (!$this->request->file('excel_file')) {
                return json(['code' => 0, 'msg' => '请选择要调试的 Excel 文件']);
            }

            // 获取上传的文件
            $file = $this->request->file('excel_file');

            // 读取 Excel 文件
            $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
            $spreadsheet = $reader->load($file->getRealPath());
            $sheet = $spreadsheet->getActiveSheet();

            // 获取所有图片
            $drawings = $sheet->getDrawingCollection();
            $mergedCells = $sheet->getMergeCells();

            $debugInfo = [
                'total_drawings' => count($drawings),
                'merged_cells' => $mergedCells,
                'drawings_info' => []
            ];

            foreach ($drawings as $index => $drawing) {
                $info = [
                    'index' => $index,
                    'type' => get_class($drawing),
                    'coordinates' => $drawing->getCoordinates(),
                    'offset_x' => $drawing->getOffsetX(),
                    'offset_y' => $drawing->getOffsetY(),
                    'width' => $drawing->getWidth(),
                    'height' => $drawing->getHeight()
                ];

                if ($drawing instanceof \PhpOffice\PhpSpreadsheet\Worksheet\MemoryDrawing) {
                    $info['mime_type'] = $drawing->getMimeType();
                    $info['description'] = $drawing->getDescription();
                    $info['name'] = $drawing->getName();
                } elseif ($drawing instanceof \PhpOffice\PhpSpreadsheet\Worksheet\Drawing) {
                    $info['path'] = $drawing->getPath();
                    $info['description'] = $drawing->getDescription();
                    $info['name'] = $drawing->getName();
                }

                $debugInfo['drawings_info'][] = $info;
            }

            \think\Log::write('图片调试信息: ' . json_encode($debugInfo, JSON_UNESCAPED_UNICODE), 'info');

            return json(['code' => 1, 'msg' => '调试完成', 'data' => $debugInfo]);

        } catch (\Exception $e) {
            \think\Log::write('图片调试失败: ' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => '调试失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 预览导入数据
     */
    public function previewImport()
    {
        try {
            // 检查是否有文件上传
            if (!$this->request->file('excel_file')) {
                return json(['code' => 0, 'msg' => '请选择要导入的 Excel 文件']);
            }
            
            // 获取模板ID
            $templateId = $this->request->post('template_id');
            if (empty($templateId)) {
                return json(['code' => 0, 'msg' => '请选择导入模板']);
            }
            
            // 获取模板配置
            $template = \app\admin\model\ResumeImportTemplate::get($templateId);
            if (!$template) {
                return json(['code' => 0, 'msg' => '导入模板不存在']);
            }
            
            // 获取模板配置
            $config = $template->config;
            if (empty($config)) {
                return json(['code' => 0, 'msg' => '模板配置不完整']);
            }

            // 获取上传的文件
            $file = $this->request->file('excel_file');

            // 确保文件可读
            if (!$file->isValid()) {
                return json(['code' => 0, 'msg' => '无效的Excel文件']);
            }

            // 检查文件扩展名
            $extension = strtolower(pathinfo($file->getInfo('name'), PATHINFO_EXTENSION));
            if (!in_array($extension, ['xlsx', 'xls'])) {
                return json(['code' => 0, 'msg' => '请上传Excel文件(.xlsx或.xls格式)']);
            }

            // 记录上传文件信息，用于调试
            $fileInfo = [
                'name' => $file->getInfo('name'),
                'type' => $file->getInfo('type'),
                'size' => $file->getInfo('size'),
                'tmp_name' => $file->getInfo('tmp_name')
            ];
            \think\Log::write('上传文件信息: ' . json_encode($fileInfo), 'debug');

            // 读取 Excel 文件
            $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
            $spreadsheet = $reader->load($file->getRealPath());
            $sheet = $spreadsheet->getActiveSheet();
            
            // 解析Excel数据但不保存
            $errorMessages = [];
            $data = $this->parseExcelWithTemplate($sheet, $config, $errorMessages);
            
            // 处理预览数据，美化显示格式
            $previewData = [];
            foreach ($data as $index => $resumeData) {
                $previewItem = [];
                
                // 处理基本字段
                $basicFields = [
                    'name' => '姓名',
                    'intended_position' => '意向岗位',
                    'applied_position' => '申请岗位',
                    'gender' => '性别',
                    'ethnicity' => '民族',
                    'height' => '身高(cm)',
                    'weight' => '体重(kg)',
                    'id_card' => '身份证号',
                    'age' => '年龄',
                    'birth_date' => '出生日期',
                    'phone' => '手机号码',
                    'marital_status' => '婚姻状况',
                    'hukou_location' => '户口所在地',
                    'residence_address' => '常住地址',
                    'highest_education' => '最高学历',
                    'cantonese_level' => '粤语熟练度',
                    'mandarin_level' => '国语熟练度',
                    'english_level' => '英语熟练度',
                    'hk_macau_passport' => '港澳通行证编号',
                    'hk_macau_passport_expiry' => '港澳通行证到期时间',
                    'overseas_experience' => '海外工作经历',
                    'overseas_region' => '海外工作地区',
                    'special_certificate' => '特殊职业资格证',
                    'hobbies' => '兴趣爱好',
                    'self_evaluation' => '自我评价',
                    'contact_person' => '对接人',
                    'status' => '状态',
                    // 图片字段
                    'avatar' => '头像',
                    'full_body_photo' => '全身照',
                    'id_card_front' => '身份证正面',
                    'id_card_back' => '身份证反面',
                    'hk_macau_passport_front' => '港澳通行证正面',
                    'hk_macau_passport_back' => '港澳通行证反面',
                    'additional_photos' => '补充照片'
                ];

                foreach ($basicFields as $field => $label) {
                    if (isset($resumeData[$field]) && !empty($resumeData[$field])) {
                        $value = $resumeData[$field];
                        
                        // 不在这里添加标签，让前端统一处理
                        $previewItem[$label] = $value;
                    }
                }
                
                // 处理联系人信息
                $contactInfo = [];
                $contactFields = [
                    'contact_relation' => '关系',
                    'contact_name' => '姓名',
                    'contact_age' => '年龄',
                    'contact_job' => '工作'
                ];
                
                foreach ($contactFields as $field => $label) {
                    if (isset($resumeData[$field]) && !empty($resumeData[$field])) {
                        $values = explode('|', $resumeData[$field]);
                        foreach ($values as $i => $value) {
                            if (!isset($contactInfo[$i])) {
                                $contactInfo[$i] = [];
                            }
                            $contactInfo[$i][$label] = $value;
                        }
                    }
                }
                
                if (!empty($contactInfo)) {
                    $previewItem['联系人信息'] = $contactInfo;
                }
                
                // 处理教育经历
                $educationInfo = [];
                $educationFields = [
                    'education_start' => '开始时间',
                    'education_end' => '结束时间',
                    'education_school' => '学校',
                    'education_major' => '专业',
                    'graduation_education' => '学历'
                ];

                foreach ($educationFields as $field => $label) {
                    if (isset($resumeData[$field]) && !empty($resumeData[$field])) {
                        $values = explode('|', $resumeData[$field]);
                        foreach ($values as $i => $value) {
                            if (!isset($educationInfo[$i])) {
                                $educationInfo[$i] = [];
                            }
                            
                            // 不在这里添加标签，让前端统一处理
                            $educationInfo[$i][$label] = $value;
                        }
                    }
                }

                if (!empty($educationInfo)) {
                    $previewItem['教育经历'] = $educationInfo;
                }
                
                // 处理工作经历
                $jobInfo = [];
                $jobFields = [
                    'job_start' => '开始时间',
                    'job_end' => '结束时间',
                    'job_company' => '公司',
                    'job_position' => '职位',
                    'job_description' => '描述'
                ];

                foreach ($jobFields as $field => $label) {
                    if (isset($resumeData[$field]) && !empty($resumeData[$field])) {
                        $values = explode('|', $resumeData[$field]);
                        foreach ($values as $i => $value) {
                            if (!isset($jobInfo[$i])) {
                                $jobInfo[$i] = [];
                            }
                            
                            // 不在这里添加标签，让前端统一处理
                            $jobInfo[$i][$label] = $value;
                        }
                    }
                }

                if (!empty($jobInfo)) {
                    $previewItem['工作经历'] = $jobInfo;
                }
                
                $previewData[] = $previewItem;
            }
            
            // 返回预览数据和错误信息
            return json(['code' => 1, 'msg' => '数据预览成功', 'data' => [
                'data' => $previewData,
                'errors' => $errorMessages,
                'count' => count($previewData)
            ]]);
            
        } catch (\PhpOffice\PhpSpreadsheet\Reader\Exception $e) {
            // 捕获Excel读取错误
            \think\Log::write('Excel读取错误：' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => 'Excel文件读取失败：' . $e->getMessage()]);
        } catch (\PhpOffice\PhpSpreadsheet\Exception $e) {
            // 捕获PhpSpreadsheet错误
            \think\Log::write('PhpSpreadsheet错误：' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => 'Excel处理失败：' . $e->getMessage()]);
        } catch (\Exception $e) {
            // 捕获所有其他错误
            \think\Log::write('预览失败错误：' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            return json(['code' => 0, 'msg' => '预览失败：' . $e->getMessage()]);
        }
    }

    /**
     * 获取字段显示名称
     */
    private function getFieldDisplayName($field)
    {
        $fieldNames = [
            'name' => '姓名',
            'intended_position' => '意向岗位',
            'applied_position' => '申请岗位',
            'gender' => '性别',
            'ethnicity' => '民族',
            'height' => '身高(cm)',
            'weight' => '体重(kg)',
            'id_card' => '身份证号',
            'age' => '年龄',
            'birth_date' => '出生日期',
            'phone' => '手机号码',
            'marital_status' => '婚姻状况',
            'hukou_location' => '户口所在地',
            'residence_address' => '常住地址',
            'highest_education' => '最高学历',
            'cantonese_level' => '粤语熟练度',
            'mandarin_level' => '国语熟练度',
            'english_level' => '英语熟练度',
            'hk_macau_passport' => '港澳通行证编号',
            'hk_macau_passport_expiry' => '港澳通行证到期时间',
            'overseas_experience' => '海外工作经历',
            'overseas_region' => '海外工作地区',
            'special_certificate' => '特殊职业资格证',
            'hobbies' => '兴趣爱好',
            'self_evaluation' => '自我评价',
            'contact_person' => '对接人',
            'status' => '状态',
            'education_start' => '教育开始时间',
            'education_end' => '教育结束时间',
            'education_school' => '学校名称',
            'education_major' => '专业',
            'graduation_education' => '学历',
            'job_start' => '工作开始时间',
            'job_end' => '工作结束时间',
            'job_company' => '工作单位',
            'job_position' => '工作岗位',
            'job_description' => '工作内容',
            'contact_relation' => '联系人关系',
            'contact_name' => '联系人姓名',
            'contact_age' => '联系人年龄',
            'contact_job' => '联系人工作'
        ];
        
        return $fieldNames[$field] ?? $field;
    }
}









