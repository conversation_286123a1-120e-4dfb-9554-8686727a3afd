<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;
use think\Exception;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 简历投递管理
 *
 * @icon fa fa-paper-plane
 */
class ResumeApply extends Backend
{

    /**
     * ResumeApply模型对象
     * @var \app\admin\model\ResumeApply
     */
    protected $model = null;
    
    // 开启关联查询
    protected $relationSearch = true;

    // 搜索字段
    protected $searchFields = 'id,resume_name,job_code,job_name';

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ResumeApply;
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        
        // 获取job_id参数
        $job_id = $this->request->get('job_id');
        
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 如果有job_id参数，添加筛选条件
            if ($job_id) {
                $where .= ' AND job_id = ' . intval($job_id);
            }

            try {
                $list = $this->model
                    ->with(['resume', 'job', 'user'])
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

                $result = array("total" => $list->total(), "rows" => $list->items());

                return json($result);
            } catch (\Exception $e) {
                return json(['total' => 0, 'rows' => [], 'error' => $e->getMessage()]);
            }
        }
        
        // 如果有job_id参数，设置页面标题
        if ($job_id) {
            $job = \app\common\model\Jobs::get($job_id);
            if ($job) {
                $this->view->assign('title', '简历投递列表 - ' . $job->job_name);
                // 将job_id传递给前端JS
                $this->assignconfig('job_id', $job_id);
            }
        }
        
        return $this->view->fetch();
    }
    
    /**
     * 添加
     */
    public function add()
    {
        // 获取简历列表
        $resumeList = \app\admin\model\Resume::where('status', '已发布')
            ->field('id, name, gender, age, highest_education, user_id')
            ->select();
        $this->view->assign('resumeList', $resumeList);
        
        // 获取岗位列表
        $jobList = \app\common\model\Jobs::where('status', '上架')
            ->field('id, title, job_code, job_name, company, salary_range')
            ->select();
        $this->view->assign('jobList', $jobList);
        
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            
            if ($params) {
                // 验证岗位和简历是否存在
                $resume = \app\admin\model\Resume::get($params['resume_id']);
                if (!$resume) {
                    $this->error(__('简历不存在'));
                }
                
                $job = \app\common\model\Jobs::get($params['job_id']);
                if (!$job) {
                    $this->error(__('岗位不存在'));
                }
                
                // 自动填充简历和岗位信息
                $params['resume_name'] = $resume['name'];
                $params['job_code'] = $job['job_code'];
                $params['job_name'] = $job['job_name'];
                // 自动设置user_id为简历对应的user_id
                $params['user_id'] = $resume['user_id'];
                
                $this->request->post(['row' => $params]);
            }
            
            return parent::add();
        }
        
        return $this->view->fetch();
    }
    
    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        // 获取简历列表
        $resumeList = \app\admin\model\Resume::where('status', '已发布')
            ->field('id, name, gender, age, highest_education, user_id')
            ->select();
        $this->view->assign('resumeList', $resumeList);
        
        // 获取岗位列表
        $jobList = \app\common\model\Jobs::where('status', '上架')
            ->field('id, title, job_code, job_name, company, salary_range')
            ->select();
        $this->view->assign('jobList', $jobList);
        
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            
            if ($params) {
                // 验证岗位和简历是否存在
                if (isset($params['resume_id'])) {
                    $resume = \app\admin\model\Resume::get($params['resume_id']);
                    if (!$resume) {
                        $this->error(__('简历不存在'));
                    }
                    $params['resume_name'] = $resume['name'];
                    // 自动设置user_id为简历对应的user_id
                    $params['user_id'] = $resume['user_id'];
                }
                
                if (isset($params['job_id'])) {
                    $job = \app\common\model\Jobs::get($params['job_id']);
                    if (!$job) {
                        $this->error(__('岗位不存在'));
                    }
                    $params['job_code'] = $job['job_code'];
                    $params['job_name'] = $job['job_name'];
                }
                
                $this->request->post(['row' => $params]);
            }
            
            return parent::edit($ids);
        }
        
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }
    
    /**
     * 查看简历详情
     */
    public function resume_detail($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        $resume = \app\admin\model\Resume::get($row['resume_id']);
        if (!$resume) {
            $this->error(__('简历不存在'));
        }
        
        // 如果是AJAX请求，返回URL
        if ($this->request->isAjax()) {
            $url = url('resume/detail', ['ids' => $row['resume_id']]);
            return json(['code' => 1, 'msg' => '', 'data' => ['url' => $url]]);
        }
        
        // 转到简历详情页
        $url = url('resume/detail', ['ids' => $row['resume_id']]);
        $this->redirect($url);
    }
    
    /**
     * 查看岗位详情
     */
    public function job_detail($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        $job = \app\common\model\Jobs::get($row['job_id']);
        if (!$job) {
            $this->error(__('岗位不存在'));
        }
        
        // 如果是AJAX请求，返回URL
        if ($this->request->isAjax()) {
            $url = url('jobs/edit', ['ids' => $row['job_id']]);
            return json(['code' => 1, 'msg' => '', 'data' => ['url' => $url]]);
        }
        
        // 转到岗位详情页
        $url = url('jobs/edit', ['ids' => $row['job_id']]);
        $this->redirect($url);
    }
    
    
    /**
     * 批量更新状态
     */
    public function batch_update()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post();
            $ids = isset($params['ids']) ? $params['ids'] : '';
            $status = isset($params['status']) ? $params['status'] : '';
            $feedback = isset($params['feedback']) ? $params['feedback'] : '';
            
            if (!$ids) {
                $this->error(__('未选择记录'));
            }
            
            if (!$status) {
                $this->error(__('请选择状态'));
            }
            
            $list = $this->model->where('id', 'in', $ids)->select();
            
            Db::startTrans();
            try {
                $interviewModel = new \app\common\model\Jobinterview;
                $interviewAction = isset($params['interview_action']) ? $params['interview_action'] : 'none';
                $interview = null;
                
                // 如果是已选中状态，且选择了添加到现有面试或创建新面试
                if ($status == '已选中' && ($interviewAction == 'existing' || $interviewAction == 'new')) {
                    
                    // 添加到现有面试
                    if ($interviewAction == 'existing') {
                        $interviewId = isset($params['interview_id']) ? intval($params['interview_id']) : 0;
                        if ($interviewId) {
                            $interview = $interviewModel->get($interviewId);
                            if (!$interview) {
                                throw new \Exception('面试不存在');
                            }
                            
                            // 初始化面试数据
                            $resumeIds = $interview['resume_id'] ? explode(',', $interview['resume_id']) : [];
                            $interviewees = $interview['interviewee'] ? explode(',', $interview['interviewee']) : [];
                            
                            // 解析当前进度数据，使用姓名:状态格式
                            $progressItems = [];
                            if ($interview['progress']) {
                                // 尝试作为JSON解析
                                $progressData = json_decode($interview['progress'], true);
                                if (is_array($progressData)) {
                                    // 转换JSON格式为名称:状态格式
                                    foreach ($progressData as $rid => $status) {
                                        // 查找匹配的姓名
                                        $name = $rid;
                                        for ($i = 0; $i < count($resumeIds); $i++) {
                                            if ($resumeIds[$i] == $rid && isset($interviewees[$i])) {
                                                $name = $interviewees[$i];
                                                break;
                                            }
                                        }
                                        $progressItems[] = $name . ':' . $status;
                                    }
                                } else {
                                    // 已经是名称:状态格式
                                    $progressItems = explode(',', $interview['progress']);
                                }
                            }
                        } else {
                            throw new \Exception('请选择面试');
                        }
                    }
                    // 创建新面试
                    else if ($interviewAction == 'new') {
                        $interviewTitle = isset($params['interview_title']) ? $params['interview_title'] : '';
                        $interviewTime = isset($params['interview_time']) ? $params['interview_time'] : '';
                        $interviewType = isset($params['interview_type']) ? $params['interview_type'] : '';
                        $interviewMaterials = isset($params['interview_materials']) ? $params['interview_materials'] : '';
                        
                        if (!$interviewTitle || !$interviewTime || !$interviewType) {
                            throw new \Exception('请填写完整的面试信息');
                        }
                        
                        // 获取第一条记录的岗位信息作为面试岗位
                        $firstRow = $list[0];
                        
                        // 创建新面试记录
                        $interview = new \app\common\model\Jobinterview;
                        $interview->title = $interviewTitle;
                        $interview->interview_time = $interviewTime;
                        $interview->job_code = $firstRow['job_code'];
                        $interview->job = $firstRow['job_name'];
                        $interview->interview_type = $interviewType;
                        $interview->interview_materials = $interviewMaterials;
                        $interview->status = '未开始';
                        
                        // 初始化面试数据
                        $resumeIds = [];
                        $interviewees = [];
                        $progressItems = [];
                        
                        // 保存面试记录
                        $interview->save();
                    }
                }
                
                foreach ($list as $row) {
                    // 更新投递状态和反馈
                    $row->status = $status;
                    if ($feedback) {
                        $row->feedback = $feedback;
                    }
                    $row->save();
                    
                    // 如果是已选中状态
                    if ($status == '已选中') {
                        // 如果需要添加到面试
                        if (($interviewAction == 'existing' || $interviewAction == 'new') && $interview) {
                            // 检查是否已经存在该简历
                            if (!in_array($row['resume_id'], $resumeIds)) {
                                $resumeIds[] = $row['resume_id'];
                                $interviewees[] = $row['resume_name'];
                                
                                // 添加面试进度
                                $progressItems[] = $row['resume_name'] . ':待面试';
                            }
                        }
                        
                        // 增加岗位的简历数
                        $job = \app\common\model\Jobs::get($row['job_id']);
                        if ($job) {
                            $job->resume_count = $job->resume_count + 1;
                            $job->save();
                        }
                    }
                }
                
                // 如果有面试记录需要更新
                if (($interviewAction == 'existing' || $interviewAction == 'new') && $interview) {
                    $interview->resume_id = implode(',', $resumeIds);
                    $interview->interviewee = implode(',', $interviewees);
                    $interview->progress = implode(',', $progressItems);
                    $interview->save();
                }
                
                Db::commit();
            } catch (\Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            
            $this->success(__('批量更新状态成功'));
        }
        
        // 获取面试列表，用于AJAX加载
        if ($this->request->isAjax() && $this->request->get('getInterviews')) {
            try {
                $interviewModel = new \app\common\model\Jobinterview;
                $interviewList = $interviewModel->where('status', 'in', ['未开始', '进行中'])
                    ->order('interview_time desc')
                    ->select();
                
                // 转换为数组
                $interviews = collection($interviewList)->toArray();
                
                // 返回面试列表数据
                return json(['code' => 1, 'msg' => '获取成功', 'data' => $interviews]);
            } catch (\Exception $e) {
                return json(['code' => 0, 'msg' => $e->getMessage(), 'data' => null]);
            }
        }
        
        return $this->view->fetch();
    }


    /**
     * 获取可用面试列表（API接口）
     */
    public function get_interviews()
    {
        try {
            $interviewModel = new \app\common\model\Jobinterview;
            $interviewList = $interviewModel->where('status', 'in', ['未开始', '进行中'])
                ->order('interview_time desc')
                ->select();
            
            // 转换为数组
            $interviews = collection($interviewList)->toArray();
            
            // 返回面试列表数据
            return json(['code' => 1, 'msg' => '获取成功', 'data' => $interviews]);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => $e->getMessage(), 'data' => null]);
        }
    }
} 