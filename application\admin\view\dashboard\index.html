<style type="text/css">
    .sm-st {
        background: #fff;
        padding: 20px;
        -webkit-border-radius: 3px;
        -moz-border-radius: 3px;
        border-radius: 3px;
        margin-bottom: 20px;
        box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
        transition: all 0.3s cubic-bezier(.25,.8,.25,1);
    }

    .sm-st:hover {
        box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
    }

    .sm-st-icon {
        width: 60px;
        height: 60px;
        display: inline-block;
        line-height: 60px;
        text-align: center;
        font-size: 30px;
        background: #eee;
        -webkit-border-radius: 5px;
        -moz-border-radius: 5px;
        border-radius: 5px;
        margin-right: 10px;
        color: #fff;
    }

    .sm-st-info {
        padding-top: 2px;
    }

    .sm-st-info span {
        display: block;
        font-size: 24px;
        font-weight: 600;
    }

    .orange {
        background: #fa8564 !important;
    }

    .tar {
        background: #45cf95 !important;
    }

    .sm-st .green {
        background: #86ba41 !important;
    }

    .pink {
        background: #AC75F0 !important;
    }

    .yellow-b {
        background: #fdd752 !important;
    }

    .stat-elem {

        background-color: #fff;
        padding: 18px;
        border-radius: 40px;

    }

    .stat-info {
        text-align: center;
        background-color: #fff;
        border-radius: 5px;
        margin-top: -5px;
        padding: 8px;
        -webkit-box-shadow: 0 1px 0px rgba(0, 0, 0, 0.05);
        box-shadow: 0 1px 0px rgba(0, 0, 0, 0.05);
        font-style: italic;
    }

    .stat-icon {
        text-align: center;
        margin-bottom: 5px;
    }

    .st-red {
        background-color: #F05050;
    }

    .st-green {
        background-color: #27C24C;
    }

    .st-violet {
        background-color: #7266ba;
    }

    .st-blue {
        background-color: #23b7e5;
    }

    .stats .stat-icon {
        color: #28bb9c;
        display: inline-block;
        font-size: 26px;
        text-align: center;
        vertical-align: middle;
        width: 50px;
    }

    .stat {
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        display: inline-block;
    }

    .stat .value {
        font-size: 20px;
        line-height: 24px;
        overflow: hidden;
        text-overflow: ellipsis;
        font-weight: 500;
    }

    .stat .name {
        overflow: hidden;
        text-overflow: ellipsis;
        margin: 5px 0;
    }

    .stat.lg .value {
        font-size: 26px;
        line-height: 28px;
    }

    .stat-col {
        margin:0 0 10px 0;
    }
    .stat.lg .name {
        font-size: 16px;
    }

    .stat-col .progress {
        height: 2px;
    }

    .stat-col .progress-bar {
        line-height: 2px;
        height: 2px;
    }

    .item {
        padding: 30px 0;
    }


    #statistics .panel {
        min-height: 150px;
    }

    #statistics .panel h5 {
        font-size: 14px;
    }

    .nav-tabs-custom {
        box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
        transition: all 0.3s cubic-bezier(.25,.8,.25,1);
    }

    .nav-tabs-custom:hover {
        box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
    }
</style>
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null, false)}
        <ul class="nav nav-tabs">
            <li class="active"><a href="#one" data-toggle="tab">{:__('Dashboard')}</a></li>
            <li><a href="#two" data-toggle="tab">{:__('Custom')}</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <!-- 统计卡片 -->
                <div class="row">
                    <div class="col-lg-3 col-xs-6">
                        <div class="small-box bg-aqua">
                            <div class="inner">
                                <h3>{$resume_count}</h3>
                                <p>简历总数</p>
                            </div>
                            <div class="icon">
                                <i class="fa fa-file-text"></i>
                            </div>
                            <a href="{:url('resume/index')}" class="small-box-footer">更多 <i class="fa fa-arrow-circle-right"></i></a>
                        </div>
                    </div>
                    <div class="col-lg-3 col-xs-6">
                        <div class="small-box bg-green">
                            <div class="inner">
                                <h3>{$job_count}</h3>
                                <p>岗位总数</p>
                            </div>
                            <div class="icon">
                                <i class="fa fa-briefcase"></i>
                            </div>
                            <a href="{:url('jobs/index')}" class="small-box-footer">更多 <i class="fa fa-arrow-circle-right"></i></a>
                        </div>
                    </div>
                    <div class="col-lg-3 col-xs-6">
                        <div class="small-box bg-yellow">
                            <div class="inner">
                                <h3>{$interview_count}</h3>
                                <p>面试总数</p>
                            </div>
                            <div class="icon">
                                <i class="fa fa-handshake-o"></i>
                            </div>
                            <a href="{:url('job_interview/index')}" class="small-box-footer">更多 <i class="fa fa-arrow-circle-right"></i></a>
                        </div>
                    </div>
                    <div class="col-lg-3 col-xs-6">
                        <div class="small-box bg-red">
                            <div class="inner">
                                <h3>{$category_count}</h3>
                                <p>岗位分类总数</p>
                            </div>
                            <div class="icon">
                                <i class="fa fa-tags"></i>
                            </div>
                            <a href="{:url('job_category/index')}" class="small-box-footer">更多 <i class="fa fa-arrow-circle-right"></i></a>
                        </div>
                    </div>
                </div>

                <!-- 图表区域 -->
                <div class="row">
                    <!-- 第一行 -->
                    <div class="col-lg-6">
                        <!-- 新增简历趋势 -->
                        <div class="nav-tabs-custom charts-custom">
                            <ul class="nav nav-tabs pull-right">
                                <li class="active"><a href="#resume-trend-line" data-toggle="tab">折线图</a></li>
                                <li><a href="#resume-trend-bar" data-toggle="tab">柱状图</a></li>
                                <li class="pull-left header">
                                    <i class="fa fa-line-chart"></i> 新增简历趋势
                                </li>
                                <select class="form-control" id="days-select" style="display: inline-block; width: auto; margin-left: 10px;" onchange="return false;">
                                    <option value="3" {if $current_days==3}selected{/if}>最近3天</option>
                                    <option value="7" {if $current_days==7}selected{/if}>最近7天</option>
                                    <option value="15" {if $current_days==15}selected{/if}>最近15天</option>
                                    <option value="30" {if $current_days==30}selected{/if}>最近30天</option>
                                </select>
                            </ul>
                            <div class="tab-content no-padding">
                                <div class="chart tab-pane active" id="resume-trend-line" style="position: relative; height: 300px;"></div>
                                <div class="chart tab-pane" id="resume-trend-bar" style="position: relative; height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <!-- 新增岗位趋势 -->
                        <div class="nav-tabs-custom charts-custom">
                            <ul class="nav nav-tabs pull-right">
                                <li class="active"><a href="#job-trend-line" data-toggle="tab">折线图</a></li>
                                <li><a href="#job-trend-bar" data-toggle="tab">柱状图</a></li>
                                <li class="pull-left header">
                                    <i class="fa fa-line-chart"></i> 新增岗位趋势
                                </li>
                                <select class="form-control" id="job-days-select" style="display: inline-block; width: auto; margin-left: 10px;" onchange="return false;">
                                    <option value="3" {if $current_days==3}selected{/if}>最近3天</option>
                                    <option value="7" {if $current_days==7}selected{/if}>最近7天</option>
                                    <option value="15" {if $current_days==15}selected{/if}>最近15天</option>
                                    <option value="30" {if $current_days==30}selected{/if}>最近30天</option>
                                </select>
                            </ul>
                            <div class="tab-content no-padding">
                                <div class="chart tab-pane active" id="job-trend-line" style="position: relative; height: 300px;"></div>
                                <div class="chart tab-pane" id="job-trend-bar" style="position: relative; height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第二行 -->
                <div class="row">
                    <div class="col-lg-6">
                        <!-- 简历统计 -->
                        <div class="nav-tabs-custom charts-custom">
                            <ul class="nav nav-tabs pull-right">
                                <li class="active"><a href="#resume-gender" data-toggle="tab">性别分布</a></li>
                                <li><a href="#resume-education" data-toggle="tab">学历分布</a></li>
                                <li><a href="#resume-map" data-toggle="tab">地区分布</a></li>
                                <li class="pull-left header"><i class="fa fa-file-text"></i> 简历统计</li>
                            </ul>
                            <div class="tab-content no-padding">
                                <div class="chart tab-pane active" id="resume-gender" style="position: relative; height: 300px;"></div>
                                <div class="chart tab-pane" id="resume-education" style="position: relative; height: 300px;"></div>
                                <div class="chart tab-pane" id="resume-map" style="position: relative; height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <!-- 岗位统计 -->
                        <div class="nav-tabs-custom charts-custom">
                            <ul class="nav nav-tabs pull-right">
                                <li class="active"><a href="#job-category" data-toggle="tab">分类分布</a></li>
                                <li><a href="#job-salary" data-toggle="tab">薪资分布</a></li>
                                <li><a href="#job-status" data-toggle="tab">状态分布</a></li>
                                <li class="pull-left header"><i class="fa fa-briefcase"></i> 岗位统计</li>
                            </ul>
                            <div class="tab-content no-padding">
                                <div class="chart tab-pane active" id="job-category" style="position: relative; height: 300px;"></div>
                                <div class="chart tab-pane" id="job-salary" style="position: relative; height: 300px;"></div>
                                <div class="chart tab-pane" id="job-status" style="position: relative; height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 第三行 -->
                <div class="row">
                    <div class="col-lg-6">
                        <!-- 面试统计 -->
                        <div class="nav-tabs-custom charts-custom">
                            <ul class="nav nav-tabs pull-right">
                                <li class="active"><a href="#interview-type" data-toggle="tab">面试类型</a></li>
                                <li><a href="#interview-status" data-toggle="tab">面试状态</a></li>
                                <li><a href="#interview-trend" data-toggle="tab">状态趋势</a></li>
                                <li class="pull-left header">
                                    <i class="fa fa-users"></i> 面试统计
                                </li>
                                <select class="form-control" id="interview-days-select" style="display: none; width: auto; margin-left: 10px;" onchange="return false;">
                                    <option value="3" {if $current_days==3}selected{/if}>最近3天</option>
                                    <option value="7" {if $current_days==7}selected{/if}>最近7天</option>
                                    <option value="15" {if $current_days==15}selected{/if}>最近15天</option>
                                    <option value="30" {if $current_days==30}selected{/if}>最近30天</option>
                                </select>
                            </ul>
                            <div class="tab-content no-padding">
                                <div class="chart tab-pane active" id="interview-type" style="position: relative; height: 300px;"></div>
                                <div class="chart tab-pane" id="interview-status" style="position: relative; height: 300px;"></div>
                                <div class="chart tab-pane" id="interview-trend" style="position: relative; height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <!-- 面试进度统计 -->
                        <div class="nav-tabs-custom charts-custom">
                            <ul class="nav nav-tabs pull-right">
                                <li class="active"><a href="#interview-progress-pie" data-toggle="tab">饼图</a></li>
                                <li><a href="#interview-progress-bar" data-toggle="tab">柱状图</a></li>
                                <li class="pull-left header">
                                    <i class="fa fa-tasks"></i> 面试进度统计
                                </li>
                            </ul>
                            <div class="tab-content no-padding">
                                <div class="chart tab-pane active" id="interview-progress-pie" style="position: relative; height: 300px;"></div>
                                <div class="chart tab-pane" id="interview-progress-bar" style="position: relative; height: 300px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="tab-pane fade" id="two">
                <div class="row">
                    <div class="col-xs-12">
                        {:__('Custom zone')}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
