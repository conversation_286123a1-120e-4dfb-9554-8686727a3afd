<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="type">
            <li class="active"><a href="#t-all" data-value="" data-toggle="tab">{:__('全部')}</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div class="panel-intro">
                        <div class="panel-lead"><em>模板管理</em>用于管理简历导出时使用的Excel和Word模板</div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="box box-success">
                                <div class="box-header with-border">
                                    <h3 class="box-title">Excel模板管理</h3>
                                    <div class="box-tools pull-right">
                                        <a href="{:url('template/excel')}" class="btn btn-box-tool"><i class="fa fa-external-link"></i> 进入管理</a>
                                    </div>
                                </div>
                                <div class="box-body">
                                    <div class="callout callout-info">
                                        <h4>Excel模板说明</h4>
                                        <p>管理用于导出简历的Excel模板，支持上传、删除、预览和下载操作。</p>
                                        <p>模板中可使用占位符（如{{姓名}}、{{年龄}}等）来引用简历数据。</p>
                                    </div>
                                    <div class="text-center">
                                        <a href="{:url('template/excel')}" class="btn btn-success btn-lg">
                                            <i class="fa fa-file-excel-o"></i> 管理Excel模板
                                        </a>
                                    </div>
                                    
                                    <div class="placeholder-list">
                                        <h4>Excel模板可用占位符列表</h4>
                                        <div class="nav-tabs-custom">
                                            <ul class="nav nav-tabs" id="excel-category-tabs">
                                                <!-- 分类选项卡将在这里动态生成 -->
                                            </ul>
                                            <div class="tab-content" id="excel-category-content">
                                                <!-- 分类内容将在这里动态生成 -->
                                            </div>
                                        </div>
                                        <div class="pagination-container text-center" id="excel-pagination">
                                            <!-- 分页控件将在这里显示 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="box box-primary">
                                <div class="box-header with-border">
                                    <h3 class="box-title">Word模板管理</h3>
                                    <div class="box-tools pull-right">
                                        <a href="{:url('template/word')}" class="btn btn-box-tool"><i class="fa fa-external-link"></i> 进入管理</a>
                                    </div>
                                </div>
                                <div class="box-body">
                                    <div class="callout callout-info">
                                        <h4>Word模板说明</h4>
                                        <p>管理用于导出简历的Word模板，支持上传、删除、预览和下载操作。</p>
                                        <p>模板中可使用占位符（如${姓名}、${年龄}等）来引用简历数据。</p>
                                    </div>
                                    <div class="text-center">
                                        <a href="{:url('template/word')}" class="btn btn-primary btn-lg">
                                            <i class="fa fa-file-word-o"></i> 管理Word模板
                                        </a>
                                    </div>
                                    
                                    <div class="placeholder-list">
                                        <h4>Word模板可用占位符列表</h4>
                                        <div class="nav-tabs-custom">
                                            <ul class="nav nav-tabs" id="word-category-tabs">
                                                <!-- 分类选项卡将在这里动态生成 -->
                                            </ul>
                                            <div class="tab-content" id="word-category-content">
                                                <!-- 分类内容将在这里动态生成 -->
                                            </div>
                                        </div>
                                        <div class="pagination-container text-center" id="word-pagination">
                                            <!-- 分页控件将在这里显示 -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Excel模板占位符数据
        var excelPlaceholders = [
            {category: '基本信息', placeholder: '{{姓名}}', description: '简历中的姓名字段'},
            {category: '基本信息', placeholder: '{{性别}}', description: '简历中的性别字段'},
            {category: '基本信息', placeholder: '{{年龄}}', description: '简历中的年龄字段'},
            {category: '基本信息', placeholder: '{{手机号}}', description: '简历中的手机号字段'},
            {category: '基本信息', placeholder: '{{最高学历}}', description: '简历中的最高学历字段'},
            {category: '基本信息', placeholder: '{{意向岗位}}', description: '简历中的意向岗位字段'},
            {category: '基本信息', placeholder: '{{申请岗位}}', description: '简历中的申请岗位字段'},
            {category: '基本信息', placeholder: '{{出生日期}}', description: '简历中的出生日期字段'},
            {category: '基本信息', placeholder: '{{身份证号}}', description: '简历中的身份证号字段'},
            {category: '基本信息', placeholder: '{{婚姻状况}}', description: '简历中的婚姻状况字段'},
            {category: '基本信息', placeholder: '{{户口所在地}}', description: '简历中的户口所在地字段'},
            {category: '基本信息', placeholder: '{{常住地址}}', description: '简历中的常住地址字段'},
            {category: '基本信息', placeholder: '{{身高}}', description: '简历中的身高字段'},
            {category: '基本信息', placeholder: '{{体重}}', description: '简历中的体重字段'},
            {category: '基本信息', placeholder: '{{民族}}', description: '简历中的民族字段'},
            {category: '语言能力', placeholder: '{{粤语水平}}', description: '简历中的粤语水平字段'},
            {category: '语言能力', placeholder: '{{国语水平}}', description: '简历中的国语水平字段'},
            {category: '语言能力', placeholder: '{{英语水平}}', description: '简历中的英语水平字段'},
            {category: '证件信息', placeholder: '{{港澳通行证}}', description: '简历中的港澳通行证字段'},
            {category: '证件信息', placeholder: '{{港澳通行证到期}}', description: '简历中的港澳通行证到期字段'},
            {category: '工作经历', placeholder: '{{海外工作经历}}', description: '简历中的海外工作经历字段'},
            {category: '工作经历', placeholder: '{{海外工作地区}}', description: '简历中的海外工作地区字段'},
            {category: '其他信息', placeholder: '{{特殊证书}}', description: '简历中的特殊证书字段'},
            {category: '其他信息', placeholder: '{{兴趣爱好}}', description: '简历中的兴趣爱好字段'},
            {category: '其他信息', placeholder: '{{自我评价}}', description: '简历中的自我评价字段'},
            {category: '其他信息', placeholder: '{{更新时间}}', description: '简历的更新时间'},
            {category: '联系人信息', placeholder: '{{联系人关系1}}', description: '第1个联系人关系'},
            {category: '联系人信息', placeholder: '{{联系人姓名1}}', description: '第1个联系人姓名'},
            {category: '联系人信息', placeholder: '{{联系人年龄1}}', description: '第1个联系人年龄'},
            {category: '联系人信息', placeholder: '{{联系人工作1}}', description: '第1个联系人工作'},
            {category: '联系人信息', placeholder: '{{联系人关系2}}', description: '第2个联系人关系'},
            {category: '联系人信息', placeholder: '{{联系人姓名2}}', description: '第2个联系人姓名'},
            {category: '联系人信息', placeholder: '{{联系人年龄2}}', description: '第2个联系人年龄'},
            {category: '联系人信息', placeholder: '{{联系人工作2}}', description: '第2个联系人工作'},
            {category: '教育经历', placeholder: '{{教育开始1}}', description: '第1段教育经历开始时间'},
            {category: '教育经历', placeholder: '{{教育结束1}}', description: '第1段教育经历结束时间'},
            {category: '教育经历', placeholder: '{{学校1}}', description: '第1段教育经历学校名称'},
            {category: '教育经历', placeholder: '{{专业1}}', description: '第1段教育经历专业名称'},
            {category: '教育经历', placeholder: '{{学历1}}', description: '第1段教育经历学历'},
            {category: '教育经历', placeholder: '{{教育开始2}}', description: '第2段教育经历开始时间'},
            {category: '教育经历', placeholder: '{{教育结束2}}', description: '第2段教育经历结束时间'},
            {category: '教育经历', placeholder: '{{学校2}}', description: '第2段教育经历学校名称'},
            {category: '教育经历', placeholder: '{{专业2}}', description: '第2段教育经历专业名称'},
            {category: '教育经历', placeholder: '{{学历2}}', description: '第2段教育经历学历'},
            {category: '工作经历', placeholder: '{{工作开始1}}', description: '第1段工作经历开始时间'},
            {category: '工作经历', placeholder: '{{工作结束1}}', description: '第1段工作经历结束时间'},
            {category: '工作经历', placeholder: '{{工作单位1}}', description: '第1段工作经历单位名称'},
            {category: '工作经历', placeholder: '{{工作岗位1}}', description: '第1段工作经历岗位名称'},
            {category: '工作经历', placeholder: '{{工作内容1}}', description: '第1段工作经历工作内容'},
            {category: '工作经历', placeholder: '{{工作开始2}}', description: '第2段工作经历开始时间'},
            {category: '工作经历', placeholder: '{{工作结束2}}', description: '第2段工作经历结束时间'},
            {category: '工作经历', placeholder: '{{工作单位2}}', description: '第2段工作经历单位名称'},
            {category: '工作经历', placeholder: '{{工作岗位2}}', description: '第2段工作经历岗位名称'},
            {category: '工作经历', placeholder: '{{工作内容2}}', description: '第2段工作经历工作内容'},
            {category: '图片字段', placeholder: '{{头像}}', description: '简历中的头像图片'},
            {category: '图片字段', placeholder: '{{全身照}}', description: '简历中的全身照图片'},
            {category: '图片字段', placeholder: '{{身份证正面}}', description: '简历中的身份证正面图片'},
            {category: '图片字段', placeholder: '{{身份证反面}}', description: '简历中的身份证反面图片'},
            {category: '图片字段', placeholder: '{{港澳通行证正面}}', description: '简历中的港澳通行证正面图片'},
            {category: '图片字段', placeholder: '{{港澳通行证反面}}', description: '简历中的港澳通行证反面图片'},
            {category: '图片字段', placeholder: '{{补充照片1}}', description: '简历中的补充照片1'},
            {category: '图片字段', placeholder: '{{补充照片2}}', description: '简历中的补充照片2'}
        ];

        // Word模板占位符数据
        var wordPlaceholders = [
            {category: '基本信息', placeholder: '${姓名}', description: '简历中的姓名字段'},
            {category: '基本信息', placeholder: '${性别}', description: '简历中的性别字段'},
            {category: '基本信息', placeholder: '${年龄}', description: '简历中的年龄字段'},
            {category: '基本信息', placeholder: '${手机号}', description: '简历中的手机号字段'},
            {category: '基本信息', placeholder: '${最高学历}', description: '简历中的最高学历字段'},
            {category: '基本信息', placeholder: '${意向岗位}', description: '简历中的意向岗位字段'},
            {category: '基本信息', placeholder: '${申请岗位}', description: '简历中的申请岗位字段'},
            {category: '基本信息', placeholder: '${出生日期}', description: '简历中的出生日期字段'},
            {category: '基本信息', placeholder: '${身份证号}', description: '简历中的身份证号字段'},
            {category: '基本信息', placeholder: '${婚姻状况}', description: '简历中的婚姻状况字段'},
            {category: '基本信息', placeholder: '${户口所在地}', description: '简历中的户口所在地字段'},
            {category: '基本信息', placeholder: '${常住地址}', description: '简历中的常住地址字段'},
            {category: '基本信息', placeholder: '${身高}', description: '简历中的身高字段'},
            {category: '基本信息', placeholder: '${体重}', description: '简历中的体重字段'},
            {category: '基本信息', placeholder: '${民族}', description: '简历中的民族字段'},
            {category: '语言能力', placeholder: '${粤语水平}', description: '简历中的粤语水平字段'},
            {category: '语言能力', placeholder: '${国语水平}', description: '简历中的国语水平字段'},
            {category: '语言能力', placeholder: '${英语水平}', description: '简历中的英语水平字段'},
            {category: '证件信息', placeholder: '${港澳通行证}', description: '简历中的港澳通行证字段'},
            {category: '证件信息', placeholder: '${港澳通行证到期}', description: '简历中的港澳通行证到期字段'},
            {category: '工作经历', placeholder: '${海外工作经历}', description: '简历中的海外工作经历字段'},
            {category: '工作经历', placeholder: '${海外工作地区}', description: '简历中的海外工作地区字段'},
            {category: '其他信息', placeholder: '${特殊证书}', description: '简历中的特殊证书字段'},
            {category: '其他信息', placeholder: '${兴趣爱好}', description: '简历中的兴趣爱好字段'},
            {category: '其他信息', placeholder: '${自我评价}', description: '简历中的自我评价字段'},
            {category: '其他信息', placeholder: '${更新时间}', description: '简历的更新时间'},
            {category: '联系人信息', placeholder: '${联系人关系1}', description: '第1个联系人关系'},
            {category: '联系人信息', placeholder: '${联系人姓名1}', description: '第1个联系人姓名'},
            {category: '联系人信息', placeholder: '${联系人年龄1}', description: '第1个联系人年龄'},
            {category: '联系人信息', placeholder: '${联系人工作1}', description: '第1个联系人工作'},
            {category: '联系人信息', placeholder: '${联系人关系2}', description: '第2个联系人关系'},
            {category: '联系人信息', placeholder: '${联系人姓名2}', description: '第2个联系人姓名'},
            {category: '联系人信息', placeholder: '${联系人年龄2}', description: '第2个联系人年龄'},
            {category: '联系人信息', placeholder: '${联系人工作2}', description: '第2个联系人工作'},
            {category: '教育经历', placeholder: '${教育开始1}', description: '第1段教育经历开始时间'},
            {category: '教育经历', placeholder: '${教育结束1}', description: '第1段教育经历结束时间'},
            {category: '教育经历', placeholder: '${学校1}', description: '第1段教育经历学校名称'},
            {category: '教育经历', placeholder: '${专业1}', description: '第1段教育经历专业名称'},
            {category: '教育经历', placeholder: '${学历1}', description: '第1段教育经历学历'},
            {category: '教育经历', placeholder: '${教育开始2}', description: '第2段教育经历开始时间'},
            {category: '教育经历', placeholder: '${教育结束2}', description: '第2段教育经历结束时间'},
            {category: '教育经历', placeholder: '${学校2}', description: '第2段教育经历学校名称'},
            {category: '教育经历', placeholder: '${专业2}', description: '第2段教育经历专业名称'},
            {category: '教育经历', placeholder: '${学历2}', description: '第2段教育经历学历'},
            {category: '工作经历', placeholder: '${工作开始1}', description: '第1段工作经历开始时间'},
            {category: '工作经历', placeholder: '${工作结束1}', description: '第1段工作经历结束时间'},
            {category: '工作经历', placeholder: '${工作单位1}', description: '第1段工作经历单位名称'},
            {category: '工作经历', placeholder: '${工作岗位1}', description: '第1段工作经历岗位名称'},
            {category: '工作经历', placeholder: '${工作内容1}', description: '第1段工作经历工作内容'},
            {category: '工作经历', placeholder: '${工作开始2}', description: '第2段工作经历开始时间'},
            {category: '工作经历', placeholder: '${工作结束2}', description: '第2段工作经历结束时间'},
            {category: '工作经历', placeholder: '${工作单位2}', description: '第2段工作经历单位名称'},
            {category: '工作经历', placeholder: '${工作岗位2}', description: '第2段工作经历岗位名称'},
            {category: '工作经历', placeholder: '${工作内容2}', description: '第2段工作经历工作内容'},
            {category: '图片字段', placeholder: '${头像}', description: '简历中的头像图片'},
            {category: '图片字段', placeholder: '${全身照}', description: '简历中的全身照图片'},
            {category: '图片字段', placeholder: '${身份证正面}', description: '简历中的身份证正面图片'},
            {category: '图片字段', placeholder: '${身份证反面}', description: '简历中的身份证反面图片'},
            {category: '图片字段', placeholder: '${港澳通行证正面}', description: '简历中的港澳通行证正面图片'},
            {category: '图片字段', placeholder: '${港澳通行证反面}', description: '简历中的港澳通行证反面图片'},
            {category: '图片字段', placeholder: '${补充照片1}', description: '简历中的补充照片1'},
            {category: '图片字段', placeholder: '${补充照片2}', description: '简历中的补充照片2'}
        ];

        // 按分类组织占位符数据
        function organizePlaceholdersByCategory(placeholders) {
            var categories = {};
            placeholders.forEach(function(item) {
                if (!categories[item.category]) {
                    categories[item.category] = [];
                }
                categories[item.category].push(item);
            });
            return categories;
        }

        // 生成选项卡和内容
        function renderCategoryTabs(placeholders, tabsId, contentId) {
            var categories = organizePlaceholdersByCategory(placeholders);
            var tabsHtml = '';
            var contentHtml = '';
            var isFirst = true;
            
            // 添加分类选项卡
            Object.keys(categories).forEach(function(category) {
                var tabId = tabsId + '-' + category.replace(/\s+/g, '-').toLowerCase();
                var activeClass = isFirst ? 'active' : '';
                
                tabsHtml += '<li class="' + activeClass + '"><a href="#' + tabId + '" data-toggle="tab">' + category + '</a></li>';
                
                contentHtml += '<div class="tab-pane ' + activeClass + '" id="' + tabId + '">';
                contentHtml += '<div class="row">';
                
                categories[category].forEach(function(item) {
                    contentHtml += createPlaceholderCard(item);
                });
                
                contentHtml += '</div></div>';
                
                if (isFirst) {
                    isFirst = false;
                }
            });
            
            document.getElementById(tabsId).innerHTML = tabsHtml;
            document.getElementById(contentId).innerHTML = contentHtml;
        }
        
        // 创建占位符卡片
        function createPlaceholderCard(item) {
            var cardColor = getColorForCategory(item.category);
            return '<div class="col-md-4 col-sm-6 col-xs-12" style="margin-bottom: 20px;">' +
                '<div class="coupon-card" style="position: relative; background: #fff; height: 130px; border-radius: 10px; box-shadow: 0 3px 15px rgba(0,0,0,0.1); overflow: hidden;">' +
                
                // 左侧彩色条
                '<div style="position: absolute; top: 0; left: 0; height: 100%; width: 10px; background-color: #' + cardColor + ';"></div>' +
                
                // 打孔效果
                '<div style="position: absolute; top: -5px; right: 15px; width: 15px; height: 15px; background: #f0f0f0; border-radius: 50%;"></div>' +
                '<div style="position: absolute; bottom: -5px; right: 15px; width: 15px; height: 15px; background: #f0f0f0; border-radius: 50%;"></div>' +
                
                // 虚线边框效果
                '<div style="position: absolute; top: 10px; right: 10px; bottom: 10px; left: 20px; border: 1px dashed #ddd; border-left: none; border-radius: 0 10px 10px 0;"></div>' +
                
                // 内容区域
                '<div style="position: relative; padding: 15px 15px 15px 25px;">' +
                    '<h5 style="margin: 0 0 10px 0; font-size: 16px; font-weight: 600; color: #333;">' + 
                        '<code style="background-color: #f5f5f5; color: #e83e8c; padding: 4px 8px; border-radius: 4px; font-size: 15px;">' + 
                        item.placeholder + '</code>' +
                    '</h5>' +
                    '<p style="margin: 0 0 10px 0; color: #555; font-size: 13px; line-height: 1.4; height: 36px; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;">' + 
                        item.description + 
                    '</p>' +
                    '<div class="text-right">' +
                        '<button class="btn btn-xs copy-btn" data-clipboard-text="' + item.placeholder + '" ' +
                        'style="border: none; background-color: #' + cardColor + '; color: #fff; padding: 3px 10px; font-size: 12px; border-radius: 15px; cursor: pointer; transition: all 0.2s ease;">' +
                        '<i class="fa fa-copy"></i> 复制</button>' +
                    '</div>' +
                '</div>' +
                '</div>' +
            '</div>';
        }
        
        // 根据分类获取颜色
        function getColorForCategory(category) {
            var colors = {
                '基本信息': '3498db',
                '语言能力': '9b59b6',
                '证件信息': 'e67e22',
                '工作经历': '2ecc71',
                '教育经历': 'f1c40f',
                '联系人信息': 'e74c3c',
                '其他信息': '1abc9c',
                '图片字段': '34495e'
            };
            
            return colors[category] || '95a5a6'; // 默认颜色
        }

        // 初始化Excel占位符选项卡
        renderCategoryTabs(excelPlaceholders, 'excel-category-tabs', 'excel-category-content');
        
        // 初始化Word占位符选项卡
        renderCategoryTabs(wordPlaceholders, 'word-category-tabs', 'word-category-content');
        
        // 初始化复制功能
        if (typeof ClipboardJS !== 'undefined') {
            new ClipboardJS('.copy-btn').on('success', function(e) {
                var originalColor = $(e.trigger).css('background-color');
                $(e.trigger).html('<i class="fa fa-check"></i> 已复制').css('color', '#000000');
                setTimeout(function() {
                    $(e.trigger).html('<i class="fa fa-copy"></i> 复制').css('color', '#ffffff');
                }, 1500);
            });
        }
    });
</script> 

<!-- 引入clipboard.js -->
<script src="https://cdn.bootcdn.net/ajax/libs/clipboard.js/2.0.8/clipboard.min.js"></script> 