<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>

<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="type">
            <li class="active"><a href="#t-all" data-value="" data-toggle="tab">{:__('Word模板详情')}</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div class="panel-intro">
                        <div class="panel-lead"><em>Word模板详情</em>简历导出时使用的Word模板详情</div>
                    </div>
                    
                    <!-- 工具栏 -->
                    <div id="toolbar" class="toolbar">
                        <a href="{:url('template/word')}" class="btn btn-info" title="返回"><i class="fa fa-reply"></i> 返回模板列表</a>
                    </div>
                    
                    <!-- 模板信息 -->
                    <div class="template-edit-container">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="template-info-card">
                                    <div class="template-info-header">
                                        <div class="template-icon">
                                            <i class="fa fa-file-word-o"></i>
                                        </div>
                                        <div class="template-info">
                                            <h3 class="template-name">{$templateInfo.name}</h3>
                                            <div class="template-meta">
                                                <span><i class="fa fa-hdd-o"></i> {$templateInfo.size|format_bytes}</span>
                                                <span><i class="fa fa-clock-o"></i> 更新时间: {$templateInfo.modified}</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="template-actions">
                                        <a href="{:url('template/previewTemplate')}?path={$templateInfo.path|urlencode}&type=word" target="_blank" class="btn btn-info">
                                            <i class="fa fa-eye"></i> 预览模板
                                        </a>
                                        <a href="{:url('template/downloadTemplate')}?path={$templateInfo.path|urlencode}&type=word" class="btn btn-success">
                                            <i class="fa fa-download"></i> 下载模板
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 占位符列表 -->
                        <div class="row mt-20">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h3 class="panel-title">占位符列表</h3>
                                    </div>
                                    <div class="panel-body">
                                        <div class="alert alert-warning">
                                            <i class="fa fa-lightbulb-o"></i> 
                                            以下是当前模板中使用的占位符列表，Word模板中使用这些占位符来表示数据字段。点击"复制"按钮可以将占位符复制到剪贴板。
                                        </div>
                                        
                                        <div class="placeholder-search mb-20">
                                            <div class="input-group">
                                                <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                                <input type="text" class="form-control" id="placeholderSearchInput" placeholder="搜索占位符...">
                                                <span class="input-group-btn">
                                                    <button class="btn btn-default" type="button" onclick="clearSearch()">
                                                        <i class="fa fa-times"></i> 清除
                                                    </button>
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <div id="placeholderList" class="placeholder-list">
                                            <div class="text-center">
                                                <i class="fa fa-spinner fa-spin fa-2x"></i>
                                                <p class="mt-10">正在加载占位符列表...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 模板编辑页样式 */
.template-edit-container {
    padding: 15px;
}

.template-info-card {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.template-info-header {
    display: flex;
    align-items: center;
}

.template-icon {
    font-size: 48px;
    margin-right: 20px;
    color: #2b579a; /* Word蓝色 */
}

.template-name {
    font-weight: 500;
    margin-top: 0;
    margin-bottom: 5px;
    color: #333;
}

.template-meta {
    display: flex;
    flex-wrap: wrap;
    color: #777;
    font-size: 14px;
}

.template-meta span {
    margin-right: 15px;
}

.template-actions {
    display: flex;
    gap: 10px;
}

.mt-10 {
    margin-top: 10px;
}

.mt-20 {
    margin-top: 20px;
}

/* 占位符列表样式 */
.placeholder-list {
    margin-top: 15px;
}

.placeholder-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.placeholder-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    margin-bottom: 10px;
    background: #f9f9f9;
    border-radius: 4px;
    border-left: 4px solid #2b579a; /* Word蓝色 */
    transition: all 0.2s ease;
    width: calc(33.333% - 20px);
    margin-left: 10px;
    margin-right: 10px;
    box-sizing: border-box;
    min-height: 70px;
}

@media (max-width: 1200px) {
    .placeholder-item {
        width: calc(50% - 20px);
    }
}

@media (max-width: 768px) {
    .placeholder-item {
        width: calc(100% - 20px);
    }
}

.placeholder-item:hover {
    background: #f0f0f0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.placeholder-info {
    flex: 1;
    min-width: 0;
    padding-right: 10px;
}

.placeholder-name {
    font-family: monospace;
    font-weight: bold;
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.placeholder-desc {
    color: #777;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 占位符分类 */
.placeholder-category {
    margin-top: 25px;
    margin-bottom: 15px;
    font-weight: 500;
    color: #555;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

.placeholder-category:first-child {
    margin-top: 0;
}

.placeholder-row {
    margin-bottom: 5px;
}

/* 搜索框样式 */
.placeholder-search {
    margin-bottom: 15px;
}

.mb-20 {
    margin-bottom: 20px;
}

/* 占位符计数 */
.placeholder-count {
    font-size: 12px;
    color: #777;
    margin-left: 10px;
}

/* 无匹配结果样式 */
.no-match {
    padding: 20px;
    text-align: center;
    background: #f9f9f9;
    border-radius: 4px;
    color: #777;
}

/* 高亮搜索结果 */
.highlight {
    background-color: #ffeb3b;
    color: #000;
    padding: 0 2px;
    border-radius: 2px;
}

.placeholder-copy {
    flex-shrink: 0;
    margin-left: 5px;
}
</style>

<script>
// 全局变量，用于跟踪加载状态
window.templateLoaded = false;
// 全局变量，存储所有占位符
let allPlaceholders = [];

// 确保在DOM完全加载后执行
document.addEventListener("DOMContentLoaded", function() {
    loadPlaceholders();
});



$(function() {
    // 加载占位符列表
    loadPlaceholders();
    

    
    // 搜索框事件处理
    $('#placeholderSearchInput').on('input', function() {
        filterPlaceholders($(this).val().trim());
    });
});

// 加载占位符列表
function loadPlaceholders() {
    const placeholderContainer = document.getElementById('placeholderList');
    
    $.ajax({
        url: '{:url("admin/template/getWordPlaceholders")}',
        type: 'GET',
        data: {path: '{$templateInfo.path}'},
        dataType: 'json',
        success: function(res) {
            if (res.code === 1) {
                // 保存所有占位符到全局变量
                allPlaceholders = res.data || [];
                
                // 渲染占位符列表
                if (allPlaceholders.length > 0) {
                    renderPlaceholders(allPlaceholders);
                } else {
                    placeholderContainer.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> 
                        当前模板中未找到任何占位符。您可以在Word中添加格式为\${字段名}的占位符，然后保存模板。
                    </div>`;
                }
            } else {
                placeholderContainer.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fa fa-exclamation-circle"></i> ${res.msg || '获取占位符列表失败'}
                </div>`;
            }
        },
        error: function() {
            placeholderContainer.innerHTML = `
            <div class="alert alert-danger">
                <i class="fa fa-times-circle"></i> 加载占位符列表失败
                <button class="btn btn-xs btn-danger ml-10" onclick="loadPlaceholders()">
                    <i class="fa fa-refresh"></i> 重试
                </button>
            </div>`;
        }
    });
}

// 渲染占位符列表
function renderPlaceholders(placeholders, searchTerm = '') {
    const placeholderContainer = document.getElementById('placeholderList');
    
    // 按类别分组占位符
    const categories = {
        '基本信息': [],
        '联系人信息': [],
        '教育经历': [],
        '工作经历': [],
        '图片': [],
        '其他': []
    };
    
    placeholders.forEach(placeholder => {
        // 使用服务器返回的category属性来分类
        const category = placeholder.category || '其他';
        
        // 确保分类存在
        if (!categories[category]) {
            categories[category] = [];
        }
        
        // 添加到对应分类
        categories[category].push(placeholder);
    });
    
    let html = '';
    let totalCount = 0;
    
    // 按照指定顺序显示分类
    const categoryOrder = ['基本信息', '联系人信息', '教育经历', '工作经历', '图片', '其他'];
    
    // 遍历分类生成HTML
    categoryOrder.forEach(category => {
        const categoryPlaceholders = categories[category] || [];
        
        if (categoryPlaceholders.length > 0) {
            // 对每个类别内的占位符按名称排序
            categoryPlaceholders.sort((a, b) => a.name.localeCompare(b.name));
            
            html += `<h4 class="placeholder-category">${category} <span class="placeholder-count">(${categoryPlaceholders.length}个)</span></h4>`;
            html += '<div class="placeholder-row">';
            
            categoryPlaceholders.forEach(placeholder => {
                // 高亮搜索词
                let displayName = placeholder.name;
                let displayDesc = placeholder.description;
                
                if (searchTerm) {
                    const regex = new RegExp(`(${escapeRegExp(searchTerm)})`, 'gi');
                    displayName = displayName.replace(regex, '<span class="highlight">$1</span>');
                    displayDesc = displayDesc.replace(regex, '<span class="highlight">$1</span>');
                }
                
                html += `
                <div class="placeholder-item">
                    <div class="placeholder-info">
                        <div class="placeholder-name">${displayName}</div>
                        <div class="placeholder-desc">${displayDesc}</div>
                    </div>
                    <button class="btn btn-xs btn-default placeholder-copy" data-placeholder="${placeholder.name.replace(/"/g, '&quot;')}">
                        <i class="fa fa-copy"></i>
                    </button>
                </div>`;
                
                totalCount++;
            });
            
            html += '</div>';
        }
    });
    
    if (totalCount === 0) {
        html = `
        <div class="no-match">
            <i class="fa fa-search"></i>
            <p>没有找到匹配的占位符</p>
        </div>`;
    }
    
    placeholderContainer.innerHTML = html;
    
    // 添加复制按钮的点击事件处理
    document.querySelectorAll('.placeholder-copy').forEach(button => {
        button.addEventListener('click', function() {
            const placeholder = this.getAttribute('data-placeholder');
            copyPlaceholder(placeholder);
        });
    });
}

// 过滤占位符
function filterPlaceholders(searchTerm) {
    if (!searchTerm) {
        // 如果搜索词为空，显示所有占位符
        renderPlaceholders(allPlaceholders);
        return;
    }
    
    // 过滤包含搜索词的占位符
    const filtered = allPlaceholders.filter(placeholder => {
        return placeholder.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
               placeholder.description.toLowerCase().includes(searchTerm.toLowerCase());
    });
    
    // 渲染过滤后的占位符
    renderPlaceholders(filtered, searchTerm);
}

// 清除搜索
function clearSearch() {
    $('#placeholderSearchInput').val('');
    renderPlaceholders(allPlaceholders);
}

// 转义正则表达式特殊字符
function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

// 复制占位符到剪贴板
function copyPlaceholder(text) {
    // 确保占位符格式正确
    text = text.replace(/\s+/g, ''); // 移除所有空格
    
    // 创建临时输入框
    const input = document.createElement('input');
    input.value = text;
    document.body.appendChild(input);
    input.select();
    document.execCommand('copy');
    document.body.removeChild(input);
    
    // 显示提示
    Layer.msg('已复制: ' + text, {icon: 1});
}
</script> 