<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\Resume as ResumeModel;
use app\common\model\ResumeEducation;
use app\common\model\ResumeWork;
use app\common\model\JobApply;
use app\common\model\Job as JobModel;
use think\Db;
use think\Exception;
use think\Validate;

/**
 * 简历相关接口
 */
class Resume extends Api
{
    // 无需登录的接口
    protected $noNeedLogin = [];
    
    // 无需鉴权的接口
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
    }
    
    /**
     * 简历列表页面
     */
    public function list()
    {
        // 检查登录状态
        if (!$this->auth->isLogin()) {
            $this->error('请先登录', url('/pages/user/login'));
        }
        
        // 记录请求方法
        \think\Log::write('请求方法: ' . $this->request->method(), 'debug');
        
        // 获取当前登录用户信息
        $user = $this->auth->getUser();
        \think\Log::write('当前用户信息: ' . json_encode($user, JSON_UNESCAPED_UNICODE), 'debug');
        
        // 初始化两个结果集
        $resumeList = [];
        $otherResumeList = [];
        
        if ($user) {
            // 1. 按用户ID查询
            $userIdFilter = ['user_id' => $user->id];
            $resumeList = model('Resume')
                ->where($userIdFilter)
                ->order('create_time', 'desc')
            ->select();
        
                \think\Log::write('用户ID查询结果数量: ' . count($resumeList), 'debug');
            
            // 2. 按realname和mobile查询
            $namePhoneFilter = [];
            if (!empty($user->realname)) {
                $namePhoneFilter['name'] = ['like', "%{$user->realname}%"];
            }
            if (!empty($user->mobile)) {
                $namePhoneFilter['phone'] = ['like', "%{$user->mobile}%"];
            }
            
            if (!empty($namePhoneFilter)) {
                $otherResumeList = model('Resume')
                    ->where($namePhoneFilter)
                    ->where('user_id', '<>', $user->id) // 排除已通过user_id查询到的结果
                    ->order('create_time', 'desc')
            ->select();
        
                \think\Log::write('姓名电话查询结果数量: ' . count($otherResumeList), 'debug');
            }
        }
        
        // 合并结果并分页
        $allResumes = array_merge($resumeList, $otherResumeList);
        $pageSize = 10;
        $currentPage = input('page', 1);
        $total = count($allResumes);
        $start = ($currentPage - 1) * $pageSize;
        $paginatedResumes = array_slice($allResumes, $start, $pageSize);
        
        // 为每个简历添加匹配方式标记
        foreach ($paginatedResumes as &$resume) {
            $resume['match_type'] = in_array($resume['id'], array_column($resumeList, 'id')) ? 'user_id' : 'name_phone';
        }
        
        // 创建分页对象
        $page = \think\paginator\driver\Bootstrap::make($paginatedResumes, $pageSize, $currentPage, $total, false, [
            'path' => request()->baseUrl(),
            'query' => request()->get()
        ]);
        
        // 返回JSON数据而不是渲染视图
        return $this->success('获取成功', [
            'resumeList' => $paginatedResumes,
            'page' => $page,
            'totalCount' => $total,
            'userIdCount' => count($resumeList),
            'namePhoneCount' => count($otherResumeList)
        ]);
    }

    
    /**
     * 删除简历
     */
    public function delete($id = null)
    {
        if ($id) {
            $user = $this->auth->getUser();
            $resume = model('Resume')->where('id', $id)->find();
            
            if (!$resume) {
                $this->error('简历不存在');
            }
            
            // 验证权限：检查用户ID或姓名和手机号是否匹配
            if ($resume->user_id != $user->id && ($resume->name != $user->realname || $resume->phone != $user->mobile)) {
                $this->error('没有权限删除此简历');
            }
            
            // 删除简历相关文件
            $fields = ['avatar', 'full_body_photo', 'id_card_front', 'id_card_back', 
                       'hk_macau_passport_front', 'hk_macau_passport_back'];
            foreach ($fields as $field) {
                if ($resume[$field]) {
                    @unlink(ROOT_PATH . 'public' . $resume[$field]);
                }
            }
            
            // 删除附加照片
            if ($resume['additional_photos']) {
                $additionalPhotos = json_decode($resume['additional_photos'], true);
                if (is_array($additionalPhotos)) {
                    foreach ($additionalPhotos as $photo) {
                        @unlink(ROOT_PATH . 'public' . $photo);
                    }
                }
            }
            
            // 删除数据库记录
            if ($resume->delete()) {
                $this->success('删除成功');
            } else {
                $this->error('删除失败');
            }
        }
        $this->error('参数错误');
    }

/**
     * 查看简历详情
     */
    public function view($id = null)
    {
        if (!$id) {
            $this->error('参数错误');
        }
        
        $resume = model('Resume')->where('id', $id)->find();
        if (!$resume) {
            $this->error('简历不存在');
        }
        
        // 检查权限：验证用户ID或姓名和手机号是否匹配
        $user = $this->auth->getUser();
        if (!$user || ($resume->user_id != $user->id && ($resume->name != $user->realname || $resume->phone != $user->mobile))) {
            $this->error('您没有权限查看此简历');
        }
        
        // 处理简历数据
        $resumeData = $resume->toArray();
        
        // 处理教育经历
        if ($resumeData['education_school']) {
            $schools = explode('|', $resumeData['education_school']);
            $starts = explode('|', $resumeData['education_start'] ?? '');
            $ends = explode('|', $resumeData['education_end'] ?? '');
            $majors = explode('|', $resumeData['education_major'] ?? '');
            $degrees = explode('|', $resumeData['graduation_education'] ?? '');
            
            $educationHistory = [];
            foreach ($schools as $index => $school) {
                if (!empty($school)) {
                    $educationHistory[] = [
                        'school' => $school,
                        'start_date' => $starts[$index] ?? '',
                        'end_date' => $ends[$index] ?? '',
                        'major' => $majors[$index] ?? '',
                        'degree' => $degrees[$index] ?? ''
                    ];
                }
            }
            $resumeData['education_history'] = $educationHistory;
        }
        
        // 处理工作经历
        if ($resumeData['job_company']) {
            $companies = explode('|', $resumeData['job_company']);
            $starts = explode('|', $resumeData['job_start'] ?? '');
            $ends = explode('|', $resumeData['job_end'] ?? '');
            $positions = explode('|', $resumeData['job_position'] ?? '');
            $descriptions = explode('|', $resumeData['job_description'] ?? '');
            
            $workHistory = [];
            foreach ($companies as $index => $company) {
                if (!empty($company)) {
                    $workHistory[] = [
                        'company' => $company,
                        'start_date' => $starts[$index] ?? '',
                        'end_date' => $ends[$index] ?? '',
                        'position' => $positions[$index] ?? '',
                        'description' => $descriptions[$index] ?? ''
                    ];
                }
            }
            $resumeData['work_history'] = $workHistory;
        }
        
        // 处理联系人信息
        if ($resumeData['contact_name']) {
            $names = explode('|', $resumeData['contact_name']);
            $relations = explode('|', $resumeData['contact_relation'] ?? '');
            $ages = explode('|', $resumeData['contact_age'] ?? '');
            $jobs = explode('|', $resumeData['contact_job'] ?? '');
            
            $contacts = [];
            foreach ($names as $index => $name) {
                if (!empty($name)) {
                    $contacts[] = [
                        'name' => $name,
                        'relation' => $relations[$index] ?? '',
                        'age' => $ages[$index] ?? '',
                        'job' => $jobs[$index] ?? ''
                    ];
                }
            }
            $resumeData['contacts'] = $contacts;
        }
        
        // 处理附加照片
        if ($resumeData['additional_photos']) {
            $resumeData['additional_photos_array'] = explode(',', $resumeData['additional_photos']);
        } else {
            $resumeData['additional_photos_array'] = [];
        }
        
        return $this->success('获取成功', $resumeData);
    }


    /**
     * 编辑简历
     */
    public function edit($id)
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录', url('/user/login'));
        }

        $resume = model('Resume')->where('id', $id)->find();
        if (!$resume) {
            $this->error('简历不存在');
        }
        
        // 验证权限：检查用户ID或姓名和手机号是否匹配
        $user = $this->auth->getUser();
        if ($resume->user_id != $user->id && ($resume->name != $user->realname || $resume->phone != $user->mobile)) {
            $this->error('无权操作');
        }

        // 显式传递OCR配置到模板
        $this->assign('ocr_enabled', config('site.baidu_ocr_enabled'));

        if ($this->request->isPost()) {
            $data = $this->request->post();
            
            // 处理户口所在地
            if (isset($data['hukou_province']) && isset($data['hukou_city'])) {
                $hukou_location = [];
                // 获取省份名称
                $province = Db::name('area')->where('id', $data['hukou_province'])->value('name');
                // 获取城市名称
                $city = Db::name('area')->where('id', $data['hukou_city'])->value('name');
                
                if (!empty($province)) $hukou_location[] = $province;
                if (!empty($city)) $hukou_location[] = $city;
                $data['hukou_location'] = implode('/', $hukou_location);
            }

            // 处理常住地址
            if (isset($data['residence_province']) && isset($data['residence_city']) && isset($data['residence_district'])) {
                $residence_address = [];
                // 获取省份名称
                $province = Db::name('area')->where('id', $data['residence_province'])->value('name');
                // 获取城市名称
                $city = Db::name('area')->where('id', $data['residence_city'])->value('name');
                // 获取区县名称
                $district = Db::name('area')->where('id', $data['residence_district'])->value('name');
                
                if (!empty($province)) $residence_address[] = $province;
                if (!empty($city)) $residence_address[] = $city;
                if (!empty($district)) $residence_address[] = $district;
                
                $data['residence_address'] = implode('/', $residence_address);
            }
            
            // 处理文件上传
            $files = $this->request->file();
            $uploadDir = '/uploads/resume/' . date('Ymd') . '/';
            
            // 确保上传目录存在
            if (!is_dir(ROOT_PATH . 'public' . $uploadDir)) {
                mkdir(ROOT_PATH . 'public' . $uploadDir, 0755, true);
            }

            // 处理单个文件上传，如果没有上传新文件则保留原文件
            $fileFields = [
                'avatar' => 'old_avatar',
                'full_body_photo' => 'old_full_body_photo',
                'id_card_front' => 'old_id_card_front',
                'id_card_back' => 'old_id_card_back',
                'hk_macau_passport_front' => 'old_hk_macau_passport_front',
                'hk_macau_passport_back' => 'old_hk_macau_passport_back'
            ];
            
            foreach ($fileFields as $field => $oldField) {
                if (isset($files[$field]) && $files[$field]->isValid()) {
                    // 上传了新文件
                    $data[$field] = $this->uploadFile($files[$field], $uploadDir);
                    // 删除旧文件
                    if (!empty($resume[$field])) {
                        @unlink(ROOT_PATH . 'public' . $resume[$field]);
                    }
                } elseif (isset($data[$oldField])) {
                    // 检查是否要删除旧文件
                    if (empty($data[$oldField])) {
                        // 如果旧文件路径为空，说明要删除文件
                        if (!empty($resume[$field])) {
                            @unlink(ROOT_PATH . 'public' . $resume[$field]);
                        }
                        $data[$field] = ''; // 设置为空字符串
                    } else {
                        // 保留原文件路径
                        $data[$field] = $data[$oldField];
                    }
                }
                // 移除临时字段
                if (isset($data[$oldField])) {
                    unset($data[$oldField]);
                }
            }
            
            // 处理多文件上传
            if (isset($files['additional_photos']) && !empty($files['additional_photos'][0])) {
                $additionalPhotos = [];
                foreach ($files['additional_photos'] as $photo) {
                    if ($photo->isValid()) {
                        $additionalPhotos[] = $this->uploadFile($photo, $uploadDir);
                    }
                }
                $data['additional_photos'] = implode(',', $additionalPhotos);
            } elseif (isset($data['old_additional_photos']) && !empty($data['old_additional_photos'])) {
                // 没有上传新的附加照片，保留原来的
                $data['additional_photos'] = $data['old_additional_photos'];
            }
            // 移除临时字段
            if (isset($data['old_additional_photos'])) {
                unset($data['old_additional_photos']);
            }
            
            // 处理数组字段
            $arrayFields = ['contact_name', 'contact_relation', 'contact_age', 'contact_job', 
                           'graduation_education', 'education_school', 'education_major', 
                           'education_start', 'education_end', 'job_company', 'job_position', 
                           'job_start', 'job_end', 'job_description'];
            
            foreach ($arrayFields as $field) {
                if (isset($data[$field]) && is_array($data[$field])) {
                    $data[$field] = implode('|', $data[$field]);
                }
            }

            // 移除不存在的字段
            unset($data['hukou_province']);
            unset($data['hukou_city']);
            unset($data['residence_province']);
            unset($data['residence_city']);
            unset($data['residence_district']);
            
            $resume->save($data);
            $this->success('保存成功', url('/pages/resume/index'));
        }

        // 处理地址数据用于显示
        if ($resume->hukou_location) {
            $hukouParts = explode('/', $resume->hukou_location);
            if (count($hukouParts) >= 2) {
                // 查找省份ID
                $province = Db::name('area')->where('name', $hukouParts[0])->where('level', 1)->find();
                if ($province) {
                    $resume->hukou_province = $province['id'];
                    // 查找城市ID
                    $city = Db::name('area')->where('name', $hukouParts[1])->where('pid', $province['id'])->find();
                    if ($city) {
                        $resume->hukou_city = $city['id'];
                    }
                }
            }
        }

        if ($resume->residence_address) {
            $residenceParts = explode('/', $resume->residence_address);
            if (count($residenceParts) >= 3) {
                // 查找省份ID
                $province = Db::name('area')->where('name', $residenceParts[0])->where('level', 1)->find();
                if ($province) {
                    $resume->residence_province = $province['id'];
                    // 查找城市ID
                    $city = Db::name('area')->where('name', $residenceParts[1])->where('pid', $province['id'])->find();
                    if ($city) {
                        $resume->residence_city = $city['id'];
                        // 查找区县ID
                        $district = Db::name('area')->where('name', $residenceParts[2])->where('pid', $city['id'])->find();
                        if ($district) {
                            $resume->residence_district = $district['id'];
                        }
                    }
                }
            }
        }

        $this->assign('resume', $resume);
        return $this->fetch();
    }

    /**
     * 扫描身份证
     */
    public function scanIdCard()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }

        // 检查OCR功能是否启用
        if (!config('site.baidu_ocr_enabled')) {
            return json(['code' => 0, 'msg' => 'OCR功能未启用']);
        }

        $file = $this->request->file('id_card');
        if (!$file) {
            return json(['code' => 0, 'msg' => '请上传身份证图片']);
        }

        // 验证文件
        $info = $file->validate(['size'=>1024*1024*2, 'ext'=>'jpg,jpeg,png'])->move(ROOT_PATH . 'public' . DS . 'uploads' . DS . 'temp');
        if (!$info) {
            return json(['code' => 0, 'msg' => $file->getError()]);
        }

        $filePath = $info->getPathname();

        try {
            // 调用百度 OCR API 进行识别
            $result = $this->baiduOcr($filePath);
            
            // 删除临时文件
            @unlink($filePath);
            
            if ($result) {
                return json(['code' => 1, 'msg' => '识别成功', 'data' => $result]);
            } else {
                return json(['code' => 0, 'msg' => '识别失败']);
            }
        } catch (Exception $e) {
            // 删除临时文件
            @unlink($filePath);
            // 记录错误日志
            \think\Log::write('身份证识别错误：' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }
    
    /**
     * 调用百度 OCR API
     */
    protected function baiduOcr($imagePath)
    {
        // 百度 OCR API 配置
        $apiKey = config('site.baidu_ocr_api_key');
        $secretKey = config('site.baidu_ocr_secret_key');
        $enabled = config('site.baidu_ocr_enabled');
        
        \think\Log::write('OCR配置：' . json_encode([
            'enabled' => $enabled,
            'api_key' => $apiKey,
            'secret_key' => $secretKey
        ], JSON_UNESCAPED_UNICODE), 'debug');
        
        if (!$enabled) {
            throw new Exception('OCR功能未启用');
        }
        
        if (empty($apiKey) || empty($secretKey)) {
            throw new Exception('请先配置百度 OCR API 密钥');
        }

        // 获取 access token
        $tokenUrl = "https://aip.baidubce.com/oauth/2.0/token?grant_type=client_credentials&client_id={$apiKey}&client_secret={$secretKey}";
        
        // 记录请求信息
        \think\Log::write('请求百度 token URL：' . $tokenUrl, 'debug');
        
        $tokenResponse = file_get_contents($tokenUrl);
        if ($tokenResponse === false) {
            throw new Exception('获取 access token 失败：无法连接到百度服务器');
        }
        
        $tokenData = json_decode($tokenResponse, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('解析 access token 响应失败：' . json_last_error_msg());
        }
        
        if (!isset($tokenData['access_token'])) {
            throw new Exception('获取百度 OCR access token 失败：' . ($tokenData['error_description'] ?? '未知错误'));
        }

        $accessToken = $tokenData['access_token'];
        
        // 调用身份证识别接口
        $url = "https://aip.baidubce.com/rest/2.0/ocr/v1/idcard?access_token={$accessToken}";
        
        // 读取图片文件
        $image = file_get_contents($imagePath);
        if ($image === false) {
            throw new Exception('读取图片文件失败');
        }
        
        $base64 = base64_encode($image);
        
        // 发送请求
        $postData = [
            'image' => $base64,
            'id_card_side' => 'front' // 默认识别正面
        ];
        
        $options = [
            'http' => [
                'method' => 'POST',
                'header' => 'Content-Type: application/x-www-form-urlencoded',
                'content' => http_build_query($postData),
                'timeout' => 30 // 设置超时时间
            ]
        ];
        
        $context = stream_context_create($options);
        $response = file_get_contents($url, false, $context);
        
        if ($response === false) {
            throw new Exception('调用百度 OCR API 失败：无法连接到服务器');
        }
        
        $result = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('解析百度 OCR 响应失败：' . json_last_error_msg());
        }
        
        if (isset($result['error_code'])) {
            throw new Exception('身份证识别失败：' . ($result['error_msg'] ?? '未知错误'));
        }
        
        // 处理识别结果
        $data = [];
        if (isset($result['words_result'])) {
            $words = $result['words_result'];
            
            // 处理出生日期格式
            $birthDate = $words['出生']['words'] ?? '';
            if (!empty($birthDate)) {
                // 将"YYYY年MM月DD日"格式转换为"YYYY-MM-DD"格式
                $birthDate = str_replace(['年', '月', '日'], ['-', '-', ''], $birthDate);
            }
            
            // 提取信息
            $data = [
                'name' => $words['姓名']['words'] ?? '',
                'gender' => $words['性别']['words'] ?? '',
                'id_card' => $words['公民身份号码']['words'] ?? '',
                'birth_date' => $birthDate,
                'age' => $this->calculateAge($birthDate)
            ];
        }
        
        return $data;
    }

    /**
     * 计算年龄
     */
    protected function calculateAge($birthDate)
    {
        if (empty($birthDate)) {
            return '';
        }
        
        // 将出生日期转换为时间戳
        $birthTimestamp = strtotime($birthDate);
        if ($birthTimestamp === false) {
            return '';
        }
        
        // 计算年龄
        $age = date('Y') - date('Y', $birthTimestamp);
        if (date('md') < date('md', $birthTimestamp)) {
            $age--;
        }
        
        return $age;
    }

    /**
     * 生成工作内容
     */
    public function generateJobDescription()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }

        // 检查是否启用了OpenAI功能
        if (config('site.openai_enabled') != '1') {
            return json(['code' => 0, 'msg' => 'AI功能未启用']);
        }

        $data = $this->request->post();
        if (empty($data['company']) || empty($data['position']) || empty($data['word_count'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        try {
            // 构建提示词
            $prompt = "请根据以下工作信息生成一段工作内容描述：\n";
            $prompt .= "工作单位：{$data['company']}\n";
            $prompt .= "工作岗位：{$data['position']}\n";
            $prompt .= "要求：\n";
            $prompt .= "1. 只描述具体的工作内容和职责，不要包含工作单位和岗位名称\n";
            $prompt .= "2. 描述要专业、具体，突出工作职责和成就\n";
            $prompt .= "3. 字数控制在{$data['word_count']}字左右\n";
            $prompt .= "4. 语言要简洁、专业\n";
            $prompt .= "5. 直接输出工作内容，不要加任何前缀或说明\n";

            // 获取API配置
            $apiKey = config('site.openai_api_key');
            $apiUrl = config('site.openai_api_url');
            $model = config('site.openai_model');
            
            if (empty($apiKey) || empty($apiUrl) || empty($model)) {
                return json(['code' => 0, 'msg' => 'API配置不完整']);
            }

            $ch = curl_init($apiUrl . '/chat/completions');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ]);

            $postData = [
                'model' => $model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => '你是一个专业的简历顾问，擅长撰写工作内容描述。请直接输出工作内容，不要包含工作单位和岗位名称，也不要加任何前缀或说明。'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.7,
                'max_tokens' => 500,
                'stream' => false
            ];

            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_errno($ch)) {
                throw new Exception('API请求错误: ' . curl_error($ch));
            }
            
            curl_close($ch);

            if ($httpCode !== 200) {
                throw new Exception('API请求失败，状态码：' . $httpCode . '，响应：' . $response);
            }

            $result = json_decode($response, true);
            if (!isset($result['choices'][0]['message']['content'])) {
                throw new Exception('API返回数据格式错误：' . $response);
            }

            $content = trim($result['choices'][0]['message']['content']);
            return json(['code' => 1, 'msg' => '生成成功', 'data' => $content]);

        } catch (Exception $e) {
            \think\Log::write('生成工作内容失败：' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }
    
    /**
     * 生成自我评价
     */
    public function generateSelfEvaluation()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }

        // 检查是否启用了OpenAI功能
        if (config('site.openai_enabled') != '1') {
            return json(['code' => 0, 'msg' => 'AI功能未启用']);
        }

        $data = $this->request->post();
        if (empty($data['content']) || empty($data['word_count'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        try {
            // 构建提示词
            $prompt = "请根据以下内容生成一段自我评价：\n";
            $prompt .= "原始内容：{$data['content']}\n";
            $prompt .= "要求：\n";
            $prompt .= "1. 保持原意的同时，使表达更加专业、得体\n";
            $prompt .= "2. 突出个人优势和特点\n";
            $prompt .= "3. 字数控制在{$data['word_count']}字左右\n";
            $prompt .= "4. 语言要简洁、专业\n";
            $prompt .= "5. 直接输出润色后的内容，不要加任何前缀或说明\n";

            // 获取API配置
            $apiKey = config('site.openai_api_key');
            $apiUrl = config('site.openai_api_url');
            $model = config('site.openai_model');
            
            if (empty($apiKey) || empty($apiUrl) || empty($model)) {
                return json(['code' => 0, 'msg' => 'API配置不完整']);
            }

            $ch = curl_init($apiUrl . '/chat/completions');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ]);

            $postData = [
                'model' => $model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => '你是一个专业的简历顾问，擅长润色自我评价。请直接输出润色后的内容，不要加任何前缀或说明。'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.7,
                'max_tokens' => 500,
                'stream' => false
            ];

            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_errno($ch)) {
                throw new Exception('API请求错误: ' . curl_error($ch));
            }
            
            curl_close($ch);

            if ($httpCode !== 200) {
                throw new Exception('API请求失败，状态码：' . $httpCode . '，响应：' . $response);
            }

            $result = json_decode($response, true);
            if (!isset($result['choices'][0]['message']['content'])) {
                throw new Exception('API返回数据格式错误：' . $response);
            }

            $content = trim($result['choices'][0]['message']['content']);
            return json(['code' => 1, 'msg' => '生成成功', 'data' => $content]);

        } catch (Exception $e) {
            \think\Log::write('生成自我评价失败：' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }
    
    /**
     * 生成兴趣爱好
     */
    public function generateHobbies()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }

        // 检查是否启用了OpenAI功能
        if (config('site.openai_enabled') != '1') {
            return json(['code' => 0, 'msg' => 'AI功能未启用']);
        }

        $data = $this->request->post();
        if (empty($data['content']) || empty($data['word_count'])) {
            return json(['code' => 0, 'msg' => '参数错误']);
        }

        try {
            // 构建提示词
            $prompt = "请根据以下内容生成一段兴趣爱好描述：\n";
            $prompt .= "原始内容：{$data['content']}\n";
            $prompt .= "要求：\n";
            $prompt .= "1. 保持原意的同时，使表达更加生动、有趣\n";
            $prompt .= "2. 突出个人特点和积极向上的态度\n";
            $prompt .= "3. 字数控制在{$data['word_count']}字左右\n";
            $prompt .= "4. 语言要简洁、自然\n";
            $prompt .= "5. 直接输出润色后的内容，不要加任何前缀或说明\n";

            // 获取API配置
            $apiKey = config('site.openai_api_key');
            $apiUrl = config('site.openai_api_url');
            $model = config('site.openai_model');
            
            if (empty($apiKey) || empty($apiUrl) || empty($model)) {
                return json(['code' => 0, 'msg' => 'API配置不完整']);
            }

            $ch = curl_init($apiUrl . '/chat/completions');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ]);

            $postData = [
                'model' => $model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => '你是一个专业的简历顾问，擅长润色兴趣爱好描述。请直接输出润色后的内容，不要加任何前缀或说明。'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.7,
                'max_tokens' => 500,
                'stream' => false
            ];

            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_errno($ch)) {
                throw new Exception('API请求错误: ' . curl_error($ch));
            }
            
            curl_close($ch);

            if ($httpCode !== 200) {
                throw new Exception('API请求失败，状态码：' . $httpCode . '，响应：' . $response);
            }

            $result = json_decode($response, true);
            if (!isset($result['choices'][0]['message']['content'])) {
                throw new Exception('API返回数据格式错误：' . $response);
            }

            $content = trim($result['choices'][0]['message']['content']);
            return json(['code' => 1, 'msg' => '生成成功', 'data' => $content]);

        } catch (Exception $e) {
            \think\Log::write('生成兴趣爱好失败：' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }
    

    
    /**
     * 获取简历统计数据
     */
    public function count()
    {
        $user = $this->auth->getUser();
        if (!$user) {
            $this->error('请先登录');
        }

        try {
            // 1. 按用户ID查询数量
            $userIdCount = Db::name('resume')
                ->where('user_id', $user->id)
                ->count();

            // 2. 按姓名和手机号查询数量（排除已通过user_id查询到的）
            $namePhoneCount = 0;
            $namePhoneFilter = [];
            
            if (!empty($user->realname)) {
                $namePhoneFilter['name'] = ['like', "%{$user->realname}%"];
            }
            if (!empty($user->mobile)) {
                $namePhoneFilter['phone'] = ['like', "%{$user->mobile}%"];
            }
            
            if (!empty($namePhoneFilter)) {
                $namePhoneCount = Db::name('resume')
                    ->where($namePhoneFilter)
                    ->where('user_id', '<>', $user->id) // 排除已通过user_id查询到的
                    ->count();
            }

            // 返回统计结果
            $this->success('获取成功', [
                'total' => $userIdCount + $namePhoneCount,
                'user_id_count' => $userIdCount,
                'name_phone_count' => $namePhoneCount
            ]);
        } catch (Exception $e) {
            $this->error('获取统计数据失败：' . $e->getMessage());
        }
    }

    /**
     * 更新简历（小程序接口）
     */
    public function update($id = null)
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        if (!$id) {
            $this->error('参数错误');
        }

        $resume = model('Resume')->where('id', $id)->find();
        if (!$resume) {
            $this->error('简历不存在');
        }
        
        // 验证权限：检查用户ID或姓名和手机号是否匹配
        $user = $this->auth->getUser();
        if ($resume->user_id != $user->id && ($resume->name != $user->realname || $resume->phone != $user->mobile)) {
            $this->error('无权操作');
        }

        if (!$this->request->isPost()) {
            $this->error('请求方法错误');
        }

        \think\Log::write('收到的数据: ' . json_encode($this->request->post(), JSON_UNESCAPED_UNICODE), 'debug');
        
        $data = $this->request->post();
        
        // 数据验证
        $validate = new Validate([
            'name' => 'require',
            'gender' => 'require',
            'phone' => 'require'
        ]);
        
        $validate->message([
            'name.require' => '姓名不能为空',
            'gender.require' => '性别不能为空',
            'phone.require' => '手机号码不能为空'
        ]);
        
        if (!$validate->check($data)) {
            $this->error($validate->getError());
        }
        
        try {
            Db::startTrans();
            
            // 更新简历
            $resume->allowField(true)->save($data);
            
            Db::commit();
            $this->success('保存成功');
        } catch (Exception $e) {
            Db::rollback();
            \think\Log::write('保存简历异常：' . $e->getMessage(), 'error');
            $this->error('保存失败：' . $e->getMessage());
        }
    }

    /**
     * 添加简历（小程序接口）
     */
    public function create()
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        if (!$this->request->isPost()) {
            $this->error('请求方法错误');
        }

        \think\Log::write('创建简历请求数据: ' . json_encode($this->request->post(), JSON_UNESCAPED_UNICODE), 'debug');
        
        $data = $this->request->post();
        
        // 数据验证
        $validate = new Validate([
            'name' => 'require',
            'gender' => 'require',
            'phone' => 'require'
        ]);
        
        $validate->message([
            'name.require' => '姓名不能为空',
            'gender.require' => '性别不能为空',
            'phone.require' => '手机号码不能为空'
        ]);
        
        if (!$validate->check($data)) {
            $this->error($validate->getError());
        }
        
        try {
            Db::startTrans();
            
            // 获取当前用户
            $user = $this->auth->getUser();
            
            // 设置用户ID
            $data['user_id'] = $user->id;
            
            // 处理户口所在地
            if (isset($data['hukou_province']) && isset($data['hukou_city'])) {
                $hukou_location = [];
                // 获取省份名称
                $province = Db::name('area')->where('id', $data['hukou_province'])->value('name');
                // 获取城市名称
                $city = Db::name('area')->where('id', $data['hukou_city'])->value('name');
                
                if (!empty($province)) $hukou_location[] = $province;
                if (!empty($city)) $hukou_location[] = $city;
                $data['hukou_location'] = implode('/', $hukou_location);
                
                // 移除不存在的字段
                unset($data['hukou_province']);
                unset($data['hukou_city']);
            }

            // 处理常住地址
            if (isset($data['residence_province']) && isset($data['residence_city']) && isset($data['residence_district'])) {
                $residence_address = [];
                // 获取省份名称
                $province = Db::name('area')->where('id', $data['residence_province'])->value('name');
                // 获取城市名称
                $city = Db::name('area')->where('id', $data['residence_city'])->value('name');
                // 获取区县名称
                $district = Db::name('area')->where('id', $data['residence_district'])->value('name');
                
                if (!empty($province)) $residence_address[] = $province;
                if (!empty($city)) $residence_address[] = $city;
                if (!empty($district)) $residence_address[] = $district;
                
                $data['residence_address'] = implode('/', $residence_address);
                
                // 移除不存在的字段
                unset($data['residence_province']);
                unset($data['residence_city']);
                unset($data['residence_district']);
            }
            
            // 处理文件上传
            $files = $this->request->file();
            $uploadDir = '/uploads/resume/' . date('Ymd') . '/';
            
            // 确保上传目录存在
            if (!is_dir(ROOT_PATH . 'public' . $uploadDir)) {
                mkdir(ROOT_PATH . 'public' . $uploadDir, 0755, true);
            }

            // 处理单个文件上传
            $fileFields = [
                'avatar',
                'full_body_photo',
                'id_card_front',
                'id_card_back',
                'hk_macau_passport_front',
                'hk_macau_passport_back'
            ];
            
            foreach ($fileFields as $field) {
                if (isset($files[$field]) && $files[$field]->isValid()) {
                    $data[$field] = $this->uploadFile($files[$field], $uploadDir);
                }
            }
            
            // 处理多文件上传
            if (isset($files['additional_photos']) && !empty($files['additional_photos'][0])) {
                $additionalPhotos = [];
                foreach ($files['additional_photos'] as $photo) {
                    if ($photo->isValid()) {
                        $additionalPhotos[] = $this->uploadFile($photo, $uploadDir);
                    }
                }
                $data['additional_photos'] = implode(',', $additionalPhotos);
            }
            
            // 处理数组字段
            $arrayFields = ['contact_name', 'contact_relation', 'contact_age', 'contact_job', 
                           'graduation_education', 'education_school', 'education_major', 
                           'education_start', 'education_end', 'job_company', 'job_position', 
                           'job_start', 'job_end', 'job_description'];
            
            foreach ($arrayFields as $field) {
                if (isset($data[$field]) && is_array($data[$field])) {
                    $data[$field] = implode('|', $data[$field]);
                }
            }
            
            // 创建简历记录
            $resume = model('Resume');
            $resume->allowField(true)->save($data);
            
            Db::commit();
            $this->success('创建成功', ['id' => $resume->id]);
        } catch (Exception $e) {
            Db::rollback();
            \think\Log::write('创建简历异常：' . $e->getMessage(), 'error');
            $this->error('创建失败：' . $e->getMessage());
        }
    }
    
    /**
     * 上传文件
     */
    protected function uploadFile($file, $uploadDir)
    {
        $info = $file->move(ROOT_PATH . 'public' . $uploadDir);
        if (!$info) {
            throw new Exception('文件上传失败：' . $file->getError());
        }
        return $uploadDir . $info->getSaveName();
    }

    /**
     * 投递简历
     */
    public function apply()
    {
        // 初始化响应数据
        $responseData = [
            'code' => 0,
            'msg' => '',
            'time' => time(),
            'data' => null
        ];
        
        try {
            // 检查登录状态
            if (!$this->auth->isLogin()) {
                $responseData['msg'] = '请先登录';
                return json($responseData);
            }
            
            // 获取当前登录用户信息
            $user = $this->auth->getUser();
            if (!$user) {
                $responseData['msg'] = '获取用户信息失败';
                return json($responseData);
            }
            
            // 获取参数
            $job_id = intval($this->request->post('job_id', 0));
            $resume_id = intval($this->request->post('resume_id', 0));
            $job_code = $this->request->post('job_code', '', 'trim');
            $job_name = $this->request->post('job_name', '', 'trim');
            
            // 记录请求数据
            \think\Log::write('简历投递请求数据: ' . json_encode([
                'job_id' => $job_id,
                'resume_id' => $resume_id,
                'job_code' => $job_code,
                'job_name' => $job_name,
                'user_id' => $user->id
            ], JSON_UNESCAPED_UNICODE), 'info');
            
            // 参数验证
            if ($job_id <= 0 || $resume_id <= 0) {
                $responseData['msg'] = '参数不完整，缺少职位ID或简历ID';
                return json($responseData);
            }
            
            // 开始数据库操作
            Db::startTrans();
            
            // 1. 验证职位是否存在
            $job = Db::name('jobs')->where('id', $job_id)->find();
            if (!$job) {
                Db::rollback();
                $responseData['msg'] = '职位不存在，职位ID: ' . $job_id;
                return json($responseData);
            }
            
            // 2. 验证简历是否存在
            $resume = Db::name('resume')->where('id', $resume_id)->find();
            if (!$resume) {
                Db::rollback();
                $responseData['msg'] = '简历不存在，简历ID: ' . $resume_id;
                return json($responseData);
            }
            
            // 3. 验证用户权限
            if ($resume['user_id'] != $user->id && ($resume['name'] != $user->realname || $resume['phone'] != $user->mobile)) {
                Db::rollback();
                $responseData['msg'] = '您没有权限使用此简历';
                return json($responseData);
            }
            
            // 4. 补全职位信息
            if (empty($job_code) && isset($job['job_code'])) {
                $job_code = $job['job_code'];
            }
            
            if (empty($job_name)) {
                if (isset($job['job_name']) && !empty($job['job_name'])) {
                    $job_name = $job['job_name'];
                } else if (isset($job['title']) && !empty($job['title'])) {
                    $job_name = $job['title'];
                }
            }
            
            // 5. 检查是否已经投递过
            $exists = Db::name('resume_apply')
                ->where('user_id', $user->id)
                ->where('job_id', $job_id)
                ->where('resume_id', $resume_id)
                ->find();
            
            $currentTime = time();
            $resultId = 0;
            
            if ($exists) {
                // 已存在记录，更新
                \think\Log::write('更新已存在的投递记录: ' . $exists['id'], 'info');
                
                $updateData = [
                    'status' => '待处理',
                    'update_time' => $currentTime
                ];
                
                $updateResult = Db::name('resume_apply')
                    ->where('id', $exists['id'])
                    ->update($updateData);
                
                \think\Log::write('更新结果: ' . $updateResult, 'info');
                $resultId = $exists['id'];
                
                // 成功响应
                $responseData = [
                    'code' => 1,
                    'msg' => '更新投递成功',
                    'time' => time(),
                    'data' => [
                        'apply_id' => $resultId,
                        'is_updated' => true,
                        'message' => '您之前已投递过该职位，已为您更新投递信息'
                    ]
                ];
            } else {
                // 新建记录
                $insertData = [
                    'user_id' => $user->id,
                    'resume_id' => $resume_id,
                    'resume_name' => $resume['name'],
                    'job_id' => $job_id,
                    'job_code' => $job_code,
                    'job_name' => $job_name,
                    'status' => '待处理',
                    'create_time' => $currentTime,
                    'update_time' => $currentTime
                ];
                
                \think\Log::write('创建新投递记录: ' . json_encode($insertData, JSON_UNESCAPED_UNICODE), 'info');
                
                // 使用直接执行SQL方式插入
                $sql = "INSERT INTO `fa_resume_apply` (`user_id`, `resume_id`, `resume_name`, `job_id`, `job_code`, `job_name`, `status`, `create_time`, `update_time`) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                
                Db::execute($sql, [
                    $user->id, 
                    $resume_id, 
                    $resume['name'], 
                    $job_id, 
                    $job_code, 
                    $job_name, 
                    '待处理', 
                    $currentTime, 
                    $currentTime
                ]);
                
                $resultId = Db::getLastInsID();
                \think\Log::write('插入ID: ' . $resultId, 'info');
                
                if ($resultId) {
                    // 更新职位投递数量
                    Db::name('jobs')->where('id', $job_id)->setInc('resume_count');
                    
                    // 成功响应
                    $responseData = [
                        'code' => 1,
                        'msg' => '投递成功',
                        'time' => time(),
                        'data' => [
                            'apply_id' => $resultId
                        ]
                    ];
                } else {
                    Db::rollback();
                    $responseData['msg'] = '投递失败，无法获取插入ID';
                    return json($responseData);
                }
            }
            
            // 提交事务
            Db::commit();
            
            return json($responseData);
            
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            
            // 记录错误
            \think\Log::write('简历投递异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            
            $responseData['msg'] = '投递失败: ' . $e->getMessage();
            return json($responseData);
        }
    }

    /**
     * 获取我的投递记录
     */
    public function applications()
    {
        // 检查登录状态
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }
        
        // 获取当前登录用户信息
        $user = $this->auth->getUser();
        
        // 获取参数
        $page = $this->request->get('page', 1, 'int');
        $limit = $this->request->get('limit', 10, 'int');
        $status = $this->request->get('status', '', 'trim'); // 添加状态筛选参数
        
        // 构建查询条件
        $where = ['user_id' => $user->id];
        
        // 添加状态筛选条件
        if (!empty($status) && $status !== 'all') {
            $where['status'] = $status;
        }
        
        // 查询投递记录
        $total = model('ResumeApply')
            ->where($where)
            ->count();
        
        $list = model('ResumeApply')
            ->where($where)
            ->order('create_time', 'desc')
            ->page($page, $limit)
            ->select();
        
        // 格式化时间并添加岗位详情
        foreach ($list as &$item) {
            $item['create_time_text'] = date('Y-m-d H:i', $item['create_time']);
            $item['update_time_text'] = date('Y-m-d H:i', $item['update_time']);
            
            // 获取岗位的薪资和总费用信息
            if (!empty($item['job_id'])) {
                $jobInfo = Db::name('jobs')
                    ->field('salary_range, total_fee')
                    ->where('id', $item['job_id'])
                    ->find();
                    
                if ($jobInfo) {
                    $item['salary_range'] = $jobInfo['salary_range'];
                    $item['total_fee'] = $jobInfo['total_fee'];
                }
            }
        }
        
        // 返回结果
        $this->success('获取成功', [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]);
    }

    /**
     * 取消简历投递
     */
    public function deleteapply($id = null)
    {
        // 检查登录状态
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }
        
        // 获取当前登录用户信息
        $user = $this->auth->getUser();
        
        // 尝试从不同来源获取ID
        if (!$id) {
            $id = $this->request->param('id');
        }
        
        if (!$id) {
            $id = $this->request->post('id');
        }
        
        if (!$id) {
            $this->error('参数错误');
        }
        
        // 记录请求信息
        \think\Log::write('取消投递请求: ID=' . $id . ', 用户ID=' . $user->id, 'info');
        
        // 查询投递记录
        $apply = model('ResumeApply')->where('id', $id)->find();
        
        if (!$apply) {
            $this->error('投递记录不存在');
        }
        
        // 验证权限
        if ($apply['user_id'] != $user->id) {
            $this->error('无权操作此记录');
        }
        
        // 检查状态，只有待处理的投递才能取消
        if ($apply['status'] != '待处理') {
            $this->error('只能取消待处理的投递');
        }
        
        try {
            // 开始事务
            Db::startTrans();
            
            // 更新状态为已取消
            $apply->status = '已取消';
            $apply->update_time = time();
            $apply->save();
            
            // 减少职位投递数量
            Db::name('jobs')->where('id', $apply['job_id'])->setDec('resume_count');
            
            // 提交事务
            Db::commit();
            
            $this->success('取消投递成功');
        } catch (\Exception $e) {
            // 回滚事务
            Db::rollback();
            
            // 记录错误
            \think\Log::write('取消投递异常: ' . $e->getMessage() . "\n" . $e->getTraceAsString(), 'error');
            
            $this->error('取消投递失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取简历基本信息（姓名和申请岗位）
     *
     * @ApiMethod (GET)
     * @param int $id 简历ID
     */
    public function basic_info()
    {
        $id = $this->request->param('id/d');
        if (!$id) {
            $this->error('参数错误');
        }

        // 获取当前登录用户ID
        $user = $this->auth->getUser();
        $userId = $user->id;
        \think\Log::write('获取简历基本信息: 用户ID=' . $userId . ', 简历ID=' . $id, 'debug');

        // 查询简历信息
        $resume = \app\common\model\Resume::where('id', $id)->find();
        if (!$resume) {
            $this->error('简历不存在');
        }

        // 验证权限（只能查看自己的简历或者分配给自己的简历）
        if ($resume->user_id != $userId) {
            \think\Log::write('权限检查失败: 简历用户ID=' . $resume->user_id . ', 当前用户ID=' . $userId, 'debug');
            $this->error('无权查看此简历');
        }

        // 返回基本信息
        $data = [
            'id' => $resume->id,
            'name' => $resume->name,
            'applied_position' => $resume->applied_position
        ];

        $this->success('获取成功', $data);
    }
} 