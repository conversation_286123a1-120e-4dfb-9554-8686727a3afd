<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use think\Db;
use think\Exception;

/**
 * 简历导入模板管理
 *
 * @icon fa fa-file-excel-o
 */
class ResumeImportTemplate extends Backend
{
    /**
     * ResumeImportTemplate模型对象
     * @var \app\admin\model\ResumeImportTemplate
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\ResumeImportTemplate;
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                
                // 处理配置信息
                if (isset($params['config']) && is_string($params['config'])) {
                    $params['config'] = json_decode($params['config'], true);
                }
                
                // 如果设置为默认，先将其他模板设为非默认
                if (isset($params['is_default']) && $params['is_default']) {
                    $this->model->where('id', '<>', 0)->update(['is_default' => 0]);
                }

                $result = $this->model->allowField(true)->save($params);
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error($this->model->getError());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                
                // 处理配置信息
                if (isset($params['config']) && is_string($params['config'])) {
                    $params['config'] = json_decode($params['config'], true);
                }
                
                // 如果设置为默认，先将其他模板设为非默认
                if (isset($params['is_default']) && $params['is_default']) {
                    $this->model->where('id', '<>', $ids)->update(['is_default' => 0]);
                }
                
                $result = $row->allowField(true)->save($params);
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error($row->getError());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 设为默认
     */
    public function setDefault($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        try {
            // 设置为默认
            $row->setAsDefault();
            $this->success();
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }
    
    /**
     * 获取所有模板
     */
    public function getTemplates()
    {
        $templates = $this->model->select();
        return json(['code' => 1, 'msg' => '获取成功', 'data' => $templates]);
    }
    
    /**
     * 获取模板详细信息
     * @param int $id 模板ID
     * @return \think\response\Json
     */
    public function getTemplateInfo($id = null)
    {
        if (!$id) {
            $id = $this->request->get('id');
        }
        
        if (!$id) {
            return json(['code' => 0, 'msg' => '未提供模板ID']);
        }
        
        $template = $this->model->get($id);
        if (!$template) {
            return json(['code' => 0, 'msg' => '模板不存在']);
        }
        
        // 手动处理配置字段，确保前端能正确使用
        $data = $template->toArray();
        
        // 调试信息
        \think\Log::write('模板配置类型: ' . gettype($data['config']), 'debug');
        \think\Log::write('模板配置内容: ' . json_encode($data['config']), 'debug');
        
        // 确保config是数组格式
        if (is_string($data['config'])) {
            try {
                $config = json_decode($data['config'], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $data['config'] = $config;
                    \think\Log::write('JSON解析成功: ' . json_encode($data['config']), 'debug');
                } else {
                    // JSON解析错误，记录日志
                    \think\Log::write('JSON解析错误: ' . json_last_error_msg(), 'error');
                    // 返回空配置
                    $data['config'] = [];
                }
            } catch (\Exception $e) {
                \think\Log::write('配置解析异常: ' . $e->getMessage(), 'error');
                $data['config'] = [];
            }
        } elseif (!is_array($data['config'])) {
            // 如果不是字符串也不是数组，设为空数组
            \think\Log::write('配置不是字符串也不是数组: ' . gettype($data['config']), 'warning');
            $data['config'] = [];
        }
        
        // 处理多数组字段，将管道符分隔的字符串转换为数组
        $arrayFields = [
            'contact_relation', 'contact_name', 'contact_age', 'contact_job', 
            'education_start', 'education_end', 'education_school', 'education_major', 'graduation_education',
            'job_start', 'job_end', 'job_company', 'job_position', 'job_description'
        ];
        
        foreach ($arrayFields as $field) {
            if (isset($data['config'][$field]) && is_string($data['config'][$field]) && !empty($data['config'][$field])) {
                $data['config'][$field] = explode('|', $data['config'][$field]);
                \think\Log::write('字段 '.$field.' 转换为数组: ' . json_encode($data['config'][$field]), 'debug');
            } elseif (!isset($data['config'][$field]) || empty($data['config'][$field])) {
                // 确保字段存在且为空数组
                $data['config'][$field] = [];
                \think\Log::write('字段 '.$field.' 初始化为空数组', 'debug');
            } elseif (is_array($data['config'][$field])) {
                \think\Log::write('字段 '.$field.' 已经是数组: ' . json_encode($data['config'][$field]), 'debug');
            } else {
                \think\Log::write('字段 '.$field.' 类型异常: ' . gettype($data['config'][$field]), 'warning');
            }
        }
        
        // 格式化时间字段
        if (isset($data['create_time']) && is_numeric($data['create_time'])) {
            $data['create_time_text'] = date('Y-m-d H:i:s', $data['create_time']);
        }
        if (isset($data['update_time']) && is_numeric($data['update_time'])) {
            $data['update_time_text'] = date('Y-m-d H:i:s', $data['update_time']);
        }

        // 输出完整的返回数据结构，帮助调试
        \think\Log::write('返回数据结构: ' . json_encode(['code' => 1, 'msg' => '获取成功', 'data' => $data]), 'debug');

        // 返回模板详细信息，包括配置
        return json(['code' => 1, 'msg' => '获取成功', 'data' => $data]);
    }

    /**
     * 调试模板配置
     */
    public function debugTemplateConfig()
    {
        $templates = $this->model->select();
        $debug = [];
        
        foreach ($templates as $template) {
            $config = $template->config;
            $debug[] = [
                'id' => $template->id,
                'name' => $template->name,
                'config_type' => gettype($config),
                'config_json' => json_encode($config),
                'raw_config' => $config
            ];
        }
        
        return json(['code' => 1, 'msg' => '调试信息', 'data' => $debug]);
    }
    
    /**
     * 调试单个模板数据
     */
    public function debugSingleTemplate($id = null)
    {
        if (!$id) {
            $id = $this->request->get('id');
        }
        
        if (!$id) {
            return json(['code' => 0, 'msg' => '未提供模板ID']);
        }
        
        $template = $this->model->get($id);
        if (!$template) {
            return json(['code' => 0, 'msg' => '模板不存在']);
        }
        
        // 获取原始数据
        $rawData = $template->getData();
        
        // 获取处理后的数据
        $processedData = $template->toArray();
        
        // 手动处理多数组字段
        $arrayFields = [
            'contact_relation', 'contact_name', 'contact_age', 'contact_job', 
            'education_start', 'education_end', 'education_school', 'education_major', 'graduation_education',
            'job_start', 'job_end', 'job_company', 'job_position', 'job_description'
        ];
        
        $manualProcessed = [];
        if (isset($processedData['config']) && is_array($processedData['config'])) {
            foreach ($arrayFields as $field) {
                if (isset($processedData['config'][$field])) {
                    if (is_string($processedData['config'][$field])) {
                        $manualProcessed[$field] = explode('|', $processedData['config'][$field]);
                    } else {
                        $manualProcessed[$field] = $processedData['config'][$field];
                    }
                }
            }
        }
        
        return json([
            'code' => 1, 
            'msg' => '调试信息', 
            'data' => [
                'raw' => $rawData,
                'processed' => $processedData,
                'manual_processed' => $manualProcessed
            ]
        ]);
    }

    /**
     * 获取可用字段列表
     * @return \think\response\Json
     */
    public function getFieldsList()
    {
        // 基本字段 - 添加缺失的字段
        $basicFields = [
            'name' => '姓名',
            'intended_position' => '意向岗位',
            'applied_position' => '申请岗位',
            'gender' => '性别',
            'ethnicity' => '民族',
            'height' => '身高(cm)',
            'weight' => '体重(kg)',
            'id_card' => '身份证号',
            'age' => '年龄',
            'birth_date' => '出生日期',
            'phone' => '手机号码',
            'marital_status' => '婚姻状况',
            'hukou_location' => '户口所在地',
            'residence_address' => '常住地址',
            'highest_education' => '最高学历',
            'cantonese_level' => '粤语熟练度',
            'mandarin_level' => '国语熟练度',
            'english_level' => '英语熟练度',
            'hk_macau_passport' => '港澳通行证编号',
            'hk_macau_passport_expiry' => '港澳通行证到期时间',
            'overseas_experience' => '海外工作经历',
            'overseas_region' => '海外工作地区'
        ];
        
        // 联系人字段
        $contactFields = [
            'contact_relation' => '联系人关系',
            'contact_name' => '联系人姓名',
            'contact_age' => '联系人年龄',
            'contact_job' => '联系人工作'
        ];
        
        // 教育经历字段
        $educationFields = [
            'education_start' => '教育开始时间',
            'education_end' => '教育结束时间',
            'education_school' => '学校名称',
            'education_major' => '专业',
            'graduation_education' => '学历'
        ];
        
        // 工作经历字段
        $jobFields = [
            'job_start' => '工作开始时间',
            'job_end' => '工作结束时间',
            'job_company' => '工作单位',
            'job_position' => '工作岗位',
            'job_description' => '工作内容'
        ];
        
        // 其他字段
        $otherFields = [
            'special_certificate' => '特殊职业资格证',
            'hobbies' => '兴趣爱好',
            'self_evaluation' => '自我评价',
            'contact_person' => '对接人',
            'status' => '状态'
        ];
        
        $fields = [
            'basic' => $basicFields,
            'contact' => $contactFields,
            'education' => $educationFields,
            'job' => $jobFields,
            'other' => $otherFields
        ];
        
        return json(['code' => 1, 'msg' => '获取成功', 'data' => $fields]);
    }
} 

