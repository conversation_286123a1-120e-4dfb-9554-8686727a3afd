<link href="__CDN__/assets/addons/wechat/css/menu.css?v={$site.version|htmlentities}" rel="stylesheet">
<style>
    .clickbox {margin:0;text-align: left;}
    .create-click {
        margin-left:0;
    }
</style>
<style data-render="darktheme">
    body.darktheme .keytitle {
        background-color: #222;
    }
</style>

<form id="edit-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label for="c-title" class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[title]" value="{$row.title|htmlentities}"  id="c-title" class="form-control" data-rule="required" />
        </div>
    </div>
    <div class="form-group">
        <label for="c-text" class="control-label col-xs-12 col-sm-2">{:__('Text')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[text]" value="{$row.text|htmlentities}"  id="c-text" class="form-control" placeholder="支持正则表达式" data-rule="required; remote(wechat/autoreply/check_text_unique, except={$row.text|htmlentities})" />
        </div>
    </div>
    <div class="form-group">
        <label for="c-eventkey" class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="clickbox">
                <input type="hidden" name="row[eventkey]" id="c-eventkey" class="form-control" value="{$row.eventkey|htmlentities}" data-rule="required" readonly />
                <span class="create-click"><a href="wechat/response/select" id="select-resources"><i class="weixin-icon big-add-gray"></i><strong>选择现有资源</strong></a><div class="keytitle">资源名:{$response.title|default="已删除"}</div></span>
                <span class="create-click"><a href="wechat/response/add" id="add-resources"><i class="weixin-icon big-add-gray"></i><strong>添加新资源</strong></a></span>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label for="c-remark" class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[remark]" value="{$row.remark|htmlentities}"  id="c-remark" class="form-control" />
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('Hidden')], $row['status'])}
        </div>
    </div>
    <div class="form-group hide layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>
