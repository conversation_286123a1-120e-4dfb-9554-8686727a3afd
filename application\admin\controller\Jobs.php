<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 岗位管理
 *
 * @icon fa fa-circle-o
 */
class Jobs extends Backend
{

    /**
     * Jobs模型对象
     * @var \app\common\model\Jobs
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\Jobs;
        $this->view->assign("statusList", [
            '上架' => __('上架'),
            '暂停' => __('暂停'),
            '下架' => __('下架')
        ]);
        
        // 获取岗位分类列表
        $categoryModel = new \app\common\model\Jobcategory;
        $categoryList = $categoryModel->getActiveCategories();
        $this->view->assign("categoryList", $categoryList);
    }

    /**
     * 获取分类图片
     */
    public function getCategoryImage()
    {
        $categoryName = $this->request->post('category_id');
        $category = \app\common\model\Jobcategory::where('name', $categoryName)->find();
        if ($category) {
            $this->success('', ['image' => $category['image']]);
        } else {
            $this->error(__('Category not found'));
        }
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                // 获取分类图片
                if (!empty($params['category'])) {
                    $category = \app\common\model\Jobcategory::where('name', $params['category'])->find();
                    if ($category) {
                        $params['image'] = $category['image'];
                    }
                }
                $this->request->post(['row' => $params]);
            }
            return parent::add();
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                // 获取分类图片
                if (!empty($params['category'])) {
                    $category = \app\common\model\Jobcategory::where('name', $params['category'])->find();
                    if ($category) {
                        $params['image'] = $category['image'];
                    }
                }
                $this->request->post(['row' => $params]);
            }
            return parent::edit($ids);
        }
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 查看
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();
            
            // 获取分类ID和收藏数量
            foreach ($list as &$item) {
                $category = \app\common\model\Jobcategory::where('name', $item['category'])->find();
                $item['category_id'] = $category ? $category['id'] : 0;
                
                // 获取收藏数量
                $item['favorite_count'] = \app\common\model\Jobfavorite::where('job_id', $item['id'])->count();
                
                // 获取简历投递数量
                $item['resume_apply_count'] = \app\common\model\ResumeApply::where('job_id', $item['id'])->count();
                
                // 获取简历投递数量（如果resume_count字段不存在或需要重新统计）
                if (!isset($item['resume_count']) || $this->request->get('refresh_count')) {
                    $item['resume_count'] = \app\common\model\ResumeApply::where('job_id', $item['id'])->count();
                    // 更新到数据库
                    $this->model->where('id', $item['id'])->update(['resume_count' => $item['resume_count']]);
                }
            }
            
            $total = $this->model
                ->where($where)
                ->count();
            $result = array("total" => $total, "rows" => $list);
            return json($result);
        }
        
        // 如果没有指定状态，默认显示"上架"状态
        if (!$this->request->get('status')) {
            $this->request->get(['status' => '上架']);
        }
        
        return $this->view->fetch();
    }

    /**
     * 查看岗位详情
     */
    public function detail($ids = null)
    {
        // 尝试从不同来源获取ID
        if (!$ids) {
            $ids = $this->request->param('ids');
        }
        
        // 记录调试信息
        \think\Log::write('正在查看岗位详情，IDS参数: ' . $ids, 'info');
        \think\Log::write('请求方法: ' . $this->request->method(), 'info');
        \think\Log::write('所有请求参数: ' . json_encode($this->request->param()), 'info');
        
        if (!$ids) {
            \think\Log::write('无效的ID参数，$ids为空', 'error');
            $this->error(__('Parameter id can not be empty'));
            return;
        }
        
        // 尝试获取记录
        $row = $this->model->get($ids);
        if (!$row) {
            \think\Log::write('未找到岗位记录，ID: ' . $ids, 'error');
            $this->error(__('No Results were found'));
            return;
        }
        
        \think\Log::write('成功获取岗位记录，ID: ' . $row['id'], 'info');
        
        // 获取收藏数量
        $row['favorite_count'] = \app\common\model\Jobfavorite::where('job_id', $row['id'])->count();
        
        // 获取简历投递数量
        $row['resume_apply_count'] = \app\common\model\ResumeApply::where('job_id', $row['id'])->count();
        
        // 获取分类信息
        $category = \app\common\model\Jobcategory::where('name', $row['category'])->find();
        $row['category_id'] = $category ? $category['id'] : 0;
        
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * AI解析岗位信息
     */
    public function parseJobInfo()
    {
        if (!$this->request->isPost()) {
            return json(['code' => 0, 'msg' => '非法请求']);
        }

        $data = $this->request->post();
        if (empty($data['content'])) {
            return json(['code' => 0, 'msg' => '请输入岗位信息']);
        }

        try {
            // 检查是否启用了OpenAI功能
            if (config('site.openai_enabled') != '1') {
                return json(['code' => 0, 'msg' => 'AI功能未启用，请先在系统配置中启用']);
            }

            // 构建提示词
            $prompt = "请根据以下岗位信息提取关键字段，并以JSON格式返回：\n";
            $prompt .= "岗位信息：{$data['content']}\n";
            $prompt .= "需要提取的字段：\n";
            $prompt .= "1. title - 岗位标题\n";
            $prompt .= "2. company - 公司名称\n";
            $prompt .= "3. job_code - 岗位编号(如果有)\n";
            $prompt .= "4. job_name - 岗位名称\n";
            $prompt .= "5. category - 岗位分类(根据内容推测最合适的分类)\n";
            $prompt .= "6. salary_range - 薪资范围\n";
            $prompt .= "7. age_requirement - 年龄要求\n";
            $prompt .= "8. gender_requirement - 性别要求\n";
            $prompt .= "9. language_requirement - 语言要求\n";
            $prompt .= "10. education_requirement - 学历要求\n";
            $prompt .= "11. experience_requirement - 经验要求\n";
            $prompt .= "12. working_hours - 工作时间\n";
            $prompt .= "13. accommodation - 食宿条件\n";
            $prompt .= "14. job_description - 岗位描述/职责内容\n";
            $prompt .= "15. total_fee - 费用(只提取数字部分)\n";
            $prompt .= "16. remark - 备注/额外信息\n";
            $prompt .= "17. job_summary - 岗位亮点/优势\n";
            $prompt .= "请确保返回的JSON格式正确，如果某个字段没有相关信息，请留空。";

            // 获取API配置
            $apiKey = config('site.openai_api_key');
            $apiUrl = config('site.openai_api_url');
            $model = config('site.openai_model');
            
            if (empty($apiKey) || empty($apiUrl) || empty($model)) {
                return json(['code' => 0, 'msg' => 'API配置不完整，请在系统配置中完善']);
            }

            $ch = curl_init($apiUrl . '/chat/completions');
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_HTTPHEADER, [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $apiKey
            ]);

            $postData = [
                'model' => $model,
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => '你是一个专业的招聘信息分析专家，擅长从招聘信息中提取关键信息。请只返回JSON格式的结果，不要有任何额外的文字。'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'temperature' => 0.5,
                'max_tokens' => 1000,
                'stream' => false
            ];

            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            
            $response = curl_exec($ch);
            $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
            
            if (curl_errno($ch)) {
                throw new \Exception('API请求错误: ' . curl_error($ch));
            }
            
            curl_close($ch);

            if ($httpCode !== 200) {
                throw new \Exception('API请求失败，状态码：' . $httpCode . '，响应：' . $response);
            }

            $result = json_decode($response, true);
            if (!isset($result['choices'][0]['message']['content'])) {
                throw new \Exception('API返回数据格式错误：' . $response);
            }

            $content = trim($result['choices'][0]['message']['content']);
            
            // 提取JSON内容
            $pattern = '/\{.*\}/s';  // 匹配最外层的大括号及其中的内容
            if (preg_match($pattern, $content, $matches)) {
                $jsonContent = $matches[0];
            } else {
                $jsonContent = $content;  // 如果没有匹配到大括号，就使用整个内容
            }
            
            // 解析JSON
            $parsedData = json_decode($jsonContent, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('解析JSON失败: ' . json_last_error_msg() . "\n原始内容: " . $content);
            }
            
            return json(['code' => 1, 'msg' => '解析成功', 'data' => $parsedData]);

        } catch (\Exception $e) {
            \think\Log::write('岗位信息解析失败：' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => $e->getMessage()]);
        }
    }

}
