define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'resume_import_template/index' + location.search,
                    add_url: 'resume_import_template/add',
                    edit_url: 'resume_import_template/edit',
                    del_url: 'resume_import_template/del',
                    multi_url: 'resume_import_template/multi',
                    set_default_url: 'resume_import_template/setDefault',
                    table: 'resume_import_template',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'name', title: __('模板名称'), operate: 'LIKE'},
                        {field: 'description', title: __('模板描述'), operate: 'LIKE'},
                        {field: 'is_default', title: __('是否默认'), searchList: {"0": __('否'), "1": __('是')}, formatter: function(value, row, index) {
                            return value == 1 ? '<span class="label label-success">是</span>' : '<span class="label label-default">否</span>';
                        }},
                        {field: 'create_time', title: __('创建时间'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'update_time', title: __('更新时间'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate, 
                            buttons: [
                                {
                                    name: 'setDefault',
                                    text: '设为默认',
                                    title: '设为默认',
                                    icon: 'fa fa-check',
                                    classname: 'btn btn-xs btn-info btn-ajax',
                                    url: 'resume_import_template/setDefault',
                                    confirm: '确认设为默认模板？',
                                    success: function (data, ret) {
                                        $(".btn-refresh").trigger("click");
                                        return false;
                                    },
                                    visible: function (row) {
                                        return row.is_default != 1;
                                    }
                                }
                            ]
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
        },
        add: function () {
            Controller.api.bindevent();
            Controller.api.initDynamicForms();
        },
        edit: function () {
            Controller.api.bindevent();

            // 先初始化动态表单结构
            Controller.api.initDynamicForms();

            // 尝试从隐藏字段获取数据
            var templateDataElem = document.getElementById('template-data');
            if (templateDataElem && templateDataElem.value) {
                try {
                    var templateData = JSON.parse(templateDataElem.value);

                    if (templateData && templateData.config) {
                        // 延迟执行，确保DOM元素已加载
                        setTimeout(function() {
                            Controller.api.initExistingData(templateData.config);
                        }, 500);
                        return;
                    }
                } catch (e) {
                    // 解析失败，继续尝试其他方式
                }
            }

            // 如果隐藏字段获取失败，尝试从URL获取ID并请求API
            var url = new URL(location.href);
            var ids = url.searchParams.get('ids');

            if (!ids) {
                return;
            }

            // 通过API获取数据
            Fast.api.ajax({
                url: 'resume_import_template/getTemplateInfo',
                data: {id: ids}
            }, function(data) {
                if (data && data.data && data.data.config) {
                    // 延迟执行，确保DOM元素已加载
                    setTimeout(function() {
                        Controller.api.initExistingData(data.data.config);
                    }, 500);
                }
                return false;
            });
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            
            // 初始化动态表单
            initDynamicForms: function() {
                // 清空已有的项目
                $("#contact-items").empty();
                $("#education-items").empty();
                $("#job-items").empty();
                
                // 添加联系人
                $("#add-contact").off("click").on("click", function() {
                    var index = $("#contact-items .contact-item").length;
                    var html = Controller.api.createContactItemHtml(index);
                    $("#contact-items").append(html);
                    
                    // 显示删除按钮，除了第一个项目外
                    if (index > 0) {
                        $("#contact-items .contact-item .contact-remove").show();
                    }
                    
                    // 绑定删除事件
                    $(".contact-remove").off("click").on("click", function() {
                        $(this).closest(".contact-item").remove();
                        // 重新编号
                        $("#contact-items .contact-item").each(function(i) {
                            $(this).attr("data-index", i);
                            $(this).find(".panel-heading").html('联系人 #' + (i+1) + '<span class="pull-right"><a href="javascript:;" class="btn btn-xs btn-danger contact-remove"><i class="fa fa-times"></i></a></span>');
                            if (i === 0) {
                                $(this).find(".contact-remove").hide();
                            } else {
                                $(this).find(".contact-remove").show();
                            }
                        });
                    });
                });
                
                // 添加教育经历
                $("#add-education").off("click").on("click", function() {
                    var index = $("#education-items .education-item").length;
                    var html = Controller.api.createEducationItemHtml(index);
                    $("#education-items").append(html);
                    
                    // 显示删除按钮，除了第一个项目外
                    if (index > 0) {
                        $("#education-items .education-item .education-remove").show();
                    }
                    
                    // 绑定删除事件
                    $(".education-remove").off("click").on("click", function() {
                        $(this).closest(".education-item").remove();
                        // 重新编号
                        $("#education-items .education-item").each(function(i) {
                            $(this).attr("data-index", i);
                            $(this).find(".panel-heading").html('教育经历 #' + (i+1) + '<span class="pull-right"><a href="javascript:;" class="btn btn-xs btn-danger education-remove"><i class="fa fa-times"></i></a></span>');
                            if (i === 0) {
                                $(this).find(".education-remove").hide();
                            } else {
                                $(this).find(".education-remove").show();
                            }
                        });
                    });
                });
                
                // 添加工作经历
                $("#add-job").off("click").on("click", function() {
                    var index = $("#job-items .job-item").length;
                    var html = Controller.api.createJobItemHtml(index);
                    $("#job-items").append(html);
                    
                    // 显示删除按钮，除了第一个项目外
                    if (index > 0) {
                        $("#job-items .job-item .job-remove").show();
                    }
                    
                    // 绑定删除事件
                    $(".job-remove").off("click").on("click", function() {
                        $(this).closest(".job-item").remove();
                        // 重新编号
                        $("#job-items .job-item").each(function(i) {
                            $(this).attr("data-index", i);
                            $(this).find(".panel-heading").html('工作经历 #' + (i+1) + '<span class="pull-right"><a href="javascript:;" class="btn btn-xs btn-danger job-remove"><i class="fa fa-times"></i></a></span>');
                            if (i === 0) {
                                $(this).find(".job-remove").hide();
                            } else {
                                $(this).find(".job-remove").show();
                            }
                        });
                    });
                });
                
                // 初始化时添加第一个项目
                if ($("#contact-items .contact-item").length === 0) {
                    $("#add-contact").trigger("click");
                }
                if ($("#education-items .education-item").length === 0) {
                    $("#add-education").trigger("click");
                }
                if ($("#job-items .job-item").length === 0) {
                    $("#add-job").trigger("click");
                }
            },
            
            // 编辑页面初始化已有数据
            initExistingData: function(config) {
                // 参数验证
                if (!config) {
                    return;
                }

                try {
                    
                    // 处理联系人数据
                    var contactFields = ['contact_relation', 'contact_name', 'contact_age', 'contact_job'];
                    
                    // 预处理字段，确保是数组格式
                    contactFields.forEach(function(field) {
                        if (config[field]) {
                            if (typeof config[field] === 'string') {
                                config[field] = config[field].split('|');
                            } else if (!Array.isArray(config[field])) {
                                config[field] = [];
                            }
                        } else {
                            config[field] = [];
                        }
                    });

                    var maxContacts = Controller.api.getMaxArrayLength(config, contactFields);

                    // 先清空默认的联系人项
                    $("#contact-items").empty();

                    // 添加已有的联系人
                    for (var i = 0; i < Math.max(1, maxContacts); i++) {
                        $("#add-contact").trigger("click");
                        var contactItem = $("#contact-items .contact-item").eq(i);

                        if (contactItem.length === 0) {
                            continue;
                        }

                        // 设置值
                        contactFields.forEach(function(field) {
                            if (config[field] && Array.isArray(config[field]) && config[field][i] !== undefined) {
                                var value = config[field][i];
                                var input = contactItem.find("input[name='row[config][" + field + "][]']");
                                if (input.length > 0) {
                                    input.val(value);
                                }
                            }
                        });
                    }
                    
                    // 处理教育经历数据
                    var educationFields = ['education_start', 'education_end', 'education_school', 'education_major', 'graduation_education'];
                    
                    // 预处理字段，确保是数组格式
                    educationFields.forEach(function(field) {
                        if (config[field]) {
                            if (typeof config[field] === 'string') {
                                config[field] = config[field].split('|');
                            } else if (!Array.isArray(config[field])) {
                                config[field] = [];
                            }
                        } else {
                            config[field] = [];
                        }
                    });

                    var maxEducation = Controller.api.getMaxArrayLength(config, educationFields);

                    // 先清空默认的教育经历项
                    $("#education-items").empty();

                    // 添加已有的教育经历
                    for (var i = 0; i < Math.max(1, maxEducation); i++) {
                        $("#add-education").trigger("click");
                        var educationItem = $("#education-items .education-item").eq(i);

                        if (educationItem.length === 0) {
                            continue;
                        }

                        // 设置值
                        educationFields.forEach(function(field) {
                            if (config[field] && Array.isArray(config[field]) && config[field][i] !== undefined) {
                                var value = config[field][i];
                                var input = educationItem.find("input[name='row[config][" + field + "][]']");
                                if (input.length > 0) {
                                    input.val(value);
                                }
                            }
                        });
                    }
                    
                    // 处理工作经历数据
                    var jobFields = ['job_start', 'job_end', 'job_company', 'job_position', 'job_description'];
                    
                    // 预处理字段，确保是数组格式
                    jobFields.forEach(function(field) {
                        if (config[field]) {
                            if (typeof config[field] === 'string') {
                                config[field] = config[field].split('|');
                            } else if (!Array.isArray(config[field])) {
                                config[field] = [];
                            }
                        } else {
                            config[field] = [];
                        }
                    });

                    var maxJobs = Controller.api.getMaxArrayLength(config, jobFields);

                    // 先清空默认的工作经历项
                    $("#job-items").empty();

                    // 添加已有的工作经历
                    for (var i = 0; i < Math.max(1, maxJobs); i++) {
                        $("#add-job").trigger("click");
                        var jobItem = $("#job-items .job-item").eq(i);

                        if (jobItem.length === 0) {
                            continue;
                        }

                        // 设置值
                        jobFields.forEach(function(field) {
                            if (config[field] && Array.isArray(config[field]) && config[field][i] !== undefined) {
                                var value = config[field][i];
                                var input = jobItem.find("input[name='row[config][" + field + "][]']");
                                if (input.length > 0) {
                                    input.val(value);
                                }
                            }
                        });
                    }
                } catch (e) {
                    // 初始化失败，静默处理
                }
            },
            
            // 获取一组字段的最大数组长度
            getMaxArrayLength: function(config, fields) {
                var maxLength = 0;

                if (!config || typeof config !== 'object') {
                    return 0;
                }

                try {
                    fields.forEach(function(field) {
                        if (config[field]) {
                            var fieldValue = config[field];
                            var length = 0;

                            if (Array.isArray(fieldValue)) {
                                // 如果是数组，获取其长度
                                length = fieldValue.length;
                            } else if (typeof fieldValue === 'string') {
                                // 如果是字符串，尝试按分隔符分割
                                var parts = fieldValue.split('|').filter(function(part) {
                                    return part !== '' && part !== null && part !== undefined;
                                });
                                length = parts.length;
                            }

                            if (length > maxLength) {
                                maxLength = length;
                            }
                        }
                    });
                } catch (e) {
                    // 计算失败，返回默认值
                }

                return maxLength;
            },
            
            // 创建联系人项目HTML
            createContactItemHtml: function(index) {
                var removeBtn = index > 0 ? '' : 'style="display:none;"';
                var html = '<div class="contact-item mb-10" data-index="' + index + '">' +
                    '<div class="panel panel-default">' +
                    '<div class="panel-heading">联系人 #' + (index+1) + '<span class="pull-right"><a href="javascript:;" class="btn btn-xs btn-danger contact-remove" ' + removeBtn + '><i class="fa fa-times"></i></a></span></div>' +
                    '<div class="panel-body">' +
                    '<div class="row">' +
                    '<div class="col-sm-6">' +
                    '<div class="form-group">' +
                    '<label>联系人关系</label>' +
                    '<input type="text" class="form-control" name="row[config][contact_relation][]" placeholder="例如：AA' + (index+2) + '">' +
                    '</div>' +
                    '</div>' +
                    '<div class="col-sm-6">' +
                    '<div class="form-group">' +
                    '<label>联系人姓名</label>' +
                    '<input type="text" class="form-control" name="row[config][contact_name][]" placeholder="例如：AB' + (index+2) + '">' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '<div class="row">' +
                    '<div class="col-sm-6">' +
                    '<div class="form-group">' +
                    '<label>联系人年龄</label>' +
                    '<input type="text" class="form-control" name="row[config][contact_age][]" placeholder="例如：AC' + (index+2) + '">' +
                    '</div>' +
                    '</div>' +
                    '<div class="col-sm-6">' +
                    '<div class="form-group">' +
                    '<label>联系人工作</label>' +
                    '<input type="text" class="form-control" name="row[config][contact_job][]" placeholder="例如：AD' + (index+2) + '">' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>';
                return html;
            },
            
            // 创建教育经历项目HTML
            createEducationItemHtml: function(index) {
                var removeBtn = index > 0 ? '' : 'style="display:none;"';
                var html = '<div class="education-item mb-10" data-index="' + index + '">' +
                    '<div class="panel panel-default">' +
                    '<div class="panel-heading">教育经历 #' + (index+1) + '<span class="pull-right"><a href="javascript:;" class="btn btn-xs btn-danger education-remove" ' + removeBtn + '><i class="fa fa-times"></i></a></span></div>' +
                    '<div class="panel-body">' +
                    '<div class="row">' +
                    '<div class="col-sm-6">' +
                    '<div class="form-group">' +
                    '<label>教育开始时间</label>' +
                    '<input type="text" class="form-control" name="row[config][education_start][]" placeholder="例如：AE' + (index+2) + '">' +
                    '</div>' +
                    '</div>' +
                    '<div class="col-sm-6">' +
                    '<div class="form-group">' +
                    '<label>教育结束时间</label>' +
                    '<input type="text" class="form-control" name="row[config][education_end][]" placeholder="例如：AF' + (index+2) + '">' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '<div class="row">' +
                    '<div class="col-sm-4">' +
                    '<div class="form-group">' +
                    '<label>学校名称</label>' +
                    '<input type="text" class="form-control" name="row[config][education_school][]" placeholder="例如：AG' + (index+2) + '">' +
                    '</div>' +
                    '</div>' +
                    '<div class="col-sm-4">' +
                    '<div class="form-group">' +
                    '<label>专业</label>' +
                    '<input type="text" class="form-control" name="row[config][education_major][]" placeholder="例如：AH' + (index+2) + '">' +
                    '</div>' +
                    '</div>' +
                    '<div class="col-sm-4">' +
                    '<div class="form-group">' +
                    '<label>学历</label>' +
                    '<input type="text" class="form-control" name="row[config][graduation_education][]" placeholder="例如：AI' + (index+2) + '">' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>';
                return html;
            },
            
            // 创建工作经历项目HTML
            createJobItemHtml: function(index) {
                var removeBtn = index > 0 ? '' : 'style="display:none;"';
                var html = '<div class="job-item mb-10" data-index="' + index + '">' +
                    '<div class="panel panel-default">' +
                    '<div class="panel-heading">工作经历 #' + (index+1) + '<span class="pull-right"><a href="javascript:;" class="btn btn-xs btn-danger job-remove" ' + removeBtn + '><i class="fa fa-times"></i></a></span></div>' +
                    '<div class="panel-body">' +
                    '<div class="row">' +
                    '<div class="col-sm-6">' +
                    '<div class="form-group">' +
                    '<label>工作开始时间</label>' +
                    '<input type="text" class="form-control" name="row[config][job_start][]" placeholder="例如：AJ' + (index+2) + '">' +
                    '</div>' +
                    '</div>' +
                    '<div class="col-sm-6">' +
                    '<div class="form-group">' +
                    '<label>工作结束时间</label>' +
                    '<input type="text" class="form-control" name="row[config][job_end][]" placeholder="例如：AK' + (index+2) + '">' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '<div class="row">' +
                    '<div class="col-sm-4">' +
                    '<div class="form-group">' +
                    '<label>工作单位</label>' +
                    '<input type="text" class="form-control" name="row[config][job_company][]" placeholder="例如：AL' + (index+2) + '">' +
                    '</div>' +
                    '</div>' +
                    '<div class="col-sm-4">' +
                    '<div class="form-group">' +
                    '<label>工作岗位</label>' +
                    '<input type="text" class="form-control" name="row[config][job_position][]" placeholder="例如：AM' + (index+2) + '">' +
                    '</div>' +
                    '</div>' +
                    '<div class="col-sm-4">' +
                    '<div class="form-group">' +
                    '<label>工作内容</label>' +
                    '<input type="text" class="form-control" name="row[config][job_description][]" placeholder="例如：AN' + (index+2) + '">' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>';
                return html;
            }
        }
    };
    return Controller;
}); 