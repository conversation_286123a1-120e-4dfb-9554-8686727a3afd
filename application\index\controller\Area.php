<?php
namespace app\index\controller;

use think\Controller;
use think\Db;

class Area extends Controller
{
    /**
     * 获取地区数据
     */
    public function getAreaData()
    {
        try {
            $areas = Db::name('area')
                ->field('id, pid, name, level')
                ->select();
            
            return json($areas);
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '获取地区数据失败']);
        }
    }
} 