<link href="__CDN__/assets/libs/bootstrap-select/dist/css/bootstrap-select.min.css" rel="stylesheet">
<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    {if config('site.openai_enabled') == '1'}
    <!-- AI功能区域 -->
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">AI智能填写:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="ai-job-content" class="form-control" rows="6" placeholder="请粘贴完整的岗位信息，点击下方的按钮使用AI自动提取信息并填充表单"></textarea>
            <div class="mt-10">
                <button type="button" class="btn btn-info btn-embossed" id="btn-parse-job-info">
                    <i class="fa fa-magic"></i> 使用AI智能填写
                </button>
                <span id="ai-parse-status" class="text-muted ml-10"></span>
            </div>
        </div>
    </div>
    <hr>
    {/if}

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" data-rule="required;length(2~50)" class="form-control" name="row[title]" type="text" value="{$row.title|htmlentities}" placeholder="请输入岗位标题(2-50字)">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Image')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-image" class="form-control" size="50" name="row[image]" type="text" readonly value="{$row.image|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-image" class="btn btn-danger faupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-image"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-image"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Company')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-company" data-rule="required;length(2~50)" class="form-control" name="row[company]" type="text" value="{$row.company|htmlentities}" placeholder="请输入公司名称(2-50字)">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weight')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weight" class="form-control" name="row[weight]" type="number" value="{$row.weight|default='0'}" placeholder="请输入权重值">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Job_code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-job_code" data-rule="required;length(2~20)" class="form-control" name="row[job_code]" type="text" value="{$row.job_code|htmlentities}" placeholder="请输入岗位编号(2-20字)">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Job_name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-job_name" data-rule="required;length(2~50)" class="form-control" name="row[job_name]" type="text" value="{$row.job_name|htmlentities}" placeholder="请输入岗位名称(2-50字)">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Category')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-category" class="form-control selectpicker" data-rule="required" name="row[category]" 
                data-live-search="true" 
                data-live-search-placeholder="搜索分类" 
                data-live-search-normalize="true"
                data-live-search-style="contains"
                data-size="10">
                <option value="">{:__('Please select')}</option>
                {foreach name="categoryList" item="vo"}
                <option value="{$vo.name}" data-image="{$vo.image}" data-content="<div class='category-option'><img src='{$vo.image}' class='category-option-img'><span class='category-option-name'>{$vo.name}</span></div>" {if $row.category==$vo.name}selected{/if}>{$vo.name}</option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Salary_range')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-salary_range" data-rule="required" class="form-control" name="row[salary_range]" type="text" value="{$row.salary_range|htmlentities}" placeholder="例如：5000-8000元/月">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Age_requirement')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-age_requirement" class="form-control" name="row[age_requirement]" type="text" value="{$row.age_requirement|htmlentities}" placeholder="例如：18-35岁">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Gender_requirement')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-gender_requirement" class="form-control" name="row[gender_requirement]" type="text" value="{$row.gender_requirement|htmlentities}" placeholder="例如：男女不限">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Language_requirement')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-language_requirement" class="form-control" name="row[language_requirement]" type="text" value="{$row.language_requirement|htmlentities}" placeholder="例如：日语N2以上">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Education_requirement')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-education_requirement" class="form-control" name="row[education_requirement]" type="text" value="{$row.education_requirement|htmlentities}" placeholder="例如：大专及以上">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Experience_requirement')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-experience_requirement" class="form-control" name="row[experience_requirement]" type="text" value="{$row.experience_requirement|htmlentities}" placeholder="例如：1年以上相关工作经验">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Working_hours')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-working_hours" class="form-control" name="row[working_hours]" type="text" value="{$row.working_hours|htmlentities}" placeholder="例如：8小时/天，双休">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Accommodation')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-accommodation" class="form-control" name="row[accommodation]" type="text" value="{$row.accommodation|htmlentities}" placeholder="例如：提供住宿，餐补">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Job_description')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-job_description" class="form-control" rows="5" name="row[job_description]" cols="50" placeholder="请详细描述岗位职责、工作内容等">{$row.job_description|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Total_fee')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-total_fee" data-rule="required;number" class="form-control" name="row[total_fee]" type="text" value="{$row.total_fee|htmlentities}" placeholder="请输入总费用(数字)">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" class="form-control" rows="5" name="row[remark]" cols="50" placeholder="其他补充说明信息">{$row.remark|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Job_summary')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-job_summary" class="form-control" rows="5" name="row[job_summary]" cols="50" placeholder="岗位亮点、优势等简要说明">{$row.job_summary|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group" style="display: none;">
        <label class="control-label col-xs-12 col-sm-2">{:__('Resume_count')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-resume_count" class="form-control" name="row[resume_count]" type="number" value="{$row.resume_count|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>
        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

<style>
    .faupload-preview {
        margin-top: 10px;
    }
    .faupload-preview .img-preview {
        width: auto;
        height: 150px;
        border: 1px solid #ddd;
        border-radius: 4px;
        overflow: hidden;
        margin-right: 10px;
        margin-bottom: 10px;
    }
    .faupload-preview .img-preview img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    .category-option {
        display: flex;
        align-items: center;
        padding: 5px 0;
    }
    .category-option-img {
        width: 40px;
        height: 40px;
        object-fit: cover;
        border-radius: 4px;
        margin-right: 10px;
    }
    .category-option-name {
        font-size: 14px;
        color: #333;
    }
    .bootstrap-select .dropdown-menu {
        min-width: 300px;
    }
    .bootstrap-select .dropdown-menu .inner {
        max-height: 400px;
    }
    .bootstrap-select .dropdown-menu .inner .selected {
        background-color: #f5f5f5;
    }
    .bootstrap-select .dropdown-menu .inner .selected .category-option {
        background-color: #f5f5f5;
    }
    .mt-10 {
        margin-top: 10px;
    }
    .ml-10 {
        margin-left: 10px;
    }
</style>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // AI解析岗位信息
        document.getElementById('btn-parse-job-info')?.addEventListener('click', function() {
            const content = document.getElementById('ai-job-content').value.trim();
            const statusElem = document.getElementById('ai-parse-status');
            
            if (!content) {
                Toastr.error('请先输入岗位信息');
            return;
        }
            
            // 显示加载状态
            this.disabled = true;
            statusElem.innerHTML = '<i class="fa fa-spinner fa-spin"></i> 正在分析岗位信息...';
            
            // 发送请求到后端
            $.ajax({
                url: '{:url("admin/jobs/parseJobInfo")}',
                type: 'POST',
                data: { content: content },
                dataType: 'json',
                success: function(res) {
                    if (res.code === 1) {
                        // 填充表单
                        fillJobForm(res.data);
                        statusElem.innerHTML = '<span class="text-success"><i class="fa fa-check-circle"></i> 解析成功</span>';
                        Toastr.success('岗位信息解析成功，已填充表单');
                    } else {
                        statusElem.innerHTML = '<span class="text-danger"><i class="fa fa-times-circle"></i> 解析失败</span>';
                        Toastr.error('解析失败: ' + res.msg);
                    }
                },
                error: function() {
                    statusElem.innerHTML = '<span class="text-danger"><i class="fa fa-times-circle"></i> 请求错误</span>';
                    Toastr.error('请求发生错误，请重试');
                },
                complete: function() {
                    document.getElementById('btn-parse-job-info').disabled = false;
                }
            });
        });
        
        // 填充岗位表单
        function fillJobForm(data) {
            // 填充各字段
            if (data.title) document.getElementById('c-title').value = data.title;
            if (data.company) document.getElementById('c-company').value = data.company;
            if (data.job_code) document.getElementById('c-job_code').value = data.job_code;
            if (data.job_name) document.getElementById('c-job_name').value = data.job_name;
            if (data.salary_range) document.getElementById('c-salary_range').value = data.salary_range;
            if (data.age_requirement) document.getElementById('c-age_requirement').value = data.age_requirement;
            if (data.gender_requirement) document.getElementById('c-gender_requirement').value = data.gender_requirement;
            if (data.language_requirement) document.getElementById('c-language_requirement').value = data.language_requirement;
            if (data.education_requirement) document.getElementById('c-education_requirement').value = data.education_requirement;
            if (data.experience_requirement) document.getElementById('c-experience_requirement').value = data.experience_requirement;
            if (data.working_hours) document.getElementById('c-working_hours').value = data.working_hours;
            if (data.accommodation) document.getElementById('c-accommodation').value = data.accommodation;
            if (data.job_description) document.getElementById('c-job_description').value = data.job_description;
            if (data.total_fee) document.getElementById('c-total_fee').value = data.total_fee;
            if (data.remark) document.getElementById('c-remark').value = data.remark;
            if (data.job_summary) document.getElementById('c-job_summary').value = data.job_summary;
            
            // 处理分类下拉框
            if (data.category) {
                // 使用bootstrap-select的selectpicker方法选择对应选项
                $('#c-category').selectpicker('val', data.category);
            }
        }
    });
</script>
