<script src="https://cdn.staticfile.org/jquery/3.6.0/jquery.min.js"></script>
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="type">
            <li class="active"><a href="#t-all" data-value="" data-toggle="tab">{:__('全部')}</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div class="panel-intro">
                        <div class="panel-lead"><em>Word模板管理</em>用于管理简历导出时使用的Word模板</div>
                    </div>
                    
                    <!-- 工具栏 -->
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="刷新" onclick="refreshTemplateList()"><i class="fa fa-refresh"></i> 刷新列表</a>
                        <a href="javascript:;" class="btn btn-success" title="上传模板" onclick="showWordUploadModal()"><i class="fa fa-upload"></i> 上传模板</a>
                        <a href="{:url('template/index')}" class="btn btn-info" title="返回"><i class="fa fa-reply"></i> 返回</a>
                    </div>
                    
                    <!-- 模板列表 -->
                    <div class="template-list-container">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> Word模板列表，支持预览、下载和删除操作
                        </div>
                        
                        <div id="wordTemplateList" class="template-list">
                            <!-- 模板列表将通过JavaScript动态加载 -->
                            <div class="text-center mt-20 mb-20">
                                <i class="fa fa-spinner fa-spin fa-2x"></i>
                                <p class="mt-10">正在加载模板列表...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Word模板上传模态框 -->
<div class="modal fade" id="wordUploadModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">上传Word模板</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="input-group">
                            <input type="text" class="form-control" id="wordTemplateFile" readonly placeholder="请选择Word模板文件">
                            <span class="input-group-btn">
                                <label class="btn btn-primary" for="uploadWordTemplate">
                                    <i class="fa fa-folder-open"></i> 浏览
                                </label>
                            </span>
                        </div>
                        <input type="file" id="uploadWordTemplate" accept=".docx" style="display:none">
                        <p class="help-block">支持.docx格式的Word文件</p>
                    </div>
                </div>
                <div id="wordTemplateUploadProgress" class="progress" style="display:none; margin-top:10px;">
                    <div class="progress-bar progress-bar-success" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="uploadWordTemplateFile()">
                    <i class="fa fa-cloud-upload"></i> 上传模板
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* 模板列表样式 */
.template-list-container {
    padding: 15px;
}

.template-list {
    margin-top: 15px;
}

.template-item {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    padding: 15px;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.template-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.template-icon {
    font-size: 36px;
    margin-right: 20px;
    color: #2b579a; /* Word蓝色 */
    flex-shrink: 0;
}

.template-info {
    flex: 1;
    min-width: 0;
}

.template-name {
    font-weight: 500;
    font-size: 16px;
    margin-bottom: 5px;
    color: #333;
}

.template-meta {
    display: flex;
    flex-wrap: wrap;
    color: #777;
    font-size: 13px;
}

.template-meta span {
    margin-right: 15px;
}

.template-actions {
    display: flex;
    margin-left: 15px;
}

.template-actions .btn {
    margin-left: 5px;
}

.mt-10 {
    margin-top: 10px;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}

/* 删除进度条容器 */
.delete-progress-container {
    width: 100%;
    margin-top: 10px;
    flex-basis: 100%;
    clear: both;
    order: 999;
}

/* 空模板列表提示 */
.empty-template-list {
    padding: 30px;
    text-align: center;
    background: #f9f9f9;
    border-radius: 6px;
    border: 2px dashed #ddd;
}

.empty-template-list i {
    font-size: 48px;
    color: #ddd;
    margin-bottom: 15px;
}

.empty-template-list h4 {
    margin-bottom: 15px;
    color: #777;
}
</style>

<script>
// 全局变量，用于跟踪加载状态
window.wordTemplateLoaded = false;

// 确保在DOM完全加载后执行
document.addEventListener("DOMContentLoaded", function() {
    initFileSelection();
    loadWordTemplateList();
});



// 初始化文件选择功能
function initFileSelection() {
    // 确保文件输入框存在
    if (document.getElementById('uploadWordTemplate')) {
        // 初始化文件选择交互
        $('#uploadWordTemplate').on('change', function() {
            if (this.files && this.files.length > 0) {
                $('#wordTemplateFile').val(this.files[0].name);
                console.log('Word模板文件已选择:', this.files[0].name);
            } else {
                $('#wordTemplateFile').val('');
                console.log('没有选择Word模板文件');
            }
        });
        
        // 确保点击"浏览"按钮时能触发文件选择对话框
        $('label[for="uploadWordTemplate"]').on('click', function(e) {
            e.preventDefault();
            $('#uploadWordTemplate').click();
        });
        
        // 点击输入框也触发文件选择
        $('#wordTemplateFile').on('click', function() {
            $('#uploadWordTemplate').click();
        });
        
    } else {
    }
}

$(function() {
    // 尝试加载模板列表
    loadWordTemplateList();
});

// 刷新模板列表
function refreshTemplateList() {
    loadWordTemplateList();
}

// 加载Word模板列表
function loadWordTemplateList() {
    const templateContainer = document.getElementById('wordTemplateList');
    
    // 显示加载中
    templateContainer.innerHTML = `
    <div class="text-center mt-20 mb-20">
        <i class="fa fa-spinner fa-spin fa-2x"></i>
        <p class="mt-10">正在加载模板列表...</p>
    </div>`;
    
    // 加载模板列表
    $.ajax({
        url: '{:url("admin/template/getWordTemplateList")}',
        type: 'GET',
        dataType: 'json',
        cache: false, // 禁用缓存
        timeout: 10000, // 设置超时时间
        success: function(res) {
            if (res.code === 1 && res.data.length > 0) {
                let html = '';
                res.data.forEach(template => {
                    // 将路径中的反斜杠替换为正斜杠
                    const templatePath = template.path.replace(/\\/g, '/');
                    html += `
                    <div class="template-item" data-template="${template.path}" data-name="${template.name}">
                        <div class="template-icon">
                            <i class="fa fa-file-word-o"></i>
                        </div>
                        <div class="template-info">
                            <div class="template-name">${template.name}</div>
                            <div class="template-meta">
                                <span><i class="fa fa-hdd-o"></i> ${formatFileSize(template.size)}</span>
                                <span><i class="fa fa-clock-o"></i> 更新时间: ${template.modified}</span>
                            </div>
                        </div>
                        <div class="template-actions">
                            <a href="{:url('template/WordTemplateDetail')}?path=${encodeURIComponent(templatePath)}" class="btn btn-warning btn-sm">
                                <i class="fa fa-detail"></i> 模版详情
                            </a>
                            <a href="{:url('template/downloadTemplate')}?path=${encodeURIComponent(templatePath)}&type=word" class="btn btn-success btn-sm">
                                <i class="fa fa-download"></i> 下载
                            </a>
                            <button class="btn btn-danger btn-sm" onclick="deleteTemplate(event, '${templatePath}', 'word')">
                                <i class="fa fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>`;
                });
                
                templateContainer.innerHTML = html;
                
                // 设置加载成功标志
                window.wordTemplateLoaded = true;
            } else {
                templateContainer.innerHTML = `
                <div class="empty-template-list">
                    <i class="fa fa-file-word-o"></i>
                    <h4>暂无Word模板</h4>
                    <p>您可以点击上方的"上传模板"按钮添加新的Word模板</p>
                    <button class="btn btn-success btn-lg mt-10" onclick="showWordUploadModal()">
                        <i class="fa fa-upload"></i> 上传Word模板
                    </button>
                </div>`;
                
                // 设置加载成功标志（即使没有模板也算成功加载）
                window.wordTemplateLoaded = true;
            }
        },
        error: function() {
            templateContainer.innerHTML = `
            <div class="alert alert-danger">
                <i class="fa fa-times-circle"></i> 加载模板列表失败，请刷新页面重试
                <button class="btn btn-sm btn-danger ml-10" onclick="loadWordTemplateList()">
                    <i class="fa fa-refresh"></i> 重试
                </button>
            </div>`;
            
            // 3秒后自动重试
            setTimeout(function() {
                loadWordTemplateList();
            }, 3000);
        }
    });
}

// 显示Word模板上传模态框
function showWordUploadModal() {
    // 重置文件选择
    $('#wordTemplateFile').val('');
    $('#uploadWordTemplate').val('');
    
    // 隐藏进度条
    $('#wordTemplateUploadProgress').hide();
    $('#wordTemplateUploadProgress .progress-bar').width('0%');
    
    // 显示模态框
    $('#wordUploadModal').modal('show');
}

// 上传Word模板文件
function uploadWordTemplateFile() {
    const fileInput = document.getElementById('uploadWordTemplate');
    if (!fileInput.files.length) {
        Toastr.error('请选择Word模板文件');
        return;
    }
    
    const file = fileInput.files[0];
    if (!file.name.endsWith('.docx')) {
        Toastr.error('请选择.docx格式的Word文件');
        return;
    }
    
    // 显示进度条
    const progressBar = document.querySelector('#wordTemplateUploadProgress .progress-bar');
    $('#wordTemplateUploadProgress').show();
    progressBar.style.width = '0%';
    
    // 创建FormData对象
    const formData = new FormData();
    formData.append('template_file', file);
    
    $.ajax({
        url: '{:url("admin/template/uploadTemplate")}?type=word',
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percent = Math.round((e.loaded / e.total) * 100);
                    progressBar.style.width = percent + '%';
                }
            });
            return xhr;
        },
        success: function(res) {
            if (res.code === 1) {
                progressBar.classList.add('progress-bar-success');
                Toastr.success('Word模板上传成功');
                
                // 清空文件选择
                $('#wordTemplateFile').val('');
                fileInput.value = '';
                
                // 重新加载模板列表
                loadWordTemplateList();
                
                // 延迟隐藏进度条和关闭模态框
                setTimeout(() => {
                    $('#wordTemplateUploadProgress').hide();
                    progressBar.classList.remove('progress-bar-success');
                    $('#wordUploadModal').modal('hide');
                }, 1000);
            } else {
                progressBar.classList.add('progress-bar-danger');
                Toastr.error('上传失败：' + res.msg);
                
                // 延迟隐藏进度条
                setTimeout(() => {
                    $('#wordTemplateUploadProgress').hide();
                    progressBar.classList.remove('progress-bar-danger');
                }, 2000);
            }
        },
        error: function() {
            progressBar.classList.add('progress-bar-danger');
            Toastr.error('上传失败：服务器错误');
            
            // 延迟隐藏进度条
            setTimeout(() => {
                $('#wordTemplateUploadProgress').hide();
                progressBar.classList.remove('progress-bar-danger');
            }, 2000);
        }
    });
}

// 删除模板文件
function deleteTemplate(event, templatePath, type) {
    // 阻止事件冒泡，避免触发模板选择
    event.stopPropagation();
    
    // 二次确认
    if (!confirm('确定要删除该模板文件吗？删除后无法恢复。')) {
        return;
    }
    
    // 获取文件名用于显示
    const fileName = templatePath.substring(templatePath.lastIndexOf('/') + 1);
    
    // 获取当前点击的模板项
    const templateItem = event.target.closest('.template-item');
    
    // 检查是否已有进度条，如果没有则添加
    let progressContainer = templateItem.querySelector('.delete-progress-container');
    if (!progressContainer) {
        progressContainer = document.createElement('div');
        progressContainer.className = 'delete-progress-container';
        progressContainer.style.width = '100%';
        progressContainer.style.marginTop = '10px';
        progressContainer.style.flexBasis = '100%';
        progressContainer.style.clear = 'both';
        progressContainer.style.order = '999';
        
        const progress = document.createElement('div');
        progress.className = 'progress';
        progress.style.marginBottom = '5px';
        
        const progressBar = document.createElement('div');
        progressBar.className = 'progress-bar progress-bar-striped active';
        progressBar.role = 'progressbar';
        progressBar.style.width = '0%';
        
        const progressStatus = document.createElement('div');
        progressStatus.className = 'text-center';
        progressStatus.style.fontSize = '12px';
        progressStatus.textContent = `准备删除文件: ${fileName}`;
        
        progress.appendChild(progressBar);
        progressContainer.appendChild(progress);
        progressContainer.appendChild(progressStatus);
        templateItem.appendChild(progressContainer);
        
        // 强制模板项使用flex布局并允许换行
        templateItem.style.display = 'flex';
        templateItem.style.flexWrap = 'wrap';
    }
    
    const progressBar = progressContainer.querySelector('.progress-bar');
    const progressStatus = progressContainer.querySelector('.text-center');
    
    // 设置初始状态
    progressBar.style.width = '10%';
    progressStatus.textContent = `准备删除文件: ${fileName}`;
    
    // 模拟进度
    setTimeout(() => {
        progressBar.style.width = '30%';
        progressStatus.textContent = `正在删除文件: ${fileName}`;
    }, 300);
    
    setTimeout(() => {
        progressBar.style.width = '60%';
    }, 600);
    
    // 发送删除请求
    $.ajax({
        url: '{:url("admin/template/deleteTemplate")}',
        type: 'POST',
        data: {
            template_path: templatePath,
            type: type
        },
        dataType: 'json',
        success: function(res) {
            // 设置进度为90%
            progressBar.style.width = '90%';
            
            setTimeout(() => {
                // 设置进度为100%
                progressBar.style.width = '100%';
                
                if (res.code === 1) {
                    // 成功状态
                    progressBar.classList.remove('progress-bar-striped', 'active');
                    progressBar.classList.add('progress-bar-success');
                    progressStatus.textContent = '删除成功！';
                    
                    // 延迟移除模板项并重新加载列表
                    setTimeout(() => {
                        Toastr.success('模板删除成功');
                        // 重新加载模板列表
                        loadWordTemplateList();
                    }, 1000);
                } else {
                    // 失败状态
                    progressBar.classList.remove('progress-bar-striped', 'active');
                    progressBar.classList.add('progress-bar-danger');
                    progressStatus.textContent = `删除失败: ${res.msg}`;
                    
                    // 延迟显示错误信息
                    setTimeout(() => {
                        Toastr.error('删除失败：' + res.msg);
                        console.error('删除失败', res);
                    }, 1000);
                }
            }, 300);
        },
        error: function(xhr, status, error) {
            // 失败状态
            progressBar.style.width = '100%';
            progressBar.classList.remove('progress-bar-striped', 'active');
            progressBar.classList.add('progress-bar-danger');
            progressStatus.textContent = '删除失败：服务器错误';
            
            // 延迟显示错误信息
            setTimeout(() => {
                Toastr.error('删除失败：服务器错误');
                console.error('删除请求失败', {xhr: xhr, status: status, error: error});
            }, 1000);
        }
    });
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}
</script> 