import { get, post } from './request';

/**
 * 账号密码登录
 * @param {Object} data - 登录数据
 * @param {string} data.account - 账号
 * @param {string} data.password - 密码
 */
export const login = (data) => {
  return post('/user/login', data);
};

/**
 * 手机验证码登录
 * @param {Object} data - 登录数据
 * @param {string} data.mobile - 手机号
 * @param {string} data.captcha - 验证码
 */
export const mobileLogin = (data) => {
  return post('/user/mobilelogin', data);
};

/**
 * 微信小程序登录
 * @param {Object} data - 登录数据
 * @param {string} data.code - 微信登录code
 */
export const wxLogin = (data) => {
  return post('/user/wx_login', data);
};

/**
 * 获取用户个人信息
 */
export const getProfile = () => {
  return get('/user/profile', {}, { auth: true });
};

/**
 * 更新用户个人信息
 * @param {Object} data - 用户信息
 */
export const updateProfile = (data) => {
  return post('/user/updateProfile', data, { auth: true });
};

/**
 * 获取用户投递记录
 */
export const getApplyHistory = (params = {}) => {
  return get('/user/apply_history', params, { auth: true });
};

/**
 * 获取用户收藏列表
 */
export const getFavorites = (params = {}) => {
  return get('/user/favorites', params, { auth: true });
};

/**
 * 添加收藏
 * @param {number} jobId - 职位ID
 */
export const addFavorite = (jobId) => {
  return post('/user/favorite', { job_id: jobId }, { auth: true });
};

/**
 * 取消收藏
 * @param {number} jobId - 职位ID
 */
export const removeFavorite = (jobId) => {
  return post('/user/unfavorite', { job_id: jobId }, { auth: true });
};

/**
 * 检查职位是否已收藏
 * @param {number} jobId - 职位ID
 */
export const checkFavorite = (jobId) => {
  return get(`/user/is_favorite/${jobId}`, {}, { auth: true });
};

/**
 * 发送短信验证码
 * @param {Object} data - 数据
 * @param {string} data.mobile - 手机号
 * @param {string} data.event - 事件类型
 */
export const sendSmsCode = (data) => {
  return post('/sms/send', data);
};

/**
 * 用户注册
 * @param {Object} data - 注册数据
 */
export const register = (data) => {
  return post('/user/register', data);
};

/**
 * 重置密码
 * @param {Object} data - 重置密码数据
 */
export const resetPassword = (data) => {
  return post('/user/resetpwd', data);
};

/**
 * 退出登录
 */
export const logout = () => {
  return post('/user/logout', {}, { auth: true });
};

/**
 * 更新微信用户信息
 * @param {Object} data - 用户信息
 * @param {string} data.nickname - 昵称
 * @param {string} data.avatar - 头像
 * @param {number} data.gender - 性别
 */
export const updateWxUserInfo = (data) => {
  return post('/user/update_wx_userinfo', data, { auth: true });
};

/**
 * 解密获取微信手机号
 * @param {Object} data - 加密数据
 * @param {string} data.encryptedData - 加密数据
 * @param {string} data.iv - 加密算法的初始向量
 */
export const wxGetPhone = (data) => {
  return post('/user/wx_get_phone', data, { auth: true });
};

/**
 * 加密方式更新微信用户信息
 * @param {Object} data - 加密数据
 * @param {string} data.encryptedData - 加密数据
 * @param {string} data.iv - 加密算法的初始向量
 */
export const wxUpdateUserInfo = (data) => {
  return post('/user/wx_update_userinfo', data, { auth: true });
};

/**
 * 非加密方式更新微信用户信息
 * @param {Object} data - 用户信息
 * @param {string} data.nickname - 昵称
 * @param {string} data.avatar - 头像
 * @param {number} data.gender - 性别
 */
export const wxUpdateProfile = (data) => {
  return post('/user/wx_update_profile', data, { auth: true });
};

export default {
  login,
  mobileLogin,
  wxLogin,
  getProfile,
  updateProfile,
  getApplyHistory,
  getFavorites,
  addFavorite,
  removeFavorite,
  checkFavorite,
  sendSmsCode,
  register,
  resetPassword,
  logout,
  updateWxUserInfo,
  wxGetPhone,
  wxUpdateUserInfo,
  wxUpdateProfile
}; 