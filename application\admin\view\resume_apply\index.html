<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-heading">
        <ul class="nav nav-tabs" data-field="status">
            <li class="{:$Think.get.status === null ? 'active' : ''}"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            {foreach name="statusList" item="vo"}
            <li class="{:$Think.get.status === (string)$key ? 'active' : ''}"><a href="#t-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>
            {/foreach}
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {if isset($job_id) && $job_id}
                        <a href="javascript:;" class="btn btn-primary btn-return" title="{:__('Back')}" ><i class="fa fa-reply"></i> {:__('Back')}</a>
                        {/if}
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('resume_apply/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('resume_apply/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('resume_apply/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>
                        <a href="javascript:;" class="btn btn-warning btn-batch-update btn-disabled disabled {:$auth->check('resume_apply/batch_update')?'':'hide'}" title="批量更新状态"><i class="fa fa-check-square-o"></i> <span class="hidden-xs">批量更新状态</span></a>

                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover"
                           data-operate-edit="{:$auth->check('resume_apply/edit')}"
                           data-operate-del="{:$auth->check('resume_apply/del')}"
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量更新状态模态框 -->
<div class="modal fade" id="batchUpdateModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">批量更新投递状态</h4>
            </div>
            <div class="modal-body">
                <form id="batch-update-form">
                    <div class="form-group">
                        <label for="status">选择状态 <span class="text-danger">*</span></label>
                        <select class="form-control" id="batch-status" name="status" required>
                            <option value="">请选择状态</option>
                            {foreach name="statusList" item="vo" key="key"}
                            <option value="{$key}">{$vo}</option>
                            {/foreach}
                        </select>
                    </div>
                    
                    <!-- 面试选择区域，当状态为"已选中"时显示 -->
                    <div id="batch-interview-section" class="hide">
                        <div class="form-group">
                            <label for="interview_action">面试安排</label>
                            <div>
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="interview_action" value="none" checked> 
                                        不安排面试
                                    </label>
                                </div>
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="interview_action" value="existing"> 
                                        添加到现有面试
                                    </label>
                                </div>
                                <div class="radio">
                                    <label>
                                        <input type="radio" name="interview_action" value="new"> 
                                        创建新面试
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 选择现有面试区域 -->
                        <div id="batch-existing-interview-section" class="hide">
                            <div class="form-group">
                                <label for="interview_id">选择面试</label>
                                <select class="form-control" id="batch-interview-id" name="interview_id">
                                    <option value="">请选择面试</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 创建新面试区域 -->
                        <div id="batch-new-interview-section" class="hide">
                            <div class="form-group">
                                <label for="interview_title">面试标题</label>
                                <input class="form-control" id="batch-interview-title" name="interview_title" type="text" placeholder="请输入面试标题">
                            </div>
                            <div class="form-group">
                                <label for="interview_time">面试时间</label>
                                <input class="form-control datetimepicker" id="batch-interview-time" name="interview_time" type="text">
                            </div>
                            <div class="form-group">
                                <label for="interview_type">面试类型</label>
                                <select class="form-control" id="batch-interview-type" name="interview_type">
                                    <option value="线上面试">线上面试</option>
                                    <option value="现场面试">现场面试</option>
                                    <option value="入港面试">入港面试</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="interview_materials">面试资料</label>
                                <textarea class="form-control" id="batch-interview-materials" name="interview_materials" rows="3" placeholder="请输入面试资料"></textarea>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="feedback">反馈意见</label>
                        <textarea class="form-control" id="batch-feedback" name="feedback" rows="4" placeholder="请输入反馈意见（可选）"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="btn-batch-update">确定</button>
            </div>
        </div>
    </div>
</div> 