<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\library\Ems;
use app\common\library\Sms;
use fast\Random;
use think\Config;
use think\Validate;
use think\Db;

/**
 * 会员接口
 */
class User extends Api
{
    protected $noNeedLogin = ['login', 'mobilelogin', 'register', 'resetpwd', 'changeemail', 'changemobile', 'third', 'wx_login', 'wx_decrypt_data', 'wx_get_phone', 'wx_update_userinfo', 'wx_update_profile'];
    protected $noNeedRight = '*';

    public function _initialize()
    {
        parent::_initialize();

        if (!Config::get('fastadmin.usercenter')) {
            $this->error(__('User center already closed'));
        }

    }

    /**
     * 会员中心
     */
    public function index()
    {
        $this->success('', ['welcome' => $this->auth->nickname]);
    }

    /**
     * 会员登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="account", type="string", required=true, description="账号")
     * @ApiParams (name="password", type="string", required=true, description="密码")
     */
    public function login()
    {
        $account = $this->request->post('account');
        $password = $this->request->post('password');
        if (!$account || !$password) {
            $this->error(__('Invalid parameters'));
        }
        $ret = $this->auth->login($account, $password);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 手机验证码登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function mobilelogin()
    {
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (!Sms::check($mobile, $captcha, 'mobilelogin')) {
            $this->error(__('Captcha is incorrect'));
        }
        $user = \app\common\model\User::getByMobile($mobile);
        if ($user) {
            if ($user->status != 'normal') {
                $this->error(__('Account is locked'));
            }
            //如果已经有账号则直接登录
            $ret = $this->auth->direct($user->id);
        } else {
            $ret = $this->auth->register($mobile, Random::alnum(), '', $mobile, []);
        }
        if ($ret) {
            Sms::flush($mobile, 'mobilelogin');
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Logged in successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 注册会员
     *
     * @ApiMethod (POST)
     * @ApiParams (name="username", type="string", required=true, description="用户名")
     * @ApiParams (name="password", type="string", required=true, description="密码")
     * @ApiParams (name="email", type="string", required=true, description="邮箱")
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="code", type="string", required=true, description="验证码")
     */
    public function register()
    {
        $username = $this->request->post('username');
        $password = $this->request->post('password');
        $email = $this->request->post('email');
        $mobile = $this->request->post('mobile');
        $code = $this->request->post('code');
        if (!$username || !$password) {
            $this->error(__('Invalid parameters'));
        }
        if ($email && !Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if ($mobile && !Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        $ret = Sms::check($mobile, $code, 'register');
        if (!$ret) {
            $this->error(__('Captcha is incorrect'));
        }
        $ret = $this->auth->register($username, $password, $email, $mobile, []);
        if ($ret) {
            $data = ['userinfo' => $this->auth->getUserinfo()];
            $this->success(__('Sign up successful'), $data);
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 退出登录
     * @ApiMethod (POST)
     */
    public function logout()
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }
        $this->auth->logout();
        $this->success(__('Logout successful'));
    }

    /**
     * 修改会员个人信息
     *
     * @ApiMethod (POST)
     * @ApiParams (name="avatar", type="string", required=true, description="头像地址")
     * @ApiParams (name="username", type="string", required=true, description="用户名")
     * @ApiParams (name="nickname", type="string", required=true, description="昵称")
     * @ApiParams (name="bio", type="string", required=true, description="个人简介")
     */
    public function profile()
    {
        $user_id = $this->auth->id;
        
        $user = Db::name('user')->where('id', $user_id)->find();
        if (!$user) {
            $this->error('用户不存在');
            }
        
        // 过滤敏感信息
        unset($user['password'], $user['salt'], $user['wx_session_key']);
        
        $this->success('获取成功', $user);
    }

    /**
     * 修改邮箱
     *
     * @ApiMethod (POST)
     * @ApiParams (name="email", type="string", required=true, description="邮箱")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function changeemail()
    {
        $user = $this->auth->getUser();
        $email = $this->request->post('email');
        $captcha = $this->request->post('captcha');
        if (!$email || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::is($email, "email")) {
            $this->error(__('Email is incorrect'));
        }
        if (\app\common\model\User::where('email', $email)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Email already exists'));
        }
        $result = Ems::check($email, $captcha, 'changeemail');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->email = 1;
        $user->verification = $verification;
        $user->email = $email;
        $user->save();

        Ems::flush($email, 'changeemail');
        $this->success();
    }

    /**
     * 修改手机号
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function changemobile()
    {
        $user = $this->auth->getUser();
        $mobile = $this->request->post('mobile');
        $captcha = $this->request->post('captcha');
        if (!$mobile || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error(__('Mobile is incorrect'));
        }
        if (\app\common\model\User::where('mobile', $mobile)->where('id', '<>', $user->id)->find()) {
            $this->error(__('Mobile already exists'));
        }
        $result = Sms::check($mobile, $captcha, 'changemobile');
        if (!$result) {
            $this->error(__('Captcha is incorrect'));
        }
        $verification = $user->verification;
        $verification->mobile = 1;
        $user->verification = $verification;
        $user->mobile = $mobile;
        $user->save();

        Sms::flush($mobile, 'changemobile');
        $this->success();
    }

    /**
     * 第三方登录
     *
     * @ApiMethod (POST)
     * @ApiParams (name="platform", type="string", required=true, description="平台名称")
     * @ApiParams (name="code", type="string", required=true, description="Code码")
     */
    public function third()
    {
        $url = url('user/index');
        $platform = $this->request->post("platform");
        $code = $this->request->post("code");
        $config = get_addon_config('third');
        if (!$config || !isset($config[$platform])) {
            $this->error(__('Invalid parameters'));
        }
        $app = new \addons\third\library\Application($config);
        //通过code换access_token和绑定会员
        $result = $app->{$platform}->getUserInfo(['code' => $code]);
        if ($result) {
            $loginret = \addons\third\library\Service::connect($platform, $result);
            if ($loginret) {
                $data = [
                    'userinfo'  => $this->auth->getUserinfo(),
                    'thirdinfo' => $result
                ];
                $this->success(__('Logged in successful'), $data);
            }
        }
        $this->error(__('Operation failed'), $url);
    }

    /**
     * 重置密码
     *
     * @ApiMethod (POST)
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="newpassword", type="string", required=true, description="新密码")
     * @ApiParams (name="captcha", type="string", required=true, description="验证码")
     */
    public function resetpwd()
    {
        $type = $this->request->post("type", "mobile");
        $mobile = $this->request->post("mobile");
        $email = $this->request->post("email");
        $newpassword = $this->request->post("newpassword");
        $captcha = $this->request->post("captcha");
        if (!$newpassword || !$captcha) {
            $this->error(__('Invalid parameters'));
        }
        //验证Token
        if (!Validate::make()->check(['newpassword' => $newpassword], ['newpassword' => 'require|regex:\S{6,30}'])) {
            $this->error(__('Password must be 6 to 30 characters'));
        }
        if ($type == 'mobile') {
            if (!Validate::regex($mobile, "^1\d{10}$")) {
                $this->error(__('Mobile is incorrect'));
            }
            $user = \app\common\model\User::getByMobile($mobile);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Sms::check($mobile, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Sms::flush($mobile, 'resetpwd');
        } else {
            if (!Validate::is($email, "email")) {
                $this->error(__('Email is incorrect'));
            }
            $user = \app\common\model\User::getByEmail($email);
            if (!$user) {
                $this->error(__('User not found'));
            }
            $ret = Ems::check($email, $captcha, 'resetpwd');
            if (!$ret) {
                $this->error(__('Captcha is incorrect'));
            }
            Ems::flush($email, 'resetpwd');
        }
        //模拟一次登录
        $this->auth->direct($user->id);
        $ret = $this->auth->changepwd($newpassword, '', true);
        if ($ret) {
            $this->success(__('Reset password successful'));
        } else {
            $this->error($this->auth->getError());
        }
    }

    /**
     * 微信小程序登录
     */
    public function wx_login()
    {
        $code = $this->request->post('code');
        if (empty($code)) {
            return json(['code' => 0, 'msg' => '参数错误：缺少code', 'time' => time(), 'data' => null]);
        }
        
        try {
            // 获取微信小程序配置，使用Wechat辅助类从site配置中加载
            $miniappConfig = \app\common\library\Wechat::config('miniapp');
            
            if (empty($miniappConfig) || empty($miniappConfig['appid']) || empty($miniappConfig['appsecret'])) {
                return json(['code' => 0, 'msg' => '微信小程序配置错误，请检查site.php中的配置', 'time' => time(), 'data' => null]);
            }
            
            $appid = $miniappConfig['appid'];
            $appsecret = $miniappConfig['appsecret'];
            
            // 记录日志
            \think\Log::write('微信登录: appid=' . $appid . ', code=' . $code, 'info');
        
            // 请求微信API获取openid和session_key
            $url = "https://api.weixin.qq.com/sns/jscode2session?appid={$appid}&secret={$appsecret}&js_code={$code}&grant_type=authorization_code";
            
            // 使用curl请求微信API
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_TIMEOUT, 10);
            $response = curl_exec($ch);
            $curl_error = curl_error($ch);
            curl_close($ch);
            
            if ($response === false) {
                \think\Log::write('微信登录失败: ' . $curl_error, 'error');
                return json(['code' => 0, 'msg' => '请求微信服务器失败: ' . $curl_error, 'time' => time(), 'data' => null]);
            }
            
            $result = json_decode($response, true);
            
            if (!is_array($result)) {
                \think\Log::write('微信登录失败: 返回数据格式错误', 'error');
                return json(['code' => 0, 'msg' => '微信返回数据格式错误', 'time' => time(), 'data' => null]);
            }
        
            if (isset($result['errcode']) && $result['errcode'] != 0) {
                \think\Log::write('微信登录失败: ' . json_encode($result), 'error');
                return json(['code' => 0, 'msg' => '微信登录失败：' . ($result['errmsg'] ?? '未知错误'), 'time' => time(), 'data' => null]);
            }
        
            $openid = isset($result['openid']) ? $result['openid'] : '';
            $session_key = isset($result['session_key']) ? $result['session_key'] : '';
        
            if (empty($openid)) {
                \think\Log::write('微信登录失败: 未返回openid', 'error');
                return json(['code' => 0, 'msg' => '获取微信用户信息失败', 'time' => time(), 'data' => null]);
            }
            
            \think\Log::write('微信登录成功: openid=' . $openid, 'info');
            
            // 根据openid查找用户
            $user = \think\Db::name('user')->where('wx_openid', $openid)->find();
        
            if (!$user) {
                // 创建新用户，使用默认信息
                $username = 'wx_' . substr($openid, -8);
                $nickname = '微信用户';
                $realname = '未填写';
                $password = \fast\Random::alnum(6);
                $salt = \fast\Random::alnum(6);
            
                $data = [
                    'username' => $username,
                    'nickname' => $nickname,
                    'realname' => $realname,
                    'avatar' => '/assets/img/avatar.png',
                    'wx_openid' => $openid,
                    'wx_unionid' => isset($result['unionid']) ? $result['unionid'] : '',
                    'wx_session_key' => $session_key,
                    'password' => md5(md5($password) . $salt),
                    'salt' => $salt,
                    'jointime' => time(),
                    'joinip' => request()->ip(),
                    'status' => 'normal',
                    'is_first_login' => 1
                ];
            
                // 插入用户
                $user_id = \think\Db::name('user')->insertGetId($data);
                if (!$user_id) {
                    \think\Log::write('微信登录失败: 新用户注册失败', 'error');
                    return json(['code' => 0, 'msg' => '注册失败', 'time' => time(), 'data' => null]);
                }
            
                $user = \think\Db::name('user')->where('id', $user_id)->find();
                \think\Log::write('微信用户注册成功: user_id=' . $user_id, 'info');
            } else {
                // 更新session_key
                \think\Db::name('user')->where('id', $user['id'])->update([
                    'wx_session_key' => $session_key,
                    'logintime' => time(),
                    'loginip' => request()->ip()
                ]);
                \think\Log::write('微信用户登录成功: user_id=' . $user['id'], 'info');
            }
        
            // 生成token
            $token = \fast\Random::uuid();
            \think\Db::name('user_token')->where('user_id', $user['id'])->delete();
            \think\Db::name('user_token')->insert([
                'token' => $token,
                'user_id' => $user['id'],
                'createtime' => time(),
                'expiretime' => time() + 30 * 24 * 3600 // 30天有效期
            ]);
        
            // 过滤敏感信息
            unset($user['password'], $user['salt'], $user['wx_session_key']);
            
            // 确保is_first_login字段为整数类型
            $user['is_first_login'] = (int)$user['is_first_login'];
            
            // 直接返回JSON响应，避免使用success方法
            return json([
                'code' => 1,
                'msg' => '登录成功',
                'time' => time(),
                'data' => [
                    'token' => $token,
                    'userInfo' => $user
                ]
            ]);
            
        } catch (\Exception $e) {
            \think\Log::write('微信登录异常: ' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => '微信登录异常: ' . $e->getMessage(), 'time' => time(), 'data' => null]);
        } catch (\Error $e) {
            \think\Log::write('微信登录错误: ' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => '微信登录错误: ' . $e->getMessage(), 'time' => time(), 'data' => null]);
        }
    }
    
    /**
     * 更新个人信息
     */
    public function updateProfile()
    {
        $user_id = $this->auth->id;
        $params = $this->request->post();
        
        $allowFields = ['nickname', 'avatar', 'gender', 'bio', 'mobile', 'email', 'realname'];
        $data = [];
        
        foreach ($allowFields as $field) {
            if (isset($params[$field])) {
                $data[$field] = $params[$field];
            }
        }
        
        if (empty($data)) {
            $this->error('未提供需要更新的信息');
        }
        
        $result = Db::name('user')->where('id', $user_id)->update($data);
        if ($result === false) {
            $this->error('更新失败');
        }
        
        $this->success('更新成功');
    }
    
    /**
     * 获取投递记录
     */
    public function apply_history()
    {
        $user_id = $this->auth->id;
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        
        $apply_list = Db::name('resume_apply')
            ->alias('a')
            ->join('jobs j', 'a.job_id = j.id')
            ->join('resume r', 'a.resume_id = r.id')
            ->where('a.user_id', $user_id)
            ->field('a.*, j.title as job_title, j.company, j.salary, r.title as resume_title')
            ->order('a.create_time', 'desc')
            ->page($page, $limit)
            ->select();
        
        $this->success('获取成功', $apply_list);
    }
    
    /**
     * 获取收藏列表
     */
    public function favorites()
    {
        $user_id = $this->auth->id;
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        
        $favorites = Db::name('user_favorite')
            ->alias('f')
            ->join('jobs j', 'f.job_id = j.id')
            ->where('f.user_id', $user_id)
            ->field('f.*, j.title, j.company, j.salary, j.category, j.education_requirement')
            ->order('f.create_time', 'desc')
            ->page($page, $limit)
            ->select();
        
        $this->success('获取成功', $favorites);
    }
    
    /**
     * 添加收藏
     */
    public function favorite()
    {
        $user_id = $this->auth->id;
        $job_id = $this->request->post('job_id');
        
        if (empty($job_id)) {
            $this->error('参数错误');
        }
        
        // 检查职位是否存在
        $job = Db::name('jobs')->where('id', $job_id)->find();
        if (!$job) {
            $this->error('职位不存在');
        }
        
        // 检查是否已收藏
        $exist = Db::name('user_favorite')
            ->where('user_id', $user_id)
            ->where('job_id', $job_id)
            ->find();
        
        if ($exist) {
            $this->error('已经收藏过该职位');
        }
        
        // 添加收藏
        $result = Db::name('user_favorite')->insert([
            'user_id' => $user_id,
            'job_id' => $job_id,
            'create_time' => time()
        ]);
        
        if (!$result) {
            $this->error('收藏失败');
        }
        
        $this->success('收藏成功');
    }
    
    /**
     * 取消收藏
     */
    public function unfavorite()
    {
        $user_id = $this->auth->id;
        $job_id = $this->request->post('job_id');
        
        if (empty($job_id)) {
            $this->error('参数错误');
        }
        
        // 删除收藏
        $result = Db::name('user_favorite')
            ->where('user_id', $user_id)
            ->where('job_id', $job_id)
            ->delete();
        
        if (!$result) {
            $this->error('取消收藏失败');
        }
        
        $this->success('取消收藏成功');
    }
    
    /**
     * 检查职位是否已收藏
     */
    public function is_favorite($job_id = null)
    {
        if (!$job_id) {
            $this->error('参数错误');
        }
        
        $user_id = $this->auth->id;
        
        // 检查是否已收藏
        $exist = Db::name('user_favorite')
            ->where('user_id', $user_id)
            ->where('job_id', $job_id)
            ->find();
        
        $this->success('', $exist ? true : false);
    }

    /**
     * 更新微信用户信息
     *
     * @ApiMethod (POST)
     * @ApiParams (name="nickname", type="string", required=true, description="昵称")
     * @ApiParams (name="avatar", type="string", required=true, description="头像")
     * @ApiParams (name="gender", type="integer", required=false, description="性别")
     */
    public function update_wx_userinfo()
    {
        $user_id = $this->auth->id;
        $params = $this->request->post();
        
        if (!isset($params['nickname']) || !isset($params['avatar'])) {
            $this->error('参数错误');
        }
        
        $data = [
            'nickname' => $params['nickname'],
            'avatar' => $params['avatar'],
            'is_first_login' => 0 // 更新信息后，不再是首次登录
        ];
        
        if (isset($params['gender'])) {
            $data['gender'] = intval($params['gender']);
        }
        
        $result = Db::name('user')->where('id', $user_id)->update($data);
        if ($result === false) {
            $this->error('更新失败');
        }
        
        // 获取更新后的用户信息
        $user = Db::name('user')->where('id', $user_id)->find();
        unset($user['password'], $user['salt'], $user['wx_session_key']);
        
        $this->success('更新成功', $user);
    }

    /**
     * 解密微信小程序用户数据
     *
     * @ApiMethod (POST)
     * @ApiParams (name="encryptedData", type="string", required=true, description="加密数据")
     * @ApiParams (name="iv", type="string", required=true, description="加密向量")
     * @ApiParams (name="token", type="string", required=true, description="用户token")
     */
    public function wx_decrypt_data()
    {
        try {
            $params = $this->request->post();
            
            // 优先从POST参数获取token
            $token = $params['token'] ?? '';
            
            // 如果POST中没有token，尝试从请求头获取
            if (empty($token)) {
                // 尝试从Token头获取
                $token = $this->request->header('Token', '');
                
                // 尝试从X-Token头获取
                if (empty($token)) {
                    $token = $this->request->header('X-Token', '');
                }
                
                // 尝试从Authorization头获取
                if (empty($token)) {
                    $authorization = $this->request->header('Authorization', '');
                    if (!empty($authorization) && strpos($authorization, 'Bearer ') === 0) {
                        $token = substr($authorization, 7);
                    }
                }
            }
            
            // 获取其他必要参数
            $encryptedData = $params['encryptedData'] ?? '';
            $iv = $params['iv'] ?? '';
            
            if (empty($encryptedData) || empty($iv) || empty($token)) {
                return json(['code' => 0, 'msg' => '参数错误', 'time' => time(), 'data' => null]);
            }
            
            // 通过token获取用户信息
            $tokenInfo = \think\Db::name('user_token')->where('token', $token)->find();
            if (!$tokenInfo) {
                return json(['code' => 0, 'msg' => '无效的token', 'time' => time(), 'data' => null]);
            }
            
            $user_id = $tokenInfo['user_id'];
            
            // 检查token是否过期
            if ($tokenInfo['expiretime'] < time()) {
                return json(['code' => 0, 'msg' => 'token已过期', 'time' => time(), 'data' => null]);
            }
            
            // 获取用户的session_key
            $user = Db::name('user')->where('id', $user_id)->find();
            if (!$user || empty($user['wx_session_key'])) {
                return json(['code' => 0, 'msg' => '未找到有效的会话密钥，请重新登录', 'time' => time(), 'data' => null]);
            }
            
            $session_key = $user['wx_session_key'];
            
            // 获取小程序配置，使用Wechat辅助类从site配置中加载
            $miniappConfig = \app\common\library\Wechat::config('miniapp');
            if (empty($miniappConfig) || empty($miniappConfig['appid'])) {
                \think\Log::write('小程序配置错误', 'error');
                return json(['code' => 0, 'msg' => '小程序配置错误', 'time' => time(), 'data' => null]);
            }
            
            $appid = $miniappConfig['appid'];
            
            // 判断是否有openssl扩展
            if (!function_exists('openssl_decrypt')) {
                return json(['code' => 0, 'msg' => '服务器缺少必要的解密功能', 'time' => time(), 'data' => null]);
            }
            
            $aesKey = base64_decode($session_key);
            $aesIV = base64_decode($iv);
            $aesCipher = base64_decode($encryptedData);
            
            $result = openssl_decrypt($aesCipher, "AES-128-CBC", $aesKey, 1, $aesIV);
            
            if ($result === false) {
                \think\Log::write('解密失败: ' . openssl_error_string(), 'error');
                return json(['code' => 0, 'msg' => '解密失败: ' . openssl_error_string(), 'time' => time(), 'data' => null]);
            }
            
            $dataObj = json_decode($result, true);
            if ($dataObj === null) {
                \think\Log::write('数据无效', 'error');
                return json(['code' => 0, 'msg' => '数据无效', 'time' => time(), 'data' => null]);
            }
            
            if ($dataObj['watermark']['appid'] !== $appid) {
                \think\Log::write('数据校验失败: appid不匹配', 'error');
                return json(['code' => 0, 'msg' => '数据校验失败', 'time' => time(), 'data' => null]);
            }
            
            // 根据数据类型更新用户信息
            if (isset($dataObj['nickName']) && isset($dataObj['avatarUrl'])) {
                // 这是用户信息
                Db::name('user')->where('id', $user_id)->update([
                    'nickname' => $dataObj['nickName'],
                    'avatar' => $dataObj['avatarUrl'],
                    'gender' => isset($dataObj['gender']) ? intval($dataObj['gender']) : 0,
                    'is_first_login' => 0
                ]);
                \think\Log::write('更新用户信息成功: user_id=' . $user_id, 'info');
            } elseif (isset($dataObj['phoneNumber'])) {
                // 这是手机号信息
                Db::name('user')->where('id', $user_id)->update([
                    'mobile' => $dataObj['phoneNumber']
                ]);
                \think\Log::write('更新用户手机号成功: user_id=' . $user_id, 'info');
            }
            
            return json([
                'code' => 1,
                'msg' => '解密成功',
                'time' => time(),
                'data' => $dataObj
            ]);
            
        } catch (\Exception $e) {
            \think\Log::write('解密异常: ' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => '解密异常: ' . $e->getMessage(), 'time' => time(), 'data' => null]);
        }
    }

    /**
     * 获取微信绑定的手机号
     *
     * @ApiMethod (POST)
     * @ApiParams (name="encryptedData", type="string", required=true, description="加密数据")
     * @ApiParams (name="iv", type="string", required=true, description="加密向量")
     * @ApiParams (name="token", type="string", required=true, description="用户token")
     */
    public function wx_get_phone()
    {
        $params = $this->request->post();
        
        // 记录请求日志
        \think\Log::record('微信获取手机号请求: ' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        
        // 优先从POST参数获取token
        $token = $params['token'] ?? '';
        
        // 如果POST中没有token，尝试从请求头获取
        if (empty($token)) {
            // 尝试从Token头获取
            $token = $this->request->header('Token', '');
            
            // 尝试从X-Token头获取
            if (empty($token)) {
                $token = $this->request->header('X-Token', '');
            }
            
            // 尝试从Authorization头获取
            if (empty($token)) {
                $authorization = $this->request->header('Authorization', '');
                if (!empty($authorization) && strpos($authorization, 'Bearer ') === 0) {
                    $token = substr($authorization, 7);
                }
            }
        }
        
        if (empty($token)) {
            return json(['code' => 0, 'msg' => '请先登录', 'time' => time(), 'data' => null]);
        }
        
        // 通过token获取用户信息
        $tokenInfo = \think\Db::name('user_token')->where('token', $token)->find();
        if (!$tokenInfo) {
            return json(['code' => 0, 'msg' => '无效的token', 'time' => time(), 'data' => null]);
        }
        
        $user_id = $tokenInfo['user_id'];
        
        // 检查token是否过期
        if ($tokenInfo['expiretime'] < time()) {
            return json(['code' => 0, 'msg' => 'token已过期', 'time' => time(), 'data' => null]);
        }
        
        if (empty($params['encryptedData']) || empty($params['iv'])) {
            return json(['code' => 0, 'msg' => '参数错误', 'time' => time(), 'data' => null]);
        }
        
        try {
            // 获取用户的session_key
            $user = Db::name('user')->where('id', $user_id)->find();
            if (!$user || empty($user['wx_session_key'])) {
                return json(['code' => 0, 'msg' => '未找到有效的会话密钥，请重新登录', 'time' => time(), 'data' => null]);
            }
            
            $session_key = $user['wx_session_key'];
            $encryptedData = $params['encryptedData'];
            $iv = $params['iv'];
            
            // 获取小程序配置，使用Wechat辅助类从site配置中加载
            $miniappConfig = \app\common\library\Wechat::config('miniapp');
            if (empty($miniappConfig) || empty($miniappConfig['appid'])) {
                \think\Log::write('小程序配置错误', 'error');
                return json(['code' => 0, 'msg' => '小程序配置错误', 'time' => time(), 'data' => null]);
            }
            
            $appid = $miniappConfig['appid'];
            
            // 判断是否有openssl扩展
            if (!function_exists('openssl_decrypt')) {
                return json(['code' => 0, 'msg' => '服务器缺少必要的解密功能', 'time' => time(), 'data' => null]);
            }
            
            $aesKey = base64_decode($session_key);
            $aesIV = base64_decode($iv);
            $aesCipher = base64_decode($encryptedData);
            
            $result = openssl_decrypt($aesCipher, "AES-128-CBC", $aesKey, 1, $aesIV);
            
            if ($result === false) {
                \think\Log::write('解密手机号失败: ' . openssl_error_string(), 'error');
                return json(['code' => 0, 'msg' => '解密失败: ' . openssl_error_string(), 'time' => time(), 'data' => null]);
            }
            
            $dataObj = json_decode($result, true);
            if ($dataObj === null) {
                \think\Log::write('手机号数据无效', 'error');
                return json(['code' => 0, 'msg' => '数据无效', 'time' => time(), 'data' => null]);
            }
            
            if ($dataObj['watermark']['appid'] !== $appid) {
                \think\Log::write('手机号数据校验失败: appid不匹配', 'error');
                return json(['code' => 0, 'msg' => '数据校验失败', 'time' => time(), 'data' => null]);
            }
            
            // 更新用户手机号
            if (isset($dataObj['phoneNumber'])) {
                Db::name('user')->where('id', $user_id)->update([
                    'mobile' => $dataObj['phoneNumber']
                ]);
                
                // 记录成功日志
                \think\Log::record('微信获取手机号成功: ' . $dataObj['phoneNumber'] . ', 用户ID: ' . $user_id, 'info');
                
                // 获取更新后的用户信息
                $updatedUser = Db::name('user')->where('id', $user_id)->find();
                unset($updatedUser['password'], $updatedUser['salt'], $updatedUser['wx_session_key']);
                
                return json([
                    'code' => 1,
                    'msg' => '获取手机号成功',
                    'time' => time(),
                    'data' => [
                        'phoneNumber' => $dataObj['phoneNumber'],
                        'userInfo' => $updatedUser
                    ]
                ]);
            } else {
                \think\Log::write('未找到手机号信息', 'error');
                return json(['code' => 0, 'msg' => '未找到手机号信息', 'time' => time(), 'data' => null]);
            }
            
        } catch (\Exception $e) {
            \think\Log::write('获取手机号异常: ' . $e->getMessage(), 'error');
            return json(['code' => 0, 'msg' => '获取手机号异常: ' . $e->getMessage(), 'time' => time(), 'data' => null]);
        }
    }
    
    /**
     * 更新微信用户信息
     *
     * @ApiMethod (POST)
     * @ApiParams (name="encryptedData", type="string", required=true, description="加密数据")
     * @ApiParams (name="iv", type="string", required=true, description="加密向量")
     * @ApiParams (name="token", type="string", required=true, description="用户token")
     */
    public function wx_update_userinfo()
    {
        $params = $this->request->post();
        
        // 记录请求日志
        \think\Log::record('微信更新用户信息请求: ' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        
        // 优先从POST参数获取token
        $token = $params['token'] ?? '';
        
        // 如果POST中没有token，尝试从请求头获取
        if (empty($token)) {
            // 尝试从Token头获取
            $token = $this->request->header('Token', '');
            
            // 尝试从X-Token头获取
            if (empty($token)) {
                $token = $this->request->header('X-Token', '');
            }
            
            // 尝试从Authorization头获取
            if (empty($token)) {
                $authorization = $this->request->header('Authorization', '');
                if (!empty($authorization) && strpos($authorization, 'Bearer ') === 0) {
                    $token = substr($authorization, 7);
                }
            }
        }
        
        if (empty($token)) {
            return json(['code' => 0, 'msg' => '请先登录', 'time' => time(), 'data' => null]);
        }
        
        // 通过token获取用户信息
        $tokenInfo = \think\Db::name('user_token')->where('token', $token)->find();
        if (!$tokenInfo) {
            return json(['code' => 0, 'msg' => '无效的token', 'time' => time(), 'data' => null]);
        }
        
        $user_id = $tokenInfo['user_id'];
        
        // 检查token是否过期
        if ($tokenInfo['expiretime'] < time()) {
            return json(['code' => 0, 'msg' => 'token已过期', 'time' => time(), 'data' => null]);
        }
        
        if (empty($params['encryptedData']) || empty($params['iv'])) {
            return json(['code' => 0, 'msg' => '参数错误', 'time' => time(), 'data' => null]);
        }
        
        try {
            // 获取用户的session_key
            $user = Db::name('user')->where('id', $user_id)->find();
            if (!$user || empty($user['wx_session_key'])) {
                return json(['code' => 0, 'msg' => '未找到有效的会话密钥，请重新登录', 'time' => time(), 'data' => null]);
            }
            
            $session_key = $user['wx_session_key'];
            $encryptedData = $params['encryptedData'];
            $iv = $params['iv'];
            
            // 获取小程序配置，使用Wechat辅助类从site配置中加载
            $miniappConfig = \app\common\library\Wechat::config('miniapp');
            if (empty($miniappConfig) || empty($miniappConfig['appid'])) {
                \think\Log::write('小程序配置错误', 'error');
                return json(['code' => 0, 'msg' => '小程序配置错误', 'time' => time(), 'data' => null]);
            }
            
            $appid = $miniappConfig['appid'];
            
            // 判断是否有openssl扩展
            if (!function_exists('openssl_decrypt')) {
                return json(['code' => 0, 'msg' => '服务器缺少必要的解密功能', 'time' => time(), 'data' => null]);
            }
            
            $aesKey = base64_decode($session_key);
            $aesIV = base64_decode($iv);
            $aesCipher = base64_decode($encryptedData);
            
            $result = openssl_decrypt($aesCipher, "AES-128-CBC", $aesKey, 1, $aesIV);
            
            if ($result === false) {
                \think\Log::write('解密失败: ' . openssl_error_string(), 'error');
                return json(['code' => 0, 'msg' => '解密失败: ' . openssl_error_string(), 'time' => time(), 'data' => null]);
            }
            
            $dataObj = json_decode($result, true);
            if ($dataObj === null) {
                \think\Log::write('数据无效', 'error');
                return json(['code' => 0, 'msg' => '数据无效', 'time' => time(), 'data' => null]);
            }
            
            if ($dataObj['watermark']['appid'] !== $appid) {
                \think\Log::write('数据校验失败: appid不匹配', 'error');
                return json(['code' => 0, 'msg' => '数据校验失败', 'time' => time(), 'data' => null]);
            }
            
            // 更新用户信息
            if (isset($dataObj['nickName']) && isset($dataObj['avatarUrl'])) {
                $updateData = [
                    'nickname' => $dataObj['nickName'],
                    'avatar' => $dataObj['avatarUrl'],
                    'is_first_login' => 0
                ];
                
                // 如果有性别信息，也更新
                if (isset($dataObj['gender'])) {
                    $updateData['gender'] = intval($dataObj['gender']);
                }
                
                Db::name('user')->where('id', $user_id)->update($updateData);
                
                // 记录成功日志
                \think\Log::record('微信更新用户信息成功: ' . $dataObj['nickName'] . ', 用户ID: ' . $user_id, 'info');
                
                // 获取更新后的用户信息
                $updatedUser = Db::name('user')->where('id', $user_id)->find();
                unset($updatedUser['password'], $updatedUser['salt'], $updatedUser['wx_session_key']);
                
                return json([
                    'code' => 1,
                    'msg' => '更新用户信息成功',
                    'time' => time(),
                    'data' => [
                        'userInfo' => $updatedUser
                    ]
                ]);
            } else {
                return json(['code' => 0, 'msg' => '未找到用户信息', 'time' => time(), 'data' => null]);
            }
            
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '更新用户信息异常: ' . $e->getMessage(), 'time' => time(), 'data' => null]);
        }
    }
    
    /**
     * 直接更新微信用户信息（不加密方式）
     * 
     * @ApiMethod (POST)
     * @ApiParams (name="nickname", type="string", required=true, description="昵称")
     * @ApiParams (name="avatar", type="string", required=true, description="头像URL")
     * @ApiParams (name="gender", type="integer", required=false, description="性别")
     * @ApiParams (name="realname", type="string", required=false, description="真实姓名")
     * @ApiParams (name="mobile", type="string", required=false, description="手机号")
     * @ApiParams (name="email", type="string", required=false, description="邮箱")
     * @ApiParams (name="bio", type="string", required=false, description="个人简介")
     * @ApiParams (name="token", type="string", required=true, description="用户token")
     */
    public function wx_update_profile()
    {
        $params = $this->request->post();
        
        // 记录请求日志
        \think\Log::record('微信更新个人资料请求: ' . json_encode($params, JSON_UNESCAPED_UNICODE), 'info');
        
        // 优先从POST参数获取token
        $token = $params['token'] ?? '';
        
        // 如果POST中没有token，尝试从请求头获取
        if (empty($token)) {
            // 尝试从Token头获取
            $token = $this->request->header('Token', '');
            
            // 尝试从X-Token头获取
            if (empty($token)) {
                $token = $this->request->header('X-Token', '');
            }
            
            // 尝试从Authorization头获取
            if (empty($token)) {
                $authorization = $this->request->header('Authorization', '');
                if (!empty($authorization) && strpos($authorization, 'Bearer ') === 0) {
                    $token = substr($authorization, 7);
                }
            }
        }
        
        if (empty($token)) {
            return json(['code' => 0, 'msg' => '请先登录', 'time' => time(), 'data' => null]);
        }
        
        // 通过token获取用户信息
        $tokenInfo = \think\Db::name('user_token')->where('token', $token)->find();
        if (!$tokenInfo) {
            return json(['code' => 0, 'msg' => '无效的token', 'time' => time(), 'data' => null]);
        }
        
        $user_id = $tokenInfo['user_id'];
        
        // 检查token是否过期
        if ($tokenInfo['expiretime'] < time()) {
            return json(['code' => 0, 'msg' => 'token已过期', 'time' => time(), 'data' => null]);
        }
        
        if (empty($params['nickname']) || empty($params['avatar'])) {
            return json(['code' => 0, 'msg' => '参数错误', 'time' => time(), 'data' => null]);
        }
        
        try {
            $updateData = [
                'nickname' => $params['nickname'],
                'avatar' => $params['avatar'],
                'is_first_login' => 0
            ];
            
            // 如果有性别信息，也更新
            if (isset($params['gender'])) {
                $updateData['gender'] = intval($params['gender']);
            }
            
            // 如果有真实姓名，也更新
            if (isset($params['realname'])) {
                $updateData['realname'] = $params['realname'];
            }
            
            // 如果有手机号，也更新
            if (isset($params['mobile'])) {
                $updateData['mobile'] = $params['mobile'];
            }
            
            // 如果有邮箱，也更新
            if (isset($params['email'])) {
                $updateData['email'] = $params['email'];
            }
            
            // 如果有个人简介，也更新
            if (isset($params['bio'])) {
                $updateData['bio'] = $params['bio'];
            }
            
            Db::name('user')->where('id', $user_id)->update($updateData);
            
            // 记录成功日志
            \think\Log::record('微信更新个人资料成功: ' . $params['nickname'] . ', 用户ID: ' . $user_id, 'info');
            
            // 获取更新后的用户信息
            $updatedUser = Db::name('user')->where('id', $user_id)->find();
            unset($updatedUser['password'], $updatedUser['salt'], $updatedUser['wx_session_key']);
            
            return json([
                'code' => 1,
                'msg' => '更新用户信息成功',
                'time' => time(),
                'data' => [
                    'userInfo' => $updatedUser
                ]
            ]);
            
        } catch (\Exception $e) {
            return json(['code' => 0, 'msg' => '更新用户信息异常: ' . $e->getMessage(), 'time' => time(), 'data' => null]);
        }
    }
}
