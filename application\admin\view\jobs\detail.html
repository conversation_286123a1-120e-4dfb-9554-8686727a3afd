<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>{:__('岗位详情')}</em></div>
    </div>
    
    <div class="panel-body">
        <!-- 顶部基本信息卡片 -->
        <div class="row">
            <div class="col-md-6">
                <div class="box box-solid">
                    <div class="box-header with-border">
                        <h3 class="box-title">{:__('基本信息')}</h3>
                    </div>
                    <div class="box-body">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <td width="30%">{:__('Title')}:</td>
                                    <td>{$row.title|htmlentities}</td>
                                </tr>
                                <tr>
                                    <td>{:__('Company')}:</td>
                                    <td>{$row.company|htmlentities}</td>
                                </tr>
                                <tr>
                                    <td>{:__('Job_code')}:</td>
                                    <td>{$row.job_code|htmlentities}</td>
                                </tr>
                                <tr>
                                    <td>{:__('Job_name')}:</td>
                                    <td>{$row.job_name|htmlentities}</td>
                                </tr>
                                <tr>
                                    <td>{:__('Category')}:</td>
                                    <td>
                                        <span class="label label-primary">
                                            <i class="fa fa-tag"></i> {$row.category|htmlentities}
                                        </span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>{:__('Total_fee')}:</td>
                                    <td>{$row.total_fee|htmlentities}</td>
                                </tr>
                                <tr>
                                    <td>{:__('Status')}:</td>
                                    <td>
                                        {if $row.status == '上架'}
                                        <span class="label label-success">{$row.status}</span>
                                        {elseif $row.status == '下架'}
                                        <span class="label label-default">{$row.status}</span>
                                        {else}
                                        <span class="label label-warning">{$row.status}</span>
                                        {/if}
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <!-- 岗位图片 -->
                {if $row.image}
                <div class="box box-solid">
                    <div class="box-header with-border">
                        <h3 class="box-title">{:__('Image')}</h3>
                    </div>
                    <div class="box-body">
                        <div style="text-align: center;">
                            <img src="{$row.image|htmlentities}" style="max-width: 100%; max-height: 200px;" class="img-thumbnail">
                        </div>
                    </div>
                </div>
                {/if}
                
                <!-- 统计信息 -->
                <div class="box box-solid">
                    <div class="box-header with-border">
                        <h3 class="box-title">{:__('统计信息')}</h3>
                    </div>
                    <div class="box-body">
                        <div class="row">
                            <div class="col-xs-6">
                                <div class="small-box bg-green">
                                    <div class="inner">
                                        <h3>{$row.favorite_count}</h3>
                                        <p>{:__('收藏次数')}</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fa fa-heart"></i>
                                    </div>
                                    <a href="javascript:;" class="small-box-footer btn-view-favorite" data-id="{$row.id}">
                                        {:__('查看详情')} <i class="fa fa-arrow-circle-right"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="col-xs-6">
                                <div class="small-box bg-yellow">
                                    <div class="inner">
                                        <h3>{$row.resume_apply_count}</h3>
                                        <p>{:__('简历投递')}</p>
                                    </div>
                                    <div class="icon">
                                        <i class="fa fa-file-text"></i>
                                    </div>
                                    <a href="javascript:;" class="small-box-footer btn-view-apply" data-id="{$row.id}">
                                        {:__('查看详情')} <i class="fa fa-arrow-circle-right"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 岗位要求和薪资 -->
        <div class="row">
            <div class="col-md-6">
                <div class="box box-solid">
                    <div class="box-header with-border">
                        <h3 class="box-title">{:__('薪资福利')}</h3>
                    </div>
                    <div class="box-body">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <td width="30%">{:__('Salary_range')}:</td>
                                    <td>{$row.salary_range|htmlentities}</td>
                                </tr>
                                <tr>
                                    <td>{:__('Working_hours')}:</td>
                                    <td>{$row.working_hours|htmlentities}</td>
                                </tr>
                                <tr>
                                    <td>{:__('Accommodation')}:</td>
                                    <td>{$row.accommodation|htmlentities}</td>
                                </tr>
                                <tr>
                                    <td>{:__('Job_summary')}:</td>
                                    <td>{$row.job_summary|htmlentities}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="box box-solid">
                    <div class="box-header with-border">
                        <h3 class="box-title">{:__('岗位要求')}</h3>
                    </div>
                    <div class="box-body">
                        <table class="table table-striped">
                            <tbody>
                                <tr>
                                    <td width="30%">{:__('Age_requirement')}:</td>
                                    <td>{$row.age_requirement|htmlentities}</td>
                                </tr>
                                <tr>
                                    <td>{:__('Gender_requirement')}:</td>
                                    <td>{$row.gender_requirement|htmlentities}</td>
                                </tr>
                                <tr>
                                    <td>{:__('Language_requirement')}:</td>
                                    <td>{$row.language_requirement|htmlentities}</td>
                                </tr>
                                <tr>
                                    <td>{:__('Education_requirement')}:</td>
                                    <td>{$row.education_requirement|htmlentities}</td>
                                </tr>
                                <tr>
                                    <td>{:__('Experience_requirement')}:</td>
                                    <td>{$row.experience_requirement|htmlentities}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 工作内容 -->
        <div class="box box-solid">
            <div class="box-header with-border">
                <h3 class="box-title">{:__('工作内容')}</h3>
            </div>
            <div class="box-body">
                <div class="well well-lg">
                    {$row.job_description|nl2br|htmlentities}
                </div>
            </div>
        </div>
        
        <!-- 备注信息 -->
        {if $row.remark}
        <div class="box box-solid">
            <div class="box-header with-border">
                <h3 class="box-title">{:__('备注')}</h3>
            </div>
            <div class="box-body">
                <div class="well">
                    {$row.remark|nl2br|htmlentities}
                </div>
            </div>
        </div>
        {/if}
        
        <!-- 系统信息 -->
        <div class="box box-solid">
            <div class="box-header with-border">
                <h3 class="box-title">{:__('系统信息')}</h3>
            </div>
            <div class="box-body">
                <table class="table table-striped">
                    <tbody>
                        <tr>
                            <td width="30%">{:__('Create_time')}:</td>
                            <td>{$row.create_time|datetime}</td>
                        </tr>
                        <tr>
                            <td>{:__('Update_time')}:</td>
                            <td>{$row.update_time|datetime}</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    
    <div class="panel-footer">
        <div class="row">
            <div class="col-xs-12 text-center">
                <button type="button" class="btn btn-default btn-back">{:__('返回')}</button>
                <button type="button" class="btn btn-primary btn-edit" data-id="{$row.id}">{:__('编辑')}</button>
            </div>
        </div>
    </div>
</div>

<!-- 自定义样式 -->
<style>
    .box {
        border-radius: 3px;
        box-shadow: 0 1px 1px rgba(0,0,0,0.1);
        margin-bottom: 20px;
    }
    .well {
        background-color: #f9f9f9;
        border-color: #e3e3e3;
    }
    .table th, .table td {
        padding: 10px;
    }
    .small-box {
        border-radius: 5px;
        position: relative;
        display: block;
        margin-bottom: 20px;
        box-shadow: 0 1px 1px rgba(0,0,0,0.1);
    }
    .small-box .icon {
        position: absolute;
        top: 10px;
        right: 15px;
        font-size: 70px;
        color: rgba(255,255,255,0.15);
    }
    .small-box .inner {
        padding: 10px;
    }
    .small-box .inner h3 {
        font-size: 38px;
        margin: 0 0 5px 0;
        white-space: nowrap;
        color: #fff;
    }
    .small-box .inner p {
        color: #fff;
        margin-bottom: 0;
    }
    .small-box .small-box-footer {
        position: relative;
        text-align: center;
        padding: 3px 0;
        color: #fff;
        color: rgba(255,255,255,0.8);
        display: block;
        z-index: 10;
        background: rgba(0,0,0,0.1);
        text-decoration: none;
    }
    .small-box .small-box-footer:hover {
        color: #fff;
        background: rgba(0,0,0,0.15);
    }
    .bg-green {
        background-color: #00a65a !important;
    }
    .bg-yellow {
        background-color: #f39c12 !important;
    }
</style> 