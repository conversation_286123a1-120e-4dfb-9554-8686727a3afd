<?php

return [
    'Id'                              => 'ID',
    'User_id'                         => '用户ID',
    'Resume_id'                       => '简历ID',
    'Resume_name'                     => '简历姓名',
    'Job_id'                          => '职位ID',
    'Job_code'                        => '岗位编号',
    'Job_name'                        => '职位名称',
    'Status'                          => '状态',
    'Feedback'                        => '反馈',
    'Create_time'                     => '创建时间',
    'Update_time'                     => '更新时间',
    'User.id'                         => 'ID',
    'User.group_id'                   => '组别ID',
    'User.username'                   => '用户名',
    'User.nickname'                   => '昵称',
    'User.realname'                   => '真实姓名',
    'User.password'                   => '密码',
    'User.salt'                       => '密码盐',
    'User.email'                      => '电子邮箱',
    'User.mobile'                     => '手机号',
    'User.avatar'                     => '头像',
    'User.level'                      => '等级',
    'User.gender'                     => '性别',
    'User.birthday'                   => '生日',
    'User.bio'                        => '格言',
    'User.money'                      => '余额',
    'User.score'                      => '积分',
    'User.successions'                => '连续登录天数',
    'User.maxsuccessions'             => '最大连续登录天数',
    'User.prevtime'                   => '上次登录时间',
    'User.logintime'                  => '登录时间',
    'User.loginip'                    => '登录IP',
    'User.loginfailure'               => '失败次数',
    'User.loginfailuretime'           => '最后登录失败时间',
    'User.joinip'                     => '加入IP',
    'User.jointime'                   => '加入时间',
    'User.createtime'                 => '创建时间',
    'User.updatetime'                 => '更新时间',
    'User.token'                      => 'Token',
    'User.status'                     => '状态',
    'User.is_first_login'             => '是否首次登录',
    'User.verification'               => '验证',
    'User.wx_openid'                  => '微信openid',
    'User.wx_unionid'                 => '微信unionid',
    'User.wx_session_key'             => '微信会话密钥',
    'Jobs.id'                         => '主键ID',
    'Jobs.title'                      => '岗位标题',
    'Jobs.image'                      => '岗位图片',
    'Jobs.company'                    => '公司名称',
    'Jobs.job_code'                   => '岗位编号',
    'Jobs.job_name'                   => '岗位名称',
    'Jobs.category'                   => '岗位分类',
    'Jobs.salary_range'               => '薪资范围',
    'Jobs.age_requirement'            => '年龄要求',
    'Jobs.gender_requirement'         => '性别要求',
    'Jobs.language_requirement'       => '语言要求',
    'Jobs.education_requirement'      => '学历要求',
    'Jobs.experience_requirement'     => '经验要求',
    'Jobs.working_hours'              => '工作时间',
    'Jobs.accommodation'              => '食宿情况',
    'Jobs.job_description'            => '岗位内容',
    'Jobs.total_fee'                  => '总费用',
    'Jobs.remark'                     => '备注',
    'Jobs.job_summary'                => '岗位汇总',
    'Jobs.resume_count'               => '应聘简历数',
    'Jobs.weight'                     => '权重',
    'Jobs.create_time'                => '创建时间',
    'Jobs.update_time'                => '更新时间',
    'Jobs.status'                     => '状态',
    'Resume.id'                       => 'ID',
    'Resume.user_id'                  => '用户ID',
    'Resume.name'                     => '姓名',
    'Resume.intended_position'        => '意向岗位',
    'Resume.applied_position'         => '申请岗位',
    'Resume.ethnicity'                => '民族',
    'Resume.height'                   => '身高(cm)',
    'Resume.weight'                   => '体重(kg)',
    'Resume.id_card'                  => '身份证号',
    'Resume.gender'                   => '性别',
    'Resume.age'                      => '年龄',
    'Resume.birth_date'               => '出生日期',
    'Resume.phone'                    => '手机号码',
    'Resume.marital_status'           => '婚姻状况',
    'Resume.hukou_location'           => '户口所在地',
    'Resume.residence_address'        => '常住地址',
    'Resume.highest_education'        => '最高学历',
    'Resume.cantonese_level'          => '粤语熟练度',
    'Resume.mandarin_level'           => '国语熟练度',
    'Resume.english_level'            => '英语熟练度',
    'Resume.hk_macau_passport'        => '港澳通行证编号',
    'Resume.hk_macau_passport_expiry' => '澳通行证到期时间',
    'Resume.special_certificate'      => '特殊职业资格证',
    'Resume.overseas_experience'      => '海外工作经历',
    'Resume.overseas_region'          => '海外工作地区',
    'Resume.hobbies'                  => '兴趣爱好',
    'Resume.self_evaluation'          => '自我评价',
    'Resume.contact_relation'         => '第一联系人关系',
    'Resume.contact_name'             => '第一联系人姓名',
    'Resume.contact_age'              => '第一联系人年龄',
    'Resume.contact_job'              => '第一联系人工作',
    'Resume.education_start'          => '读书开始时间',
    'Resume.education_end'            => '读书结束时间',
    'Resume.education_school'         => '学校名称',
    'Resume.education_major'          => '选修专业',
    'Resume.graduation_education'     => '毕业学历',
    'Resume.job_start'                => '工作开始时间',
    'Resume.job_end'                  => '工作结束时间',
    'Resume.job_company'              => '工作单位',
    'Resume.job_position'             => '工作岗位',
    'Resume.job_description'          => '工作内容',
    'Resume.avatar'                   => '头像',
    'Resume.full_body_photo'          => '全身照',
    'Resume.id_card_front'            => '身份证正面',
    'Resume.id_card_back'             => '身份证反面',
    'Resume.hk_macau_passport_front'  => '港澳通行证正面',
    'Resume.hk_macau_passport_back'   => '港澳通行证反面',
    'Resume.additional_photos'        => '补充照片',
    'Resume.contact_person'           => '对接人',
    'Resume.status'                   => '状态',
    'Resume.status draft'             => '草稿',
    'Resume.status published'         => '已发布',
    'Resume.create_time'              => '创建时间',
    'Resume.update_time'              => '更新时间'
];
