<link href="__CDN__/assets/libs/bootstrap-select/dist/css/bootstrap-select.min.css" rel="stylesheet">
<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-2">{:__('简历')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <select id="c-resume_id" data-rule="required" class="form-control selectpicker" name="row[resume_id]"
                                    data-live-search="true" 
                                    data-live-search-placeholder="搜索姓名/性别/年龄/学历" 
                                    data-live-search-normalize="true"
                                    data-live-search-style="contains"
                                    data-size="8">
                                    <option value="">{:__('请选择简历')}</option>
                                    {foreach name="resumeList" item="resume"}
                                    <option value="{$resume.id}" 
                                        data-name="{$resume.name}" 
                                        data-user-id="{$resume.user_id}"
                                        data-content="<div class='resume-title'>{$resume.name}</div>
                                            <div class='resume-info'>
                                                <span><i class='label'>性别：</i>{$resume.gender}</span>
                                                <span><i class='label'>年龄：</i>{$resume.age}岁</span>
                                                <span><i class='label'>学历：</i>{$resume.highest_education}</span>
                                                <span><i class='label'>ID：</i>{$resume.id}</span>
                                            </div>">
                                        {$resume.name}
                                    </option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-2">{:__('岗位')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <select id="c-job_id" data-rule="required" class="form-control selectpicker" name="row[job_id]"
                                    data-live-search="true" 
                                    data-live-search-placeholder="搜索岗位名称/公司/编号" 
                                    data-live-search-normalize="true"
                                    data-live-search-style="contains"
                                    data-size="8">
                                    <option value="">{:__('请选择岗位')}</option>
                                    {foreach name="jobList" item="job"}
                                    <option value="{$job.id}" 
                                        data-job-code="{$job.job_code}" 
                                        data-job-name="{$job.job_name}"
                                        data-content="<div class='job-title'>{$job.title}</div>
                                            <div class='job-info'>
                                                <span><i class='label'>编号：</i>{$job.job_code}</span>
                                                <span><i class='label'>公司：</i>{$job.company}</span>
                                                {if isset($job.salary_range)}<span class='salary'><i class='label'>薪资：</i>{$job.salary_range}</span>{/if}
                                            </div>">
                                        {$job.title}
                                    </option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-2">{:__('状态')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <select id="c-status" data-rule="required" class="form-control selectpicker" name="row[status]">
                                    {foreach name="statusList" item="vo" key="key"}
                                    <option value="{$key}" {if $key=='待处理'}selected{/if}>{$vo}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-2">{:__('反馈')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <textarea id="c-feedback" class="form-control" name="row[feedback]" rows="5"></textarea>
                            </div>
                        </div>
                        
                        <div class="form-group layer-footer">
                            <label class="control-label col-xs-12 col-sm-2"></label>
                            <div class="col-xs-12 col-sm-8">
                                <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
                                <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* 自定义下拉框样式 - FastAdmin 风格 */
    .bootstrap-select .dropdown-menu {
        min-width: 100%;  /* 改为100%宽度 */
        max-width: 100%;  /* 添加最大宽度限制 */
        padding: 0;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0,0,0,.1);
    }
    /* 下拉框按钮样式 */
    .bootstrap-select {
        width: 100% !important;  /* 确保下拉框按钮占满容器宽度 */
    }
    .bootstrap-select > .dropdown-toggle {
        width: 100%;  /* 确保按钮占满容器宽度 */
        background-color: #fff;
        border: 1px solid #ddd;
        color: #333;
        text-align: left;  /* 文本左对齐 */
        padding-right: 25px;  /* 为下拉箭头留出空间 */
    }
    /* 下拉框内容样式 */
    .bootstrap-select .dropdown-menu .inner {
        max-height: 300px;
        width: 100%;  /* 确保内容区域占满宽度 */
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item {
        width: 100%;  /* 确保选项占满宽度 */
        padding: 6px 10px;
        border-bottom: 1px solid #f0f0f0;
        color: #333;
        white-space: normal;  /* 允许文本换行 */
        word-wrap: break-word;  /* 允许长文本换行 */
    }
    /* 搜索框样式 */
    .bootstrap-select .bs-searchbox {
        width: 100%;  /* 确保搜索框占满宽度 */
        padding: 8px;
        border-bottom: 1px solid #eee;
        background-color: #f8f9fa;
    }
    .bootstrap-select .bs-searchbox .form-control {
        width: 100%;  /* 确保搜索输入框占满宽度 */
        border-radius: 4px;
        padding: 6px 12px;
        border: 1px solid #ddd;
        background-color: #fff;
    }
    /* 选项内容样式 */
    .bootstrap-select .dropdown-menu .inner .dropdown-item .resume-title {
        font-weight: 500;
        color: #333;
        margin-bottom: 2px;  /* 减小间距 */
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .resume-info {
        color: #666;
        font-size: 12px;
        line-height: 1.4;  /* 减小行高 */
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .resume-info span {
        display: inline-block;
        margin-right: 12px;  /* 减小间距 */
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .resume-info .label {
        color: #999;
        margin-right: 3px;  /* 减小间距 */
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .job-title {
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .job-info {
        color: #666;
        font-size: 12px;
        line-height: 1.5;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .job-info span {
        display: inline-block;
        margin-right: 15px;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .job-info .label {
        color: #999;
        margin-right: 5px;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .job-info .salary {
        color: #e74c3c;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item:hover {
        background-color: #f8f9fa;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item.active {
        background-color: #1cbbb4;
        color: #fff;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item.active .job-title,
    .bootstrap-select .dropdown-menu .inner .dropdown-item.active .resume-title {
        color: #fff;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item.active .job-info,
    .bootstrap-select .dropdown-menu .inner .dropdown-item.active .resume-info {
        color: rgba(255,255,255,0.8);
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item.active .job-info .label,
    .bootstrap-select .dropdown-menu .inner .dropdown-item.active .resume-info .label {
        color: rgba(255,255,255,0.6);
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item.active .job-info .salary {
        color: #ffd700;
    }
</style> 