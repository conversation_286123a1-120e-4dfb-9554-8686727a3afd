<link href="__CDN__/assets/libs/bootstrap-select/dist/css/bootstrap-select.min.css" rel="stylesheet">
<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-title" class="form-control" name="row[title]" type="text" value="{$row.title|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Interview_time')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-interview_time" class="form-control datetimepicker" data-date-format="YYYY-MM-DD HH:mm:ss" data-use-current="true" name="row[interview_time]" type="text" value="{$row.interview_time}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Job')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-job" class="form-control selectpicker" data-rule="required" name="row[job]" 
                data-live-search="true" 
                data-live-search-placeholder="搜索岗位名称/编号/公司/薪资" 
                data-live-search-normalize="true"
                data-live-search-style="contains"
                data-size="10">
                <option value="">{:__('Please select')}</option>
                {foreach name="jobsList" item="vo"}
                <option value="{$vo.job_name}" 
                    data-code="{$vo.job_code}"
                    data-content="<div class='job-title'>{$vo.job_name}</div>
                        <div class='job-info'>
                            <span><i class='label'>编号：</i>{$vo.job_code}</span>
                            <span><i class='label'>公司：</i>{$vo.company}</span>
                            <span class='salary'><i class='label'>薪资：</i>{$vo.salary_range}</span>
                            <span class='fee'><i class='label'>总费用：</i>{$vo.total_fee}</span>
                        </div>"
                    {if $row.job==$vo.job_name}selected{/if}>
                    {$vo.job_name}
                </option>
                {/foreach}
            </select>
        </div>
    </div>
    <div class="form-group" style="display: none;">
        <label class="control-label col-xs-12 col-sm-2">{:__('Job_code')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-job_code" class="form-control" name="row[job_code]" type="text" value="{$row.job_code|htmlentities}" readonly>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Interview_type')}:</label>
        <div class="col-xs-12 col-sm-8">
                        
            <select  id="c-interview_type" class="form-control selectpicker" name="row[interview_type]">
                {foreach name="interviewTypeList" item="vo"}
                    <option value="{$key}" {in name="key" value="$row.interview_type"}selected{/in}>{$vo}</option>
                {/foreach}
            </select>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Interview_materials')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-interview_materials" class="form-control " rows="5" name="row[interview_materials]" cols="50">{$row.interview_materials|htmlentities}</textarea>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Interviewee')}:</label>
        <div class="col-xs-12 col-sm-8">
            <select id="c-interviewee" class="form-control selectpicker" data-rule="required" name="row[interviewee][]" 
                data-live-search="true" 
                data-live-search-placeholder="搜索姓名/性别/年龄/申请岗位" 
                data-live-search-normalize="true"
                data-live-search-style="contains"
                data-size="8"
                multiple
                data-selected-text-format="values"
                data-show-content="false">
                {foreach name="resumeList" item="vo"}
                <option value="{$vo.name}" 
                    data-resume-id="{$vo.id}"
                    data-content="<div class='resume-title'>{$vo.name}</div>
                        <div class='resume-info'>
                            <span><i class='label'>性别：</i>{$vo.gender}</span>
                            <span><i class='label'>年龄：</i>{$vo.age}</span>
                            <span><i class='label'>申请岗位：</i>{$vo.applied_position}</span>
                            <span><i class='label'>ID：</i>{$vo.id}</span>
                        </div>"
                    {if in_array($vo.id, $row.resume_id)}selected{/if}>
                    {$vo.name}
                </option>
                {/foreach}
            </select>
        </div>
    </div>
    <div id="resume-ids-container" style="display: none;">
        <!-- 简历ID将在这里通过JS动态生成 -->
        {foreach name="row.resume_id" item="resumeId" key="k"}
            <input type="hidden" name="row[resume_id][]" value="{$resumeId}" id="resume-id-{$k}">
        {/foreach}
    </div>
    <div id="progress-container">
        <!-- 动态进度选择框将在这里生成 -->
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

<style>
    /* 自定义下拉框样式 - FastAdmin 风格 */
    .bootstrap-select .dropdown-menu {
        min-width: 100%;  /* 改为100%宽度 */
        max-width: 100%;  /* 添加最大宽度限制 */
        padding: 0;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 2px 4px rgba(0,0,0,.1);
    }
    /* 下拉框按钮样式 */
    .bootstrap-select {
        width: 100% !important;  /* 确保下拉框按钮占满容器宽度 */
    }
    .bootstrap-select > .dropdown-toggle {
        width: 100%;  /* 确保按钮占满容器宽度 */
        background-color: #fff;
        border: 1px solid #ddd;
        color: #333;
        text-align: left;  /* 文本左对齐 */
        padding-right: 25px;  /* 为下拉箭头留出空间 */
    }
    /* 下拉框内容样式 */
    .bootstrap-select .dropdown-menu .inner {
        max-height: 300px;
        width: 100%;  /* 确保内容区域占满宽度 */
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item {
        width: 100%;  /* 确保选项占满宽度 */
        padding: 6px 10px;
        border-bottom: 1px solid #f0f0f0;
        color: #333;
        white-space: normal;  /* 允许文本换行 */
        word-wrap: break-word;  /* 允许长文本换行 */
    }
    /* 搜索框样式 */
    .bootstrap-select .bs-searchbox {
        width: 100%;  /* 确保搜索框占满宽度 */
        padding: 8px;
        border-bottom: 1px solid #eee;
        background-color: #f8f9fa;
    }
    .bootstrap-select .bs-searchbox .form-control {
        width: 100%;  /* 确保搜索输入框占满宽度 */
        border-radius: 4px;
        padding: 6px 12px;
        border: 1px solid #ddd;
        background-color: #fff;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .resume-title {
        font-weight: 500;
        color: #333;
        margin-bottom: 2px;  /* 减小间距 */
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .resume-info {
        color: #666;
        font-size: 12px;
        line-height: 1.4;  /* 减小行高 */
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .resume-info span {
        display: inline-block;
        margin-right: 12px;  /* 减小间距 */
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .resume-info .label {
        color: #999;
        margin-right: 3px;  /* 减小间距 */
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .job-title {
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .job-info {
        color: #666;
        font-size: 12px;
        line-height: 1.5;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .job-info span {
        display: inline-block;
        margin-right: 15px;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .job-info .label {
        color: #999;
        margin-right: 5px;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .job-info .salary {
        color: #e74c3c;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .job-info .fee {
        color: #27ae60;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item:hover {
        background-color: #f8f9fa;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item.active {
        background-color: #1cbbb4;
        color: #fff;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item.active .job-title {
        color: #fff;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item.active .job-info {
        color: rgba(255,255,255,0.8);
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item.active .job-info .label {
        color: rgba(255,255,255,0.6);
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item.active .job-info .salary {
        color: #ffd700;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item.active .job-info .fee {
        color: #98fb98;
    }
    .bootstrap-select .bs-searchbox {
        padding: 8px;
        border-bottom: 1px solid #eee;
        background-color: #f8f9fa;
    }
    .bootstrap-select .bs-searchbox .form-control {
        border-radius: 4px;
        padding: 6px 12px;
        border: 1px solid #ddd;
        background-color: #fff;
    }
    .bootstrap-select .bs-searchbox .form-control:focus {
        border-color: #1cbbb4;
        box-shadow: 0 0 0 0.2rem rgba(28,187,180,.25);
    }
    /* 选择后展示的文本样式 */
    .bootstrap-select .filter-option {
        color: #333 !important;
    }
    .bootstrap-select .filter-option .job-title {
        color: #333 !important;
        font-weight: 500;
    }
    .bootstrap-select .filter-option .job-info {
        color: #333 !important;
    }
    .bootstrap-select .filter-option .job-info .label {
        color: #666 !important;
    }
    .bootstrap-select .filter-option .job-info .salary {
        color: #e74c3c !important;
    }
    .bootstrap-select .filter-option .job-info .fee {
        color: #27ae60 !important;
    }
    /* 简历选择框样式 */
    .bootstrap-select .dropdown-menu .inner .dropdown-item .resume-title {
        font-weight: 500;
        color: #333;
        margin-bottom: 4px;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .resume-info {
        color: #666;
        font-size: 12px;
        line-height: 1.5;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .resume-info span {
        display: inline-block;
        margin-right: 15px;
    }
    .bootstrap-select .dropdown-menu .inner .dropdown-item .resume-info .label {
        color: #999;
        margin-right: 5px;
    }
    .bootstrap-select .filter-option .resume-title {
        color: #333 !important;
        font-weight: 500;
    }
    .bootstrap-select .filter-option .resume-info {
        color: #333 !important;
    }
    .bootstrap-select .filter-option .resume-info .label {
        color: #666 !important;
    }
</style>
