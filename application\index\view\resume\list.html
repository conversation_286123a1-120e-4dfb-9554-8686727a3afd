<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>我的简历</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/sweetalert2/11.7.32/sweetalert2.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-bg: #f8f9fa;
            --dark-bg: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f1c40f;
            --border-radius: 12px;
            --box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }
        
        body {
            background-color: var(--light-bg);
            color: var(--primary-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            padding-top: 100px;
        }

        .navbar {
        background-color: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px);
        box-shadow: var(--box-shadow);
        padding: 1rem 0;
        transition: var(--transition);
    }

    .navbar.scrolled {
        padding: 0.5rem 0;
    }

    .navbar-brand {
        font-weight: 700;
        color: var(--primary-color) !important;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
        letter-spacing: -0.5px;
    }

    .navbar-brand i {
        color: var(--primary-color);
        margin-right: 0.5rem;
        font-size: 1.8rem;
    }

    .nav-link {
        color: var(--text-primary) !important;
        font-weight: 500;
        padding: 0.75rem 1.25rem !important;
        margin: 0 0.25rem;
        border-radius: var(--border-radius);
        transition: var(--transition);
        position: relative;
    }

    .nav-link::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 2px;
        background-color: var(--primary-color);
        transition: var(--transition);
    }

    .nav-link:hover::after {
        width: 80%;
    }

    .nav-link:hover {
        background-color: rgba(26, 115, 232, 0.08);
        color: var(--primary-color) !important;
    }

    .nav-link.active {
        background-color: var(--primary-color) !important;
        color: white !important;
    }

    .nav-link.active::after {
        display: none;
    }

    @media (max-width: 991.98px) {
        .navbar-collapse {
            background-color: rgba(255, 255, 255, 0.98);
            padding: 1rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-top: 1rem;
            backdrop-filter: blur(10px);
        }

        .nav-link {
            padding: 1rem !important;
            margin: 0.5rem 0;
        }

        .navbar-buttons {
            margin-top: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .navbar-buttons .btn {
            width: 100%;
        }
    }
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .card {
            border: none;
            box-shadow: var(--box-shadow);
            border-radius: var(--border-radius);
            transition: var(--transition);
            margin-bottom: 1.5rem;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 1.5rem;
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
        }

        .card-body {
            padding: 1.5rem;
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--secondary-color);
            display: flex;
            align-items: center;
            flex: 1;
        }

        .section-title i {
            margin-right: 0.75rem;
            color: var(--secondary-color);
        }

        .btn {
            border-radius: var(--border-radius);
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .btn-primary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
            transform: translateY(-1px);
        }

        .btn-outline-primary {
            color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--secondary-color);
            color: white;
            transform: translateY(-1px);
        }

        .btn-danger {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }

        .btn-danger:hover {
            background-color: #c0392b;
            border-color: #c0392b;
        }

        .resume-card {
            position: relative;
            transition: var(--transition);
        }

        .resume-card:hover .resume-actions {
            opacity: 1;
        }

        .resume-info {
            padding: 1rem;
        }

        .resume-name {
            font-weight: 600;
            font-size: 1.2rem;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .resume-position {
            color: var(--secondary-color);
            margin-bottom: 0.5rem;
            font-weight: 500;
        }

        .resume-details {
            display: flex;
            flex-wrap: wrap;
            gap: 0.8rem;
            margin-top: 1rem;
        }

        .resume-detail {
            background-color: var(--light-bg);
            padding: 0.3rem 0.7rem;
            border-radius: 20px;
            font-size: 0.85rem;
            display: flex;
            align-items: center;
        }

        .resume-detail i {
            margin-right: 0.4rem;
            color: var(--secondary-color);
        }

        .resume-actions {
            position: absolute;
            top: 1rem;
            right: 1rem;
            opacity: 0;
            transition: var(--transition);
        }

        .resume-status {
            position: absolute;
            top: 1rem;
            right: 1rem;
            padding: 0.3rem 0.7rem;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .status-draft {
            background-color: var(--light-bg);
            color: var(--primary-color);
        }

        .status-published {
            background-color: var(--success-color);
            color: white;
        }

        .status-submitted {
            background-color: var(--warning-color);
            color: white;
        }

        .empty-state {
            text-align: center;
            padding: 3rem 1rem;
        }

        .empty-state i {
            font-size: 4rem;
            color: #d1d1d1;
            margin-bottom: 1rem;
        }

        .empty-state h4 {
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .form-control, .form-select {
            border-radius: var(--border-radius);
            border: 1px solid #e0e0e0;
            padding: 0.75rem;
            transition: var(--transition);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .pagination {
            margin-top: 2rem;
            justify-content: center;
        }

        .pagination .page-item .page-link {
            color: var(--secondary-color);
            border-radius: var(--border-radius);
            margin: 0 0.2rem;
            transition: var(--transition);
        }

        .pagination .page-item.active .page-link {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            color: white;
        }

        .pagination .page-item .page-link:hover {
            background-color: var(--light-bg);
            transform: translateY(-2px);
        }

        @media (max-width: 576px) {
            .resume-info {
                padding: 1rem;
            }
            
            .btn-sm {
                padding: 0.5rem 0.75rem;
                font-size: 0.9rem;
            }
            
            .resume-detail {
                padding: 0.4rem 0.8rem;
                font-size: 0.9rem;
            }
            
            .resume-name {
                font-size: 1.2rem;
                margin-bottom: 0.5rem;
            }
            
            .resume-position {
                font-size: 1rem;
                margin-bottom: 0.5rem;
            }

            .resume-status {
                font-size: 0.9rem;
                padding: 0.4rem 0.8rem;
            }

            .section-title {
                font-size: 1.4rem;
                margin-bottom: 1rem;
            }

            .alert {
                font-size: 1rem;
                padding: 0.8rem;
            }

            .badge {
                font-size: 0.9rem;
                padding: 0.4rem 0.6rem;
            }

            .empty-state i {
                font-size: 3rem;
            }

            .empty-state h4 {
                font-size: 1.2rem;
            }

            .empty-state p {
                font-size: 1rem;
            }

            .text-muted {
                font-size: 0.9rem;
            }
        }
        
        @media (max-width: 768px) {
            body {
                padding-top: 90px;
            }
            .resume-actions {
                position: static;
                opacity: 1;
                margin-top: 1rem;
                text-align: right;
            }
            
            .resume-status {
                position: absolute;
                top: 0.5rem;
                right: 0.5rem;
                display: inline-block;
                margin-top: 0;
                font-size: 0.75rem;
                padding: 0.2rem 0.5rem;
            }
        }

        @media (min-width: 992px) {
            body {
                padding-top: 110px;
            }
        }

        .btn-add-resume {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
            border-radius: var(--border-radius);
            padding: 0.6rem 1.2rem;
            font-weight: 500;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
        }
        
        .btn-add-resume:hover {
            background-color: #2980b9;
            border-color: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        /* 搜索部分的顶部间距 */
        .search-section {
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
<nav class="navbar navbar-expand-lg navbar-light fixed-top">
    <div class="container">
        <a class="navbar-brand" href="{:url('index/index/index')}">
            <i class="fas fa-briefcase"></i>{$site.name|htmlentities}
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='Index'}active{/eq}" href="{:url('index/index/index')}">
                        <i class="fas fa-home"></i>首页
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='Job'}active{/eq}" href="{:url('index/job/list')}">
                        <i class="fas fa-list me-1"></i>职位列表
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='News'}active{/eq}" href="{:url('index/resume/list')}">
                        <i class="fas fa-newspaper me-1"></i>我的简历
                    </a>
                </li>
            </ul>
            <div class="navbar-buttons">
                {if $user}
                <!-- 已登录状态 -->
                <a href="{:url('index/user/index')}" class="btn btn-outline-primary me-2">
                    <i class="fas fa-user me-1"></i>个人中心
                </a>
                <a href="javascript:;" class="btn btn-outline-danger" id="btn-logout">
                    <i class="fas fa-sign-out-alt me-1"></i>退出登录
                </a>
                <!-- 退出登录表单 -->
                <form id="logout-form" action="{:url('index/user/logout')}" method="post" style="display: none;">
                    {:token()}
                </form>
                {else}
                <!-- 未登录状态 -->
                <a href="{:url('index/user/login')}" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-1"></i>登录/注册
                </a>
                {/if}
            </div>
        </div>
    </div>
</nav>

    <!-- 主要内容 -->
    <div class="container">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h4 class="section-title mb-0">
                        <i class="fas fa-file-alt"></i>我的简历列表
                    </h4>
                    <a href="{:url('index/resume/add')}" class="btn btn-primary btn-add-resume">
                        <i class="fas fa-plus me-1"></i>新增简历
                    </a>
                </div>
                <div class="alert alert-info" role="alert">
                    <i class="fas fa-info-circle me-2"></i>
                    共找到 {$totalCount} 份简历
                    {if condition="$userIdCount > 0"}
                        <span class="badge bg-primary ms-2">用户ID匹配：{$userIdCount}份</span>
                    {/if}
                    {if condition="$namePhoneCount > 0"}
                        <span class="badge bg-secondary ms-2">姓名电话匹配：{$namePhoneCount}份</span>
                    {/if}
                </div>
            </div>
        </div>

        <!-- 简历列表 -->
        <div class="row">
            {empty name="resumeList"}
                <div class="col-12">
                    <div class="card">
                        <div class="card-body empty-state">
                            <i class="fas fa-search"></i>
                            <h4>未找到相关简历</h4>
                            <p>您还没有提交过简历</p>
                        </div>
                    </div>
                </div>
            {else/}
                {volist name="resumeList" id="resume"}
                    <div class="col-md-6">
                        <div class="card resume-card">
                            <!-- 简历状态标签 -->
                            {switch name="resume.status"}
                                {case value="草稿"}
                                    <span class="resume-status status-draft">
                                        <i class="fas fa-pencil-alt me-1"></i>草稿
                                    </span>
                                {/case}
                                {case value="已发布"}
                                    <span class="resume-status status-published">
                                        <i class="fas fa-check me-1"></i>已发布
                                    </span>
                                {/case}
                                {case value="官网提交"}
                                    <span class="resume-status status-submitted">
                                        <i class="fas fa-paper-plane me-1"></i>已提交
                                    </span>
                                {/case}
                            {/switch}
                            
                            <!-- 匹配方式标签 -->
                            {if condition="$resume.match_type == 'user_id'"}
                                <span class="resume-status status-published" style="top: 3rem;">
                                    <i class="fas fa-user-check me-1"></i>用户ID匹配
                                </span>
                            {else/}
                                <span class="resume-status status-published" style="top: 3rem;">
                                    <i class="fas fa-search me-1"></i>姓名电话匹配
                                </span>
                            {/if}
                            
                            <div class="card-body resume-info">
                                <div class="row">
                                    <div class="col-auto pe-0 pe-md-2">  <!-- 移动设备上减少右边距 -->
                                        <div class="avatar-container" style="width: 60px; height: 60px; overflow: hidden; border-radius: 50%;">
                                            {if condition="$resume.avatar"}
                                                <img src="{$resume.avatar}" style="width: 100%; height: 100%; object-fit: cover;">
                                            {else}
                                                <div style="width: 100%; height: 100%; background-color: #e9ecef; display: flex; align-items: center; justify-content: center;">
                                                    <i class="fas fa-user" style="font-size: 1.5rem; color: #adb5bd;"></i>
                                                </div>
                                            {/if}
                                        </div>
                                    </div>
                                    <div class="col">
                                        <h5 class="resume-name">{$resume.name|htmlentities}</h5>
                                        <div class="resume-position">
                                            {if condition="$resume.applied_position"}
                                                <span><i class="fas fa-briefcase me-1"></i>{$resume.applied_position|htmlentities}</span>
                                            {elseif condition="$resume.intended_position"}
                                                <span><i class="fas fa-bullseye me-1"></i>{$resume.intended_position|htmlentities}</span>
                                            {else}
                                                <span class="text-muted"><i class="fas fa-bullseye me-1"></i>暂无意向岗位</span>
                                            {/if}
                                        </div>
                                        
                                        <div class="resume-details">
                                            {if condition="$resume.gender"}
                                                <span class="resume-detail">
                                                    <i class="fas fa-venus-mars"></i>
                                                    {$resume.gender|htmlentities}
                                                </span>
                                            {/if}
                                            
                                            {if condition="$resume.age"}
                                                <span class="resume-detail">
                                                    <i class="fas fa-birthday-cake"></i>
                                                    {$resume.age}岁
                                                </span>
                                            {/if}
                                            
                                            {if condition="$resume.highest_education"}
                                                <span class="resume-detail">
                                                    <i class="fas fa-graduation-cap"></i>
                                                    {$resume.highest_education|htmlentities}
                                                </span>
                                            {/if}
                                            
                                            {if condition="$resume.phone"}
                                                <span class="resume-detail">
                                                    <i class="fas fa-phone"></i>
                                                    {$resume.phone|htmlentities}
                                                </span>
                                            {/if}
                                        </div>
                                        
                                        <div class="mt-3">
                                            <small class="text-muted">
                                                创建时间：{:date('Y-m-d H:i', $resume.create_time)}
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 操作按钮 -->
                                <div class="mt-3 d-flex justify-content-end flex-wrap">
                                    <a href="{:url('index/resume/view', ['id' => $resume.id])}" class="btn btn-sm btn-outline-primary me-2 mb-1">
                                        <i class="fas fa-eye me-1"></i>查看
                                    </a>
                                    <a href="{:url('index/resume/edit', ['id' => $resume.id])}" class="btn btn-sm btn-outline-primary me-2 mb-1">
                                        <i class="fas fa-edit me-1"></i>编辑
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-danger delete-resume mb-1" data-id="{$resume.id}">
                                        <i class="fas fa-trash me-1"></i>删除
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                {/volist}
            {/empty}
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
            {$page}
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.bootcdn.net/ajax/libs/sweetalert2/11.7.32/sweetalert2.all.min.js"></script>
    <script>
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 删除简历操作
            document.querySelectorAll('.delete-resume').forEach(function(button) {
                button.addEventListener('click', function() {
                    const resumeId = this.getAttribute('data-id');
                    
                    Swal.fire({
                        title: '确认删除',
                        text: '您确定要删除这份简历吗？删除后将无法恢复！',
                        icon: 'warning',
                        showCancelButton: true,
                        confirmButtonColor: '#e74c3c',
                        cancelButtonColor: '#7f8c8d',
                        confirmButtonText: '确认删除',
                        cancelButtonText: '取消'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            window.location.href = "{:url('index/resume/delete')}" + '?id=' + resumeId;
                        }
                    });
                });
            });
        });
    </script>
    <script>
        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
        
        // 退出登录按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const logoutBtn = document.getElementById('btn-logout');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function() {
                    document.getElementById('logout-form').submit();
                });
            }
        });
    </script> 
</body>
</html> 