<form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('User_id')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-user_id" data-rule="required" min="0" data-source="user/user/index" data-field="nickname" class="form-control selectpage" name="row[user_id]" type="text" value="{$row.user_id|htmlentities}">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}" placeholder="请输入姓名">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Intended_position')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-intended_position" class="form-control" name="row[intended_position]" type="text" value="{$row.intended_position|htmlentities}" placeholder="请输入意向岗位">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Applied_position')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-applied_position" class="form-control" name="row[applied_position]" type="text" value="{$row.applied_position|htmlentities}" placeholder="请输入申请岗位">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Ethnicity')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-ethnicity" class="form-control" step="0.01" name="row[ethnicity]" type="text" value="{$row.ethnicity|htmlentities}" placeholder="请输入民族">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Height')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-height" class="form-control" step="0.01" name="row[height]" type="number" value="{$row.height|htmlentities}" placeholder="请输入身高(cm)">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Weight')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-weight" class="form-control" step="0.01" name="row[weight]" type="number" value="{$row.weight|htmlentities}" placeholder="请输入体重(kg)">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Id_card')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-id_card" class="form-control" name="row[id_card]" type="text" value="{$row.id_card|htmlentities}" onchange="autoFillFromID()" placeholder="请输入身份证号">
<script>
function autoFillFromID() {
    const idCard = document.getElementById('c-id_card').value;
    if(idCard.length === 18) {
        // 提取性别
        const genderNum = parseInt(idCard.substr(16,1));
        const gender = genderNum % 2 === 1 ? '男' : '女';
        document.querySelector(`input[name="row[gender]"][value="${gender}"]`).checked = true;
        
        // 提取出生日期
        const birthYear = idCard.substr(6,4);
        const birthMonth = idCard.substr(10,2);
        const birthDay = idCard.substr(12,2);
        document.getElementById('c-birth_date').value = `${birthYear}-${birthMonth}-${birthDay}`;
        
        // 计算年龄
        const currentYear = new Date().getFullYear();
        const age = currentYear - parseInt(birthYear);
        document.getElementById('c-age').value = age;
    }
}
</script>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Gender')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="genderList" item="vo"}
            <label for="row[gender]-{$key}"><input id="row[gender]-{$key}" name="row[gender]" type="radio" value="{$key}" {in name="key" value="$row.gender"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Age')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-age" class="form-control" name="row[age]" type="number" value="{$row.age|htmlentities}" placeholder="请输入年龄">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Birth_date')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-birth_date" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" name="row[birth_date]" type="text" value="{$row.birth_date}" placeholder="请选择出生日期">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Phone')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-phone" data-rule="required" class="form-control" name="row[phone]" type="text" value="{$row.phone|htmlentities}" placeholder="请输入手机号码">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Marital_status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="maritalStatusList" item="vo"}
            <label for="row[marital_status]-{$key}"><input id="row[marital_status]-{$key}" name="row[marital_status]" type="radio" value="{$key}" {in name="key" value="$row.marital_status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hukou_location')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hukou_location" class="form-control" data-toggle="city-picker" data-level="city" data-simple="true" data-responsive="true" name="row[hukou_location]" type="text" value="{$row.hukou_location|htmlentities}" placeholder="请选择户口所在地"/>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Residence_address')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-residence_address" class="form-control" data-toggle="city-picker" data-level="district" data-simple="true" data-responsive="true" name="row[residence_address]" type="text" value="{$row.residence_address|htmlentities}" placeholder="请选择常住地址"/>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Highest_education')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-highest_education" class="form-control" name="row[highest_education]" type="text" value="{$row.highest_education|htmlentities}" placeholder="请输入最高学历">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Cantonese_level')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="cantoneseLevelList" item="vo"}
            <label for="row[cantonese_level]-{$key}"><input id="row[cantonese_level]-{$key}" name="row[cantonese_level]" type="radio" value="{$key}" {in name="key" value="$row.cantonese_level"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Mandarin_level')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="mandarinLevelList" item="vo"}
            <label for="row[mandarin_level]-{$key}"><input id="row[mandarin_level]-{$key}" name="row[mandarin_level]" type="radio" value="{$key}" {in name="key" value="$row.mandarin_level"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>


    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('English_level')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="englishLevelList" item="vo"}
            <label for="row[english_level]-{$key}"><input id="row[english_level]-{$key}" name="row[english_level]" type="radio" value="{$key}" {in name="key" value="$row.english_level"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hk_macau_passport')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hk_macau_passport" class="form-control" name="row[hk_macau_passport]" type="text" value="{$row.hk_macau_passport|htmlentities}" placeholder="请输入港澳通行证编号">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hk_macau_passport_expiry')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hk_macau_passport_expiry" class="form-control datetimepicker" data-date-format="YYYY-MM-DD" data-use-current="true" name="row[hk_macau_passport_expiry]" type="text" value="{$row.hk_macau_passport_expiry}" placeholder="请选择港澳通行证到期时间">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Special_certificate')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-special_certificate" class="form-control" name="row[special_certificate]" type="text" value="{$row.special_certificate|htmlentities}" placeholder="请输入特殊职业资格证书">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Overseas_experience')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="overseasExperienceList" item="vo"}
            <label for="row[overseas_experience]-{$key}"><input id="row[overseas_experience]-{$key}" name="row[overseas_experience]" type="radio" value="{$key}" {in name="key" value="$row.overseas_experience"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Overseas_region')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-overseas_region" class="form-control" name="row[overseas_region]" type="text" value="{$row.overseas_region|htmlentities}" placeholder="请输入海外工作地区">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hobbies')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-hobbies" class="form-control" name="row[hobbies]" type="text" value="{$row.hobbies|htmlentities}" placeholder="请输入兴趣爱好">
            {if condition="config('site.openai_enabled') eq '1'"}
            <div class="mt-2">
                <div class="input-group">
                    <span class="input-group-addon">字数控制</span>
                    <select id="hobbiesWordCount" class="form-control">
                        <option value="10">10字左右</option>
                        <option value="20">20字左右</option>
                        <option value="30">30字左右</option>
                        <option value="50">50字左右</option>
                        <option value="100" selected>100字左右</option>
                        <option value="150">150字左右</option>
                        <option value="200">200字左右</option>
                        <option value="300">300字左右</option>
                    </select>
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-info" onclick="generateHobbies()"><i class="fa fa-magic"></i> AI润色</button>
                    </span>
                </div>
            </div>
            {/if}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Self_evaluation')}:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-self_evaluation" class="form-control" rows="5" name="row[self_evaluation]" cols="50" placeholder="请输入自我评价">{$row.self_evaluation|htmlentities}</textarea>
            {if condition="config('site.openai_enabled') eq '1'"}
            <div class="mt-2">
                <div class="input-group">
                    <span class="input-group-addon">字数控制</span>
                    <select id="selfEvaluationWordCount" class="form-control">
                        <option value="10">10字左右</option>
                        <option value="20">20字左右</option>
                        <option value="30">30字左右</option>
                        <option value="50">50字左右</option>
                        <option value="100" selected>100字左右</option>
                        <option value="150">150字左右</option>
                        <option value="200">200字左右</option>
                        <option value="300">300字左右</option>
                    </select>
                    <span class="input-group-btn">
                        <button type="button" class="btn btn-info" onclick="generateSelfEvaluation()"><i class="fa fa-magic"></i> AI润色</button>
                    </span>
                </div>
            </div>
            {/if}
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">紧急联系人:</label>
        <div class="col-xs-12 col-sm-10">
            <table class="table table-bordered" id="contacts-table">
                <thead>
                    <tr>
                        <th style="width: 50px;">排序</th>
                        <th>关系</th>
                        <th>姓名</th>
                        <th>年龄</th>
                        <th>工作</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {php}
                        $relations = explode('|', $row['contact_relation'] ?? '');
                        $names = explode('|', $row['contact_name'] ?? '');
                        $ages = explode('|', $row['contact_age'] ?? '');
                        $jobs = explode('|', $row['contact_job'] ?? '');
                    {/php}
                    {foreach $relations as $key => $relation}
                    <tr draggable="true">
                        <td><i class="fa fa-arrows-alt handle" style="cursor: move;"></i></td>
                        <td><input type="text" class="form-control" name="contacts[{$key}][relation]" value="{$relation|htmlentities}" required></td>
                        <td><input type="text" class="form-control" name="contacts[{$key}][name]" value="{$names[$key]|htmlentities}" required></td>
                        <td><input type="text" class="form-control" name="contacts[{$key}][age]" value="{$ages[$key]|htmlentities}" required></td>
                        <td><input type="text" class="form-control" name="contacts[{$key}][job]" value="{$jobs[$key]|htmlentities}" required></td>
                        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeContactRow(this)">删除</button></td>
                    </tr>
                    {/foreach}
                </tbody>
            </table>
            <button type="button" class="btn btn-primary" onclick="addContactRow()">添加联系人</button>
            <script>
                let contactCounter = parseInt('{$relations|count}');
                function addContactRow() {
                    const tbody = document.querySelector('#contacts-table tbody');
                    const newRow = document.createElement('tr');
                    newRow.setAttribute('draggable', 'true');
                    newRow.innerHTML = `
                        <td><i class="fa fa-arrows-alt handle" style="cursor: move;"></i></td>
                        <td><input type="text" class="form-control" name="contacts[${contactCounter}][relation]" required></td>
                        <td><input type="text" class="form-control" name="contacts[${contactCounter}][name]" required></td>
                        <td><input type="text" class="form-control" name="contacts[${contactCounter}][age]" required></td>
                        <td><input type="text" class="form-control" name="contacts[${contactCounter}][job]" required></td>
                        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeContactRow(this)">删除</button></td>
                    `;
                    tbody.appendChild(newRow);
                    contactCounter++;
                }
                function removeContactRow(button) {
                    const row = button.closest('tr');
                    row.remove();
                }
                document.addEventListener('DOMContentLoaded', function() {
                    const tbody = document.querySelector('#contacts-table tbody');
                    let draggedItem = null;

                    tbody.addEventListener('dragstart', function(e) {
                        draggedItem = e.target.closest('tr');
                        if (!draggedItem) return;
                        e.dataTransfer.effectAllowed = 'move';
                        e.dataTransfer.setData('text/html', draggedItem.innerHTML);
                        draggedItem.classList.add('dragging');
                    });

                    tbody.addEventListener('dragend', function(e) {
                        if (draggedItem) {
                            draggedItem.classList.remove('dragging');
                            draggedItem = null;
                        }
                    });

                    tbody.addEventListener('dragover', function(e) {
                        e.preventDefault();
                        e.dataTransfer.dropEffect = 'move';
                    });

                    tbody.addEventListener('drop', function(e) {
                        e.preventDefault();
                        const targetItem = e.target.closest('tr');
                        if (targetItem && draggedItem && targetItem !== draggedItem) {
                            const allRows = [...tbody.getElementsByTagName('tr')];
                            const draggedIndex = allRows.indexOf(draggedItem);
                            const droppedIndex = allRows.indexOf(targetItem);

                            if (draggedIndex < droppedIndex) {
                                targetItem.parentNode.insertBefore(draggedItem, targetItem.nextSibling);
                            } else {
                                targetItem.parentNode.insertBefore(draggedItem, targetItem);
                            }

                            updateContactIndexes();
                        }
                    });

                    let touchstartY = 0;
                    let touchMoveItem = null;
                    let initialTouchMoveItemTop = 0;

                    tbody.addEventListener('touchstart', function(e) {
                        if (e.target.closest('.handle')) {
                            e.preventDefault();
                            touchMoveItem = e.target.closest('tr');
                            if (!touchMoveItem) return;
                            touchstartY = e.touches[0].clientY;
                            touchMoveItem.classList.add('dragging');
                            initialTouchMoveItemTop = touchMoveItem.getBoundingClientRect().top;
                        }
                    });

                    tbody.addEventListener('touchmove', function(e) {
                        if (touchMoveItem) {
                            e.preventDefault();
                            const currentY = e.touches[0].clientY;
                            const diffY = currentY - touchstartY;
                            touchMoveItem.style.transform = `translateY(${diffY}px)`;
                        }
                    });

                    tbody.addEventListener('touchend', function(e) {
                        if (touchMoveItem) {
                            touchMoveItem.classList.remove('dragging');
                            touchMoveItem.style.transform = '';

                            const finalY = initialTouchMoveItemTop + (e.changedTouches[0].clientY - touchstartY);
                            const allRows = [...tbody.getElementsByTagName('tr')];
                            let targetIndex = allRows.length - 1;

                            for (let i = 0; i < allRows.length; i++) {
                                if (allRows[i] !== touchMoveItem) {
                                    const rowRect = allRows[i].getBoundingClientRect();
                                    if (finalY < rowRect.top + rowRect.height / 2) {
                                        targetIndex = i;
                                        break;
                                    } else {
                                        targetIndex = i + 1;
                                    }
                                }
                            }

                            const currentIndex = allRows.indexOf(touchMoveItem);

                            if (currentIndex !== targetIndex) {
                                if (targetIndex === allRows.length) {
                                    tbody.appendChild(touchMoveItem);
                                } else if (targetIndex === 0 && currentIndex === 1) {
                                    tbody.insertBefore(touchMoveItem, allRows[0]);
                                } else if (targetIndex < currentIndex) {
                                    tbody.insertBefore(touchMoveItem, allRows[targetIndex]);
                                } else {
                                    tbody.insertBefore(touchMoveItem, allRows[targetIndex].nextSibling);
                                }
                                updateContactIndexes();
                            }

                            touchMoveItem = null;
                            initialTouchMoveItemTop = 0;
                        }
                    });
                });
                function updateContactIndexes() {
                    const rows = document.querySelectorAll('#contacts-table tbody tr');
                    rows.forEach((row, index) => {
                        row.querySelectorAll('input').forEach(input => {
                            let name = input.getAttribute('name');
                            if (name) {
                                name = name.replace(/contacts\[\d+\]/, `contacts[${index}]`);
                                input.setAttribute('name', name);
                            }
                        });
                    });
                }
            </script>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">教育经历:</label>
        <div class="col-xs-12 col-sm-10">
            <table class="table table-bordered" id="education-table">
                <thead>
                    <tr>
                        <th style="width: 50px;">排序</th>
                        <th>开始时间</th>
                        <th>结束时间</th>
                        <th>学校名称</th>
                        <th>专业</th>
                        <th>学历</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {php}
                        $starts = explode('|', $row['education_start'] ?? '');
                        $ends = explode('|', $row['education_end'] ?? '');
                        $schools = explode('|', $row['education_school'] ?? '');
                        $majors = explode('|', $row['education_major'] ?? '');
                        $degrees = explode('|', $row['graduation_education'] ?? '');
                    {/php}
                    {foreach $starts as $key => $start}
                    <tr draggable="true">
                        <td><i class="fa fa-arrows-alt handle" style="cursor: move;"></i></td>
                        <td><input type="month" class="form-control" name="education[{$key}][start]" value="{$start|htmlentities}" onclick="this.showPicker()" required></td>
                        <td><input type="month" class="form-control" name="education[{$key}][end]" value="{$ends[$key]|htmlentities}" onclick="this.showPicker()" required></td>
                        <td><input type="text" class="form-control" name="education[{$key}][school]" value="{$schools[$key]|htmlentities}" required></td>
                        <td><input type="text" class="form-control" name="education[{$key}][major]" value="{$majors[$key]|htmlentities}" required></td>
                        <td><input type="text" class="form-control" name="education[{$key}][degree]" value="{$degrees[$key]|htmlentities}" required></td>
                        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeEducationRow(this)">删除</button></td>
                    </tr>
                    {/foreach}
                </tbody>
            </table>
            <button type="button" class="btn btn-primary" onclick="addEducationRow()">添加教育经历</button>
            <script>
                let educationCounter = parseInt('{$starts|count}');
                function addEducationRow() {
                    const tbody = document.querySelector('#education-table tbody');
                    const newRow = document.createElement('tr');
                    newRow.setAttribute('draggable', 'true');
                    newRow.innerHTML = `
                        <td><i class="fa fa-arrows-alt handle" style="cursor: move;"></i></td>
                        <td><input type="month" class="form-control" name="education[${educationCounter}][start]" onclick="this.showPicker()" required></td>
                        <td><input type="month" class="form-control" name="education[${educationCounter}][end]" onclick="this.showPicker()" required></td>
                        <td><input type="text" class="form-control" name="education[${educationCounter}][school]" required></td>
                        <td><input type="text" class="form-control" name="education[${educationCounter}][major]" required></td>
                        <td><input type="text" class="form-control" name="education[${educationCounter}][degree]" required></td>
                        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeEducationRow(this)">删除</button></td>
                    `;
                    tbody.appendChild(newRow);
                    educationCounter++;
                }
                function removeEducationRow(button) {
                    const row = button.closest('tr');
                    row.remove();
                }
                document.addEventListener('DOMContentLoaded', function() {
                    const tbody = document.querySelector('#education-table tbody');
                    let draggedItem = null;

                    tbody.addEventListener('dragstart', function(e) {
                         draggedItem = e.target.closest('tr');
                         if (!draggedItem) return;
                         e.dataTransfer.effectAllowed = 'move';
                         e.dataTransfer.setData('text/html', draggedItem.innerHTML);
                         draggedItem.classList.add('dragging');
                    });

                    tbody.addEventListener('dragend', function(e) {
                         if (draggedItem) {
                             draggedItem.classList.remove('dragging');
                             draggedItem = null;
                         }
                    });

                    tbody.addEventListener('dragover', function(e) {
                         e.preventDefault();
                         e.dataTransfer.dropEffect = 'move';
                    });

                    tbody.addEventListener('drop', function(e) {
                         e.preventDefault();
                         const targetItem = e.target.closest('tr');
                         if (targetItem && draggedItem && targetItem !== draggedItem) {
                            const allRows = [...tbody.getElementsByTagName('tr')];
                            const draggedIndex = allRows.indexOf(draggedItem);
                            const droppedIndex = allRows.indexOf(targetItem);

                            if (draggedIndex < droppedIndex) {
                                targetItem.parentNode.insertBefore(draggedItem, targetItem.nextSibling);
                            } else {
                                targetItem.parentNode.insertBefore(draggedItem, targetItem);
                            }

                             updateEducationIndexes();
                         }
                    });

                    let touchstartY = 0;
                    let touchMoveItem = null;
                    let initialTouchMoveItemTop = 0;

                    tbody.addEventListener('touchstart', function(e) {
                        if (e.target.closest('.handle')) {
                            e.preventDefault();
                            touchMoveItem = e.target.closest('tr');
                            if (!touchMoveItem) return;
                            touchstartY = e.touches[0].clientY;
                            touchMoveItem.classList.add('dragging');
                            initialTouchMoveItemTop = touchMoveItem.getBoundingClientRect().top;
                        }
                    });

                    tbody.addEventListener('touchmove', function(e) {
                        if (touchMoveItem) {
                            e.preventDefault();
                            const currentY = e.touches[0].clientY;
                            const diffY = currentY - touchstartY;
                            touchMoveItem.style.transform = `translateY(${diffY}px)`;
                        }
                    });

                    tbody.addEventListener('touchend', function(e) {
                        if (touchMoveItem) {
                            touchMoveItem.classList.remove('dragging');
                            touchMoveItem.style.transform = '';

                            const finalY = initialTouchMoveItemTop + (e.changedTouches[0].clientY - touchstartY);
                            const allRows = [...tbody.getElementsByTagName('tr')];
                            let targetIndex = allRows.length - 1;

                            for (let i = 0; i < allRows.length; i++) {
                                if (allRows[i] !== touchMoveItem) {
                                    const rowRect = allRows[i].getBoundingClientRect();
                                    if (finalY < rowRect.top + rowRect.height / 2) {
                                        targetIndex = i;
                                        break;
                                    } else {
                                        targetIndex = i + 1;
                                    }
                                }
                            }

                            const currentIndex = allRows.indexOf(touchMoveItem);

                            if (currentIndex !== targetIndex) {
                                if (targetIndex === allRows.length) {
                                    tbody.appendChild(touchMoveItem);
                                } else if (targetIndex === 0 && currentIndex === 1) {
                                    tbody.insertBefore(touchMoveItem, allRows[0]);
                                } else if (targetIndex < currentIndex) {
                                    tbody.insertBefore(touchMoveItem, allRows[targetIndex]);
                                } else {
                                    tbody.insertBefore(touchMoveItem, allRows[targetIndex].nextSibling);
                                }
                                updateEducationIndexes();
                            }

                            touchMoveItem = null;
                            initialTouchMoveItemTop = 0;
                        }
                    });
                });
                function updateEducationIndexes() {
                    const rows = document.querySelectorAll('#education-table tbody tr');
                    rows.forEach((row, index) => {
                        row.querySelectorAll('input').forEach(input => {
                            let name = input.getAttribute('name');
                            if (name) {
                                name = name.replace(/education\[\d+\]/, `education[${index}]`);
                                input.setAttribute('name', name);
                            }
                        });
                    });
                }
            </script>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">工作经历:</label>
        <div class="col-xs-12 col-sm-10">
            <table class="table table-bordered" id="job-table">
                <thead>
                    <tr>
                        <th style="width: 50px;">排序</th>
                        <th>开始时间</th>
                        <th>结束时间</th>
                        <th>工作单位</th>
                        <th>工作岗位</th>
                        <th>工作内容</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {php}
                        $job_starts = explode('|', $row['job_start'] ?? '');
                        $job_ends = explode('|', $row['job_end'] ?? '');
                        $job_companies = explode('|', $row['job_company'] ?? '');
                        $job_positions = explode('|', $row['job_position'] ?? '');
                        $job_descriptions = explode('|', $row['job_description'] ?? '');
                    {/php}
                    {foreach $job_starts as $key => $start}
                    <tr draggable="true">
                        <td><i class="fa fa-arrows-alt handle" style="cursor: move;"></i></td>
                        <td><input type="month" class="form-control" name="jobs[{$key}][start]" value="{$start|htmlentities}" onclick="this.showPicker()" required></td>
                        <td><input type="month" class="form-control" name="jobs[{$key}][end]" value="{$job_ends[$key]|htmlentities}" onclick="this.showPicker()" required></td>
                        <td><input type="text" class="form-control" name="jobs[{$key}][company]" value="{$job_companies[$key]|htmlentities}" required></td>
                        <td><input type="text" class="form-control" name="jobs[{$key}][position]" value="{$job_positions[$key]|htmlentities}" required></td>
                        <td>
                            <textarea class="form-control" name="jobs[{$key}][description]" required>{$job_descriptions[$key]|htmlentities}</textarea>
                            {if condition="config('site.openai_enabled') eq '1'"}
                            <div class="mt-2">
                                <div class="input-group">
                                    <span class="input-group-addon">字数控制</span>
                                    <select class="form-control job-word-count" data-index="{$key+1}">
                                        <option value="10">10字左右</option>
                                        <option value="20">20字左右</option>
                                        <option value="30">30字左右</option>
                                        <option value="50">50字左右</option>
                                        <option value="100" selected>100字左右</option>
                                        <option value="150">150字左右</option>
                                        <option value="200">200字左右</option>
                                        <option value="300">300字左右</option>
                                    </select>
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-info" data-index="{$key+1}" onclick="generateJobDescription(this.getAttribute('data-index'))"><i class="fa fa-magic"></i> AI生成</button>
                                    </span>
                                </div>
                            </div>
                            {/if}
                        </td>
                        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeJobRow(this)">删除</button></td>
                    </tr>
                    {/foreach}
                </tbody>
            </table>
            <button type="button" class="btn btn-primary" onclick="addJobRow()">添加工作经历</button>
            <script>
                let jobCounter = parseInt('{$job_starts|count}');
                function addJobRow() {
                    const tbody = document.querySelector('#job-table tbody');
                    const newRow = document.createElement('tr');
                    newRow.setAttribute('draggable', 'true');
                    newRow.innerHTML = `
                        <td><i class="fa fa-arrows-alt handle" style="cursor: move;"></i></td>
                        <td><input type="month" class="form-control" name="jobs[${jobCounter}][start]" onclick="this.showPicker()" required></td>
                        <td><input type="month" class="form-control" name="jobs[${jobCounter}][end]" onclick="this.showPicker()" required></td>
                        <td><input type="text" class="form-control" name="jobs[${jobCounter}][company]" required></td>
                        <td><input type="text" class="form-control" name="jobs[${jobCounter}][position]" required></td>
                        <td>
                            <textarea class="form-control" name="jobs[${jobCounter}][description]" required></textarea>
                            {if condition="config('site.openai_enabled') eq '1'"}
                            <div class="mt-2">
                                <div class="input-group">
                                    <span class="input-group-addon">字数控制</span>
                                    <select class="form-control job-word-count" data-index="${jobCounter + 1}">
                                        <option value="10">10字左右</option>
                                        <option value="20">20字左右</option>
                                        <option value="30">30字左右</option>
                                        <option value="50">50字左右</option>
                                        <option value="100" selected>100字左右</option>
                                        <option value="150">150字左右</option>
                                        <option value="200">200字左右</option>
                                        <option value="300">300字左右</option>
                                    </select>
                                    <span class="input-group-btn">
                                        <button type="button" class="btn btn-info" data-index="${jobCounter + 1}" onclick="generateJobDescription(this.getAttribute('data-index'))"><i class="fa fa-magic"></i> AI生成</button>
                                    </span>
                                </div>
                            </div>
                            {/if}
                        </td>
                        <td><button type="button" class="btn btn-danger btn-sm" onclick="removeJobRow(this)">删除</button></td>
                    `;
                    tbody.appendChild(newRow);
                    jobCounter++;
                }
                function removeJobRow(button) {
                    const row = button.closest('tr');
                    row.remove();
                }
                document.addEventListener('DOMContentLoaded', function() {
                    const tbody = document.querySelector('#job-table tbody');
                    let draggedItem = null;

                    tbody.addEventListener('dragstart', function(e) {
                         draggedItem = e.target.closest('tr');
                         if (!draggedItem) return;
                         e.dataTransfer.effectAllowed = 'move';
                         e.dataTransfer.setData('text/html', draggedItem.innerHTML);
                         draggedItem.classList.add('dragging');
                    });

                    tbody.addEventListener('dragend', function(e) {
                         if (draggedItem) {
                             draggedItem.classList.remove('dragging');
                             draggedItem = null;
                         }
                    });

                    tbody.addEventListener('dragover', function(e) {
                         e.preventDefault();
                         e.dataTransfer.dropEffect = 'move';
                    });

                    tbody.addEventListener('drop', function(e) {
                         e.preventDefault();
                         const targetItem = e.target.closest('tr');
                         if (targetItem && draggedItem && targetItem !== draggedItem) {
                            const allRows = [...tbody.getElementsByTagName('tr')];
                            const draggedIndex = allRows.indexOf(draggedItem);
                            const droppedIndex = allRows.indexOf(targetItem);

                            if (draggedIndex < droppedIndex) {
                                targetItem.parentNode.insertBefore(draggedItem, targetItem.nextSibling);
                            } else {
                                targetItem.parentNode.insertBefore(draggedItem, targetItem);
                            }

                             updateJobIndexes();
                         }
                    });

                    let touchstartY = 0;
                    let touchMoveItem = null;
                    let initialTouchMoveItemTop = 0;

                    tbody.addEventListener('touchstart', function(e) {
                        if (e.target.closest('.handle')) {
                            e.preventDefault();
                            touchMoveItem = e.target.closest('tr');
                            if (!touchMoveItem) return;
                            touchstartY = e.touches[0].clientY;
                            touchMoveItem.classList.add('dragging');
                            initialTouchMoveItemTop = touchMoveItem.getBoundingClientRect().top;
                        }
                    });

                    tbody.addEventListener('touchmove', function(e) {
                        if (touchMoveItem) {
                            e.preventDefault();
                            const currentY = e.touches[0].clientY;
                            const diffY = currentY - touchstartY;
                            touchMoveItem.style.transform = `translateY(${diffY}px)`;
                        }
                    });

                    tbody.addEventListener('touchend', function(e) {
                        if (touchMoveItem) {
                            touchMoveItem.classList.remove('dragging');
                            touchMoveItem.style.transform = '';

                            const finalY = initialTouchMoveItemTop + (e.changedTouches[0].clientY - touchstartY);
                            const allRows = [...tbody.getElementsByTagName('tr')];
                            let targetIndex = allRows.length - 1;

                            for (let i = 0; i < allRows.length; i++) {
                                if (allRows[i] !== touchMoveItem) {
                                    const rowRect = allRows[i].getBoundingClientRect();
                                    if (finalY < rowRect.top + rowRect.height / 2) {
                                        targetIndex = i;
                                        break;
                                    } else {
                                        targetIndex = i + 1;
                                    }
                                }
                            }

                            const currentIndex = allRows.indexOf(touchMoveItem);

                            if (currentIndex !== targetIndex) {
                                if (targetIndex === allRows.length) {
                                    tbody.appendChild(touchMoveItem);
                                } else if (targetIndex === 0 && currentIndex === 1) {
                                    tbody.insertBefore(touchMoveItem, allRows[0]);
                                } else if (targetIndex < currentIndex) {
                                    tbody.insertBefore(touchMoveItem, allRows[targetIndex]);
                                } else {
                                    tbody.insertBefore(touchMoveItem, allRows[targetIndex].nextSibling);
                                }
                                updateJobIndexes();
                            }

                            touchMoveItem = null;
                            initialTouchMoveItemTop = 0;
                        }
                    });
                });
                function updateJobIndexes() {
                    const rows = document.querySelectorAll('#job-table tbody tr');
                    rows.forEach((row, index) => {
                        row.querySelectorAll('input, textarea').forEach(input => {
                            let name = input.getAttribute('name');
                            if (name) {
                                name = name.replace(/jobs\[\d+\]/, `jobs[${index}]`);
                                input.setAttribute('name', name);
                            }
                        });
                    });
                }
            </script>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Avatar')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-avatar" class="form-control" size="50" name="row[avatar]" type="hidden" value="{$row.avatar|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-avatar" class="btn btn-danger faupload" data-input-id="c-avatar" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-avatar"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-avatar" class="btn btn-primary fachoose" data-input-id="c-avatar" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-avatar"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-avatar"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Full_body_photo')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-full_body_photo" class="form-control" size="50" name="row[full_body_photo]" type="hidden" value="{$row.full_body_photo|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-full_body_photo" class="btn btn-danger faupload" data-input-id="c-full_body_photo" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-full_body_photo"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-full_body_photo" class="btn btn-primary fachoose" data-input-id="c-full_body_photo" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-full_body_photo"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-full_body_photo"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Id_card_front')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-id_card_front" class="form-control" size="50" name="row[id_card_front]" type="hidden" value="{$row.id_card_front|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-id_card_front" class="btn btn-danger faupload" data-input-id="c-id_card_front" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-id_card_front"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-id_card_front" class="btn btn-primary fachoose" data-input-id="c-id_card_front" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-id_card_front"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-id_card_front"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Id_card_back')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-id_card_back" class="form-control" size="50" name="row[id_card_back]" type="hidden" value="{$row.id_card_back|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-id_card_back" class="btn btn-danger faupload" data-input-id="c-id_card_back" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-id_card_back"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-id_card_back" class="btn btn-primary fachoose" data-input-id="c-id_card_back" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-id_card_back"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-id_card_back"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hk_macau_passport_front')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-hk_macau_passport_front" class="form-control" size="50" name="row[hk_macau_passport_front]" type="hidden" value="{$row.hk_macau_passport_front|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-hk_macau_passport_front" class="btn btn-danger faupload" data-input-id="c-hk_macau_passport_front" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-hk_macau_passport_front"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-hk_macau_passport_front" class="btn btn-primary fachoose" data-input-id="c-hk_macau_passport_front" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-hk_macau_passport_front"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-hk_macau_passport_front"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Hk_macau_passport_back')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-hk_macau_passport_back" class="form-control" size="50" name="row[hk_macau_passport_back]" type="hidden" value="{$row.hk_macau_passport_back|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-hk_macau_passport_back" class="btn btn-danger faupload" data-input-id="c-hk_macau_passport_back" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="false" data-preview-id="p-hk_macau_passport_back"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-hk_macau_passport_back" class="btn btn-primary fachoose" data-input-id="c-hk_macau_passport_back" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-hk_macau_passport_back"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-hk_macau_passport_back"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Additional_photos')}:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-additional_photos" class="form-control" size="50" name="row[additional_photos]" type="hidden" value="{$row.additional_photos|htmlentities}">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="faupload-additional_photos" class="btn btn-danger faupload" data-input-id="c-additional_photos" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp,image/webp" data-multiple="true" data-max="10" data-preview-id="p-additional_photos"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-additional_photos" class="btn btn-primary fachoose" data-input-id="c-additional_photos" data-mimetype="image/*" data-multiple="true"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-additional_photos"></span>
            </div>
            <ul class="row list-inline faupload-preview" id="p-additional_photos"></ul>
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Contact_person')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-contact_person" class="form-control" name="row[contact_person]" type="text" value="{$row.contact_person|htmlentities}" placeholder="请输入对接人姓名">
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            
            <div class="radio">
            {foreach name="statusList" item="vo"}
            <label for="row[status]-{$key}"><input id="row[status]-{$key}" name="row[status]" type="radio" value="{$key}" {in name="key" value="$row.status"}checked{/in} /> {$vo}</label> 
            {/foreach}
            </div>

        </div>
    </div>
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
        </div>
    </div>
</form>

<style>
    .dragging {
        opacity: 0.5;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    /* 优化表格在小屏幕下的显示 */
    @media (max-width: 768px) {
        #contacts-table, #education-table, #job-table {
            border: 1px solid #ccc;
        }

        #contacts-table thead, #education-table thead, #job-table thead {
            display: none;
        }

        #contacts-table tbody tr, #education-table tbody tr, #job-table tbody tr {
            display: block;
            margin-bottom: 15px;
            border: 1px solid #ddd;
            padding: 10px;
        }

        #contacts-table tbody td, #education-table tbody td, #job-table tbody td {
            display: block;
            text-align: right;
            padding-left: 20%; /* Make space for the label */
            position: relative;
        }

        #contacts-table tbody td::before, #education-table tbody td::before, #job-table tbody td::before {
            content: attr(data-label);
            position: absolute;
            left: 10px;
            width: 70%; /* Adjust as needed */
            text-align: left;
            font-weight: bold;
        }

        /* Specific adjustments for columns */
        #contacts-table td:nth-of-type(1)::before { content: '排序:'; }
        #contacts-table td:nth-of-type(2)::before { content: '关系:'; }
        #contacts-table td:nth-of-type(3)::before { content: '姓名:'; }
        #contacts-table td:nth-of-type(4)::before { content: '年龄:'; }
        #contacts-table td:nth-of-type(5)::before { content: '工作:'; }
        #contacts-table td:nth-of-type(6)::before { content: '操作:'; }

        #education-table td:nth-of-type(1)::before { content: '排序:'; }
        #education-table td:nth-of-type(2)::before { content: '开始时间:'; }
        #education-table td:nth-of-type(3)::before { content: '结束时间:'; }
        #education-table td:nth-of-type(4)::before { content: '学校名称:'; }
        #education-table td:nth-of-type(5)::before { content: '专业:'; }
        #education-table td:nth-of-type(6)::before { content: '学历:'; }
        #education-table td:nth-of-type(7)::before { content: '操作:'; }

        #job-table td:nth-of-type(1)::before { content: '排序:'; }
        #job-table td:nth-of-type(2)::before { content: '开始时间:'; }
        #job-table td:nth-of-type(3)::before { content: '结束时间:'; }
        #job-table td:nth-of-type(4)::before { content: '工作单位:'; }
        #job-table td:nth-of-type(5)::before { content: '工作岗位:'; }
        #job-table td:nth-of-type(6)::before { content: '工作内容:'; }
        #job-table td:nth-of-type(7)::before { content: '操作:'; }
    }
</style>

<!-- 添加AI功能的JS代码 -->
<script>
// AI相关JS方法
async function generateJobDescription(index) {
    const workItems = document.querySelectorAll('#job-table tbody tr');
    const workItem = workItems[index - 1];
    if (!workItem) {
        Toastr.error('未找到对应的工作经历');
        return;
    }
    
    const companyInput = workItem.querySelector('input[name^="job"][name$="[company]"], input[name^="jobs"][name$="[company]"]');
    const positionInput = workItem.querySelector('input[name^="job"][name$="[position]"], input[name^="jobs"][name$="[position]"]');
    const wordCountSelect = workItem.querySelector('select.job-word-count[data-index="' + index + '"]');
    
    if (!companyInput || !positionInput || !wordCountSelect) {
        Toastr.error('表单元素未找到');
        return;
    }
    
    const company = companyInput.value;
    const position = positionInput.value;
    const wordCount = wordCountSelect.value;
    
    if (!company || !position) {
        Toastr.warning('请先填写工作单位和岗位');
        return;
    }
    
    try {
        Layer.load(1, {shade: [0.1, '#fff']});
        const response = await fetch('{:url("admin/resume/generateJobDescription")}', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
            body: JSON.stringify({ company: company, position: position, word_count: wordCount })
        });
        
        const result = await response.json();
        Layer.closeAll('loading');
        
        if (result.code === 1) {
            const textarea = workItem.querySelector('textarea[name^="job"][name$="[description]"], textarea[name^="jobs"][name$="[description]"]');
            if (textarea) {
                textarea.value = result.data;
                Toastr.success('生成成功');
            } else {
                throw new Error('未找到工作内容文本框');
            }
        } else {
            throw new Error(result.msg || '生成失败');
        }
    } catch (error) {
        Layer.closeAll('loading');
        Toastr.error(error.message || '生成失败，请稍后重试');
    }
}

async function generateSelfEvaluation() {
    const textarea = document.querySelector('textarea[name="row[self_evaluation]"]');
    const wordCount = document.getElementById('selfEvaluationWordCount').value;
    
    if (!textarea.value) {
        Toastr.warning('请先输入自我评价内容');
        return;
    }
    
    try {
        Layer.load(1, {shade: [0.1, '#fff']});
        const response = await fetch('{:url("admin/resume/generateSelfEvaluation")}', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
            body: JSON.stringify({ content: textarea.value, word_count: wordCount })
        });
        
        const result = await response.json();
        Layer.closeAll('loading');
        
        if (result.code === 1) {
            textarea.value = result.data;
            Toastr.success('润色成功');
        } else {
            throw new Error(result.msg || '润色失败');
        }
    } catch (error) {
        Layer.closeAll('loading');
        Toastr.error(error.message || '润色失败，请稍后重试');
    }
}

async function generateHobbies() {
    const input = document.querySelector('input[name="row[hobbies]"]');
    const wordCount = document.getElementById('hobbiesWordCount').value;
    
    if (!input.value) {
        Toastr.warning('请先输入兴趣爱好内容');
        return;
    }
    
    try {
        Layer.load(1, {shade: [0.1, '#fff']});
        const response = await fetch('{:url("admin/resume/generateHobbies")}', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json', 'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content') },
            body: JSON.stringify({ content: input.value, word_count: wordCount })
        });
        
        const result = await response.json();
        Layer.closeAll('loading');
        
        if (result.code === 1) {
            input.value = result.data;
            Toastr.success('润色成功');
        } else {
            throw new Error(result.msg || '润色失败');
        }
    } catch (error) {
        Layer.closeAll('loading');
        Toastr.error(error.message || '润色失败，请稍后重试');
    }
}
</script>
