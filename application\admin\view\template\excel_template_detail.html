<script>
// 监听页面完全加载事件
window.addEventListener('load', function() {
    // 防止重复初始化
    if (window.excelLoadEventTriggered) {
        console.log('Excel加载事件已触发，跳过');
        return;
    }
    
    window.excelLoadEventTriggered = true;
    console.log('触发Excel加载事件');
    
    // 再次检查LuckySheet初始化状态
    setTimeout(function() {
        
        
        // 强制触发加载Excel文件
        
        if(typeof loadExcelFile === 'function') {
            loadExcelFile();
        } else {
            console.error('loadExcelFile函数未定义');
        }
    }, 100);
});
</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<!-- jQuery CDN fallback -->
<script>
    if (typeof jQuery === 'undefined') {
        document.write('<script src="/assets/libs/jquery/dist/jquery.min.js"><\/script>');
    }
</script>

<!-- Spectrum Colorpicker -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/spectrum-colorpicker2@2.0.8/dist/spectrum.min.css">
<script src="https://cdn.jsdelivr.net/npm/spectrum-colorpicker2@2.0.8/dist/spectrum.min.js"></script>

<!-- 确保Spectrum可用 -->
<script>
// 检查Spectrum是否可用，如果不可用则内联
if (typeof jQuery !== 'undefined' && typeof jQuery.fn.spectrum === 'undefined') {
    
    
    // 添加必要的CSS样式
    var spectrumCss = document.createElement('style');
    spectrumCss.type = 'text/css';
    spectrumCss.innerHTML = `
    .sp-container {
        position: absolute;
        top: 0;
        left: 0;
        display: inline-block;
        z-index: 9999;
        overflow: hidden;
        box-sizing: content-box;
        background: #fff;
        border-radius: 4px;
        padding: 12px;
        box-shadow: 0 0 5px rgba(0,0,0,.5);
    }
    .sp-picker-container {
        position: relative;
    }
    .sp-color {
        width: 90px;
        height: 90px;
        position: relative;
        border: 1px solid #ccc;
    }
    .sp-palette-container {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
    }
    .sp-palette-button {
        display: inline-block;
        width: 20px;
        height: 20px;
        margin: 2px;
        border: 1px solid #ccc;
        cursor: pointer;
    }
    .sp-button-container {
        margin-top: 10px;
        text-align: right;
    }
    .sp-cancel, .sp-choose {
        display: inline-block;
        padding: 5px 10px;
        margin-left: 5px;
        border-radius: 3px;
        text-decoration: none;
        font-size: 12px;
    }
    .sp-cancel {
        color: #777;
        background: #f5f5f5;
        border: 1px solid #ccc;
    }
    .sp-choose {
        color: #fff;
        background: #3498db;
        border: 1px solid #2980b9;
    }
    `;
    document.head.appendChild(spectrumCss);
    
    // 内联基本的Spectrum功能
    (function($) {
        // 基本颜色转换功能
        function hexToRgb(hex) {
            hex = hex.replace(/^#/, '');
            var bigint = parseInt(hex, 16);
            var r = (bigint >> 16) & 255;
            var g = (bigint >> 8) & 255;
            var b = bigint & 255;
            return [r, g, b];
        }
        
        function rgbToHex(r, g, b) {
            return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
        }
        
        // 定义spectrum函数
        $.fn.spectrum = function(options) {
            var opts = $.extend({
                color: '#000000',
                showInput: true,
                showPalette: true,
                showSelectionPalette: true,
                palette: [
                    ['#000000', '#434343', '#666666', '#999999', '#b7b7b7', '#cccccc', '#d9d9d9', '#efefef', '#f3f3f3', '#ffffff'],
                    ['#980000', '#ff0000', '#ff9900', '#ffff00', '#00ff00', '#00ffff', '#4a86e8', '#0000ff', '#9900ff', '#ff00ff'],
                    ['#e6b8af', '#f4cccc', '#fce5cd', '#fff2cc', '#d9ead3', '#d0e0e3', '#c9daf8', '#cfe2f3', '#d9d2e9', '#ead1dc'],
                    ['#dd7e6b', '#ea9999', '#f9cb9c', '#ffe599', '#b6d7a8', '#a2c4c9', '#a4c2f4', '#9fc5e8', '#b4a7d6', '#d5a6bd']
                ],
                change: function() {},
                hide: function() {}
            }, options);
            
            return this.each(function() {
                var $this = $(this);
                var initialColor = $this.val() || opts.color;
                
                // 如果是input元素
                if ($this.is('input')) {
                    // 标准的颜色输入
                    try {
                        // 首先尝试使用标准的color输入类型
                        var $colorInput = $('<input type="color">');
                        if ($colorInput[0].type === 'color') {
                            $this.attr('type', 'color');
                            $this.val(initialColor);
                            
                            $this.on('change', function() {
                                if (typeof opts.change === 'function') {
                                    opts.change.call($this, $this.val());
                                }
                            });
                            
                            // 提供API
                            $this.spectrum = {
                                set: function(color) {
                                    $this.val(color);
                                    return this;
                                },
                                get: function() {
                                    return $this.val();
                                },
                                container: null,
                                show: function() { return this; },
                                hide: function() { return this; },
                                destroy: function() { return this; }
                            };
                            
                            return;
                        }
                    } catch(e) {
                        console.warn('无法设置input为color类型，使用自定义实现');
                    }
                    
                    // 自定义实现
                    var containerId = 'sp-' + Math.floor(Math.random() * 1000000);
                    var currentColor = initialColor;
                    
                    // 创建容器
                    var $container = $('<div class="sp-container" id="' + containerId + '"></div>').hide();
                    var $pickerContainer = $('<div class="sp-picker-container"></div>');
                    var $colorArea = $('<div class="sp-color"></div>');
                    var $paletteContainer = $('<div class="sp-palette-container"></div>');
                    var $buttonContainer = $('<div class="sp-button-container"></div>');
                    
                    // 创建调色板
                    if (opts.showPalette) {
                        for (var i = 0; i < opts.palette.length; i++) {
                            var row = opts.palette[i];
                            for (var j = 0; j < row.length; j++) {
                                var color = row[j];
                                var $paletteButton = $('<div class="sp-palette-button"></div>')
                                    .css('background-color', color)
                                    .data('color', color)
                                    .on('click', function() {
                                        var color = $(this).data('color');
                                        currentColor = color;
                                        $this.val(color);
                                        if (typeof opts.change === 'function') {
                                            opts.change.call($this, color);
                                        }
                                        $container.hide();
                                        if (typeof opts.hide === 'function') {
                                            opts.hide.call($this);
                                        }
                                    });
                                $paletteContainer.append($paletteButton);
                            }
                        }
                    }
                    
                    // 创建按钮
                    var $cancelButton = $('<a href="#" class="sp-cancel">取消</a>')
                        .on('click', function(e) {
                            e.preventDefault();
                            $container.hide();
                            if (typeof opts.hide === 'function') {
                                opts.hide.call($this);
                            }
                        });
                    
                    var $chooseButton = $('<a href="#" class="sp-choose">确定</a>')
                        .on('click', function(e) {
                            e.preventDefault();
                            $this.val(currentColor);
                            if (typeof opts.change === 'function') {
                                opts.change.call($this, currentColor);
                            }
                            $container.hide();
                            if (typeof opts.hide === 'function') {
                                opts.hide.call($this);
                            }
                        });
                    
                    $buttonContainer.append($cancelButton).append($chooseButton);
                    
                    // 组装颜色选择器
                    $pickerContainer.append($colorArea);
                    $container.append($pickerContainer).append($paletteContainer).append($buttonContainer);
                    $('body').append($container);
                    
                    // 输入框点击时显示选择器
                    $this.on('click', function(e) {
                        // 定位选择器
                        var offset = $this.offset();
                        $container.css({
                            top: offset.top + $this.outerHeight() + 5,
                            left: offset.left
                        }).show();
                        
                        // 阻止点击事件冒泡
                        e.stopPropagation();
                    });
                    
                    // 点击页面其他区域关闭选择器
                    $(document).on('click', function(e) {
                        if (!$(e.target).closest('#' + containerId).length && !$(e.target).is($this)) {
                            $container.hide();
                        }
                    });
                    
                    // 提供API
                    $this.spectrum = {
                        set: function(color) {
                            currentColor = color;
                            $this.val(color);
                            return this;
                        },
                        get: function() {
                            return currentColor;
                        },
                        container: $container,
                        show: function() {
                            $container.show();
                            return this;
                        },
                        hide: function() {
                            $container.hide();
                            return this;
                        },
                        destroy: function() {
                            $container.remove();
                            return this;
                        }
                    };
                }
            });
        };
    })(jQuery);
}
</script>

<!-- 内联jQuery mousewheel插件 -->
<script>
/*!
 * jQuery Mousewheel 3.1.13
 * Copyright jQuery Foundation and other contributors
 */
(function(factory) {
    if (typeof define === 'function' && define.amd) {
        define(['jquery'], factory);
    } else if (typeof exports === 'object') {
        module.exports = factory;
    } else {
        factory(jQuery);
    }
}(function($) {
    var toFix = ['wheel', 'mousewheel', 'DOMMouseScroll', 'MozMousePixelScroll'],
        toBind = ('onwheel' in document || document.documentMode >= 9) ? ['wheel'] : ['mousewheel', 'DomMouseScroll', 'MozMousePixelScroll'],
        slice = Array.prototype.slice,
        nullLowestDeltaTimeout, lowestDelta;

    if ($.event.fixHooks) {
        for (var i = toFix.length; i;) {
            $.event.fixHooks[toFix[--i]] = $.event.mouseHooks;
        }
    }

    var special = $.event.special.mousewheel = {
        version: '3.1.12',
        setup: function() {
            if (this.addEventListener) {
                for (var i = toBind.length; i;) {
                    this.addEventListener(toBind[--i], handler, false);
                }
            } else {
                this.onmousewheel = handler;
            }
            $.data(this, 'mousewheel-line-height', special.getLineHeight(this));
            $.data(this, 'mousewheel-page-height', special.getPageHeight(this));
        },
        teardown: function() {
            if (this.removeEventListener) {
                for (var i = toBind.length; i;) {
                    this.removeEventListener(toBind[--i], handler, false);
                }
            } else {
                this.onmousewheel = null;
            }
            $.removeData(this, 'mousewheel-line-height');
            $.removeData(this, 'mousewheel-page-height');
        },
        getLineHeight: function(elem) {
            var $elem = $(elem),
                $parent = $elem['offsetParent' in $.fn ? 'offsetParent' : 'parent']();
            if (!$parent.length) {
                $parent = $('body');
            }
            return parseInt($parent.css('fontSize'), 10) || parseInt($elem.css('fontSize'), 10) || 16;
        },
        getPageHeight: function(elem) {
            return $(elem).height();
        },
        settings: {
            adjustOldDeltas: true, // see shouldAdjustOldDeltas() below
            normalizeOffset: true  // calls getBoundingClientRect for each event
        }
    };

    $.fn.extend({
        mousewheel: function(fn) {
            return fn ? this.bind('mousewheel', fn) : this.trigger('mousewheel');
        },
        unmousewheel: function(fn) {
            return this.unbind('mousewheel', fn);
        }
    });

    function handler(event) {
        var orgEvent = event || window.event,
            args = slice.call(arguments, 1),
            delta = 0,
            deltaX = 0,
            deltaY = 0,
            absDelta = 0,
            offsetX = 0,
            offsetY = 0;
        event = $.event.fix(orgEvent);
        event.type = 'mousewheel';

        // Old school scrollwheel delta
        if ('detail' in orgEvent) { deltaY = orgEvent.detail * -1; }
        if ('wheelDelta' in orgEvent) { deltaY = orgEvent.wheelDelta; }
        if ('wheelDeltaY' in orgEvent) { deltaY = orgEvent.wheelDeltaY; }
        if ('wheelDeltaX' in orgEvent) { deltaX = orgEvent.wheelDeltaX * -1; }

        // Firefox < 17 horizontal scrolling related to DOMMouseScroll event
        if ('axis' in orgEvent && orgEvent.axis === orgEvent.HORIZONTAL_AXIS) {
            deltaX = deltaY * -1;
            deltaY = 0;
        }

        // Set delta to be deltaY or deltaX if deltaY is 0 for backwards compatabilitiy
        delta = deltaY === 0 ? deltaX : deltaY;

        // New school wheel delta (wheel event)
        if ('deltaY' in orgEvent) {
            deltaY = orgEvent.deltaY * -1;
            delta = deltaY;
        }
        if ('deltaX' in orgEvent) {
            deltaX = orgEvent.deltaX;
            if (deltaY === 0) { delta = deltaX * -1; }
        }

        // No change actually happened, no reason to go any further
        if (deltaY === 0 && deltaX === 0) { return; }

        // Need to convert lines and pages to pixels if we aren't already in pixels
        // There are three delta modes:
        //   * deltaMode 0 is by pixels, nothing to do
        //   * deltaMode 1 is by lines
        //   * deltaMode 2 is by pages
        if (orgEvent.deltaMode === 1) {
            var lineHeight = $.data(this, 'mousewheel-line-height');
            delta *= lineHeight;
            deltaY *= lineHeight;
            deltaX *= lineHeight;
        } else if (orgEvent.deltaMode === 2) {
            var pageHeight = $.data(this, 'mousewheel-page-height');
            delta *= pageHeight;
            deltaY *= pageHeight;
            deltaX *= pageHeight;
        }

        // Store lowest absolute delta to normalize the delta values
        absDelta = Math.max(Math.abs(deltaY), Math.abs(deltaX));

        if (!lowestDelta || absDelta < lowestDelta) {
            lowestDelta = absDelta;
            // Adjust older deltas if necessary
            if (shouldAdjustOldDeltas(orgEvent, absDelta)) {
                lowestDelta /= 40;
            }
        }

        // Adjust older deltas if necessary
        if (shouldAdjustOldDeltas(orgEvent, absDelta)) {
            // Divide all the things by 40!
            delta /= 40;
            deltaX /= 40;
            deltaY /= 40;
        }

        // Get a whole, normalized value for the deltas
        delta = Math[delta >= 1 ? 'floor' : 'ceil'](delta / lowestDelta);
        deltaX = Math[deltaX >= 1 ? 'floor' : 'ceil'](deltaX / lowestDelta);
        deltaY = Math[deltaY >= 1 ? 'floor' : 'ceil'](deltaY / lowestDelta);

        // Normalise offsetX and offsetY properties
        if (special.settings.normalizeOffset && this.getBoundingClientRect) {
            var boundingRect = this.getBoundingClientRect();
            offsetX = event.clientX - boundingRect.left;
            offsetY = event.clientY - boundingRect.top;
        }

        // Add information to the event object
        event.deltaX = deltaX;
        event.deltaY = deltaY;
        event.deltaFactor = lowestDelta;
        event.offsetX = offsetX;
        event.offsetY = offsetY;
        // Go ahead and set deltaMode to 0 since we converted to pixels
        // Although this is a little odd since we overwrite the deltaX/Y
        // properties with normalized deltas.
        event.deltaMode = 0;

        // Add event and delta to the front of the arguments
        args.unshift(event, delta, deltaX, deltaY);

        // Clearout lowestDelta after sometime to better
        // handle multiple device types that give different
        // a different lowestDelta
        // Ex: trackpad = 3 and mouse wheel = 120
        if (nullLowestDeltaTimeout) { clearTimeout(nullLowestDeltaTimeout); }
        nullLowestDeltaTimeout = setTimeout(nullLowestDelta, 200);

        return ($.event.dispatch || $.event.handle).apply(this, args);
    }

    function nullLowestDelta() {
        lowestDelta = null;
    }

    function shouldAdjustOldDeltas(orgEvent, absDelta) {
        // If this is an older event and the delta is divisable by 120,
        // then we are assuming that the browser is treating this as an
        // older mouse wheel event and that we should divide the deltas
        // by 40 to try and get a more usable deltaFactor.
        // Side note, this actually impacts the reported scroll distance
        // in older browsers and can cause scrolling to be slower than native.
        // Turn this off by setting $.event.special.mousewheel.settings.adjustOldDeltas to false.
        return special.settings.adjustOldDeltas && orgEvent.type === 'mousewheel' && absDelta % 120 === 0;
    }
}));
</script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/spectrum-colorpicker2@2.0.8/dist/spectrum.min.js"></script>
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/spectrum-colorpicker2@2.0.8/dist/spectrum.min.css">

<!-- 自定义Layer.js简单实现，处理Layer未定义问题 -->
<script>
var Layer = {
    msg: function(msg, options) {
        options = options || {};
        var icon = options.icon || 0;
        var time = options.time || 3000;
        var shade = options.shade || false;
        
        // 创建消息容器
        var msgDiv = document.createElement('div');
        msgDiv.className = 'excel-layer-msg';
        
        // 设置图标
        var iconHtml = '';
        if (icon === 1) {
            iconHtml = '<i class="fa fa-check-circle"></i> ';
        } else if (icon === 2) {
            iconHtml = '<i class="fa fa-times-circle"></i> ';
        } else if (icon === 16) {
            iconHtml = '<i class="fa fa-spinner fa-spin"></i> ';
        } else {
            iconHtml = '<i class="fa fa-info-circle"></i> ';
        }
        
        // 设置内容
        msgDiv.innerHTML = iconHtml + msg;
        
        // 设置样式
        msgDiv.style.position = 'fixed';
        msgDiv.style.top = '50%';
        msgDiv.style.left = '50%';
        msgDiv.style.transform = 'translate(-50%, -50%)';
        msgDiv.style.zIndex = '19891015';
        msgDiv.style.padding = '12px 25px';
        msgDiv.style.background = 'rgba(0, 0, 0, 0.7)';
        msgDiv.style.color = '#fff';
        msgDiv.style.borderRadius = '3px';
        msgDiv.style.fontSize = '14px';
        
        // 添加到body
        document.body.appendChild(msgDiv);
        
        // 创建遮罩
        var shadeDiv = null;
        if (shade) {
            shadeDiv = document.createElement('div');
            shadeDiv.className = 'excel-layer-shade';
            shadeDiv.style.position = 'fixed';
            shadeDiv.style.top = '0';
            shadeDiv.style.left = '0';
            shadeDiv.style.width = '100%';
            shadeDiv.style.height = '100%';
            shadeDiv.style.background = 'rgba(0, 0, 0, ' + (typeof shade === 'number' ? shade : 0.3) + ')';
            shadeDiv.style.zIndex = '19891014';
            document.body.appendChild(shadeDiv);
        }
        
        // 定时关闭
        var timer = null;
        if (time !== 0) {
            timer = setTimeout(function() {
                document.body.removeChild(msgDiv);
                if (shadeDiv) {
                    document.body.removeChild(shadeDiv);
                }
            }, time);
        }
        
        // 返回索引，用于手动关闭
        return {
            index: new Date().getTime(),
            close: function() {
                if (timer) {
                    clearTimeout(timer);
                }
                if (document.body.contains(msgDiv)) {
                    document.body.removeChild(msgDiv);
                }
                if (shadeDiv && document.body.contains(shadeDiv)) {
                    document.body.removeChild(shadeDiv);
                }
            }
        };
    },
    close: function(index) {
        // 简单实现，仅用于关闭消息
        var msgElements = document.getElementsByClassName('excel-layer-msg');
        for (var i = 0; i < msgElements.length; i++) {
            document.body.removeChild(msgElements[i]);
        }
        
        var shadeElements = document.getElementsByClassName('excel-layer-shade');
        for (var j = 0; j < shadeElements.length; j++) {
            document.body.removeChild(shadeElements[j]);
        }
    },
    confirm: function(msg, options) {
        options = options || {};
        return Layer.msg(msg, $.extend({icon: 3}, options));
    }
};

// 确保window.layer也可用
window.layer = Layer;
</script>

<!-- 引入LuckySheet相关资源 -->
<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/plugins/css/pluginsCss.css' />
<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/plugins/plugins.css' />
<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/css/luckysheet.css' />
<link rel='stylesheet' href='https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/assets/iconfont/iconfont.css' />
<script src="https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/plugins/js/plugin.js"></script>
<script src="https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/luckysheet.umd.js"></script>
<!-- 引入LuckyExcel用于解析Excel文件 -->
<script src="https://cdn.jsdelivr.net/npm/exceljs@4.3.0/dist/exceljs.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/luckyexcel@1.0.1/dist/luckyexcel.umd.js"></script>

<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="type">
            <li><a href="#t-edit" data-value="" data-toggle="tab">{:__('在线编辑')}</a></li>
            <li class="active"><a href="#t-view" data-value="" data-toggle="tab">{:__('Excel模板详情')}</a></li>

        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <!-- 模板详情视图 -->
            <div class="tab-pane fade active in" id="t-view">
                <div class="widget-body no-padding">
                    <div class="panel-intro">
                        <div class="panel-lead"><em>Excel模板详情</em>简历导出时使用的Excel模板详情</div>
                    </div>
                    
                    <!-- 工具栏 -->
                    <div id="toolbar" class="toolbar">
                        <a href="{:url('template/excel')}" class="btn btn-info" title="返回"><i class="fa fa-reply"></i> 返回模板列表</a>
                    </div>
                    
                    <!-- 模板信息 -->
                    <div class="template-edit-container">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="template-info-card">
                                    <div class="template-info-header">
                                        <div class="template-icon">
                                            <i class="fa fa-file-excel-o"></i>
                                        </div>
                                        <div class="template-info">
                                            <h3 class="template-name">{$templateInfo.name}</h3>
                                            <div class="template-meta">
                                                <span><i class="fa fa-hdd-o"></i> {$templateInfo.size|format_bytes}</span>
                                                <span><i class="fa fa-clock-o"></i> 更新时间: {$templateInfo.modified}</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="template-actions">
                                        <a href="{:url('template/previewTemplate')}?path={$templateInfo.path|urlencode}&type=excel" target="_blank" class="btn btn-info">
                                            <i class="fa fa-eye"></i> 预览模板
                                        </a>
                                        <a href="{:url('template/downloadTemplate')}?path={$templateInfo.path|urlencode}&type=excel" class="btn btn-success">
                                            <i class="fa fa-download"></i> 下载模板
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 占位符列表 -->
                        <div class="row mt-20">
                            <div class="col-md-12">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h3 class="panel-title">占位符列表</h3>
                                    </div>
                                    <div class="panel-body">
                                        <div class="alert alert-warning">
                                            <i class="fa fa-lightbulb-o"></i> 
                                            以下是当前模板中使用的占位符列表，Excel模板中使用这些占位符来表示数据字段。点击"复制"按钮可以将占位符复制到剪贴板。
                                        </div>
                                        
                                        <div class="placeholder-search mb-20">
                                            <div class="input-group">
                                                <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                                <input type="text" class="form-control" id="placeholderSearchInput" placeholder="搜索占位符...">
                                                <span class="input-group-btn">
                                                    <button class="btn btn-default btn-clear-search" type="button">
                                                        <i class="fa fa-times"></i> 清除
                                                    </button>
                                                </span>
                                            </div>
                                        </div>
                                        
                                        <div id="placeholderList" class="placeholder-list" data-path="{$templateInfo.path}">
                                            <div class="text-center">
                                                <i class="fa fa-spinner fa-spin fa-2x"></i>
                                                <p class="mt-10">正在加载占位符列表...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 在线编辑视图 -->
            <div class="tab-pane fade" id="t-edit">
                <div class="widget-body no-padding">
                    <div class="panel-intro">
                        <div class="panel-lead"><em>在线编辑Excel模板</em>您可以直接修改单元格内容，添加或编辑占位符（格式为 {{占位符名称}}）</div>
        </div>
                    
                    <!-- 工具栏 -->
                    <div id="edit-toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-success" id="btnSaveExcel"><i class="fa fa-save"></i> 保存修改</a>
                        <a href="{:url('template/excel')}" class="btn btn-info" title="返回"><i class="fa fa-reply"></i> 返回模板列表</a>
                        <a href="javascript:;" class="btn btn-info" id="btnInsertPlaceholder"><i class="fa fa-plus"></i> 插入占位符</a>
                    </div>
                    
                    <!-- 在线编辑器容器 -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="panel panel-default">
                                <div class="panel-body" style="padding: 0;">
                                    <div id="luckysheet" style="margin:0px;padding:0px;width:100%;height:1200px;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 模板编辑页样式 */
.template-edit-container {
    padding: 15px;
}

.template-info-card {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.template-info-header {
    display: flex;
    align-items: center;
}

.template-icon {
    font-size: 48px;
    margin-right: 20px;
    color: #217346; /* Excel绿色 */
}

.template-name {
    font-weight: 500;
    margin-top: 0;
    margin-bottom: 5px;
    color: #333;
}

.template-meta {
    display: flex;
    flex-wrap: wrap;
    color: #777;
    font-size: 14px;
}

.template-meta span {
    margin-right: 15px;
}

.template-actions {
    display: flex;
    gap: 10px;
}

.mt-10 {
    margin-top: 10px;
}

.mt-20 {
    margin-top: 20px;
}

/* 占位符列表样式 */
.placeholder-list {
    margin-top: 15px;
}

.placeholder-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -10px;
}

.placeholder-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    margin-bottom: 10px;
    background: #f9f9f9;
    border-radius: 4px;
    border-left: 4px solid #217346;
    transition: all 0.2s ease;
    width: calc(33.333% - 20px);
    margin-left: 10px;
    margin-right: 10px;
    box-sizing: border-box;
    min-height: 70px;
}

@media (max-width: 1200px) {
    .placeholder-item {
        width: calc(50% - 20px);
    }
}

@media (max-width: 768px) {
    .placeholder-item {
        width: calc(100% - 20px);
    }
}

.placeholder-item:hover {
    background: #f0f0f0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.placeholder-info {
    flex: 1;
    min-width: 0;
    padding-right: 10px;
}

.placeholder-name {
    font-family: monospace;
    font-weight: bold;
    font-size: 14px;
    color: #333;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.placeholder-desc {
    color: #777;
    font-size: 13px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 占位符分类 */
.placeholder-category {
    margin-top: 25px;
    margin-bottom: 15px;
    font-weight: 500;
    color: #555;
    border-bottom: 1px solid #eee;
    padding-bottom: 8px;
}

.placeholder-category:first-child {
    margin-top: 0;
}

.placeholder-row {
    margin-bottom: 5px;
}

/* 搜索框样式 */
.placeholder-search {
    margin-bottom: 15px;
}

.mb-20 {
    margin-bottom: 20px;
}

/* 占位符计数 */
.placeholder-count {
    font-size: 12px;
    color: #777;
    margin-left: 10px;
}

/* 无匹配结果样式 */
.no-match {
    padding: 20px;
    text-align: center;
    background: #f9f9f9;
    border-radius: 4px;
    color: #777;
}

/* 高亮搜索结果 */
.highlight {
    background-color: #ffeb3b;
    color: #000;
    padding: 0 2px;
    border-radius: 2px;
}

.placeholder-copy {
    flex-shrink: 0;
    margin-left: 5px;
}

/* 自适应高度的LuckySheet容器 */
#luckysheet, [id^="luckysheet_"] {
    height: calc(100vh - 250px) !important;
    min-height: 600px;
}

.custom-sheet-tabs {
    display: flex;
    background: #f5f5f5;
    border-top: 1px solid #e0e0e0;
    padding: 5px 10px;
    overflow-x: auto;
    white-space: nowrap;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
}

.custom-sheet-tab {
    padding: 5px 15px;
    margin-right: 5px;
    border: 1px solid #ccc;
    border-radius: 3px;
    cursor: pointer;
    background: #fff;
    transition: all 0.2s;
    user-select: none;
}

.custom-sheet-tab:hover {
    background: #e9e9e9;
}

.custom-sheet-tab.active {
    background: #217346;
    color: white;
    border-color: #217346;
}

.excel-toolbar {
    margin-bottom: 15px;
}

@media (max-width: 767px) {
    .placeholder-item {
        flex: 0 0 calc(50% - 10px);
    }
}

@media (max-width: 480px) {
    .placeholder-item {
        flex: 0 0 calc(100% - 10px);
    }
}

/* 自定义确认对话框样式 */
.layer-custom-confirm {
    background-color: #fff !important;
    border-radius: 5px !important;
    box-shadow: 0 2px 10px rgba(0,0,0,0.2) !important;
    padding: 0 !important;
    min-width: 300px !important;
    max-width: 90% !important;
}

.layer-confirm {
    padding: 20px;
}

.layer-confirm-msg {
    margin-bottom: 20px;
    font-size: 14px;
    line-height: 1.5;
}

.layer-confirm-msg .fa {
    color: #f39c12;
    margin-right: 8px;
    font-size: 16px;
}

.layer-confirm-btns {
    text-align: right;
}

.layer-confirm-btns .btn {
    margin-left: 10px;
    padding: 5px 15px;
}
</style>

<script>
// 全局变量，存储所有占位符
var allPlaceholders = [];
// 存储Excel数据和luckysheet实例
var excelData = null;
var luckysheetInstance = null;
// 依赖项加载状态
var dependenciesLoaded = false;

// 主初始化函数
function initTemplatePage() {
    // 记录初始化开始
    
    
    // 确保jQuery可用
    if (typeof jQuery === 'undefined') {
        console.error('jQuery未加载，尝试加载本地版本');
        var script = document.createElement('script');
        script.src = '/assets/libs/jquery/dist/jquery.min.js';
        script.onload = function() {
            
            preloadDependencies();
            setupTemplatePage();
        };
        script.onerror = function() {
            console.error('本地jQuery加载失败');
            showJQueryError();
        };
        document.head.appendChild(script);
    } else {
        // jQuery已加载，预加载其他依赖项
        
        preloadDependencies();
        // 直接初始化
        setupTemplatePage();
    }
}

// 预加载依赖项
function preloadDependencies() {
    // 检查必要的依赖项
    var dependencies = [
        {
            test: function() { return typeof moment !== 'undefined'; },
            url: 'https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.1/moment.min.js',
            name: 'Moment.js'
        },
        {
            test: function() { return typeof jQuery.fn.spectrum !== 'undefined'; },
            url: 'https://cdn.jsdelivr.net/npm/spectrum-colorpicker2@2.0.8/dist/spectrum.min.js',
            name: 'Spectrum Colorpicker'
        }
    ];
    
    var loadCount = 0;
    var totalDeps = dependencies.length;
    
    // 添加mousewheel支持
    if (typeof jQuery.fn.mousewheel === 'undefined') {
        
    }
    
    // 添加spectrum支持
    if (typeof jQuery.fn.spectrum === 'undefined') {
        
    }
    
    // 检查每个依赖项
    dependencies.forEach(function(dependency) {
        if (!dependency.test()) {
            
            var script = document.createElement('script');
            script.src = dependency.url;
            script.onload = function() {
                
                loadCount++;
                if (loadCount === totalDeps) {
                    dependenciesLoaded = true;
                    
                }
            };
            script.onerror = function() {
                console.error('加载依赖项失败: ' + dependency.name);
                loadCount++;
                if (loadCount === totalDeps) {
                    dependenciesLoaded = true;
                    
                }
            };
            document.head.appendChild(script);
        } else {
            
            loadCount++;
            if (loadCount === totalDeps) {
                dependenciesLoaded = true;
                
            }
        }
    });
    
    // 如果没有需要加载的依赖项
    if (dependencies.length === 0 || loadCount === totalDeps) {
        dependenciesLoaded = true;
        
    }
}

// 显示jQuery错误消息
function showJQueryError() {
    var placeholderContainer = document.getElementById('placeholderList');
    if (placeholderContainer) {
        placeholderContainer.innerHTML = 
            '<div class="alert alert-danger">' +
            '<i class="fa fa-exclamation-circle"></i> ' +
            'jQuery未加载，页面无法正常工作。请检查网络连接后刷新页面' +
            '<button class="btn btn-xs btn-danger ml-10" onclick="location.reload()">' +
            '<i class="fa fa-refresh"></i> 刷新页面</button>' +
            '</div>';
    }
    }
    
// 设置模板详情页面功能
function setupTemplatePage() {
    // jQuery已加载，直接使用
    var $ = jQuery;
    
    
    // 检查LuckySheet脚本是否加载
    if (typeof window.luckysheet === 'undefined') {
        console.error('LuckySheet未加载，尝试手动加载');
        loadLuckySheetScript();
        return;
    }
    
    // 加载占位符列表
    loadPlaceholders();
    
    // 保存Excel按钮事件
    $('#btnSaveExcel').off('click').on('click', function() {
        saveExcelFile();
    });
    
    // 添加插入占位符按钮事件 - 确保先移除可能存在的事件处理程序
    $('#btnInsertPlaceholder').off('click').on('click', function() {
        showPlaceholderSelector();
    });
    
    // 搜索框事件处理
    $('#placeholderSearchInput').off('input').on('input', function() {
        filterPlaceholders($(this).val().trim());
    });
    
    // 清除搜索按钮事件 - 使用委托方式绑定，避免重复绑定
    $(document).off('click', '.btn-clear-search').on('click', '.btn-clear-search', function() {
        clearSearch();
    });
    
    // 初始化选项卡切换事件
    $('a[data-toggle="tab"]').off('shown.bs.tab').on('shown.bs.tab', function (e) {
        var target = $(e.target).attr("href");
        
        if (target === "#t-edit") {
            // 如果切换到编辑选项卡，加载Excel
            loadExcelFile();
        }
    });

    // 如果当前是编辑选项卡，立即加载Excel
    if ($('.nav-tabs li.active a').attr('href') === '#t-edit' || window.location.href.indexOf('edit=1') > -1) {
        
        // 切换到编辑选项卡
        $('.nav-tabs a[href="#t-edit"]').tab('show');
        // 延迟加载Excel以确保DOM已经更新
        setTimeout(function() {
            loadExcelFile();
        }, 500);
    }
}

// 手动加载LuckySheet脚本
function loadLuckySheetScript() {
    
    
    // 先加载LuckySheet CSS
    var cssLinks = [
        'https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/plugins/css/pluginsCss.css',
        'https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/plugins/plugins.css',
        'https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/css/luckysheet.css',
        'https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/assets/iconfont/iconfont.css'
    ];
    
    cssLinks.forEach(function(href) {
        if (!document.querySelector('link[href="' + href + '"]')) {
            var link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = href;
            document.head.appendChild(link);
            
        }
    });
    
    // 按顺序加载JS
    var scripts = [
        'https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/plugins/js/plugin.js',
        'https://cdn.jsdelivr.net/npm/luckysheet@2.1.13/dist/luckysheet.umd.js',
        'https://cdn.jsdelivr.net/npm/exceljs@4.3.0/dist/exceljs.min.js',
        'https://cdn.jsdelivr.net/npm/luckyexcel@1.0.1/dist/luckyexcel.umd.js'
    ];
    
    function loadScriptSequentially(index) {
        if (index >= scripts.length) {
            
            setTimeout(function() {
                setupTemplatePage();
            }, 500);
            return;
        }
        
        var src = scripts[index];
        if (!document.querySelector('script[src="' + src + '"]')) {
            var script = document.createElement('script');
            script.src = src;
            script.onload = function() {
                
                loadScriptSequentially(index + 1);
            };
            script.onerror = function() {
                console.error('脚本加载失败: ' + src);
                loadScriptSequentially(index + 1);
            };
            document.head.appendChild(script);
        } else {
            
            loadScriptSequentially(index + 1);
        }
    }
    
    loadScriptSequentially(0);
}

// 加载Excel文件到LuckySheet
function loadExcelFile() {
    
    
    // 检查LuckySheet是否已加载
    if (typeof window.luckysheet === 'undefined') {
        console.error('LuckySheet未加载，无法初始化编辑器');
        Layer.msg('编辑器未加载，请刷新页面重试', {icon: 2});
        // 尝试手动加载
        loadLuckySheetScript();
        return;
    }
    
    // 如果已经初始化，不需要重复加载
    if (luckysheetInstance && document.querySelector('.luckysheet-cell-main')) {
        
        return;
    }
    
    var templatePath = $('#placeholderList').data('path');
    if (!templatePath) {
        console.error('找不到模板路径');
        templatePath = document.querySelector('#placeholderList').getAttribute('data-path');
        if (!templatePath) {
            Layer.msg('无法获取模板路径，请刷新页面重试', {icon: 2});
            return;
        }
    }
    
    
    
    var loadingHtml = '<div class="text-center" style="padding: 50px;">' +
                     '<i class="fa fa-spinner fa-spin fa-3x"></i>' +
                     '<p class="mt-10">正在加载Excel文件，请稍候...</p>' +
                     '</div>';
    
    // 清理可能存在的LuckySheet实例和DOM
    cleanupLuckySheet();
    
    $('#luckysheet').html(loadingHtml);
    
    // 检查依赖项是否已加载
    if (!dependenciesLoaded) {
        // 加载依赖项并等待
        preloadDependencies();
        Layer.msg('正在加载依赖项，请稍候...', {icon: 16});
        
        // 每隔200ms检查一次依赖项是否加载完成
        var checkInterval = setInterval(function() {
            if (dependenciesLoaded) {
                clearInterval(checkInterval);
                // 依赖项加载完成后，开始加载Excel文件
                actuallyLoadExcel(templatePath);
            }
        }, 200);
        
        // 设置最大等待时间（10秒）
        setTimeout(function() {
            clearInterval(checkInterval);
            if (!dependenciesLoaded) {
                Layer.msg('依赖项加载超时，可能影响编辑器功能', {icon: 0});
                // 确保所有依赖项可用
                ensureDependencies();
                // 尝试继续加载
                actuallyLoadExcel(templatePath);
            }
        }, 10000);
    } else {
        // 依赖项已加载，确保依赖项可用
        ensureDependencies();
        // 直接加载Excel文件
        actuallyLoadExcel(templatePath);
    }
}

// 实际加载Excel文件的函数
function actuallyLoadExcel(templatePath) {
    
    
    // 首先确保依赖项可用
    ensureDependencies();
    
    // 确保LuckySheet容器存在
    if (!document.getElementById('luckysheet')) {
        console.error('找不到LuckySheet容器元素');
        Layer.msg('找不到编辑器容器，请刷新页面重试', {icon: 2});
        return;
    }
    
    // 尝试手动加载spectrum
    if (typeof jQuery.fn.spectrum === 'undefined') {
        
        
        // 添加CSS
        if (!document.getElementById('spectrum-css')) {
            var link = document.createElement('link');
            link.id = 'spectrum-css';
            link.rel = 'stylesheet';
            link.href = 'https://cdn.jsdelivr.net/npm/spectrum-colorpicker2@2.0.8/dist/spectrum.min.css';
            document.head.appendChild(link);
        }
        
        // 添加JS
        var script = document.createElement('script');
        script.src = 'https://cdn.jsdelivr.net/npm/spectrum-colorpicker2@2.0.8/dist/spectrum.min.js';
        script.onload = function() {
            
            continueLoading();
        };
        script.onerror = function() {
            console.error('spectrum加载失败，使用polyfill');
            ensureDependencies(); // 包含spectrum polyfill
            continueLoading();
        };
        document.head.appendChild(script);
    } else {
        continueLoading();
    }
    
    function continueLoading() {
        
        
        // 切换到编辑选项卡，确保容器可见
        $('.nav-tabs a[href="#t-edit"]').tab('show');
        
        // 确保容器已经在DOM中并可见
        if (!$('#luckysheet').is(':visible')) {
            
            setTimeout(function() {
                continueLoading();
            }, 500);
            return;
        }
        
        // 显示加载中提示
        var loadingMsg = Layer.msg('正在加载Excel文件...', {icon: 16, time: 0, shade: 0.3});
        
        // 直接使用下载链接获取Excel文件
        var downloadUrl = '{:url("template/downloadTemplate")}?path=' + encodeURIComponent(templatePath) + '&type=excel';
        
        
        // 使用fetch API获取Excel文件的ArrayBuffer
        fetch(downloadUrl)
            .then(function(response) {
                if (!response.ok) {
                    throw new Error('网络响应错误: ' + response.status);
                }
                
                return response.arrayBuffer();
            })
            .then(function(arrayBuffer) {
                
                
                // 检查LuckyExcel是否可用
                if (typeof LuckyExcel === 'undefined') {
                    console.error('LuckyExcel未加载，尝试手动加载');
                    Layer.close(loadingMsg);
                    loadLuckySheetScript();
                    return;
                }
                
                // 解析Excel文件
                
                LuckyExcel.transformExcelToLucky(
                    arrayBuffer,
                    function(exportJson, luckysheetfile) {
                        
                        Layer.close(loadingMsg);
                        
                        if (exportJson.sheets == null || exportJson.sheets.length === 0) {
                            Layer.msg('Excel文件解析失败，未找到任何工作表', {icon: 2});
                            return;
                        }
                        
                        // 保存Excel数据
                        excelData = exportJson;
                        
                        
                        // 再次确保依赖项可用
                        ensureDependencies();
                        
                        // 初始化LuckySheet
                        
                        initLuckySheet(exportJson);
                        
                        Layer.msg('Excel文件加载成功，可以开始编辑', {icon: 1});
                    },
                    function(err) {
                        console.error('解析Excel文件失败', err);
                        Layer.close(loadingMsg);
                        // 如果解析方法失败，尝试base64方式
                        Layer.msg('解析Excel文件失败，尝试备用方法', {icon: 0});
                        setTimeout(function() {
                            loadExcelByBase64(templatePath);
                        }, 1000);
                    }
                );
            })
            .catch(function(error) {
                console.error('获取Excel文件失败', error);
                Layer.close(loadingMsg);
                // 如果fetch API失败，尝试base64方式
                Layer.msg('获取Excel文件失败，尝试备用方法', {icon: 0});
                setTimeout(function() {
                    loadExcelByBase64(templatePath);
                }, 1000);
            });
    }
}

// 通过base64方式加载Excel
function loadExcelByBase64(templatePath) {
    
    
    // 确保所有依赖项可用
    ensureDependencies();
    
    // 显示加载中提示
    var loadingMsg = Layer.msg('正在通过备用方式加载Excel...', {icon: 16, time: 0, shade: 0.3});
    
    $.ajax({
        url: '{:url("template/getExcelContent")}',
        type: 'GET',
        data: {path: templatePath},
        dataType: 'json',
        success: function(res) {
            
            
            if (res.code === 1 && res.data.base64) {
                try {
                    
                    
                    // 获取base64数据，需要删除可能的mime-type前缀
                    var base64Data = res.data.base64;
                    if (base64Data.indexOf(',') > -1) {
                        base64Data = base64Data.split(',')[1];
                    }
                    
                    
                    
                    // 通过二进制方式处理
                    var binaryString = window.atob(base64Data);
                    var bytes = new Uint8Array(binaryString.length);
                    for (var i = 0; i < binaryString.length; i++) {
                        bytes[i] = binaryString.charCodeAt(i);
                    }
                    
                    
                    
                    // 再次确保所有依赖项可用
                    ensureDependencies();
                    
                    // 检查LuckyExcel是否可用
                    if (typeof LuckyExcel === 'undefined') {
                        console.error('LuckyExcel未加载，尝试再次加载');
                        Layer.close(loadingMsg);
                        loadLuckySheetScript();
                        setTimeout(function() {
                            loadExcelByBase64(templatePath);
                        }, 2000);
                        return;
                    }
                    
                    // 解析Excel文件
                    
                    LuckyExcel.transformExcelToLucky(
                        bytes.buffer, 
                        function(exportJson, luckysheetfile) {
                            
                            Layer.close(loadingMsg);
                            
                            if (exportJson.sheets == null || exportJson.sheets.length === 0) {
                                Layer.msg('Excel文件解析失败，未找到任何工作表', {icon: 2});
                                return;
                            }
                            
                            // 保存Excel数据
                            excelData = exportJson;
                            
                            
                            // 第三次确保所有依赖项可用（关键时刻）
                            ensureDependencies();
                            
                            // 初始化LuckySheet
                            
                            initLuckySheet(exportJson);
                            
                            Layer.msg('Excel文件加载成功，可以开始编辑', {icon: 1});
                        },
                        function(err) {
                            console.error('解析Excel文件失败，尝试备用方法', err);
                            Layer.close(loadingMsg);
                            Layer.msg('无法解析Excel文件，请尝试重新上传或刷新页面', {icon: 2});
                            
                            // 显示错误信息和重试按钮
                            $('#luckysheet').html(
                                '<div class="text-center" style="padding: 50px;">' +
                                '<i class="fa fa-exclamation-circle fa-3x text-danger"></i>' +
                                '<p class="mt-10 text-danger">Excel文件解析失败</p>' +
                                '<p class="text-muted">错误信息: ' + err.message + '</p>' +
                                '<button class="btn btn-primary mt-10" id="btnRetryLoad">重试加载</button> ' +
                                '<a href="javascript:location.reload();" class="btn btn-default mt-10">刷新页面</a>' +
                                '</div>'
                            );
                            
                            // 绑定重试按钮事件
                            $('#btnRetryLoad').on('click', function() {
                                loadExcelFile();
                            });
                        }
                    );
                } catch (e) {
                    console.error('解析Excel文件时发生错误', e);
                    Layer.close(loadingMsg);
                    Layer.msg('Excel文件解析错误: ' + e.message, {icon: 2});
                    
                    // 显示错误信息和重试按钮
                    $('#luckysheet').html(
                        '<div class="text-center" style="padding: 50px;">' +
                        '<i class="fa fa-exclamation-circle fa-3x text-danger"></i>' +
                        '<p class="mt-10 text-danger">Excel文件解析失败</p>' +
                        '<p class="text-muted">错误信息: ' + e.message + '</p>' +
                        '<button class="btn btn-primary mt-10" id="btnRetryLoad">重试加载</button> ' +
                        '<a href="javascript:location.reload();" class="btn btn-default mt-10">刷新页面</a>' +
                        '</div>'
                    );
                    
                    // 绑定重试按钮事件
                    $('#btnRetryLoad').on('click', function() {
                        loadExcelFile();
                    });
                }
            } else {
                console.error('获取Excel内容失败:', res.msg);
                Layer.close(loadingMsg);
                Layer.msg(res.msg || 'Excel文件加载失败', {icon: 2});
                
                // 显示错误信息和重试按钮
                $('#luckysheet').html(
                    '<div class="text-center" style="padding: 50px;">' +
                    '<i class="fa fa-exclamation-circle fa-3x text-danger"></i>' +
                    '<p class="mt-10 text-danger">Excel文件加载失败</p>' +
                    '<p class="text-muted">' + (res.msg || '服务器返回错误') + '</p>' +
                    '<button class="btn btn-primary mt-10" id="btnRetryLoad">重试加载</button> ' +
                    '<a href="javascript:location.reload();" class="btn btn-default mt-10">刷新页面</a>' +
                    '</div>'
                );
                
                // 绑定重试按钮事件
                $('#btnRetryLoad').on('click', function() {
                    loadExcelFile();
                });
            }
        },
        error: function(xhr, status, error) {
            console.error('AJAX请求失败:', status, error);
            Layer.close(loadingMsg);
            Layer.msg('网络错误，无法加载Excel文件: ' + error, {icon: 2});
            
            // 显示错误信息和重试按钮
            $('#luckysheet').html(
                '<div class="text-center" style="padding: 50px;">' +
                '<i class="fa fa-exclamation-circle fa-3x text-danger"></i>' +
                '<p class="mt-10 text-danger">网络错误</p>' +
                '<p class="text-muted">无法从服务器获取Excel文件: ' + error + '</p>' +
                '<button class="btn btn-primary mt-10" id="btnRetryLoad">重试加载</button> ' +
                '<a href="javascript:location.reload();" class="btn btn-default mt-10">刷新页面</a>' +
                '</div>'
            );
            
            // 绑定重试按钮事件
            $('#btnRetryLoad').on('click', function() {
                loadExcelFile();
            });
        }
    });
}

// 初始化LuckySheet
function initLuckySheet(data) {
    
    
    // 检查必要的依赖项
    if (typeof jQuery === 'undefined') {
        console.error('jQuery is not loaded');
        Layer.msg('初始化失败：jQuery未加载', {icon: 2});
        return null;
    }
    
    // 确保所有依赖项已经可用（通过ensureDependencies函数）
    ensureDependencies();
    
    // 确保luckysheet容器可见并准备好
    var $container = $('#luckysheet');
    if ($container.length === 0) {
        console.error('找不到LuckySheet容器');
        Layer.msg('初始化失败：找不到编辑器容器', {icon: 2});
        return null;
    }
    
    // 设置容器的高度和宽度
    $container.css({
        'width': '100%',
        'height': 'calc(100vh - 200px)',
        'min-height': '600px',
        'position': 'relative'
    });
    
    // 确保容器是空的
    $container.empty();
    
    // 创建一个唯一ID以防止冲突
    var containerId = 'luckysheet_' + Date.now();
    $container.attr('id', containerId);
    
    
    
    // 清理可能存在的旧实例和DOM元素
    cleanupLuckySheet();
    
    // 设置默认选中的工作表
    var defaultSheetIndex = 0;
    if (data && data.sheets && data.sheets.length > 0) {
        for (var i = 0; i < data.sheets.length; i++) {
            if (data.sheets[i].status === 1) {
                defaultSheetIndex = i;
                break;
            }
        }
    } else {
        // 如果数据为空或格式不正确
        console.error('Excel数据格式不正确', data);
        Layer.msg('Excel数据格式不正确', {icon: 2});
        return null;
    }
    
    // 配置项
    var options = {
        container: containerId,
        data: data.sheets,
        title: data.info ? data.info.name : '在线编辑Excel',
        lang: 'zh',
        showinfobar: false,
        allowEdit: true,
        showtoolbar: false,
        showsheetbar: false, // 禁用sheetbar，避免重复显示问题
        showstatisticBar: true,
        enableAddRow: true,
        enableAddCol: true,
        showstatisticBarConfig: {
            count: true,
            view: true,
            zoom: true
        },
        cellRightClickConfig: {
            copy: true,
            paste: true,
            insertRow: false,
            insertColumn: false,
            deleteRow: false,
            deleteColumn: false,
            deleteCell: false,
            hideRow: false,
            hideColumn: false,
            rowHeight: false,
            columnWidth: false,
            clear: false,
            matrix: false,
            sort: false,
            filter: false,
            chart: false,
            image: false,
            link: false,
            data: false,
            cellFormat: false,
            copyAs: false
        },
        hook: {
            workbookCreateAfter: function() {
                // 创建完成后的回调
                dependenciesLoaded = true;
                
                
                // 创建自定义工作表切换界面
                createCustomSheetTabs(data.sheets);
            }
        },
        defaultSheetIndex: defaultSheetIndex
    };
    
    try {
        // 确保页面上没有多余的LuckySheet UI元素
        $('.luckysheet-cell-main').remove();
        $('.luckysheet-grid-window').remove();
        
        // 确保用于初始化的容器已在DOM中并可见
        if (!$('#' + containerId).is(':visible')) {
            console.warn('LuckySheet容器不可见，尝试让它可见');
            $('#' + containerId).css('display', 'block');
            // 如果在t-edit选项卡中，确保选项卡是活跃的
            $('.nav-tabs a[href="#t-edit"]').tab('show');
            // 等待50ms让DOM更新
            setTimeout(function() {
                if ($('#' + containerId).is(':visible')) {
                    
                    doInitLuckySheet();
                } else {
                    console.error('容器仍然不可见，无法初始化');
                    Layer.msg('编辑器容器不可见，无法初始化', {icon: 2});
                }
            }, 50);
        } else {
            // 容器可见，直接初始化
            doInitLuckySheet();
        }
        
        function doInitLuckySheet() {
            // 初始化LuckySheet
            luckysheetInstance = window.luckysheet.create(options);
            
            return luckysheetInstance;
        }
    } catch (e) {
        console.error('初始化LuckySheet失败:', e);
        // 显示更详细的错误信息
        Layer.msg('初始化编辑器失败: ' + e.message + ' (请检查浏览器控制台获取详细信息)', {icon: 2});
        return null;
    }
}

// 清理LuckySheet的DOM和实例
function cleanupLuckySheet() {
    
    
    // 销毁实例
    if (window.luckysheet) {
        try {
            
            window.luckysheet.destroy();
        } catch (e) {
            console.warn('销毁旧实例失败', e);
        }
    }
    
    // 移除自定义工作表切换界面
    $('#custom-sheet-tabs').remove();
    
    // 移除所有LuckySheet相关的DOM元素
    $('.luckysheet-cell-main').remove();
    $('.luckysheet-grid-window').remove();
    $('.luckysheet-scrollbar-y').remove();
    $('.luckysheet-scrollbar-x').remove();
    $('.luckysheet-sheets-add').remove();
    $('.luckysheet-sheets-list').remove();
    $('.luckysheet-stat-area').remove();
    $('.luckysheet-print-viewList').remove();
    $('.luckysheet-print-viewBtn').remove();
    $('.luckysheet-cols-h').remove();
    $('.luckysheet-rows-h').remove();
    $('.luckysheet-cols-menu').remove();
    $('.luckysheet-rows-menu').remove();
    $('.luckysheet-load-mask').remove();
    $('.luckysheet-filter-menu').remove();
    $('.luckysheet-filter-submenu').remove();
    $('.luckysheet-cols-menu-btn').remove();
    $('.luckysheet-rows-menu-btn').remove();
    
    // 移除所有以luckysheet开头的DOM元素
    $('[class^="luckysheet"]').remove();
    
    // 重置全局变量
    luckysheetInstance = null;
    
    
}

// 确保页面加载完成后立即执行初始化
$(document).ready(function() {
    // 防止重复初始化
    if (window.templatePageInitialized) {
        console.log('模板页面已经初始化，跳过重复初始化');
        return;
    }
    
    window.templatePageInitialized = true;
    console.log('初始化模板页面');
    initTemplatePage();
    
    // 添加额外的确保脚本加载的步骤
    setTimeout(function() {
        if (typeof window.luckysheet === 'undefined') {
            console.warn('LuckySheet未在document.ready后加载，尝试手动加载');
            loadLuckySheetScript();
        }
    }, 1000);
    
    // 检查URL中是否有编辑参数
    if (window.location.href.indexOf('edit=1') > -1) {
        
        // 如果有编辑参数，切换到编辑选项卡
        setTimeout(function() {
            if (typeof jQuery !== 'undefined') {
                jQuery('.nav-tabs a[href="#t-edit"]').tab('show');
                // 延迟加载Excel文件
                setTimeout(function() {
                    
                    if (typeof loadExcelFile === 'function') {
                        loadExcelFile();
                    } else {
                        console.error('loadExcelFile函数未定义');
                    }
                }, 800);
            }
        }, 500);
    }
});

// 页面完全加载后再次尝试
window.addEventListener("load", function() {
    // 防止重复初始化
    if (!window.templatePageInitialized) {
        console.log('页面完全加载后初始化模板页面');
        window.templatePageInitialized = true;
        initTemplatePage();
    }
    
    // 加载完成后再次检查是否需要显示编辑选项卡
    if (window.location.href.indexOf('edit=1') > -1) {
        setTimeout(function() {
            if (typeof jQuery !== 'undefined') {
                jQuery('.nav-tabs a[href="#t-edit"]').tab('show');
                // 确保Excel加载
                if (typeof loadExcelFile === 'function') {
                    loadExcelFile();
                }
            }
        }, 1000);
    }
});

// 保存前确保编辑状态结束
function ensureEditModeExited() {
    // 尝试多种方法结束编辑状态
    try {
        // 方法1: 使用luckysheet API
        if (typeof luckysheet !== 'undefined' && typeof luckysheet.exitEditMode === 'function') {
            luckysheet.exitEditMode();
        }
        
        // 方法2: 模拟按下Enter键
        var enterEvent = new KeyboardEvent('keydown', {
            bubbles: true,
            cancelable: true,
            key: 'Enter',
            keyCode: 13
        });
        document.dispatchEvent(enterEvent);
        
        // 方法3: 直接操作DOM，查找编辑框并触发blur事件
        var editBox = document.querySelector('.luckysheet-input-box');
        if (editBox) {
            var blurEvent = new Event('blur', { bubbles: true });
            editBox.dispatchEvent(blurEvent);
        }
        
        // 方法4: 点击一个空白区域
        var cellMain = document.querySelector('.luckysheet-cell-main');
        if (cellMain) {
            var clickEvent = new MouseEvent('mousedown', {
                bubbles: true,
                cancelable: true,
                view: window
            });
            cellMain.dispatchEvent(clickEvent);
        }
        
        return true;
    } catch (e) {
        console.warn('结束编辑状态失败:', e);
        return false;
    }
}

// 保存Excel文件
function saveExcelFile() {
    if (!window.luckysheet) {
        Layer.msg('编辑器未初始化，无法保存', {icon: 2});
        return;
    }
    
    // 先尝试结束编辑状态
    ensureEditModeExited();
    
    // 给一点时间让LuckySheet处理编辑完成的操作
    setTimeout(function() {
        showSaveConfirmDialog();
    }, 100);
}

// 显示保存确认对话框
function showSaveConfirmDialog() {
    // 使用自定义确认对话框
    var confirmHtml = 
        '<div class="layer-confirm">' +
        '<div class="layer-confirm-msg"><i class="fa fa-question-circle"></i> 保存将仅更新修改的单元格内容，保持原模板格式不变。确定要保存吗？</div>' +
        '<div class="layer-confirm-btns">' +
        '<button class="btn btn-primary btn-confirm">确定保存</button> ' +
        '<button class="btn btn-default btn-cancel">取消</button>' +
        '</div>' +
        '</div>';
    
    var confirmIndex = Layer.msg(confirmHtml, {
        time: 0,
        shade: 0.3,
        skin: 'layer-custom-confirm'
    });
    
    // 添加确认和取消按钮事件
    $('.layer-confirm .btn-confirm').on('click', function() {
        Layer.close(confirmIndex);
        doSaveExcelFile();
    });
    
    $('.layer-confirm .btn-cancel').on('click', function() {
        Layer.close(confirmIndex);
    });
}

// 实际执行保存操作的函数
function doSaveExcelFile() {
    try {
        // 获取模板路径
        var templatePath = $('#placeholderList').data('path');
        
        // 显示加载中提示
        var loadingIndex = Layer.msg('正在保存Excel文件...', {
            icon: 16,
            time: 0,
            shade: 0.3
        });
        
        // 获取当前LuckySheet的所有数据
        var allSheets;
        try {
            allSheets = luckysheet.getAllSheets();
        } catch (e) {
            console.warn('获取工作表数据失败，尝试备用方法:', e);
            // 备用方法：直接从全局变量获取
            if (window.luckysheetfile && Array.isArray(window.luckysheetfile)) {
                allSheets = window.luckysheetfile;
            } else {
                throw new Error('无法获取工作表数据');
            }
        }
        
        if (!allSheets || !Array.isArray(allSheets) || allSheets.length === 0) {
            Layer.close(loadingIndex);
            Layer.msg('无法获取工作表数据，保存失败', {icon: 2});
            return;
        }
        
        // 确保清空单元格和换行内容的操作被正确记录
        if (allSheets && allSheets.length > 0) {
            allSheets.forEach(function(sheet) {
                // 确保celldata存在
                if (!sheet.celldata) {
                    sheet.celldata = [];
                }
                
                // 获取当前工作表的数据网格
                var grid = sheet.data || [];
                
                // 创建一个映射，记录celldata中已存在的单元格位置
                var cellMap = {};
                if (Array.isArray(sheet.celldata)) {
                    for (var i = 0; i < sheet.celldata.length; i++) {
                        var cell = sheet.celldata[i];
                        if (cell && typeof cell.r === 'number' && typeof cell.c === 'number') {
                            var key = cell.r + '_' + cell.c;
                            cellMap[key] = i;
                        }
                    }
                    
                    // 处理包含换行符的单元格
                    for (var i = 0; i < sheet.celldata.length; i++) {
                        var cell = sheet.celldata[i];
                        if (cell && cell.v && typeof cell.v.v === 'string') {
                            var value = cell.v.v;
                            // 检查是否包含换行符
                            if (value.indexOf('\n') !== -1 || value.indexOf('\r') !== -1) {
                                console.log('检测到换行符单元格:', cell.r, cell.c, value);
                                
                                // 确保单元格有正确的格式设置
                                if (!cell.v.ct) {
                                    cell.v.ct = { t: 's', fa: '@' };
                                }
                                
                                // 设置自动换行属性
                                if (!cell.v.tb) {
                                    cell.v.tb = 1; // 1表示自动换行
                                }
                                
                                // 标记为包含换行符的单元格，避免被清空处理
                                cell.hasNewlines = true;
                                
                                // 将此单元格添加到映射表
                                var key = cell.r + '_' + cell.c;
                                cellMap[key] = i;
                            }
                            
                            // 检查是否只有空格和换行符
                            var trimmedValue = value.replace(/[\r\n\s]/g, '');
                            if (trimmedValue === '') {
                                // 如果只包含空格和换行符，标记为可清空
                                cell.onlyWhitespaceAndNewlines = true;
                            } else if (value.indexOf('\n') !== -1 || value.indexOf('\r') !== -1) {
                                // 如果包含换行符和实际内容，标记为不可清空
                                cell.hasContentWithNewlines = true;
                            }
                        }
                    }
                }
                
                // 遍历数据网格，查找被清空的单元格
                if (grid && grid.length > 0) {
                    for (var r = 0; r < grid.length; r++) {
                        var row = grid[r];
                        if (row && row.length > 0) {
                            for (var c = 0; c < row.length; c++) {
                                var cellValue = row[c];
                                var key = r + '_' + c;
                                
                                // 检查单元格是否为空或已被清空
                                var isEmpty = cellValue === null || cellValue === undefined || 
                                             (cellValue && cellValue.v === null) || 
                                             (cellValue && cellValue.v === '');
                                
                                // 特别处理：确保带换行符的内容不被误认为空
                                if (cellValue && cellValue.v && typeof cellValue.v === 'string') {
                                    var value = cellValue.v;
                                    
                                    // 如果单元格内容只包含换行符或空格，则认为是空
                                    var trimmedValue = value.replace(/[\r\n\s]/g, '');
                                    if (trimmedValue === '') {
                                        // 只包含空格和换行符，可以认为是空
                                        isEmpty = true;
                                        console.log('单元格只包含空格和换行符:', r, c, value);
                                    } else if (value.indexOf('\n') !== -1 || value.indexOf('\r') !== -1) {
                                        // 包含换行符和实际内容，不能认为是空
                                        isEmpty = false;
                                        console.log('单元格包含换行符和实际内容:', r, c, value);
                                    }
                                }
                                
                                // 检查单元格是否已被标记为包含换行符和实际内容
                                var key = r + '_' + c;
                                if (key in cellMap) {
                                    var index = cellMap[key];
                                    var cell = sheet.celldata[index];
                                    
                                    if (cell.hasContentWithNewlines) {
                                        // 如果单元格包含换行符和实际内容，不认为是空
                                        isEmpty = false;
                                        console.log('单元格被标记为包含换行符和实际内容:', r, c);
                                    } else if (cell.onlyWhitespaceAndNewlines) {
                                        // 如果单元格只包含空格和换行符，可以认为是空
                                        isEmpty = true;
                                        console.log('单元格被标记为只包含空格和换行符:', r, c);
                                    }
                                }
                                
                                if (isEmpty) {
                                    // 如果单元格在celldata中已存在，更新它
                                    if (key in cellMap) {
                                        var index = cellMap[key];
                                        sheet.celldata[index].v = {v: null, m: '', ct: {t: 'g', fa: 'General'}};
                                    } 
                                    // 如果单元格不在celldata中，添加一条记录
                                    else {
                                        sheet.celldata.push({
                                            r: r,
                                            c: c,
                                            v: {v: null, m: '', ct: {t: 'g', fa: 'General'}}
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
                
                // 特别处理：查找可能在LuckySheet UI中被清空但未反映在数据中的单元格
                // 这种情况通常发生在用户直接删除单元格内容后
                try {
                    if (typeof luckysheet !== 'undefined' && typeof luckysheet.getConfig === 'function') {
                        var config = luckysheet.getConfig();
                        if (config && config.curSheetIndex === sheet.index) {
                            // 获取当前选择的区域
                            var selections = luckysheet.getSelection();
                            if (selections && selections.length > 0) {
                                for (var s = 0; s < selections.length; s++) {
                                    var selection = selections[s];
                                    // 如果有选择区域，并且最近执行了删除操作
                                    if (selection && selection.row && selection.column) {
                                        for (var r = selection.row[0]; r <= selection.row[1]; r++) {
                                            for (var c = selection.column[0]; c <= selection.column[1]; c++) {
                                                var key = r + '_' + c;
                                                // 如果单元格在celldata中已存在，确保它被标记为null
                                                if (key in cellMap) {
                                                    var index = cellMap[key];
                                                    // 只有当我们确定单元格被清空时才设置为null
                                                    if (!sheet.celldata[index].v || sheet.celldata[index].v.v === '') {
                                                        sheet.celldata[index].v = {v: null, m: '', ct: {t: 'g', fa: 'General'}};
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (e) {
                    console.warn('处理选择区域失败:', e);
                }
            });
        }
        
        // 更新到原始数据结构
        if (excelData) {
            excelData.sheets = allSheets;
        } else {
            excelData = {
                info: {
                    name: '在线编辑Excel',
                    author: 'LuckySheet',
                    date: new Date().toISOString()
                },
                sheets: allSheets
            };
        }
        
        // 转换为JSON字符串
        var jsonData = JSON.stringify(excelData);
        
        // 发送保存请求
        $.ajax({
            url: '{:url("template/saveExcelContent")}',
            type: 'POST',
            data: {
                path: templatePath,
                content: jsonData
            },
            dataType: 'json',
            success: function(res) {
                Layer.close(loadingIndex);
                
                if (res.code === 1) {
                    // 显示更新单元格数量信息
                    var updatedCells = res.data && res.data.updated_cells ? res.data.updated_cells : 0;
                    var successMsg = 'Excel文件保存成功';
                    
                    if (updatedCells > 0) {
                        successMsg += '，共更新了 ' + updatedCells + ' 个单元格';
                    } else {
                        successMsg += '，未检测到内容变更';
                    }
                    
                    Layer.msg(successMsg, {icon: 1});
                    
                    // 刷新占位符列表
                    setTimeout(function() {
                        loadPlaceholders();
                    }, 1000);
                } else {
                    Layer.msg(res.msg || 'Excel文件保存失败', {icon: 2});
                }
            },
            error: function() {
                Layer.close(loadingIndex);
                Layer.msg('网络错误，无法保存Excel文件', {icon: 2});
            }
        });
    } catch (e) {
        Layer.msg('保存过程中发生错误: ' + e.message, {icon: 2});
        console.error('保存Excel文件失败', e);
    }
}

    // 加载占位符列表
    function loadPlaceholders() {
        var placeholderContainer = document.getElementById('placeholderList');
        var templatePath = placeholderContainer.dataset.path;
    
        $.ajax({
            url: '{:url("template/getExcelPlaceholders")}',
            type: 'GET',
            data: {path: templatePath},
            dataType: 'json',
            success: function(res) {
                if (res.code === 1) {
                    // 保存所有占位符到全局变量
                    allPlaceholders = res.data || [];
                    
                    // 渲染占位符列表
                    if (allPlaceholders.length > 0) {
                        renderPlaceholders(allPlaceholders);
                    } else {
                        placeholderContainer.innerHTML = 
                            '<div class="alert alert-info">' +
                            '<i class="fa fa-info-circle"></i> ' +
                            '当前模板中未找到任何占位符。您可以在Excel中添加格式为{{字段名}}的占位符，然后保存模板。' +
                            '</div>';
                    }
                } else {
                    placeholderContainer.innerHTML = 
                        '<div class="alert alert-warning">' +
                        '<i class="fa fa-exclamation-circle"></i> ' + 
                        (res.msg || '获取占位符列表失败') +
                        '</div>';
                }
            },
            error: function() {
                placeholderContainer.innerHTML = 
                    '<div class="alert alert-danger">' +
                    '<i class="fa fa-times-circle"></i> 加载占位符列表失败' +
                    '<button class="btn btn-xs btn-danger ml-10" id="reload-placeholders">' +
                    '<i class="fa fa-refresh"></i> 重试</button>' +
                    '</div>';
                
                // 添加重试按钮事件
                $('#reload-placeholders').on('click', function() {
                    loadPlaceholders();
                });
            }
        });
    }

    // 渲染占位符列表
    function renderPlaceholders(placeholders, searchTerm) {
        var placeholderContainer = document.getElementById('placeholderList');
    
        // 按类别分组占位符
        var categories = {
            '基本信息': [],
            '联系人信息': [],
            '教育经历': [],
            '工作经历': [],
            '图片': [],
            '其他': []
        };
    
        placeholders.forEach(function(placeholder) {
            // 使用服务器返回的category属性来分类
            var category = placeholder.category || '其他';
        
            // 确保分类存在
            if (!categories[category]) {
                categories[category] = [];
            }
        
            // 添加到对应分类
            categories[category].push(placeholder);
        });
    
        var html = '';
        var totalCount = 0;
    
        // 按照指定顺序显示分类
        var categoryOrder = ['基本信息', '联系人信息', '教育经历', '工作经历', '图片', '其他'];
    
        // 遍历分类生成HTML
        categoryOrder.forEach(function(category) {
            var categoryPlaceholders = categories[category] || [];
        
            if (categoryPlaceholders.length > 0) {
                // 对每个类别内的占位符按名称排序
                categoryPlaceholders.sort(function(a, b) {
                    return a.name.localeCompare(b.name);
                });
            
                html += '<h4 class="placeholder-category">' + category + 
                    ' <span class="placeholder-count">(' + categoryPlaceholders.length + '个)</span></h4>';
                html += '<div class="placeholder-row">';
            
                categoryPlaceholders.forEach(function(placeholder) {
                    // 高亮搜索词
                    var displayName = placeholder.name;
                    var displayDesc = placeholder.description;
                
                    if (searchTerm) {
                        var regex = new RegExp('(' + escapeRegExp(searchTerm) + ')', 'gi');
                        displayName = displayName.replace(regex, '<span class="highlight">$1</span>');
                        displayDesc = displayDesc.replace(regex, '<span class="highlight">$1</span>');
                    }
                
                    html += 
                        '<div class="placeholder-item">' +
                        '<div class="placeholder-info">' +
                        '<div class="placeholder-name">' + displayName + '</div>' +
                        '<div class="placeholder-desc">' + displayDesc + '</div>' +
                        '</div>' +
                        '<button class="btn btn-xs btn-default placeholder-copy" data-clipboard-text="' + placeholder.name + '">' +
                        '<i class="fa fa-copy"></i>' +
                        '</button>' +
                        '</div>';
                
                    totalCount++;
                });
            
                html += '</div>';
            }
        });
    
        if (totalCount === 0) {
            html = 
                '<div class="no-match">' +
                '<i class="fa fa-search"></i>' +
                '<p>没有找到匹配的占位符</p>' +
                '</div>';
        }
    
        placeholderContainer.innerHTML = html;
        
        // 初始化复制按钮
        initClipboard();
    }

    // 初始化复制功能
    function initClipboard() {
        // 使用简单的方法实现复制功能
        $(document).on('click', '.placeholder-copy', function() {
            var text = $(this).data('clipboard-text');
            copyToClipboard(text);
        // 直接使用全局Layer对象
                Layer.msg('已复制: ' + text, {icon: 1});
        });
    }
    
    // 复制文本到剪贴板
    function copyToClipboard(text) {
        // 创建临时输入框
        var input = document.createElement('input');
        input.value = text;
        document.body.appendChild(input);
        input.select();
        document.execCommand('copy');
        document.body.removeChild(input);
    }

    // 过滤占位符
    function filterPlaceholders(searchTerm) {
        if (!searchTerm) {
            // 如果搜索词为空，显示所有占位符
            renderPlaceholders(allPlaceholders);
            return;
        }
    
        // 过滤包含搜索词的占位符
        var filtered = allPlaceholders.filter(function(placeholder) {
            return placeholder.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                placeholder.description.toLowerCase().includes(searchTerm.toLowerCase());
        });
    
        // 渲染过滤后的占位符
        renderPlaceholders(filtered, searchTerm);
    }

    // 清除搜索
    function clearSearch() {
        $('#placeholderSearchInput').val('');
        renderPlaceholders(allPlaceholders);
    }

    // 转义正则表达式特殊字符
    function escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

// 确保依赖项可用的函数
function ensureDependencies() {
    // 确保mousewheel可用
    if (typeof jQuery.fn.mousewheel === 'undefined') {
        
        
        // 定义mousewheel方法
        jQuery.fn.mousewheel = function(callback) {
            return this.each(function() {
                var el = this;
                
                // 移除可能存在的事件处理程序，避免重复
                $(el).off('wheel mousewheel DOMMouseScroll');
                
                // 标准鼠标滚轮事件
                $(el).on('wheel', function(e) {
                    var delta = e.originalEvent.deltaY || e.originalEvent.detail || e.originalEvent.wheelDelta;
                    callback.call(el, e, delta < 0 ? 1 : -1);
                });
                
                // 旧版浏览器支持
                $(el).on('mousewheel DOMMouseScroll', function(e) {
                    var delta = e.originalEvent.wheelDelta || -e.originalEvent.detail * 40;
                    callback.call(el, e, delta > 0 ? 1 : -1);
                });
            });
        };
        
        // 添加unmousewheel方法
        jQuery.fn.unmousewheel = function(fn) {
            return this.unbind('wheel mousewheel DOMMouseScroll', fn);
        };
    }
    
    // 确保spectrum可用
    if (typeof jQuery.fn.spectrum === 'undefined') {
        
        
        // 提供简单的spectrum polyfill
        jQuery.fn.spectrum = function(options) {
            var opts = options || {};
            
            return this.each(function() {
                var $this = $(this);
                
                // 创建一个简单的颜色选择器
                if ($this.is('input')) {
                    // 保存初始值
                    var initialColor = $this.val() || '#000000';
                    
                    // 尝试转换为颜色输入框
                    try {
                        $this.attr('type', 'color');
                    } catch(e) {
                        console.warn('无法设置input类型为color');
                    }
                    
                    $this.val(initialColor);
                    
                    // 处理change事件
                    $this.on('change', function() {
                        if (typeof opts.change === 'function') {
                            opts.change.call($this, $this.val());
                        }
                    });
                    
                    // 模拟Spectrum API
                    $this.data('spectrum', {
                        set: function(color) {
                            $this.val(color);
                            return this;
                        },
                        get: function() {
                            return $this.val();
                        }
                    });
                }
            });
        };
    }
}

// 创建自定义工作表切换界面
function createCustomSheetTabs(sheets) {
    
    if (!sheets || !sheets.length) {
        console.warn('没有工作表数据，无法创建工作表切换界面');
        return;
    }
    
    // 移除可能已存在的自定义工作表切换界面
    $('#custom-sheet-tabs').remove();
    
    // 创建自定义工作表切换界面容器
    var $tabsContainer = $('<div id="custom-sheet-tabs" class="custom-sheet-tabs"></div>');
    
    // 添加样式
    if (!$('#custom-sheet-tabs-style').length) {
        var style = `
        <style id="custom-sheet-tabs-style">
            .custom-sheet-tabs {
                display: flex;
                background: #f5f5f5;
                border-top: 1px solid #e0e0e0;
                padding: 5px 10px;
                overflow-x: auto;
                white-space: nowrap;
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                z-index: 1000;
                box-shadow: 0 -2px 5px rgba(0,0,0,0.1);
            }
            .custom-sheet-tab {
                padding: 5px 15px;
                margin-right: 5px;
                border: 1px solid #ccc;
                border-radius: 3px;
                cursor: pointer;
                background: #fff;
                transition: all 0.2s;
                user-select: none;
            }
            .custom-sheet-tab:hover {
                background: #e9e9e9;
            }
            .custom-sheet-tab.active {
                background: #217346;
                color: white;
                border-color: #217346;
            }
            #luckysheet, [id^="luckysheet_"] {
                margin-bottom: 40px !important;
            }
        </style>
        `;
        $('head').append(style);
    }
    
    // 为每个工作表创建一个标签
    for (var i = 0; i < sheets.length; i++) {
        var sheet = sheets[i];
        var isActive = sheet.status === 1;
        var $tab = $('<div class="custom-sheet-tab' + (isActive ? ' active' : '') + '" data-index="' + i + '">' + sheet.name + '</div>');
        
        // 点击标签切换工作表
        $tab.on('click', function() {
            var index = parseInt($(this).data('index'));
            if (window.luckysheet) {
                try {
                    
                    window.luckysheet.setSheetActive(index);
                    // 更新标签状态
                    $('.custom-sheet-tab').removeClass('active');
                    $(this).addClass('active');
                } catch (e) {
                    console.error('切换工作表失败:', e);
                }
            }
        });
        
        $tabsContainer.append($tab);
    }
    
    // 将自定义工作表切换界面添加到页面
    $('#t-edit').append($tabsContainer);
    
}

// 加载Excel文件的最后备用方案
function loadExcelFallback(templatePath) {
    
    
    // 显示加载中提示
    var loadingMsg = Layer.msg('正在加载备用编辑器...', {icon: 16, time: 0, shade: 0.3});
    
    // 准备简单的表格编辑器
    $('#luckysheet').html(
        '<div class="text-center" style="padding: 20px 0;">' +
        '<div class="alert alert-warning">' +
        '<i class="fa fa-exclamation-triangle"></i> 高级Excel编辑器加载失败，已切换到基本编辑模式' +
        '</div>' +
        '</div>' +
        '<div id="simple-excel-editor" style="width:100%; overflow:auto;"></div>'
    );
    
    // 加载Excel文件原始内容
    $.ajax({
        url: '{:url("template/getExcelContent")}',
        type: 'GET',
        data: {path: templatePath, format: 'html'},
        dataType: 'json',
        success: function(res) {
            Layer.close(loadingMsg);
            
            if (res.code === 1 && res.data.html) {
                $('#simple-excel-editor').html(res.data.html);
                
                // 添加简单的编辑功能
                $('#simple-excel-editor').find('td').attr('contenteditable', 'true');
                
                // 添加保存按钮
                var saveBtn = $('<button class="btn btn-success" id="save-simple-excel"><i class="fa fa-save"></i> 保存更改</button>');
                $('#simple-excel-editor').before(saveBtn);
                
                // 绑定保存事件
                $('#save-simple-excel').on('click', function() {
                    var htmlContent = $('#simple-excel-editor').html();
                    
                    // 保存HTML内容
                    $.ajax({
                        url: '{:url("template/saveExcelHtml")}',
                        type: 'POST',
                        data: {
                            path: templatePath,
                            html: htmlContent
                        },
                        dataType: 'json',
                        success: function(res) {
                            if (res.code === 1) {
                                Layer.msg('保存成功', {icon: 1});
                            } else {
                                Layer.msg(res.msg || '保存失败', {icon: 2});
                            }
                        },
                        error: function() {
                            Layer.msg('网络错误，保存失败', {icon: 2});
                        }
                    });
                });
                
                Layer.msg('备用编辑器已加载', {icon: 1});
            } else {
                $('#simple-excel-editor').html(
                    '<div class="alert alert-danger">' +
                    '<i class="fa fa-times-circle"></i> 无法加载Excel内容' +
                    '</div>'
                );
                
                // 添加重试按钮
                var retryBtn = $('<button class="btn btn-primary" id="retry-load-excel"><i class="fa fa-refresh"></i> 重试加载高级编辑器</button>');
                $('#simple-excel-editor').before(retryBtn);
                
                // 绑定重试事件
                $('#retry-load-excel').on('click', function() {
                    loadExcelFile();
                });
            }
        },
        error: function() {
            Layer.close(loadingMsg);
            $('#simple-excel-editor').html(
                '<div class="alert alert-danger">' +
                '<i class="fa fa-times-circle"></i> 网络错误，无法加载Excel内容' +
                '</div>'
            );
            
            // 添加重试按钮
            var retryBtn = $('<button class="btn btn-primary" id="retry-load-excel"><i class="fa fa-refresh"></i> 重试加载</button>');
            $('#simple-excel-editor').before(retryBtn);
            
            // 绑定重试事件
            $('#retry-load-excel').on('click', function() {
                loadExcelFile();
            });
        }
    });
}

// 添加全局错误处理
window.addEventListener('error', function(event) {
    console.error('全局错误:', event.error);
    
    // 如果错误来自LuckySheet相关代码，尝试恢复
    if (event.filename && (
        event.filename.indexOf('luckysheet') > -1 || 
        event.filename.indexOf('luckyexcel') > -1
    )) {
        
        
        // 如果已尝试加载LuckySheet但失败，可以提供备用方案
        if (!document.querySelector('.luckysheet-cell-main') && $('#luckysheet').is(':visible')) {
            
            
            // 获取模板路径
            var templatePath = $('#placeholderList').data('path');
            if (templatePath) {
                // 延迟加载备用编辑器
                setTimeout(function() {
                    loadExcelFallback(templatePath);
                }, 1000);
            }
        }
    }
});

// 设置全局超时检测，确保LuckySheet能够加载
$(document).ready(function() {
    // 设置一个超时检测，如果10秒后LuckySheet仍未加载，则尝试备用方案
    setTimeout(function() {
        // 如果在编辑模式但LuckySheet未加载
        if ($('#t-edit').is(':visible') && 
            !document.querySelector('.luckysheet-cell-main') && 
            $('#luckysheet').is(':visible')) {
            
            
            
            // 获取模板路径
            var templatePath = $('#placeholderList').data('path');
            if (templatePath) {
                loadExcelFallback(templatePath);
            }
        }
    }, 10000);
});

// 监听窗口大小变化，调整LuckySheet大小
$(window).on('resize', function() {
    if (window.luckysheet) {
        try {
            window.luckysheet.resize();
        } catch (e) {
            console.warn('调整LuckySheet大小失败:', e);
        }
    }
});

// 显示占位符选择器弹窗
function showPlaceholderSelector() {
    // 获取简历模板占位符列表
    getResumePlaceholders(function(resumePlaceholders) {
        if (!resumePlaceholders || resumePlaceholders.length === 0) {
            Layer.msg('无法获取占位符列表，请稍后再试', {icon: 2});
            return;
        }
        
        // 检查LuckySheet是否初始化
        if (!window.luckysheet) {
            Layer.msg('编辑器未初始化，请稍后再试', {icon: 2});
            return;
        }
        
        // 按类别分组占位符
        var categories = {};
        resumePlaceholders.forEach(function(placeholder) {
            if (!categories[placeholder.category]) {
                categories[placeholder.category] = [];
            }
            categories[placeholder.category].push(placeholder);
        });
        
        // 创建选项卡内容
        var content = '<div class="placeholder-selector">' +
            '<div class="placeholder-search">' +
            '<input type="text" class="form-control" id="placeholderSearchPopup" placeholder="搜索占位符...">' +
            '</div>' +
            '<ul class="nav nav-tabs placeholder-tabs">';
        
        // 添加选项卡标签
        var firstCategory = true;
        for (var category in categories) {
            content += '<li class="' + (firstCategory ? 'active' : '') + '">' +
                '<a href="#tab-' + category.replace(/\s+/g, '-').toLowerCase() + '" data-toggle="tab">' + category + '</a>' +
                '</li>';
            firstCategory = false;
        }
        
        content += '</ul><div class="tab-content placeholder-tab-content">';
        
        // 添加选项卡内容
        firstCategory = true;
        for (var category in categories) {
            var tabId = 'tab-' + category.replace(/\s+/g, '-').toLowerCase();
            content += '<div class="tab-pane ' + (firstCategory ? 'active' : '') + '" id="' + tabId + '">' +
                '<div class="placeholder-list">';
            
            categories[category].forEach(function(placeholder) {
                content += '<div class="placeholder-item" data-name="{{' + placeholder.name + '}}" data-category="' + category + '">' +
                    '<div class="placeholder-name">{{' + placeholder.name + '}}</div>' +
                    '<div class="placeholder-description">' + placeholder.description + '</div>' +
                    '</div>';
            });
            
            content += '</div></div>';
            firstCategory = false;
        }
        
        content += '</div></div>';
        
        // 添加弹窗样式
        if (!$('#placeholder-selector-style').length) {
            var style = '<style id="placeholder-selector-style">' +
                '.placeholder-selector { padding: 10px; }' +
                '.placeholder-search { margin-bottom: 15px; }' +
                '.placeholder-tabs { margin-bottom: 15px; }' +
                '.placeholder-tab-content { padding-top: 10px; }' +
                '.placeholder-list { display: flex; flex-wrap: wrap; }' +
                '.placeholder-item { width: 48%; margin: 1%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; cursor: pointer; transition: all 0.2s; }' +
                '.placeholder-item:hover { background-color: #f5f5f5; border-color: #1890ff; }' +
                '.placeholder-name { font-weight: bold; margin-bottom: 5px; color: #1890ff; }' +
                '.placeholder-description { font-size: 12px; color: #666; }' +
                '.nav-tabs>li>a { padding: 8px 12px; }' +
                '@media (max-width: 768px) {' +
                '  .placeholder-item { width: 100%; margin: 1% 0; }' +
                '}' +
                '</style>';
            $('head').append(style);
        }
        
        // 关闭可能已存在的弹窗
        Layer.closeAll();
        
        // 打开弹窗
        Layer.open({
            type: 1,
            title: '选择要插入的占位符',
            area: ['700px', 'auto'],
            maxHeight: '80%',
            content: content,
            success: function(layero, index) {
                // 初始化选项卡
                $(layero).find('.nav-tabs a').on('click', function(e) {
                    e.preventDefault();
                    $(this).tab('show');
                });
                
                // 绑定搜索功能
                $('#placeholderSearchPopup').off('input').on('input', function() {
                    var searchText = $(this).val().toLowerCase();
                    if (searchText === '') {
                        // 如果搜索框为空，恢复选项卡视图
                        $(layero).find('.nav-tabs').show();
                        $(layero).find('.nav-tabs a:first').tab('show');
                        $(layero).find('.placeholder-item').show();
                    } else {
                        // 如果有搜索文本，隐藏选项卡，在所有选项卡中搜索
                        $(layero).find('.nav-tabs').hide();
                        $(layero).find('.tab-pane').addClass('active').show();
                        
                        // 根据搜索文本过滤占位符
                        $(layero).find('.placeholder-item').each(function() {
                            var name = $(this).data('name').toLowerCase();
                            var description = $(this).find('.placeholder-description').text().toLowerCase();
                            var category = $(this).data('category').toLowerCase();
                            
                            if (name.indexOf(searchText) > -1 || 
                                description.indexOf(searchText) > -1 || 
                                category.indexOf(searchText) > -1) {
                                $(this).show();
                            } else {
                                $(this).hide();
                            }
                        });
                    }
                });
                
                // 先移除可能存在的事件绑定
                $(layero).find('.placeholder-item').off('click');
                
                // 绑定点击事件
                $(layero).find('.placeholder-item').on('click', function() {
                    var placeholderName = $(this).data('name');
                    insertPlaceholderDirectly(placeholderName);
                    Layer.close(index);
                });
            }
        });
    });
}

// 直接插入占位符到活动单元格（不依赖选区）
function insertPlaceholderDirectly(placeholderName) {
    try {
        console.log('尝试直接插入占位符:', placeholderName);
        
        // 检查LuckySheet是否初始化
        if (!window.luckysheet) {
            Layer.msg('编辑器未初始化，无法插入占位符', {icon: 2});
            return;
        }
        
        // 1. 首先尝试检查是否有正在编辑的单元格
        var editingCell = document.querySelector('.luckysheet-cell-input.luckysheet-cell-input-show');
        if (editingCell) {
            console.log('检测到正在编辑的单元格，直接插入文本');
            try {
                // 获取当前光标位置
                var selection = window.getSelection();
                var range = selection.getRangeAt(0);
                
                // 创建文本节点
                var textNode = document.createTextNode(placeholderName);
                
                // 在光标位置插入文本
                range.insertNode(textNode);
                
                // 移动光标到插入的文本后面
                range.setStartAfter(textNode);
                range.setEndAfter(textNode);
                selection.removeAllRanges();
                selection.addRange(range);
                
                // 触发输入事件
                var event = new Event('input', { bubbles: true });
                editingCell.dispatchEvent(event);
                
                Layer.msg('占位符 ' + placeholderName + ' 已插入', {icon: 1});
                return;
            } catch (e) {
                console.error('直接插入文本失败:', e);
            }
        }
        
        // 2. 直接使用LuckySheet API获取选中单元格
        console.log('尝试使用LuckySheet API获取选中单元格');
        
        // 获取全局变量
        console.log('检查全局变量:');
        if (window.luckysheet_select_save) {
            console.log('luckysheet_select_save:', window.luckysheet_select_save);
        }
        if (window.luckysheetcurrentsheetselectdata) {
            console.log('luckysheetcurrentsheetselectdata:', window.luckysheetcurrentsheetselectdata);
        }
        
        // 尝试获取当前选中区域
        var selectedRange = null;
        
        // 方法1: 使用全局变量
        if (window.luckysheet_select_save && Array.isArray(window.luckysheet_select_save) && window.luckysheet_select_save.length > 0) {
            selectedRange = window.luckysheet_select_save[0];
            console.log('从luckysheet_select_save获取选区:', selectedRange);
        }
        
        // 方法2: 使用API方法
        if (!selectedRange && window.luckysheet && typeof window.luckysheet.getluckysheet_select_save === 'function') {
            try {
                var ranges = window.luckysheet.getluckysheet_select_save();
                if (Array.isArray(ranges) && ranges.length > 0) {
                    selectedRange = ranges[0];
                    console.log('从getluckysheet_select_save获取选区:', selectedRange);
                }
            } catch (e) {
                console.error('调用getluckysheet_select_save失败:', e);
            }
        }
        
        // 方法3: 使用其他可能的API
        if (!selectedRange && window.luckysheet) {
            // 尝试其他可能的API方法
            var apiMethods = [
                'getRangeValue',
                'getRangeValuesTo',
                'getRangeByEle',
                'getSelection',
                'getSelectionPosition'
            ];
            
            for (var i = 0; i < apiMethods.length; i++) {
                var method = apiMethods[i];
                if (typeof window.luckysheet[method] === 'function') {
                    try {
                        var result = window.luckysheet[method]();
                        console.log('尝试API方法 ' + method + ':', result);
                        
                        if (result && (result.row !== undefined || result.startRow !== undefined)) {
                            selectedRange = result;
                            console.log('从 ' + method + ' 获取选区:', selectedRange);
                            break;
                        }
                    } catch (e) {
                        console.error('调用 ' + method + ' 失败:', e);
                    }
                }
            }
        }
        
        // 如果找到了选区，提取行列信息
        var selectedCell = {
            found: false,
            row: 0,
            col: 0
        };
        
        if (selectedRange) {
            // 根据不同的选区格式提取行列信息
            if (selectedRange.row !== undefined && selectedRange.column !== undefined) {
                // 格式: {row: [0,0], column: [0,0]}
                selectedCell.row = Array.isArray(selectedRange.row) ? selectedRange.row[0] : selectedRange.row;
                selectedCell.col = Array.isArray(selectedRange.column) ? selectedRange.column[0] : selectedRange.column;
                selectedCell.found = true;
            } else if (selectedRange.r !== undefined && selectedRange.c !== undefined) {
                // 格式: {r: 0, c: 0}
                selectedCell.row = selectedRange.r;
                selectedCell.col = selectedRange.c;
                selectedCell.found = true;
            } else if (selectedRange.startRow !== undefined && selectedRange.startColumn !== undefined) {
                // 格式: {startRow: 0, startColumn: 0}
                selectedCell.row = selectedRange.startRow;
                selectedCell.col = selectedRange.startColumn;
                selectedCell.found = true;
            }
            
            if (selectedCell.found) {
                console.log('从API获取到选中单元格位置:', selectedCell);
            }
        }
        
        // 如果API方法失败，尝试从DOM元素获取
        if (!selectedCell.found) {
            try {
                // 获取用户当前选中的单元格
                selectedCell = findSelectedCellFromDOM();
            } catch (e) {
                console.error('从DOM获取选中单元格失败:', e);
                // 确保使用默认值
                selectedCell = {
                    found: false,
                    row: 0,
                    col: 0
                };
            }
        }
        
        // 3. 尝试使用LuckySheet的API插入占位符
        try {
            console.log('尝试使用API插入占位符');
            
            if (window.luckysheet && window.luckysheet.getSheetData) {
                var sheetData = window.luckysheet.getSheetData();
                console.log('工作表数据:', sheetData ? '已获取' : '未获取');
                
                if (sheetData) {
                    // 使用用户选中的单元格或默认的A1单元格
                    var row = selectedCell.found ? selectedCell.row : 0;
                    var col = selectedCell.found ? selectedCell.col : 0;
                    
                    console.log('目标单元格位置:', {row: row, col: col});
                    
                    var success = false;
                    
                    // 方法1: 使用setCellValue API
                    if (typeof window.luckysheet.setCellValue === 'function') {
                        try {
                            // 先获取当前值
                            var currentValue = '';
                            if (typeof window.luckysheet.getCellValue === 'function') {
                                currentValue = window.luckysheet.getCellValue(row, col) || '';
                                console.log('当前单元格值:', currentValue);
                            }
                            
                            // 设置新值
                            var newValue = currentValue + placeholderName;
                            console.log('尝试设置单元格值:', newValue);
                            
                            window.luckysheet.setCellValue(row, col, newValue);
                            console.log('设置单元格值成功');
                            
                            // 强制刷新
                            if (typeof window.luckysheet.refresh === 'function') {
                                window.luckysheet.refresh();
                            }
                            
                            var cellAddress = getCellAddress(row, col);
                            Layer.msg('占位符 ' + placeholderName + ' 已插入到单元格' + cellAddress, {icon: 1});
                            success = true;
                            return;
                        } catch (e) {
                            console.error('使用setCellValue API失败:', e);
                        }
                    }
                    
                    // 方法2: 直接修改数据结构
                    if (!success && sheetData) {
                        try {
                            // 确保目标行存在
                            while (sheetData.length <= row) {
                                sheetData.push([]);
                            }
                            
                            // 确保目标列存在
                            if (!sheetData[row]) {
                                sheetData[row] = [];
                            }
                            
                            while (sheetData[row].length <= col) {
                                sheetData[row].push(null);
                            }
                            
                            // 获取当前值
                            var cellObj = sheetData[row][col];
                            var currentValue = '';
                            
                            if (cellObj) {
                                if (typeof cellObj === 'object' && cellObj.v !== undefined) {
                                    currentValue = cellObj.v || '';
                                } else if (typeof cellObj === 'string' || typeof cellObj === 'number') {
                                    currentValue = cellObj;
                                }
                            }
                            
                            // 设置新值
                            var newValue = currentValue + placeholderName;
                            console.log('直接修改数据结构，设置单元格值:', newValue);
                            
                            // 更新单元格值
                            if (typeof cellObj === 'object') {
                                cellObj.v = newValue;
                                cellObj.m = newValue;
                            } else {
                                sheetData[row][col] = { v: newValue, m: newValue };
                            }
                            
                            // 强制刷新
                            if (typeof window.luckysheet.refresh === 'function') {
                                window.luckysheet.refresh();
                            } else if (typeof window.luckysheet.jfrefreshgrid === 'function') {
                                window.luckysheet.jfrefreshgrid();
                            }
                            
                            var cellAddress = getCellAddress(row, col);
                            Layer.msg('占位符 ' + placeholderName + ' 已插入到单元格' + cellAddress, {icon: 1});
                            success = true;
                            return;
                        } catch (e) {
                            console.error('直接修改数据结构失败:', e);
                        }
                    }
                }
            }
            
            // 4. 尝试模拟用户操作
            if (selectedCell.found) {
                console.log('尝试模拟用户操作到选中的单元格');
                
                // 尝试找到对应的单元格元素
                var cellElement = document.querySelector('.luckysheet-cell[row="' + selectedCell.row + '"][col="' + selectedCell.col + '"]');
                
                if (!cellElement) {
                    // 尝试其他选择器
                    var cells = document.querySelectorAll('.luckysheet-cell');
                    for (var i = 0; i < cells.length; i++) {
                        var cell = cells[i];
                        var rowAttr = cell.getAttribute('row');
                        var colAttr = cell.getAttribute('col');
                        
                        if (rowAttr == selectedCell.row && colAttr == selectedCell.col) {
                            cellElement = cell;
                            break;
                        }
                    }
                }
                
                if (cellElement) {
                    console.log('找到选中的单元格元素，尝试模拟双击');
                    
                    // 模拟点击
                    var clickEvent = new MouseEvent('mousedown', {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    cellElement.dispatchEvent(clickEvent);
                    
                    // 模拟双击
                    setTimeout(function() {
                        var dblClickEvent = new MouseEvent('dblclick', {
                            bubbles: true,
                            cancelable: true,
                            view: window
                        });
                        cellElement.dispatchEvent(dblClickEvent);
                        
                        // 等待编辑框出现
                        setTimeout(function() {
                            var inputBox = document.querySelector('.luckysheet-cell-input');
                            if (inputBox) {
                                console.log('找到编辑框元素，设置文本');
                                
                                // 获取当前文本
                                var currentText = inputBox.innerText || '';
                                
                                // 设置新文本
                                inputBox.innerText = currentText + placeholderName;
                                
                                // 触发输入事件
                                var inputEvent = new Event('input', { bubbles: true });
                                inputBox.dispatchEvent(inputEvent);
                                
                                // 模拟按下Enter键确认编辑
                                setTimeout(function() {
                                    var enterEvent = new KeyboardEvent('keydown', {
                                        bubbles: true,
                                        cancelable: true,
                                        key: 'Enter',
                                        keyCode: 13
                                    });
                                    inputBox.dispatchEvent(enterEvent);
                                    
                                    var cellAddress = getCellAddress(selectedCell.row, selectedCell.col);
                                    Layer.msg('占位符 ' + placeholderName + ' 已插入到单元格' + cellAddress, {icon: 1});
                                }, 100);
                            } else {
                                console.error('找不到编辑框元素');
                                // 如果找不到编辑框，尝试其他方法
                                tryFallbackMethods();
                            }
                        }, 200);
                    }, 100);
                    
                    return;
                }
            }
            
            // 如果无法找到选中的单元格元素，尝试其他方法
            tryFallbackMethods();
            
        } catch (e) {
            console.error('插入占位符失败:', e);
            tryFallbackMethods();
        }
        
        // 尝试备用方法
        function tryFallbackMethods() {
            console.log('尝试备用方法');
            
            // 尝试查找任何可见的单元格元素
            var cellSelectors = [
                '.luckysheet-cell',
                '.luckysheet-cell-main .luckysheet-cell',
                '.luckysheet-grid-window .luckysheet-cell',
                '.luckysheet-grid-window canvas',
                '.luckysheet-grid-window',
                '#luckysheet-cell-main .luckysheet-cell'
            ];
            
            var cellElement = null;
            for (var i = 0; i < cellSelectors.length; i++) {
                var elements = document.querySelectorAll(cellSelectors[i]);
                if (elements && elements.length > 0) {
                    cellElement = elements[0];
                    console.log('找到单元格元素:', cellSelectors[i], cellElement);
                    break;
                }
            }
            
            if (cellElement) {
                console.log('尝试模拟双击单元格元素');
                
                // 模拟点击
                var clickEvent = new MouseEvent('mousedown', {
                    bubbles: true,
                    cancelable: true,
                    view: window
                });
                cellElement.dispatchEvent(clickEvent);
                
                // 模拟双击
                setTimeout(function() {
                    var dblClickEvent = new MouseEvent('dblclick', {
                        bubbles: true,
                        cancelable: true,
                        view: window
                    });
                    cellElement.dispatchEvent(dblClickEvent);
                    
                    // 等待编辑框出现
                    setTimeout(function() {
                        var inputBox = document.querySelector('.luckysheet-cell-input');
                        if (inputBox) {
                            console.log('找到编辑框元素，设置文本');
                            
                            // 获取当前文本
                            var currentText = inputBox.innerText || '';
                            
                            // 设置新文本
                            inputBox.innerText = currentText + placeholderName;
                            
                            // 触发输入事件
                            var inputEvent = new Event('input', { bubbles: true });
                            inputBox.dispatchEvent(inputEvent);
                            
                            // 模拟按下Enter键确认编辑
                            setTimeout(function() {
                                var enterEvent = new KeyboardEvent('keydown', {
                                    bubbles: true,
                                    cancelable: true,
                                    key: 'Enter',
                                    keyCode: 13
                                });
                                inputBox.dispatchEvent(enterEvent);
                                
                                Layer.msg('占位符 ' + placeholderName + ' 已插入', {icon: 1});
                            }, 100);
                        } else {
                            console.error('找不到编辑框元素');
                            // 如果找不到编辑框，直接提供复制功能
                            offerCopyOption();
                        }
                    }, 200);
                }, 100);
                
                return;
            }
            
            // 如果所有方法都失败，提供复制功能
            console.log('所有自动插入方法都失败，提供复制功能');
            offerCopyOption();
        }
    } catch (e) {
        console.error('整个插入过程失败:', e);
        Layer.msg('操作失败: ' + e.message, {icon: 2});
        offerCopyOption();
    }
    
    // 提供复制占位符到剪贴板的选项
    function offerCopyOption() {
        Layer.confirm('无法自动插入占位符，是否复制到剪贴板？', {
            btn: ['复制','取消'],
            title: '复制占位符'
        }, function(index){
            // 复制占位符到剪贴板
            copyToClipboard(placeholderName);
            Layer.msg('占位符 ' + placeholderName + ' 已复制到剪贴板，请手动粘贴到需要的单元格', {icon: 1});
            Layer.close(index);
        });
    }
    
    // 将行列索引转换为Excel单元格地址（如A1, B2等）
    function getCellAddress(row, col) {
        var address = '';
        
        // 转换列索引为字母（0=A, 1=B, 等）
        var colStr = '';
        var tempCol = col;
        
        do {
            var remainder = tempCol % 26;
            colStr = String.fromCharCode(65 + remainder) + colStr;
            tempCol = Math.floor(tempCol / 26) - 1;
        } while (tempCol >= 0);
        
        // 行索引加1（因为Excel是从1开始的）
        var rowStr = (row + 1).toString();
        
        return colStr + rowStr;
    }
    
    // 从DOM元素获取选中单元格
    function findSelectedCellFromDOM() {
        var selectedCell = {
            found: false,
            row: 0,
            col: 0
        };
        
        try {
            console.log('尝试检测用户选中的单元格');
            
            // 方法1: 检查高亮的选择区域
            var highlightElements = document.querySelectorAll('.luckysheet-selection-highlight');
            if (highlightElements && highlightElements.length > 0) {
                console.log('找到选择高亮元素:', highlightElements.length);
                
                // 获取第一个高亮元素
                var highlight = highlightElements[0];
                
                // 尝试从属性中获取行列信息
                var rowAttr = highlight.getAttribute('row');
                var colAttr = highlight.getAttribute('col');
                
                if (rowAttr !== null && colAttr !== null) {
                    selectedCell.row = parseInt(rowAttr);
                    selectedCell.col = parseInt(colAttr);
                    selectedCell.found = true;
                    console.log('从属性获取选中单元格位置:', selectedCell);
                } else {
                    // 尝试从类名中获取
                    var classList = highlight.className.split(' ');
                    for (var i = 0; i < classList.length; i++) {
                        var cls = classList[i];
                        if (cls.indexOf('luckysheet-selection-highlight_') === 0) {
                            var parts = cls.split('_');
                            if (parts.length >= 3) {
                                selectedCell.row = parseInt(parts[1]);
                                selectedCell.col = parseInt(parts[2]);
                                selectedCell.found = true;
                                console.log('从类名获取选中单元格位置:', selectedCell);
                                break;
                            }
                        }
                    }
                }
            }
            
            // 方法3: 检查当前活动单元格
            if (!selectedCell.found) {
                var activeCell = document.querySelector('#luckysheet-cell-selected');
                if (activeCell) {
                    console.log('找到活动单元格元素:', activeCell);
                    
                    // 尝试从style属性中获取位置信息
                    var style = activeCell.getAttribute('style');
                    if (style) {
                        // 提取left和top值
                        var leftMatch = style.match(/left:\s*(\d+)px/);
                        var topMatch = style.match(/top:\s*(\d+)px/);
                        var widthMatch = style.match(/width:\s*(\d+)px/);
                        var heightMatch = style.match(/height:\s*(\d+)px/);
                        
                        if (leftMatch && leftMatch.length > 1 && topMatch && topMatch.length > 1) {
                            var left = parseInt(leftMatch[1]);
                            var top = parseInt(topMatch[1]);
                            var width = widthMatch && widthMatch.length > 1 ? parseInt(widthMatch[1]) : 0;
                            var height = heightMatch && heightMatch.length > 1 ? parseInt(heightMatch[1]) : 0;
                            
                            console.log('从style属性获取位置和大小:', {left: left, top: top, width: width, height: height});
                            
                            // 尝试查找实际单元格
                            console.log('尝试查找与位置匹配的单元格');
                            
                            // 方法1: 尝试直接查找位置匹配的单元格
                            var cells = document.querySelectorAll('.luckysheet-cell');
                            var bestMatch = null;
                            var minDistance = Infinity;
                            
                            for (var i = 0; i < cells.length; i++) {
                                var cell = cells[i];
                                var cellStyle = window.getComputedStyle(cell);
                                var cellLeft = parseInt(cellStyle.left);
                                var cellTop = parseInt(cellStyle.top);
                                
                                if (!isNaN(cellLeft) && !isNaN(cellTop)) {
                                    // 计算距离
                                    var distance = Math.sqrt(
                                        Math.pow(cellLeft - left, 2) + 
                                        Math.pow(cellTop - top, 2)
                                    );
                                    
                                    if (distance < minDistance) {
                                        minDistance = distance;
                                        bestMatch = cell;
                                    }
                                }
                            }
                            
                            if (bestMatch) {
                                var rowAttr = bestMatch.getAttribute('row');
                                var colAttr = bestMatch.getAttribute('col');
                                
                                if (rowAttr !== null && colAttr !== null) {
                                    selectedCell.row = parseInt(rowAttr);
                                    selectedCell.col = parseInt(colAttr);
                                    selectedCell.found = true;
                                    console.log('找到最接近的单元格:', {row: selectedCell.row, col: selectedCell.col});
                                }
                            }
                            
                            // 方法2: 如果找不到匹配的单元格，使用改进的估算方法
                            if (!selectedCell.found) {
                                // 获取所有可见的单元格，用于计算平均单元格大小
                                var visibleCells = document.querySelectorAll('.luckysheet-cell');
                                var totalWidth = 0;
                                var totalHeight = 0;
                                var cellCount = 0;
                                
                                // 收集所有单元格的行列信息
                                var cellPositions = [];
                                
                                for (var i = 0; i < visibleCells.length; i++) {
                                    var cell = visibleCells[i];
                                    var rowAttr = cell.getAttribute('row');
                                    var colAttr = cell.getAttribute('col');
                                    
                                    if (rowAttr !== null && colAttr !== null) {
                                        var row = parseInt(rowAttr);
                                        var col = parseInt(colAttr);
                                        
                                        if (!isNaN(row) && !isNaN(col)) {
                                            var cellStyle = window.getComputedStyle(cell);
                                            var cellWidth = parseInt(cellStyle.width);
                                            var cellHeight = parseInt(cellStyle.height);
                                            
                                            if (!isNaN(cellWidth) && !isNaN(cellHeight)) {
                                                totalWidth += cellWidth;
                                                totalHeight += cellHeight;
                                                cellCount++;
                                                
                                                cellPositions.push({
                                                    row: row,
                                                    col: col,
                                                    left: parseInt(cellStyle.left),
                                                    top: parseInt(cellStyle.top),
                                                    width: cellWidth,
                                                    height: cellHeight
                                                });
                                            }
                                        }
                                    }
                                }
                                
                                // 如果有足够的单元格信息
                                if (cellPositions.length > 0) {
                                    console.log('收集到的单元格位置信息:', cellPositions.length);
                                    
                                    // 查找最接近的单元格
                                    var bestMatch = null;
                                    var minDistance = Infinity;
                                    
                                    for (var i = 0; i < cellPositions.length; i++) {
                                        var cellPos = cellPositions[i];
                                        var cellCenterX = cellPos.left + cellPos.width / 2;
                                        var cellCenterY = cellPos.top + cellPos.height / 2;
                                        var selectedCenterX = left + width / 2;
                                        var selectedCenterY = top + height / 2;
                                        
                                        var distance = Math.sqrt(
                                            Math.pow(cellCenterX - selectedCenterX, 2) + 
                                            Math.pow(cellCenterY - selectedCenterY, 2)
                                        );
                                        
                                        if (distance < minDistance) {
                                            minDistance = distance;
                                            bestMatch = cellPos;
                                        }
                                    }
                                    
                                    if (bestMatch) {
                                        selectedCell.row = bestMatch.row;
                                        selectedCell.col = bestMatch.col;
                                        selectedCell.found = true;
                                        console.log('找到中心点最接近的单元格:', {row: selectedCell.row, col: selectedCell.col});
                                    }
                                }
                            }
                        }
                    }
                }
            }
            
            // 确保选中的单元格位置是有效的数字
            if (selectedCell.found && (isNaN(selectedCell.row) || isNaN(selectedCell.col))) {
                console.log('检测到的单元格位置无效，使用默认值');
                selectedCell.row = 0;
                selectedCell.col = 0;
                selectedCell.found = false;
            }
        } catch (e) {
            console.error('检测选中单元格失败:', e);
            // 确保使用默认值
            selectedCell.row = 0;
            selectedCell.col = 0;
            selectedCell.found = false;
        }
        
        return selectedCell;
    }
}

// 获取简历模板占位符列表
function getResumePlaceholders(callback) {
    // 定义简历模板占位符列表
    var placeholders = [
        // 基本信息
        { name: '姓名', description: '简历所有者姓名', category: '基本信息' },
        { name: '性别', description: '简历所有者性别', category: '基本信息' },
        { name: '年龄', description: '简历所有者年龄', category: '基本信息' },
        { name: '出生日期', description: '简历所有者出生日期', category: '基本信息' },
        { name: '身份证号', description: '身份证号码', category: '基本信息' },
        { name: '民族', description: '简历所有者民族', category: '基本信息' },
        { name: '婚姻状况', description: '婚姻状况（未婚/已婚/离异）', category: '基本信息' },
        { name: '身高', description: '身高（厘米）', category: '基本信息' },
        { name: '体重', description: '体重（公斤）', category: '基本信息' },
        { name: '户口所在地', description: '户口登记地址', category: '基本信息' },
        { name: '常住地址', description: '当前居住地址', category: '基本信息' },
        
        // 联系信息
        { name: '手机号', description: '联系电话', category: '联系信息' },
        { name: '联系人关系1', description: '第1位联系人关系', category: '联系信息' },
        { name: '联系人姓名1', description: '第1位联系人姓名', category: '联系信息' },
        { name: '联系人年龄1', description: '第1位联系人年龄', category: '联系信息' },
        { name: '联系人工作1', description: '第1位联系人工作', category: '联系信息' },
        { name: '联系人关系2', description: '第2位联系人关系', category: '联系信息' },
        { name: '联系人姓名2', description: '第2位联系人姓名', category: '联系信息' },
        { name: '联系人年龄2', description: '第2位联系人年龄', category: '联系信息' },
        { name: '联系人工作2', description: '第2位联系人工作', category: '联系信息' },
        { name: '联系人关系3', description: '第3位联系人关系', category: '联系信息' },
        { name: '联系人姓名3', description: '第3位联系人姓名', category: '联系信息' },
        { name: '联系人年龄3', description: '第3位联系人年龄', category: '联系信息' },
        { name: '联系人工作3', description: '第3位联系人工作', category: '联系信息' },
        
        // 教育经历
        { name: '最高学历', description: '最高教育程度', category: '教育经历' },
        { name: '教育开始1', description: '第1段教育经历开始时间', category: '教育经历' },
        { name: '教育结束1', description: '第1段教育经历结束时间', category: '教育经历' },
        { name: '学校1', description: '第1段教育经历学校名称', category: '教育经历' },
        { name: '专业1', description: '第1段教育经历专业', category: '教育经历' },
        { name: '学历1', description: '第1段教育经历学历', category: '教育经历' },
        { name: '教育开始2', description: '第2段教育经历开始时间', category: '教育经历' },
        { name: '教育结束2', description: '第2段教育经历结束时间', category: '教育经历' },
        { name: '学校2', description: '第2段教育经历学校名称', category: '教育经历' },
        { name: '专业2', description: '第2段教育经历专业', category: '教育经历' },
        { name: '学历2', description: '第2段教育经历学历', category: '教育经历' },
        { name: '教育开始3', description: '第3段教育经历开始时间', category: '教育经历' },
        { name: '教育结束3', description: '第3段教育经历结束时间', category: '教育经历' },
        { name: '学校3', description: '第3段教育经历学校名称', category: '教育经历' },
        { name: '专业3', description: '第3段教育经历专业', category: '教育经历' },
        { name: '学历3', description: '第3段教育经历学历', category: '教育经历' },
        
        // 工作经历
        { name: '意向岗位', description: '求职意向岗位', category: '工作经历' },
        { name: '申请岗位', description: '实际申请岗位', category: '工作经历' },
        { name: '工作开始1', description: '第1段工作经历开始时间', category: '工作经历' },
        { name: '工作结束1', description: '第1段工作经历结束时间', category: '工作经历' },
        { name: '工作单位1', description: '第1段工作经历公司名称', category: '工作经历' },
        { name: '工作岗位1', description: '第1段工作经历职位', category: '工作经历' },
        { name: '工作内容1', description: '第1段工作经历工作描述', category: '工作经历' },
        { name: '工作开始2', description: '第2段工作经历开始时间', category: '工作经历' },
        { name: '工作结束2', description: '第2段工作经历结束时间', category: '工作经历' },
        { name: '工作单位2', description: '第2段工作经历公司名称', category: '工作经历' },
        { name: '工作岗位2', description: '第2段工作经历职位', category: '工作经历' },
        { name: '工作内容2', description: '第2段工作经历工作描述', category: '工作经历' },
        { name: '工作开始3', description: '第3段工作经历开始时间', category: '工作经历' },
        { name: '工作结束3', description: '第3段工作经历结束时间', category: '工作经历' },
        { name: '工作单位3', description: '第3段工作经历公司名称', category: '工作经历' },
        { name: '工作岗位3', description: '第3段工作经历职位', category: '工作经历' },
        { name: '工作内容3', description: '第3段工作经历工作描述', category: '工作经历' },
        
        // 语言能力
        { name: '粤语水平', description: '粤语熟练程度', category: '语言能力' },
        { name: '国语水平', description: '国语熟练程度', category: '语言能力' },
        { name: '英语水平', description: '英语熟练程度', category: '语言能力' },
        
        // 其他信息
        { name: '港澳通行证', description: '港澳通行证号码', category: '其他信息' },
        { name: '港澳通行证到期', description: '港澳通行证到期日期', category: '其他信息' },
        { name: '海外工作经历', description: '是否有海外工作经历', category: '其他信息' },
        { name: '海外工作地区', description: '海外工作地区', category: '其他信息' },
        { name: '特殊证书', description: '特殊职业资格证书', category: '其他信息' },
        { name: '兴趣爱好', description: '个人兴趣爱好', category: '其他信息' },
        { name: '自我评价', description: '个人自我评价', category: '其他信息' },
        { name: '更新时间', description: '简历更新时间', category: '其他信息' },
        
        // 图片
        { name: '头像', description: '个人头像照片', category: '图片' },
        { name: '全身照', description: '个人全身照片', category: '图片' },
        { name: '身份证正面', description: '身份证正面照片', category: '图片' },
        { name: '身份证反面', description: '身份证反面照片', category: '图片' },
        { name: '港澳通行证正面', description: '港澳通行证正面照片', category: '图片' },
        { name: '港澳通行证反面', description: '港澳通行证反面照片', category: '图片' },
        { name: '补充照片1', description: '补充照片1', category: '图片' },
        { name: '补充照片2', description: '补充照片2', category: '图片' },
        { name: '补充照片3', description: '补充照片3', category: '图片' }
    ];
    
    // 如果有回调函数，执行回调
    if (typeof callback === 'function') {
        callback(placeholders);
    }
    
    return placeholders;
}
</script> 