<link href="__CDN__/assets/addons/wechat/css/menu.css?v={$site.version|htmlentities}" rel="stylesheet">
<style>
    .clickbox {margin:0;text-align: left;}
    .create-click {
        margin-left:0;
    }
</style>
<form id="add-form" class="form-horizontal form-ajax" role="form" data-toggle="validator" method="POST" action="">
    <div class="form-group">
        <label for="c-title" class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[title]" value=""  id="c-title" class="form-control" data-rule="required" />
        </div>
    </div>
    <div class="form-group">
        <label for="c-text" class="control-label col-xs-12 col-sm-2">{:__('Text')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[text]" value=""  id="c-text" class="form-control" placeholder="支持正则表达式" data-rule="required; remote(wechat/autoreply/check_text_unique)" />
        </div>
    </div>
    <div class="form-group">
        <label for="c-eventkey" class="control-label col-xs-12 col-sm-2">{:__('Event key')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="hidden" name="row[eventkey]" id="c-eventkey" class="form-control" value="" data-rule="required" readonly />
            <div class="clickbox">
                <span class="create-click"><a href="{:url('wechat.response/select')}" id="select-resources"><i class="weixin-icon big-add-gray"></i><strong>选择现有资源</strong></a></span>
                <span class="create-click"><a href="{:url('wechat.response/add')}" id="add-resources"><i class="weixin-icon big-add-gray"></i><strong>添加新资源</strong></a></span>
            </div>
        </div>
    </div>
    <div class="form-group">
        <label for="c-remark" class="control-label col-xs-12 col-sm-2">{:__('Remark')}:</label>
        <div class="col-xs-12 col-sm-8">
            <input type="text" name="row[remark]" value=""  id="c-remark" class="form-control" />
        </div>
    </div>
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
        <div class="col-xs-12 col-sm-8">
            {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('Hidden')])}
        </div>
    </div>
    <div class="form-group hide layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>

</form>
