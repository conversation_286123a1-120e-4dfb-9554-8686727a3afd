<?php

namespace app\admin\model;

use think\Model;
use traits\model\SoftDelete;

class ResumeApply extends Model
{

    use SoftDelete;

    

    // 表名
    protected $name = 'resume_apply';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text',
        'create_time_text',
        'update_time_text',
        'gender_text',
        'age_text',
        'education_text',
        'company_text',
        'salary_text'
    ];
    
    public function getStatusList()
    {
        return ['待处理' => __('待处理'), '未选中' => __('未选中'), '已选中' => __('已选中'), '已取消' => __('已取消')];
    }
    
    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['status']) ? $data['status'] : '');
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }
    
    public function getCreateTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['create_time']) ? $data['create_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    public function getUpdateTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['update_time']) ? $data['update_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }
    
    // 获取性别
    public function getGenderTextAttr($value, $data)
    {
        // 如果有关联的简历数据
        if (isset($data['resume']) && $data['resume']) {
            return $data['resume']['gender'] ?? '';
        }
        // 如果没有关联数据，尝试从resume_id查询
        if (isset($data['resume_id']) && $data['resume_id']) {
            $resume = \app\admin\model\Resume::get($data['resume_id']);
            return $resume ? $resume['gender'] : '';
        }
        return '';
    }
    
    // 获取年龄
    public function getAgeTextAttr($value, $data)
    {
        // 如果有关联的简历数据
        if (isset($data['resume']) && $data['resume']) {
            return $data['resume']['age'] ?? '';
        }
        // 如果没有关联数据，尝试从resume_id查询
        if (isset($data['resume_id']) && $data['resume_id']) {
            $resume = \app\admin\model\Resume::get($data['resume_id']);
            return $resume ? $resume['age'] : '';
        }
        return '';
    }
    
    // 获取学历
    public function getEducationTextAttr($value, $data)
    {
        // 如果有关联的简历数据
        if (isset($data['resume']) && $data['resume']) {
            return $data['resume']['highest_education'] ?? '';
        }
        // 如果没有关联数据，尝试从resume_id查询
        if (isset($data['resume_id']) && $data['resume_id']) {
            $resume = \app\admin\model\Resume::get($data['resume_id']);
            return $resume ? $resume['highest_education'] : '';
        }
        return '';
    }
    
    // 获取公司
    public function getCompanyTextAttr($value, $data)
    {
        // 如果有关联的岗位数据
        if (isset($data['job']) && $data['job']) {
            return $data['job']['company'] ?? '';
        }
        // 如果没有关联数据，尝试从job_id查询
        if (isset($data['job_id']) && $data['job_id']) {
            $job = \app\common\model\Jobs::get($data['job_id']);
            return $job ? $job['company'] : '';
        }
        return '';
    }
    
    // 获取薪资范围
    public function getSalaryTextAttr($value, $data)
    {
        // 如果有关联的岗位数据
        if (isset($data['job']) && $data['job']) {
            return $data['job']['salary_range'] ?? '';
        }
        // 如果没有关联数据，尝试从job_id查询
        if (isset($data['job_id']) && $data['job_id']) {
            $job = \app\common\model\Jobs::get($data['job_id']);
            return $job ? $job['salary_range'] : '';
        }
        return '';
    }
    
    // 关联简历表
    public function resume()
    {
        return $this->belongsTo('app\common\model\Resume', 'resume_id', 'id');
    }
    
    // 关联岗位表
    public function job()
    {
        return $this->belongsTo('app\common\model\Jobs', 'job_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    
    // 关联用户表
    public function user()
    {
        return $this->belongsTo('app\common\model\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }

} 