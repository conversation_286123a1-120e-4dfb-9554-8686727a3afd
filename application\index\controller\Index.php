<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use think\Db;

class Index extends Frontend
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
    }

    public function index()
    {
        // 获取热门职位
        $hotJobs = Db::name('jobs')
            ->where('status', '上架')
            ->order('weight', 'desc')
            ->order('resume_count', 'desc')
            ->limit(6)
            ->select();

        // 获取最新职位
        $latestJobs = Db::name('jobs')
            ->where('status', '上架')
            ->order('create_time', 'desc')
            ->limit(6)
            ->select();

        // 获取职位分类
        $categories = Db::name('jobs')
            ->where('status', '上架')
            ->column('DISTINCT category');

        $this->assign([
            'hotJobs' => $hotJobs,
            'latestJobs' => $latestJobs,
            'categories' => $categories
        ]);

        return $this->view->fetch();
    }
}
