<link href="__CDN__/assets/addons/wechat/css/menu.css?v={$site['version']}" rel="stylesheet"/>
<style>
    .form-item dl dt {
        width: 120px;
    }

    .form-item dl dd {
        margin-left: 120px;
    }

    .form-item dl dd input {
        font-size: 12px;
    }
</style>


<style data-render="darktheme">
    body.darktheme .mobile-menu-preview {
        border-color: #222;
    }

    body.darktheme .weixin-content, body.darktheme .no-weixin-content {
        background-color: #262626;
        border: 1px solid #262626;
    }

    body.darktheme #menu-content {
        background-color: #2d2d2d;
        border: 1px solid #404040;
    }

    body.darktheme .keytitle {
        background-color: #222;
    }

    body.darktheme .mobile-menu-preview .menu-list {
        filter: invert(1) hue-rotate(180deg);
    }
</style>

<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div class="weixin-menu-setting clearfix">
                        <div class="mobile-menu-preview">
                            <div class="mobile-head-title">{$site.name|htmlentities}</div>
                            <ul class="menu-list" id="menu-list">
                            </ul>
                        </div>
                        <div class="weixin-body">
                            <div class="weixin-content" style="display:none">
                                <div class="item-info">
                                    <form id="form-item" class="form-item" novalidate data-value="">
                                        <div class="item-head">
                                            <h4 id="current-item-name">添加子菜单</h4>
                                            <div class="item-delete"><a href="javascript:;" id="item_delete">删除菜单</a></div>
                                        </div>
                                        <div style="margin-top: 20px;" id="item-body">

                                        </div>
                                    </form>
                                </div>
                            </div>
                            <div class="no-weixin-content">
                                点击左侧菜单进行编辑操作
                            </div>
                        </div>
                    </div>

                    <div class="text-center" style="position:relative;">
                        <div class="text-danger" style="width:317px;position:absolute;left:0;top:0;">
                            <i class="fa fa-lightbulb-o"></i> <small>可直接拖动菜单排序</small>
                        </div>
                        <div style="padding-left:337px;">
                            <a href="javascript:;" id="menuRemote" class="btn btn-success"><i class="fa fa-cloud-download"></i> 加载服务端菜单</a>
                            <a href="javascript:;" id="menuSyn" class="btn btn-danger"><i class="fa fa-check"></i> 保存并发布</a>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </div>
</div>
<script type="text/html" id="menutpl">
    <%for(var i=0; i< menu.length; i++){%>
    <%var first=menu[i];%>
    <li id="menu-<%=i%>" class="menu-item" data-type="<%=first['type']%>" data-key="<%=first['key']%>" data-name="<%=first['name']%>" data-url="<%=first['url']%>" data-appid="<%=first['appid']%>" data-pagepath="<%=first['pagepath']%>">
        <a href="javascript:;" class="menu-link">
            <i class="icon-menu-dot"></i> <i class="weixin-icon sort-gray"></i> <span class="title"><%=first['name']%></span>
        </a>

        <div class="sub-menu-box" style="display:none;">
            <ul class="sub-menu-list">
                <%if(typeof first['sub_button']!='undefined'){%>
                <%for(var j=0; j< first['sub_button'].length; j++){%>
                <%var second=first['sub_button'][j];%>
                <li id="sub-menu-<%=j%>" class="sub-menu-item" data-type="<%=second['type']%>" data-key="<%=second['key']%>" data-name="<%=second['name']%>" data-url="<%=second['url']%>" data-appid="<%=second['appid']%>" data-pagepath="<%=second['pagepath']%>"><a href="javascript:;"> <i class="weixin-icon sort-gray"></i><span class="sub-title"><%=second['name']%></span></a></li>
                <%}%>
                <%}%>
                <li class="add-sub-item <%if(typeof first['sub_button']!='undefined' && first['sub_button'].length>=5){%>hidden<%}%>"><a href="javascript:;" title="添加子菜单"><span class=" "><i class="weixin-icon add-gray"></i></span></a></li>
            </ul>
            <i class="arrow arrow-out"></i> <i class="arrow arrow-in"></i>
        </div>
    </li>
    <%}%>
    <li class="add-item extra" id="add-item">
        <a href="javascript:;" class="menu-link" title="添加菜单"><i class="weixin-icon add-gray"></i></a>
    </li>
</script>
<script type="text/html" id="itemtpl">
    <dl>
        <dt id="current-item-option"><span class="is-sub-item <%=first?'hidden':''%>">子</span>菜单标题：</dt>
        <dd>
            <div class="input-box"><input id="item_title" name="name" class="form-control" type="text" value="<%=name%>"></div>
        </dd>
    </dl>
    <%if(!hasChild){%>
    <dl class="is-item">
        <dt id="current-item-type"><span class="is-sub-item <%=first?'hidden':''%>">子</span>菜单内容：</dt>
        <dd>
            <%for(var i=0;i< typeList.length; i++){%>
            <input id="type<%=i%>" type="radio" name="type" value="<%=typeList[i]['name']%>" <%=typeList[i]['name']==type?'checked':''%> /><label for="type<%=i%>"><span class="lbl_content"><%=typeList[i]['title']%></span></label>
            <%}%>
        </dd>
    </dl>
    <div id="menu-content" class="is-item">
        <%if(type=='view'){%>
        <div class="viewbox is-view">
            <p class="menu-content-tips">点击该<span class="is-sub-item <%=first?'hidden':''%>">子</span>菜单会跳到以下链接</p>
            <dl>
                <dt>页面地址：</dt>
                <dd>
                    <div class="input-box"><input type="text" name="url" class="form-control" value="<%=url%>"></div>
                </dd>
            </dl>
        </div>
        <%}%>
        <%if(type!='view'&&type!='miniprogram'){%>
        <div class="clickbox is-click">
            <input type="hidden" name="key" id="key" value="<%=key%>"/>
            <span class="create-click">
                    <%if(keytitle){%>
                    <div class="keytitle">资源名:<%=keytitle%></div>
                    <%}%>
                    <a href="{:url('wechat.response/select')}" id="select-resources"><i class="weixin-icon big-add-gray"></i><strong>选择现有资源</strong></a>
                </span>
            <span class="create-click">
                    <a href="{:url('wechat.response/add')}" id="add-resources"><i class="weixin-icon big-add-gray"></i><strong>添加新资源</strong></a>
                </span>
        </div>
        <%}%>
        <%if(type=='miniprogram'){%>
        <div class="viewbox is-miniprogram">
            <p class="menu-content-tips">点击该<span class="is-sub-item <%=first?'':'hidden'%>">子</span>菜单会跳到以下小程序</p>
            <dl>
                <dt>小程序ID：</dt>
                <dd>
                    <div class="input-box"><input type="text" id="appid" name="appid" class="form-control" placeholder="在小程序后台获取" value="<%=appid%>"></div>
                </dd>
            </dl>
            <dl>
                <dt>小程序页面路径：</dt>
                <dd>
                    <div class="input-box"><input type="text" id="pagepath" name="pagepath" class="form-control" placeholder="小程序页面路径" value="<%=pagepath%>"></div>
                </dd>
            </dl>
            <dl>
                <dt>页面地址：</dt>
                <dd>
                    <div class="input-box"><input type="text" name="url" class="form-control" placeholder="页面地址，当不支持小程序时会跳转此页面" value="<%=url%>"></div>
                </dd>
            </dl>
        </div>
        <%}%>
    </div>
    <%}%>
</script>
<!--@formatter:off-->
<script type="text/javascript">
    var menu = {:json_encode($menu, JSON_UNESCAPED_UNICODE)};
    var responselist = {:json_encode($responselist, JSON_UNESCAPED_UNICODE)};
</script>
<!--@formatter:on-->
