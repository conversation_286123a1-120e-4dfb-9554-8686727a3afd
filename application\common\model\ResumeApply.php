<?php

namespace app\common\model;

use think\Model;

class ResumeApply extends Model
{
    // 表名
    protected $name = 'resume_apply';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = false;

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 追加属性
    protected $append = [
        'status_text',
        'create_time_text',
        'update_time_text'
    ];

    // 状态文本
    public function getStatusTextAttr($value, $data)
    {
        $status = isset($data['status']) ? $data['status'] : '';
        $list = [
            '待处理' => '待处理',
            '已处理' => '已处理', 
            '未选中' => '未选中',
            '已选中' => '已选中'
        ];
        return isset($list[$status]) ? $list[$status] : '';
    }
    
    // 创建时间文本
    public function getCreateTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['create_time']) ? $data['create_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    // 更新时间文本
    public function getUpdateTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['update_time']) ? $data['update_time'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    // 用户关联
    public function user()
    {
        return $this->belongsTo('app\\common\\model\\User', 'user_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    
    // 简历关联
    public function resume()
    {
        return $this->belongsTo('app\\common\\model\\Resume', 'resume_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
    
    // 职位关联
    public function job()
    {
        return $this->belongsTo('app\\common\\model\\Job', 'job_id', 'id', [], 'LEFT')->setEagerlyType(0);
    }
} 