import { get, post } from './request';

/**
 * 获取用户面试列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.status - 面试状态
 * @param {string} params.interview_type - 面试类型
 */
export const getMyInterviews = (params = {}) => {
  // 简单记录请求参数
  console.log('API getMyInterviews 原始参数:', JSON.stringify(params));
  
  // 直接传递参数，不做额外处理
  return get('/interview/my_interviews', params, { auth: true });
};

/**
 * 获取面试详情
 * @param {number} id - 面试ID
 */
export const getInterviewDetail = (id) => {
  return get('/interview/detail', { id }, { auth: true });
};

/**
 * 确认参加面试
 * @param {number} id - 面试ID
 */
export const confirmInterview = (id) => {
  return post('/interview/confirm', { id }, { auth: true });
};

/**
 * 取消面试
 * @param {number} id - 面试ID
 * @param {string} reason - 取消原因
 */
export const cancelInterview = (data) => {
  return post('/interview/cancel', data, { auth: true });
};

/**
 * 获取面试统计数据
 */
export const getInterviewStats = () => {
  return get('/interview/stats', {}, { auth: true });
};

/**
 * 获取面试状态列表（诊断接口）
 */
export const getStatusList = () => {
  return get('/interview/status_list', {}, { auth: true });
};

export default {
  getMyInterviews,
  getInterviewDetail,
  confirmInterview,
  cancelInterview,
  getInterviewStats,
  getStatusList
}; 