const app = getApp();
const auth = require('../utils/auth');

/**
 * 网络请求封装
 * @param {Object} options - 请求参数
 * @param {string} options.url - 请求地址
 * @param {string} options.method - 请求方法
 * @param {Object} options.data - 请求数据
 * @param {boolean} options.loading - 是否显示加载提示
 * @param {boolean} options.auth - 是否需要认证
 */
const request = (options) => {
  const { url, method = 'GET', data = {}, loading = true, auth: requireAuth = false } = options;
  
  return new Promise((resolve, reject) => {
    // 显示加载提示
    if (loading) {
      wx.showLoading({
        title: '加载中...',
        mask: true
      });
    }
    
    const header = {};
    let requestData = {...data};
    
    // 添加认证信息
    if (requireAuth) {
      // 优先使用全局token
      let token = app.globalData.token;
      // 如果全局token不存在，尝试从存储获取
      if (!token) {
        token = wx.getStorageSync('token');
        // 如果找到了token，更新全局状态
        if (token) {
          app.globalData.token = token;
          app.globalData.isLogin = true;
        }
      }
      
      if (!token) {
        wx.hideLoading();
        console.error('[请求错误] 未找到认证Token', { url, method });
        // 未登录，跳转到登录页
        wx.navigateTo({
          url: '/pages/user/login/index'
        });
        reject({ code: 401, message: '请先登录' });
        return;
      }
      
      // 根据FastAdmin源码，尝试三种方式传递token:
      
      // 方式1: 通过请求参数传递 - 最可靠的方式
      if (method.toUpperCase() === 'GET') {
        // GET请求将token添加到URL参数
        requestData.token = token;
      } else {
        // POST等请求将token添加到请求体
        requestData.token = token;
      }
      
      // 方式2: 通过header传递 - 尝试多种可能的header名称
      header['Token'] = token;
      header['X-Token'] = token;
      
      console.log('[请求] 添加认证信息', { token });
    }
    
    // 设置通用的Content-Type
    if (method.toUpperCase() !== 'GET') {
      header['Content-Type'] = 'application/x-www-form-urlencoded';
    }
    
    const fullUrl = app.globalData.baseApi + url;
    console.log(`[请求] ${method} ${fullUrl}`, { data: requestData, requireAuth });
    
    // 添加更详细的日志输出
    if (method.toUpperCase() === 'GET') {
      console.log(`[请求详情] 完整URL: ${fullUrl}?${Object.keys(requestData).map(key => `${key}=${encodeURIComponent(requestData[key])}`).join('&')}`);
    }
    
    // 确保GET请求的中文参数正确编码
    let finalData = requestData;
    if (method.toUpperCase() === 'GET') {
      // 对GET请求的参数进行编码处理
      finalData = {};
      Object.keys(requestData).forEach(key => {
        const value = requestData[key];
        // 只对字符串类型的值进行编码
        if (typeof value === 'string') {
          // 不要重复编码已经编码过的值
          try {
            const decoded = decodeURIComponent(value);
            if (decoded === value) {
              // 值没有被编码过，需要编码
              finalData[key] = encodeURIComponent(value);
            } else {
              // 值已经被编码过，保持原样
              finalData[key] = value;
            }
          } catch (e) {
            // 解码失败，说明不是有效的编码值，需要编码
            finalData[key] = encodeURIComponent(value);
          }
        } else {
          finalData[key] = value;
        }
      });
      
      // 特别处理status参数，确保它被正确编码
      if (finalData.status) {
        console.log(`编码前status: "${finalData.status}"`);
        finalData.status = encodeURIComponent(finalData.status);
        console.log(`编码后status: "${finalData.status}"`);
      }
    }
    
    // 发起请求
    wx.request({
      url: fullUrl,
      method,
      data: finalData,
      header,
      success: (res) => {
        // 隐藏加载提示
        if (loading) {
          wx.hideLoading();
        }
        
        // 记录完整响应以便调试
        console.log(`[完整响应] ${method} ${url}`, { 
          statusCode: res.statusCode, 
          data: res.data,
          header: res.header
        });
        
        // 请求成功
        if (res.statusCode === 200) {
          // 检查响应格式，确保至少有code字段
          if (res.data === undefined || res.data === null) {
            console.error('[响应错误] 响应内容为空', { url });
            reject({ code: -1, message: '服务器返回空响应' });
            return;
          }
          
          // 如果返回的是HTML而不是JSON，可能是服务器错误
          if (typeof res.data === 'string' && res.data.indexOf('<!DOCTYPE html>') === 0) {
            console.error('[响应错误] 服务器返回了HTML而不是JSON', { url });
            reject({ code: -1, message: '服务器响应格式错误，返回了HTML页面' });
            return;
          }
          
          if (res.data.code === undefined) {
            console.error('[响应错误] 缺少code字段', { url, data: res.data });
            reject({ code: -1, message: '服务器响应格式错误' });
            return;
          }
          
          // 检查业务逻辑错误
          if (res.data && res.data.code === 0) {
            console.warn('[业务错误]', { url, response: res.data });
            reject({ 
              code: res.data.code || 0, 
              message: res.data.msg || '操作失败',
              data: res.data
            });
            return;
          }
          resolve(res.data);
        } else if (res.statusCode === 401) {
          // 未授权或token过期
          console.error('[认证错误] 401 未授权', { url, response: res.data });
          auth.logout(); // 使用正确导入的auth模块的logout方法
          
          wx.showToast({
            title: '登录状态已过期，请重新登录',
            icon: 'none'
          });
          
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/user/login/index'
            });
          }, 1500);
          
          reject({ code: res.statusCode, message: '登录状态已过期' });
        } else if (res.statusCode === 404) {
          // 接口不存在
          console.error('[请求错误] 404 接口不存在', { url, response: res.data });
          
          // 如果返回的是HTML，提取更有用的错误信息
          let errorMsg = '请求的接口不存在';
          if (typeof res.data === 'string' && res.data.indexOf('<!DOCTYPE html>') === 0) {
            errorMsg = '接口不存在或未实现';
          }
          
          reject({ code: res.statusCode, message: errorMsg, data: res.data });
        } else if (res.statusCode === 500) {
          // 服务器内部错误
          console.error('[服务器错误] 500 内部错误', { url, response: res.data });
          
          let errorMsg = '服务器内部错误';
          
          // 尝试从不同格式中获取错误信息
          if (res.data) {
            if (typeof res.data === 'string') {
              // 如果是字符串，可能是HTML错误页面
              const errorMatch = res.data.match(/<p>.*?Exception:(.*?)<\/p>/);
              if (errorMatch && errorMatch[1]) {
                errorMsg = '服务器错误: ' + errorMatch[1].trim();
              } else {
                // 截取部分内容作为错误提示
                errorMsg = res.data.substring(0, 100);
              }
            } else if (res.data.msg) {
              // FastAdmin API 常用格式
              errorMsg = res.data.msg;
            } else if (res.data.message) {
              // 标准JSON错误格式
              errorMsg = res.data.message;
            } else if (res.data.error) {
              // 另一种常见错误格式
              errorMsg = res.data.error;
            }
          }
          
          reject({ code: res.statusCode, message: errorMsg, data: res.data });
        } else {
          // 其他错误
          console.error('[请求错误]', { 
            url, 
            statusCode: res.statusCode, 
            response: res.data 
          });
          
          let errorMsg = '请求失败';
          if (res.data && res.data.msg) {
            errorMsg = res.data.msg;
          } else if (res.data && res.data.message) {
            errorMsg = res.data.message;
          }
          
          reject({ code: res.statusCode, message: errorMsg, data: res.data });
        }
      },
      fail: (err) => {
        // 隐藏加载提示
        if (loading) {
          wx.hideLoading();
        }
        
        console.error('[网络错误]', { url, error: err });
        
        wx.showToast({
          title: '网络请求失败',
          icon: 'none'
        });
        
        reject({ code: -1, message: err.errMsg || '网络请求失败' });
      }
    });
  });
};

// 封装常用请求方法
export const get = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  });
};

export const post = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  });
};

export const put = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  });
};

export const del = (url, data = {}, options = {}) => {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  });
};

export default {
  request,
  get,
  post,
  put,
  del
}; 