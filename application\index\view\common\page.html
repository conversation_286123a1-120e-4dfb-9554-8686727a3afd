{extend name="common/base" /}

{block name="style"}
<style>
    .page-header {
        background: var(--gradient-primary);
        padding: 6rem 0 3rem;
        margin-bottom: 2rem;
        color: white;
        position: relative;
        overflow: hidden;
    }

    .page-header::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" fill="rgba(255,255,255,0.1)"/></svg>');
        opacity: 0.1;
    }

    .page-header h1 {
        font-weight: 700;
        font-size: 2.5rem;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.2);
    }

    .page-content {
        padding: 2rem 0 4rem;
    }

    .page-content h2 {
        color: var(--primary-color);
        margin-top: 2rem;
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .page-content p {
        margin-bottom: 1.5rem;
        line-height: 1.7;
        color: var(--text-color);
    }
    
    @media (max-width: 768px) {
        .page-header {
            padding: 5rem 0 2rem;
        }
        
        .page-header h1 {
            font-size: 2rem;
        }
    }
</style>
{/block}

{block name="content"}
<!-- 页面头部 -->
<div class="page-header">
    <div class="container">
        <h1>{$page_title|default='页面标题'}</h1>
    </div>
</div>

<!-- 页面内容 -->
<div class="page-content">
    <div class="container">
        {$page_content|html_entity_decode}
    </div>
</div>
{/block} 