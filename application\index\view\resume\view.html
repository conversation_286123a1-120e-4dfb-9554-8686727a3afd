<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{$resume.name|htmlentities} - 简历详情</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-bg: #f8f9fa;
            --dark-bg: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f1c40f;
            --border-radius: 12px;
            --box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }
        
        body {
            background-color: var(--light-bg);
            color: var(--primary-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            padding-top: 70px;
        }

        .navbar {
        background-color: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px);
        box-shadow: var(--box-shadow);
        padding: 1rem 0;
        transition: var(--transition);
    }

    .navbar.scrolled {
        padding: 0.5rem 0;
    }

    .navbar-brand {
        font-weight: 700;
        color: var(--primary-color) !important;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
        letter-spacing: -0.5px;
    }

    .navbar-brand i {
        color: var(--primary-color);
        margin-right: 0.5rem;
        font-size: 1.8rem;
    }

    .nav-link {
        color: var(--text-primary) !important;
        font-weight: 500;
        padding: 0.75rem 1.25rem !important;
        margin: 0 0.25rem;
        border-radius: var(--border-radius);
        transition: var(--transition);
        position: relative;
    }

    .nav-link::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 2px;
        background-color: var(--primary-color);
        transition: var(--transition);
    }

    .nav-link:hover::after {
        width: 80%;
    }

    .nav-link:hover {
        background-color: rgba(26, 115, 232, 0.08);
        color: var(--primary-color) !important;
    }

    .nav-link.active {
        background-color: var(--primary-color) !important;
        color: white !important;
    }

    .nav-link.active::after {
        display: none;
    }

    @media (max-width: 991.98px) {
        .navbar-collapse {
            background-color: rgba(255, 255, 255, 0.98);
            padding: 1rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-top: 1rem;
            backdrop-filter: blur(10px);
        }

        .nav-link {
            padding: 1rem !important;
            margin: 0.5rem 0;
        }

        .navbar-buttons {
            margin-top: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .navbar-buttons .btn {
            width: 100%;
        }
    }
        .card {
            border: none;
            box-shadow: var(--box-shadow);
            border-radius: var(--border-radius);
            transition: var(--transition);
            margin-bottom: 1.5rem;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 1.5rem;
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
        }

        .card-body {
            padding: 1.5rem;
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--secondary-color);
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 0.75rem;
            color: var(--secondary-color);
        }

        .btn {
            border-radius: var(--border-radius);
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .btn-primary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
            transform: translateY(-1px);
        }
        
        .profile-header {
            display: flex;
            align-items: center;
            margin-bottom: 2rem;
        }
        
        .profile-avatar {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            overflow: hidden;
            margin-right: 2rem;
            border: 3px solid white;
            box-shadow: var(--box-shadow);
        }
        
        .profile-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .profile-info h2 {
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }
        
        .profile-info p {
            color: var(--secondary-color);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }
        
        .resume-section {
            margin-bottom: 2rem;
        }
        
        .info-group {
            margin-bottom: 1.5rem;
        }
        
        .info-label {
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: 0.3rem;
        }
        
        .info-value {
            color: #495057;
        }
        
        .resume-item {
            padding: 1rem;
            border-left: 3px solid var(--secondary-color);
            background-color: white;
            margin-bottom: 1rem;
            border-radius: 0 var(--border-radius) var(--border-radius) 0;
            box-shadow: var(--box-shadow);
        }
        
        .resume-item-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
        }
        
        .resume-item-title {
            font-weight: 600;
            color: var(--primary-color);
        }
        
        .resume-item-subtitle {
            color: var(--secondary-color);
            font-weight: 500;
        }
        
        .resume-item-date {
            color: #6c757d;
            font-size: 0.9rem;
        }
        
        .resume-item-content {
            margin-top: 0.5rem;
            color: #495057;
        }
        
        .language-level {
            display: flex;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .language-name {
            width: 80px;
            font-weight: 500;
            color: var(--primary-color);
        }
        
        .language-dots {
            display: flex;
            margin-left: 1rem;
        }
        
        .language-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 5px;
            background-color: #e9ecef;
        }
        
        .language-dot.active {
            background-color: var(--secondary-color);
        }
        
        .photo-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            gap: 1rem;
        }
        
        .photo-item {
            border-radius: var(--border-radius);
            overflow: hidden;
            box-shadow: var(--box-shadow);
            cursor: pointer;
            transition: var(--transition);
        }
        
        .photo-item:hover {
            transform: scale(1.05);
        }
        
        .photo-item img {
            width: 100%;
            height: 150px;
            object-fit: cover;
        }
        
        @media (max-width: 768px) {
            .profile-header {
                flex-direction: column;
                text-align: center;
            }
            
            .profile-avatar {
                margin-right: 0;
                margin-bottom: 1rem;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
<nav class="navbar navbar-expand-lg navbar-light fixed-top">
    <div class="container">
        <a class="navbar-brand" href="{:url('index/index/index')}">
            <i class="fas fa-briefcase"></i>{$site.name|htmlentities}
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='Index'}active{/eq}" href="{:url('index/index/index')}">
                        <i class="fas fa-home"></i>首页
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='Job'}active{/eq}" href="{:url('index/job/list')}">
                        <i class="fas fa-list me-1"></i>职位列表
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='News'}active{/eq}" href="{:url('index/resume/list')}">
                        <i class="fas fa-newspaper me-1"></i>我的简历
                    </a>
                </li>
            </ul>
            <div class="navbar-buttons">
                {if $user}
                <!-- 已登录状态 -->
                <a href="{:url('index/user/index')}" class="btn btn-outline-primary me-2">
                    <i class="fas fa-user me-1"></i>个人中心
                </a>
                <a href="javascript:;" class="btn btn-outline-danger" id="btn-logout">
                    <i class="fas fa-sign-out-alt me-1"></i>退出登录
                </a>
                <!-- 退出登录表单 -->
                <form id="logout-form" action="{:url('index/user/logout')}" method="post" style="display: none;">
                    {:token()}
                </form>
                {else}
                <!-- 未登录状态 -->
                <a href="{:url('index/user/login')}" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-1"></i>登录/注册
                </a>
                {/if}
            </div>
        </div>
    </div>
</nav>

    <!-- 主要内容 -->
    <div class="container">
        <div class="mb-4">
            <a href="{:url('index/resume/list')}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>返回列表
            </a>
            <a href="{:url('index/resume/edit', ['id' => $resume.id])}" class="btn btn-primary ms-2">
                <i class="fas fa-edit me-2"></i>编辑简历
            </a>
        </div>
        
        <!-- 个人信息头部 -->
        <div class="card">
            <div class="card-body">
                <div class="profile-header">
                    <div class="profile-avatar">
                        {if condition="$resume.avatar"}
                            <img src="{$resume.avatar}" alt="{$resume.name|htmlentities}">
                        {else}
                            <div style="width: 100%; height: 100%; background-color: #e9ecef; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-user" style="font-size: 3rem; color: #adb5bd;"></i>
                            </div>
                        {/if}
                    </div>
                    <div class="profile-info">
                        <h2>{$resume.name|htmlentities}</h2>
                        {if condition="$resume.applied_position"}
                            <p><i class="fas fa-briefcase me-2"></i>{$resume.applied_position|htmlentities}</p>
                        {elseif condition="$resume.intended_position"}
                            <p><i class="fas fa-bullseye me-2"></i>{$resume.intended_position|htmlentities}</p>
                        {/if}
                        <div class="d-flex flex-wrap gap-3 mt-3">
                            {if condition="$resume.gender"}
                                <span><i class="fas fa-venus-mars me-1"></i> {$resume.gender|htmlentities}</span>
                            {/if}
                            
                            {if condition="$resume.age"}
                                <span><i class="fas fa-birthday-cake me-1"></i> {$resume.age}岁</span>
                            {/if}
                            
                            {if condition="$resume.phone"}
                                <span><i class="fas fa-phone me-1"></i> {$resume.phone|htmlentities}</span>
                            {/if}
                            
                            {if condition="$resume.marital_status"}
                                <span><i class="fas fa-heart me-1"></i> {$resume.marital_status|htmlentities}</span>
                            {/if}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 基本信息 -->
        <div class="card resume-section">
            <div class="card-header">
                <h5 class="section-title mb-0">
                    <i class="fas fa-user"></i>基本信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="info-group">
                            <div class="info-label">身份证号</div>
                            <div class="info-value">{$resume.id_card|default='未填写'|htmlentities}</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-group">
                            <div class="info-label">民族</div>
                            <div class="info-value">{$resume.ethnicity|default='未填写'|htmlentities}</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-group">
                            <div class="info-label">出生日期</div>
                            <div class="info-value">{$resume.birth_date|default='未填写'|htmlentities}</div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="info-group">
                            <div class="info-label">身高</div>
                            <div class="info-value">{$resume.height|default='未填写'} cm</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-group">
                            <div class="info-label">体重</div>
                            <div class="info-value">{$resume.weight|default='未填写'} kg</div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="info-group">
                            <div class="info-label">婚姻状况</div>
                            <div class="info-value">{$resume.marital_status|default='未填写'|htmlentities}</div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-group">
                            <div class="info-label">户口所在地</div>
                            <div class="info-value">{$resume.hukou_location|default='未填写'|htmlentities}</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="info-group">
                            <div class="info-label">常住地址</div>
                            <div class="info-value">{$resume.residence_address|default='未填写'|htmlentities}</div>
                        </div>
                    </div>
                </div>
                
                {if condition="$resume.special_certificate"}
                <div class="row">
                    <div class="col-md-12">
                        <div class="info-group">
                            <div class="info-label">特殊职业资格证</div>
                            <div class="info-value">{$resume.special_certificate|htmlentities}</div>
                        </div>
                    </div>
                </div>
                {/if}
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="info-group">
                            <div class="info-label">自我评价</div>
                            <div class="info-value">{$resume.self_evaluation|default='未填写'|htmlentities}</div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-12">
                        <div class="info-group">
                            <div class="info-label">兴趣爱好</div>
                            <div class="info-value">{$resume.hobbies|default='未填写'|htmlentities}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 语言能力 -->
        <div class="card resume-section">
            <div class="card-header">
                <h5 class="section-title mb-0">
                    <i class="fas fa-language"></i>语言能力
                </h5>
            </div>
            <div class="card-body">
                <div class="language-level">
                    <div class="language-name">粤语</div>
                    <div class="language-level-value">{$resume.cantonese_level|default='未填写'|htmlentities}</div>
                    <div class="language-dots">
                        {switch name="resume.cantonese_level"}
                            {case value="不会"}
                                <div class="language-dot"></div>
                                <div class="language-dot"></div>
                                <div class="language-dot"></div>
                            {/case}
                            {case value="一般"}
                                <div class="language-dot active"></div>
                                <div class="language-dot active"></div>
                                <div class="language-dot"></div>
                            {/case}
                            {case value="熟练"}
                                <div class="language-dot active"></div>
                                <div class="language-dot active"></div>
                                <div class="language-dot active"></div>
                            {/case}
                            {default /}
                                <div class="language-dot"></div>
                                <div class="language-dot"></div>
                                <div class="language-dot"></div>
                        {/switch}
                    </div>
                </div>
                
                <div class="language-level">
                    <div class="language-name">国语</div>
                    <div class="language-level-value">{$resume.mandarin_level|default='未填写'|htmlentities}</div>
                    <div class="language-dots">
                        {switch name="resume.mandarin_level"}
                            {case value="不会"}
                                <div class="language-dot"></div>
                                <div class="language-dot"></div>
                                <div class="language-dot"></div>
                            {/case}
                            {case value="一般"}
                                <div class="language-dot active"></div>
                                <div class="language-dot active"></div>
                                <div class="language-dot"></div>
                            {/case}
                            {case value="熟练"}
                                <div class="language-dot active"></div>
                                <div class="language-dot active"></div>
                                <div class="language-dot active"></div>
                            {/case}
                            {default /}
                                <div class="language-dot"></div>
                                <div class="language-dot"></div>
                                <div class="language-dot"></div>
                        {/switch}
                    </div>
                </div>
                
                <div class="language-level">
                    <div class="language-name">英语</div>
                    <div class="language-level-value">{$resume.english_level|default='未填写'|htmlentities}</div>
                    <div class="language-dots">
                        {switch name="resume.english_level"}
                            {case value="不会"}
                                <div class="language-dot"></div>
                                <div class="language-dot"></div>
                                <div class="language-dot"></div>
                            {/case}
                            {case value="一般"}
                                <div class="language-dot active"></div>
                                <div class="language-dot active"></div>
                                <div class="language-dot"></div>
                            {/case}
                            {case value="熟练"}
                                <div class="language-dot active"></div>
                                <div class="language-dot active"></div>
                                <div class="language-dot active"></div>
                            {/case}
                            {default /}
                                <div class="language-dot"></div>
                                <div class="language-dot"></div>
                                <div class="language-dot"></div>
                        {/switch}
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 联系人信息 -->
        {if condition="$resume.contact_name"}
        <div class="card resume-section">
            <div class="card-header">
                <h5 class="section-title mb-0">
                    <i class="fas fa-address-book"></i>联系人信息
                </h5>
            </div>
            <div class="card-body">
                {php}
                $relations = explode('|', $resume['contact_relation'] ?? '');
                $names = explode('|', $resume['contact_name'] ?? '');
                $ages = explode('|', $resume['contact_age'] ?? '');
                $jobs = explode('|', $resume['contact_job'] ?? '');
                
                // 确保所有数组长度一致
                $maxLength = max(count($relations), count($names), count($ages), count($jobs));
                $relations = array_pad($relations, $maxLength, '');
                $names = array_pad($names, $maxLength, '');
                $ages = array_pad($ages, $maxLength, '');
                $jobs = array_pad($jobs, $maxLength, '');
                {/php}
                
                {for start="0" end="count($names)" name="i"}
                {if condition="$names[$i]"}
                <div class="resume-item">
                    <div class="resume-item-header">
                        <div class="resume-item-title">{$names[$i]|htmlentities}</div>
                        <div class="resume-item-subtitle">{$relations[$i]|default=''|htmlentities}</div>
                    </div>
                    <div class="resume-item-content">
                        {if condition="!empty($ages[$i])"}
                        <div>年龄：{$ages[$i]}</div>
                        {/if}
                        {if condition="!empty($jobs[$i])"}
                        <div>工作：{$jobs[$i]|htmlentities}</div>
                        {/if}
                    </div>
                </div>
                {/if}
                {/for}
            </div>
        </div>
        {/if}
        
        <!-- 教育背景 -->
        {if condition="$resume.education_school"}
        <div class="card resume-section">
            <div class="card-header">
                <h5 class="section-title mb-0">
                    <i class="fas fa-graduation-cap"></i>教育背景
                </h5>
            </div>
            <div class="card-body">
                {php}
                $starts = explode('|', $resume['education_start'] ?? '');
                $ends = explode('|', $resume['education_end'] ?? '');
                $schools = explode('|', $resume['education_school'] ?? '');
                $majors = explode('|', $resume['education_major'] ?? '');
                $degrees = explode('|', $resume['graduation_education'] ?? '');

                // 确保所有数组长度一致
                $maxLength = max(count($starts), count($ends), count($schools), count($majors), count($degrees));
                $starts = array_pad($starts, $maxLength, '');
                $ends = array_pad($ends, $maxLength, '');
                $schools = array_pad($schools, $maxLength, '');
                $majors = array_pad($majors, $maxLength, '');
                $degrees = array_pad($degrees, $maxLength, '');
                {/php}
                
                {for start="0" end="count($schools)" name="i"}
                {if condition="$schools[$i]"}
                <div class="resume-item">
                    <div class="resume-item-header">
                        <div class="resume-item-title">{$schools[$i]|htmlentities}</div>
                        <div class="resume-item-date">
                            {$starts[$i]} 至 {$ends[$i]}
                        </div>
                    </div>
                    <div class="resume-item-subtitle">
                        {if condition="!empty($majors[$i])"}
                        {$majors[$i]|htmlentities}
                        {/if}
                        {if condition="!empty($degrees[$i])"}
                        | {$degrees[$i]|htmlentities}
                        {/if}
                    </div>
                </div>
                {/if}
                {/for}
            </div>
        </div>
        {/if}
        
        <!-- 工作经历 -->
        {if condition="$resume.job_company"}
        <div class="card resume-section">
            <div class="card-header">
                <h5 class="section-title mb-0">
                    <i class="fas fa-briefcase"></i>工作经历
                </h5>
            </div>
            <div class="card-body">
                {php}
                $companies = explode('|', $resume['job_company'] ?? '');
                $positions = explode('|', $resume['job_position'] ?? '');
                $descriptions = explode('|', $resume['job_description'] ?? '');
                $starts = explode('|', $resume['job_start'] ?? '');
                $ends = explode('|', $resume['job_end'] ?? '');
                
                // 确保所有数组长度一致
                $maxLength = max(count($companies), count($positions), count($descriptions), count($starts), count($ends));
                $companies = array_pad($companies, $maxLength, '');
                $positions = array_pad($positions, $maxLength, '');
                $descriptions = array_pad($descriptions, $maxLength, '');
                $starts = array_pad($starts, $maxLength, '');
                $ends = array_pad($ends, $maxLength, '');
                {/php}
                
                {for start="0" end="count($companies)" name="i"}
                {if condition="$companies[$i]"}
                <div class="resume-item">
                    <div class="resume-item-header">
                        <div class="resume-item-title">{$companies[$i]|htmlentities}</div>
                        <div class="resume-item-date">
                            {$starts[$i]} 至 {$ends[$i]}
                        </div>
                    </div>
                    {if condition="!empty($positions[$i])"}
                    <div class="resume-item-subtitle">{$positions[$i]|htmlentities}</div>
                    {/if}
                    {if condition="!empty($descriptions[$i])"}
                    <div class="resume-item-content">
                        {$descriptions[$i]|htmlentities}
                    </div>
                    {/if}
                </div>
                {/if}
                {/for}
            </div>
        </div>
        {/if}
        
        <!-- 证件信息 -->
        <div class="card resume-section">
            <div class="card-header">
                <h5 class="section-title mb-0">
                    <i class="fas fa-id-card"></i>证件信息
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    {if condition="$resume.hk_macau_passport"}
                    <div class="col-md-6">
                        <div class="info-group">
                            <div class="info-label">港澳通行证编号</div>
                            <div class="info-value">{$resume.hk_macau_passport|htmlentities}</div>
                        </div>
                    </div>
                    {/if}
                    
                    {if condition="$resume.hk_macau_passport_expiry"}
                    <div class="col-md-6">
                        <div class="info-group">
                            <div class="info-label">港澳通行证到期时间</div>
                            <div class="info-value">{$resume.hk_macau_passport_expiry|htmlentities}</div>
                        </div>
                    </div>
                    {/if}
                </div>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="info-label mb-2">身份证正面</div>
                        {if condition="$resume.id_card_front"}
                            <div class="photo-item">
                                <img src="{$resume.id_card_front}" alt="身份证正面">
                            </div>
                        {else}
                            <div class="text-muted">未上传</div>
                        {/if}
                    </div>
                    <div class="col-md-6">
                        <div class="info-label mb-2">身份证反面</div>
                        {if condition="$resume.id_card_back"}
                            <div class="photo-item">
                                <img src="{$resume.id_card_back}" alt="身份证反面">
                            </div>
                        {else}
                            <div class="text-muted">未上传</div>
                        {/if}
                    </div>
                </div>
                
                {if condition="$resume.hk_macau_passport_front || $resume.hk_macau_passport_back"}
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="info-label mb-2">港澳通行证正面</div>
                        {if condition="$resume.hk_macau_passport_front"}
                            <div class="photo-item">
                                <img src="{$resume.hk_macau_passport_front}" alt="港澳通行证正面">
                            </div>
                        {else}
                            <div class="text-muted">未上传</div>
                        {/if}
                    </div>
                    <div class="col-md-6">
                        <div class="info-label mb-2">港澳通行证反面</div>
                        {if condition="$resume.hk_macau_passport_back"}
                            <div class="photo-item">
                                <img src="{$resume.hk_macau_passport_back}" alt="港澳通行证反面">
                            </div>
                        {else}
                            <div class="text-muted">未上传</div>
                        {/if}
                    </div>
                </div>
                {/if}
            </div>
        </div>
        
        <!-- 照片 -->
        <div class="card resume-section">
            <div class="card-header">
                <h5 class="section-title mb-0">
                    <i class="fas fa-camera"></i>照片
                </h5>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <div class="info-label mb-2">全身照</div>
                        {if condition="$resume.full_body_photo"}
                            <div class="photo-item">
                                <img src="{$resume.full_body_photo}" alt="全身照">
                            </div>
                        {else}
                            <div class="text-muted">未上传</div>
                        {/if}
                    </div>
                </div>
                
                {if condition="$resume.additional_photos"}
                <div class="row">
                    <div class="col-12">
                        <div class="info-label mb-2">补充照片</div>
                        <div class="photo-gallery">
                            {php}
                            $photos = explode(',', $resume['additional_photos'] ?? '');
                            {/php}
                            
                            {volist name="photos" id="photo"}
                            {if condition="$photo"}
                            <div class="photo-item">
                                <img src="{$photo}" alt="补充照片">
                            </div>
                            {/if}
                            {/volist}
                        </div>
                    </div>
                </div>
                {/if}
            </div>
        </div>
    </div>

    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script>
        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
        
        // 退出登录按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const logoutBtn = document.getElementById('btn-logout');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function() {
                    document.getElementById('logout-form').submit();
                });
            }
        });
    </script> 
</body>
</html> 