<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 面试管理管理
 *
 * @icon fa fa-circle-o
 */
class Jobinterview extends Backend
{

    /**
     * Jobinterview模型对象
     * @var \app\common\model\Jobinterview
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\Jobinterview;
        $this->view->assign("interviewTypeList", $this->model->getInterviewTypeList());
        $this->view->assign("progressList", $this->model->getProgressList());
        $this->view->assign("statusList", $this->model->getStatusList());
        
        // 获取岗位列表
        $jobsModel = new \app\common\model\Jobs;
        $jobsList = $jobsModel->where('status', '上架')
            ->field('id, job_name, job_code, company, salary_range, total_fee')
            ->order('id', 'desc')
            ->select();
        $this->view->assign("jobsList", $jobsList);

        // 获取简历列表
        $resumeModel = new \app\common\model\Resume;
        $resumeList = $resumeModel->where('status', '已发布')
            ->field('id, name, gender, age, applied_position')
            ->order('id', 'desc')
            ->select();
        $this->view->assign("resumeList", $resumeList);
    }

    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            // 如果传入了job_id参数，添加岗位筛选条件
            $job_id = $this->request->get('job_id');
            if ($job_id) {
                $where[] = ['job_id', '=', $job_id];
            }

            $list = $this->model
                    ->where($where)
                    ->order($sort, $order)
                    ->paginate($limit);

            $result = array("total" => $list->total(), "rows" => $list->items());
            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 获取岗位编号
     */
    public function getJobCode()
    {
        $jobName = $this->request->post('job_name');
        $job = \app\common\model\Jobs::where('job_name', $jobName)
            ->where('status', '展示')
            ->find();
        if ($job) {
            $this->success('', ['job_code' => $job['job_code']]);
        } else {
            $this->error(__('Job not found'));
        }
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                // 处理多选的面试人员
                if (isset($params['interviewee']) && is_array($params['interviewee'])) {
                    $params['interviewee'] = implode(',', $params['interviewee']);
                }
                
                // 处理多选的简历ID
                if (isset($params['resume_id']) && is_array($params['resume_id'])) {
                    $params['resume_id'] = implode(',', $params['resume_id']);
                }
                
                // 处理每个面试人员的进度
                if (isset($params['progress']) && is_array($params['progress'])) {
                    $progressData = [];
                    foreach ($params['progress'] as $interviewee => $progress) {
                        if (!empty($interviewee) && !empty($progress)) {
                            $progressData[] = $interviewee . ':' . $progress;
                        }
                    }
                    $params['progress'] = !empty($progressData) ? implode(',', $progressData) : '';
                }
                $result = $this->model->allowField(true)->save($params);
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error($this->model->getError());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                // 处理多选的面试人员
                if (isset($params['interviewee']) && is_array($params['interviewee'])) {
                    $params['interviewee'] = implode(',', $params['interviewee']);
                }
                
                // 处理多选的简历ID
                if (isset($params['resume_id']) && is_array($params['resume_id'])) {
                    $params['resume_id'] = implode(',', $params['resume_id']);
                }
                
                // 处理每个面试人员的进度
                if (isset($params['progress']) && is_array($params['progress'])) {
                    // 将JSON格式的进度数据转换为"姓名:状态"的逗号分隔格式
                    $progressData = [];
                    $resumeIds = isset($params['resume_id']) ? explode(',', $params['resume_id']) : [];
                    $interviewees = isset($params['interviewee']) ? explode(',', $params['interviewee']) : [];
                    
                    // 创建简历ID到姓名的映射
                    $resumeIdToName = [];
                    for ($i = 0; $i < count($resumeIds); $i++) {
                        if (isset($interviewees[$i])) {
                            $resumeIdToName[$resumeIds[$i]] = $interviewees[$i];
                        }
                    }
                    
                    foreach ($params['progress'] as $resumeId => $status) {
                        // 查找对应的姓名
                        $name = isset($resumeIdToName[$resumeId]) ? $resumeIdToName[$resumeId] : $resumeId;
                        if (!empty($name) && !empty($status)) {
                            $progressData[] = $name . ':' . $status;
                        }
                    }
                    
                    $params['progress'] = !empty($progressData) ? implode(',', $progressData) : '';
                }
                
                $result = $row->allowField(true)->save($params);
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error($row->getError());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        
        // 处理多选的面试人员数据
        if (!empty($row['interviewee'])) {
            $row['interviewee'] = explode(',', $row['interviewee']);
        } else {
            $row['interviewee'] = [];
        }
        
        // 处理多选的简历ID数据
        if (!empty($row['resume_id'])) {
            $row['resume_id'] = explode(',', $row['resume_id']);
        } else {
            $row['resume_id'] = [];
        }
        
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 生成测试数据
     */
    public function generate_test_data()
    {
        $this->model->startTrans();
        try {
            // 创建一些测试数据
            $testData = [
                [
                    'title' => '前端开发工程师面试',
                    'job' => '前端开发工程师',
                    'job_code' => 'FE-001',
                    'interviewee' => 'admin,test',
                    'resume_id' => '1,2',
                    'progress' => 'admin:未面试,test:未面试',
                    'interview_time' => time() + 86400, // 明天
                    'interview_type' => '线上面试',
                    'status' => '未开始',
                    'interview_materials' => "面试准备资料：\n1. 请准备自我介绍\n2. 准备作品集展示\n3. 复习JavaScript基础知识",
                    'create_time' => time(),
                    'update_time' => time()
                ],
                [
                    'title' => '后端开发工程师面试',
                    'job' => '后端开发工程师',
                    'job_code' => 'BE-002',
                    'interviewee' => 'admin,test',
                    'resume_id' => '1,2',
                    'progress' => 'admin:已面试,test:未面试',
                    'interview_time' => time() - 86400, // 昨天
                    'interview_type' => '现场面试',
                    'status' => '进行中',
                    'interview_materials' => "面试准备资料：\n1. 请准备自我介绍\n2. 准备代码演示\n3. 复习PHP/MySQL知识",
                    'create_time' => time() - 172800, // 两天前
                    'update_time' => time() - 86400
                ],
                [
                    'title' => '产品经理面试',
                    'job' => '产品经理',
                    'job_code' => 'PM-003',
                    'interviewee' => 'admin',
                    'resume_id' => '1',
                    'progress' => 'admin:已通过',
                    'interview_time' => time() - 259200, // 三天前
                    'interview_type' => '线上面试',
                    'status' => '已结束',
                    'interview_materials' => "面试准备资料：\n1. 请准备自我介绍\n2. 准备产品案例分析\n3. 产品设计思路讲解",
                    'create_time' => time() - 345600, // 四天前
                    'update_time' => time() - 259200
                ]
            ];
            
            foreach ($testData as $data) {
                $this->model->create($data);
            }
            
            $this->model->commit();
            $this->success('测试数据生成成功');
        } catch (\Exception $e) {
            $this->model->rollback();
            $this->error('测试数据生成失败: ' . $e->getMessage());
        }
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}
