<!-- 简历导入页面 -->
<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead">
            <i class="fa fa-upload text-primary"></i>
            <strong>基于模板导入简历</strong>
            <small class="text-muted">支持Excel文件批量导入</small>
        </div>
        <div class="panel-tools">
            <a href="{:url('resume_import_template/index')}" class="btn btn-xs btn-default" target="_blank">
                <i class="fa fa-cog"></i> 管理模板
            </a>
        </div>
    </div>

    <!-- 优化后的样式 -->
    <style>
        /* 基础样式 */
        .import-container { padding: 20px; }
        .step-indicator { margin-bottom: 30px; }
        .step-item { display: inline-block; margin-right: 30px; position: relative; }
        .step-item:not(:last-child):after {
            content: '→';
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            color: #ddd;
            font-weight: bold;
        }
        .step-item.active { color: #337ab7; font-weight: bold; }
        .step-item.completed { color: #5cb85c; }

        /* 表单样式 */
        .form-section { margin-bottom: 30px; padding: 20px; background: #f9f9f9; border-radius: 5px; }
        .form-section h4 { margin-top: 0; color: #333; border-bottom: 2px solid #337ab7; padding-bottom: 10px; }
        .file-upload-area {
            border: 2px dashed #ddd;
            border-radius: 5px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .file-upload-area:hover { border-color: #337ab7; background-color: #f0f8ff; }
        .file-upload-area.dragover { border-color: #5cb85c; background-color: #f0fff0; }
        .file-selected { border-color: #5cb85c !important; background-color: #f0fff0 !important; }

        /* 模板信息样式 */
        .template-info {
            margin-top: 20px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        .template-card {
            border: 1px solid #e0e0e0;
            border-radius: 5px;
            overflow: hidden;
            background: #ffffff;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            margin-bottom: 15px;
            padding: 15px;
        }
        .template-card .card-header {
            background: #f8f9fa;
            padding: 12px 15px;
            font-weight: bold;
            color: #333;
            border-bottom: 1px solid #e0e0e0;
            margin: -15px -15px 15px -15px;
            position: relative;
            border-radius: 5px 5px 0 0;
        }
        .template-card .card-header .pull-right {
            position: absolute;
            right: 15px;
            top: 50%;
            transform: translateY(-50%);
        }
        .template-card .card-body {
            padding: 0;
            max-height: 300px;
            overflow-y: auto;
            background: #ffffff;
        }
        .mapping-table { margin: 0; }
        .mapping-table th { background: #f8f9fa; }
        .mapping-table td { padding: 8px 12px; }

        /* 预览样式 */
        #preview-container {
            display: none;
            margin-top: 30px;
            animation: slideDown 0.3s ease-out;
        }
        @keyframes slideDown {
            from { opacity: 0; transform: translateY(-20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .preview-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 5px 5px 0 0;
        }
        .preview-stats {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
            flex-wrap: wrap;
            gap: 2px;
        }
        .preview-stat {
            text-align: center;
            flex: 1;
            min-width: 65px;
            padding: 0 1px;
        }
        .preview-stat .number { font-size: 24px; font-weight: bold; }
        .preview-stat .label { font-size: 12px; opacity: 0.9; }

        .preview-content {
            background: white;
            border: 1px solid #ddd;
            border-top: none;
            max-height: 600px;
            overflow-y: auto;
        }
        .preview-resume {
            margin: 20px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .preview-resume h4 {
            margin: 0 0 15px 0;
            color: #337ab7;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
        }
        .preview-section { margin-bottom: 15px; }
        .preview-section .section-title {
            font-weight: bold;
            color: #555;
            margin-bottom: 10px;
            padding: 5px 10px;
            background: #f8f9fa;
            border-left: 3px solid #337ab7;
        }
        .preview-item {
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px dotted #eee;
        }
        .preview-item:last-child { border-bottom: none; }

        /* 图片预览样式 */
        .image-preview-container {
            text-align: center;
        }

        .image-preview-container img {
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid #ddd;
            border-radius: 4px;
        }

        .image-preview-container img:hover {
            border-color: #3c8dbc;
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        /* 模态框样式优化 */
        .modal-lg {
            width: 90%;
            max-width: 800px;
        }

        .modal-body img {
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        /* 加载和错误样式 */
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        .loading .spinner {
            font-size: 2em;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }

        /* 控制按钮样式 */
        .preview-controls {
            padding: 20px;
            text-align: center;
            background: #f8f9fa;
            border-top: 1px solid #ddd;
        }
        .btn-action {
            margin: 0 10px;
            min-width: 120px;
            padding: 10px 20px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .step-indicator { text-align: center; }
            .step-item { display: block; margin: 10px 0; }
            .step-item:after { display: none; }
            .preview-stats { flex-direction: column; }
            .preview-stat { margin: 10px 0; }
        }

        /* 工具提示样式 */
        .tooltip-icon {
            color: #999;
            cursor: help;
            margin-left: 5px;
        }
        .tooltip-icon:hover { color: #337ab7; }

        /* 状态指示器 */
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-success { background-color: #5cb85c; }
        .status-warning { background-color: #f0ad4e; }
        .status-error { background-color: #d9534f; }
        .status-info { background-color: #5bc0de; }

        /* 映射统计样式 */
        .mapping-stats {
            margin-bottom: 20px;
        }
        .stat-item {
            text-align: center;
            padding: 10px;
        }
        .stat-number {
            display: block;
            font-size: 24px;
            font-weight: bold;
            line-height: 1;
        }
        .stat-label {
            display: block;
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }
        .summary-stats {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .stat-box {
            padding: 10px;
        }
        .mapping-preview h5 {
            color: #333;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #eee;
        }
        .mapping-item {
            padding: 5px 0;
            border-bottom: 1px dotted #eee;
        }
        .mapping-item:last-child {
            border-bottom: none;
        }

        /* 表格行状态样式 */
        .mapping-table tr.success {
            background-color: #f0fff0;
        }
        .mapping-table tr.warning {
            background-color: #fffbf0;
        }
        .mapping-table tr.danger {
            background-color: #fff0f0;
        }
        .badge-success {
            background-color: #5cb85c;
        }
        .badge-warning {
            background-color: #f0ad4e;
        }
        .badge-danger {
            background-color: #d9534f;
        }

        /* 字段状态详细信息 */
        .mapping-table code {
            font-size: 12px;
            padding: 2px 6px;
        }
        .mapping-table .label {
            font-size: 11px;
        }
        .mapping-table small {
            color: #999;
        }

        /* 字段映射折叠面板样式 */
        .panel-heading[onclick] {
            cursor: pointer;
            transition: background-color 0.2s ease;
        }
        .panel-heading[onclick]:hover {
            background-color: #e8f4fd !important;
        }
        .panel-heading .fa-chevron-down,
        .panel-heading .fa-chevron-up {
            transition: transform 0.2s ease;
        }
        .panel-heading .pull-right {
            line-height: 1.4;
        }
        .panel-heading .text-success {
            font-weight: bold;
        }
        .panel-heading .text-warning {
            font-weight: bold;
        }

        /* 简历详情页面样式 */
        .preview-resume-panel {
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
            border: 1px solid #e4e7ed;
        }

        .preview-resume-panel .panel-heading {
            background: #fff;
            border-bottom: 1px solid #e4e7ed;
            padding: 20px;
        }

        .nav-tabs > li.active > a,
        .nav-tabs > li.active > a:focus,
        .nav-tabs > li.active > a:hover {
            color: #409eff;
            background: #fff;
            border: 1px solid #e4e7ed;
            border-bottom-color: transparent;
        }

        .nav-tabs > li > a {
            border-radius: 4px 4px 0 0;
            color: #606266;
            padding: 10px 15px;
        }

        .nav-tabs > li > a:hover {
            background-color: #f5f7fa;
            border-color: #e4e7ed;
        }

        .resume-table {
            margin-bottom: 0;
        }

        .resume-table th,
        .resume-table td {
            vertical-align: middle!important;
            padding: 12px 8px;
            word-break: break-all;
            border: 1px solid #e4e7ed;
        }

        .resume-table th {
            background-color: #f5f7fa;
            color: #606266;
            font-weight: 500;
            width: 120px;
        }

        .resume-table tr.info th {
            background-color: #409eff;
            color: #fff;
            text-align: center;
            font-weight: bold;
            padding: 15px 8px;
        }

        .resume-table tr.four-columns th {
            width: 15%;
        }

        .resume-table tr.four-columns td {
            width: 35%;
        }

        .profile-user-img {
            border: 3px solid #adb5bd;
            margin: 0 auto 10px;
            padding: 3px;
        }

        .profile-username {
            font-size: 21px;
            margin-top: 5px;
        }

        /* 时间轴样式 */
        .timeline {
            position: relative;
            margin: 20px 0;
            padding-left: 30px;
        }

        .timeline::before {
            content: '';
            position: absolute;
            left: 15px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e4e7ed;
        }

        .timeline-item {
            position: relative;
            margin-bottom: 30px;
            padding-left: 50px;
        }

        .timeline-marker {
            position: absolute;
            left: -35px;
            top: 0;
        }

        .timeline-marker-icon {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: #409eff;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }

        .timeline-content {
            background: #fff;
            border: 1px solid #e4e7ed;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }

        .timeline-header {
            margin-bottom: 15px;
            border-bottom: 1px solid #f0f0f0;
            padding-bottom: 10px;
        }

        .timeline-title {
            margin: 0 0 5px 0;
            color: #303133;
            font-size: 18px;
            font-weight: 600;
        }

        .timeline-subtitle {
            margin: 0;
            color: #909399;
            font-size: 14px;
        }

        .timeline-body {
            color: #606266;
            line-height: 1.6;
        }

        /* 照片卡片样式 */
        .photo-card {
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
            transition: all 0.3s ease;
            background: #fff;
            border: 1px solid #e4e7ed;
        }

        .photo-card:hover {
            box-shadow: 0 4px 20px 0 rgba(0,0,0,.15);
            transform: translateY(-2px);
        }

        .photo-card h5 {
            margin: 0;
            padding: 10px 15px;
            background: #f5f7fa;
            color: #606266;
            font-size: 14px;
            font-weight: 500;
            border-bottom: 1px solid #e4e7ed;
        }

        .photo-card .photo-info {
            padding: 8px 15px;
            background: #f5f7fa;
            border-top: 1px solid #e4e7ed;
            text-align: center;
        }

        .photo-card img {
            transition: transform 0.3s ease;
        }

        .photo-card:hover img {
            transform: scale(1.05);
        }

        /* 图片预览样式 */
        .photo-preview {
            display: block;
            height: 100%;
            position: relative;
            cursor: zoom-in;
        }

        .photo-preview:hover img {
            transform: scale(1.05);
        }

        /* 批量导入样式 */
        .import-mode-group .btn {
            padding: 10px 20px;
            margin-right: 10px;
        }

        .import-mode-group .btn.active {
            background-color: #337ab7;
            color: white;
            border-color: #337ab7;
        }

        .file-item {
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            padding: 10px;
            margin-bottom: 10px;
            background: #f9f9f9;
            transition: all 0.3s ease;
        }

        .file-item:hover {
            background: #f0f8ff;
            border-color: #337ab7;
        }

        .file-item.success {
            border-color: #5cb85c;
            background: #f0fff0;
        }

        .file-item.error {
            border-color: #d9534f;
            background: #fff0f0;
        }

        .file-item .file-info {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .file-item .file-details {
            flex: 1;
        }

        .file-item .file-name {
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .file-item .file-meta {
            font-size: 12px;
            color: #666;
        }

        .file-item .file-actions {
            display: flex;
            gap: 5px;
        }

        .file-item .file-status {
            padding: 2px 8px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
        }

        .file-status.pending {
            background: #f0ad4e;
            color: white;
        }

        .file-status.success {
            background: #5cb85c;
            color: white;
        }

        .file-status.error {
            background: #d9534f;
            color: white;
        }

        .batch-stats {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 15px;
        }

        .batch-stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 10px;
            transition: all 0.3s ease;
        }

        .batch-stat-item.clickable-stat {
            cursor: pointer;
            border: 2px solid transparent;
        }

        .batch-stat-item.clickable-stat:hover {
            background: #e9ecef;
            border-color: #ffc107;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .batch-stats-header {
            background: #f8f9fa;
            border-radius: 6px;
            padding: 10px;
            border: 1px solid #e9ecef;
        }

        .batch-stat-item-small {
            text-align: center;
            padding: 8px;
            background: #fff;
            border-radius: 4px;
            margin-bottom: 5px;
            border: 1px solid #e9ecef;
        }

        .batch-stat-number-small {
            font-size: 18px;
            font-weight: bold;
            display: block;
        }

        .batch-stat-label-small {
            font-size: 12px;
            color: #666;
            display: block;
            margin-top: 2px;
        }

        .batch-stat-number {
            display: block;
            font-size: 24px;
            font-weight: bold;
            line-height: 1;
        }

        .batch-stat-label {
            display: block;
            font-size: 12px;
            color: #666;
            margin-top: 5px;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .timeline {
                padding-left: 20px;
            }

            .timeline-item {
                padding-left: 40px;
            }

            .timeline-marker {
                left: -25px;
            }

            .timeline-marker-icon {
                width: 25px;
                height: 25px;
                font-size: 12px;
            }

            .resume-table th,
            .resume-table td {
                padding: 8px 5px;
                font-size: 12px;
            }

            .photo-card {
                margin-bottom: 15px;
            }
        }
    </style>
    <div class="panel-body import-container">
        <!-- 步骤指示器 -->
        <div class="step-indicator">
            <div class="step-item active" id="step-1">
                <i class="fa fa-cog"></i> 1. 选择模板
            </div>
            <div class="step-item" id="step-2">
                <i class="fa fa-upload"></i> 2. 上传文件
            </div>
            <div class="step-item" id="step-3">
                <i class="fa fa-eye"></i> 3. 预览数据
            </div>
            <div class="step-item" id="step-4">
                <i class="fa fa-check"></i> 4. 确认导入
            </div>
        </div>

        <form id="import-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="{:url('resume/importWithTemplate')}" enctype="multipart/form-data">

            <!-- 第一步：选择导入模式和模板 -->
            <div class="form-section" id="template-section">
                <h4><i class="fa fa-cog text-primary"></i> 选择导入模式和模板</h4>

                <!-- 导入模式选择 -->
                <div class="form-group">
                    <label class="control-label col-xs-12 col-sm-3">
                        导入模式
                        <i class="fa fa-question-circle tooltip-icon" title="选择单文件导入或批量导入模式"></i>
                    </label>
                    <div class="col-xs-12 col-sm-8">
                        <div class="btn-group" data-toggle="buttons" id="import-mode-group">
                            <label class="btn btn-default active" id="single-mode-btn">
                                <input type="radio" name="import_mode" value="single" checked>
                                <i class="fa fa-file"></i> 单文件导入
                            </label>
                            <label class="btn btn-default" id="batch-mode-btn">
                                <input type="radio" name="import_mode" value="batch">
                                <i class="fa fa-files-o"></i> 批量导入
                            </label>
                        </div>
                        <div class="help-block">
                            <small class="text-muted">
                                <strong>单文件导入：</strong>一次导入一个Excel文件，一个文件对应一份简历<br>
                                <strong>批量导入：</strong>一次导入多个Excel文件，每个文件对应一份简历
                            </small>
                        </div>
                    </div>
                </div>

                <!-- 导入模板选择 -->
                <div class="form-group">
                    <label for="template_id" class="control-label col-xs-12 col-sm-3">
                        导入模板
                        <i class="fa fa-question-circle tooltip-icon" title="选择预先配置好的字段映射模板"></i>
                    </label>
                    <div class="col-xs-12 col-sm-8">
                        <select class="form-control" id="template_id" name="template_id" required>
                            <option value="">请选择导入模板</option>
                            {foreach $templates as $template}
                            <option value="{$template.id}" {if $template.is_default}selected{/if}>
                                {$template.name} {if $template.is_default}(默认){/if}
                            </option>
                            {/foreach}
                        </select>
                        <div class="help-block">
                            <i class="fa fa-info-circle text-info"></i>
                            如果没有合适的模板，请先
                            <a href="{:url('resume_import_template/add')}" target="_blank" class="text-primary">
                                <i class="fa fa-plus"></i> 创建导入模板
                            </a>
                        </div>
                    </div>
                </div>
            </div>
                        <!-- 模板信息显示 -->
            <div class="template-info" id="template-info" style="display:none;">
                <div class="row">
                    <div class="col-md-6">
                        <div class="template-card">
                            <h4 class="card-header">
                                <i class="fa fa-info-circle text-primary"></i> 模板描述
                            </h4>
                            <div class="card-body" id="template-description">
                                请先选择导入模板
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="template-card">
                            <h4 class="card-header">
                                <i class="fa fa-table text-primary"></i> 详细字段映射
                                <button type="button" class="btn btn-xs btn-default pull-right" id="toggle-mapping">
                                    <i class="fa fa-eye"></i> 显示详情
                                </button>
                            </h4>
                            <div class="card-body" id="template-mapping" style="display:none;">
                                <!-- 映射统计信息 -->
                                <div class="summary-stats">
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="stat-box text-center">
                                                <span class="stat-number text-primary" id="mapping-count">0</span>
                                                <span class="stat-label">字段映射数</span>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="stat-box text-center">
                                                <span class="stat-number text-success" id="required-count">0</span>
                                                <span class="stat-label">必填字段</span>
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="stat-box text-center">
                                                <span class="stat-number text-info" id="optional-count">0</span>
                                                <span class="stat-label">可选字段</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 详细映射信息 -->
                                <div id="template-mapping-summary">
                                    <div class="alert alert-info">请先选择导入模板查看字段映射详情</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <br>

            <!-- 第二步：上传文件 -->
            <div class="form-section" id="upload-section">
                <h4><i class="fa fa-upload text-success"></i> 上传Excel文件</h4>

                <!-- 单文件上传模式 -->
                <div class="form-group" id="single-upload-group">
                    <label for="excel_file" class="control-label col-xs-12 col-sm-3">
                        Excel文件
                        <i class="fa fa-question-circle tooltip-icon" title="支持.xlsx格式，建议文件大小不超过10MB"></i>
                    </label>
                    <div class="col-xs-12 col-sm-8">
                        <!-- 拖拽上传区域 -->
                        <div class="file-upload-area" id="upload-area">
                            <div class="upload-content">
                                <i class="fa fa-cloud-upload fa-3x text-muted"></i>
                                <h4 class="text-muted">点击选择文件或拖拽文件到此处</h4>
                                <p class="text-muted">
                                    支持 .xlsx 格式文件
                                    <br>
                                    <small>建议文件大小不超过 10MB</small>
                                </p>
                                <button type="button" class="btn btn-primary" id="browse-button">
                                    <i class="fa fa-folder-open"></i> 浏览文件
                                </button>
                            </div>
                            <div class="upload-success" style="display:none;">
                                <i class="fa fa-check-circle fa-3x text-success"></i>
                                <h4 class="text-success">文件已选择</h4>
                                <p class="file-info">
                                    <strong id="file-name"></strong>
                                    <br>
                                    <small class="text-muted">大小: <span id="file-size"></span></small>
                                </p>
                                <button type="button" class="btn btn-default btn-sm" id="change-file">
                                    <i class="fa fa-refresh"></i> 更换文件
                                </button>
                            </div>
                        </div>
                        <input type="file" name="excel_file" id="excel_file" accept=".xlsx,.xls" style="display:none;">
                        <div class="help-block with-errors"></div>
                    </div>
                </div>

                <!-- 批量文件上传模式 -->
                <div class="form-group" id="batch-upload-group" style="display:none;">
                    <label class="control-label col-xs-12 col-sm-3">
                        Excel文件列表
                        <i class="fa fa-question-circle tooltip-icon" title="支持同时选择多个.xlsx文件进行批量导入"></i>
                    </label>
                    <div class="col-xs-12 col-sm-8">
                        <!-- 批量拖拽上传区域 -->
                        <div class="file-upload-area" id="batch-upload-area">
                            <div class="upload-content">
                                <i class="fa fa-cloud-upload fa-3x text-muted"></i>
                                <h4 class="text-muted">点击选择多个文件或拖拽文件到此处</h4>
                                <p class="text-muted">
                                    支持同时选择多个 .xlsx 格式文件
                                    <br>
                                    <small>每个文件建议不超过 10MB，最多同时处理 20 个文件</small>
                                </p>
                                <button type="button" class="btn btn-primary" id="batch-browse-button">
                                    <i class="fa fa-folder-open"></i> 选择多个文件
                                </button>
                            </div>
                        </div>
                        <input type="file" name="excel_files[]" id="excel_files" accept=".xlsx,.xls" multiple style="display:none;">

                        <!-- 文件列表 -->
                        <div id="batch-file-list" style="display:none; margin-top: 15px;">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h5 class="panel-title">
                                        <i class="fa fa-list"></i> 已选择的文件
                                        <span class="badge" id="file-count">0</span>
                                        <button type="button" class="btn btn-xs btn-default pull-right" id="clear-all-files">
                                            <i class="fa fa-trash"></i> 清空列表
                                        </button>
                                    </h5>
                                </div>
                                <div class="panel-body" style="max-height: 300px; overflow-y: auto;">
                                    <div id="file-items"></div>
                                </div>
                            </div>
                        </div>
                        <div class="help-block with-errors"></div>
                    </div>
                </div>
            </div>


            <!-- 操作按钮 -->
            <div class="form-section">
                <h4><i class="fa fa-cogs text-info"></i> 操作控制</h4>
                <div class="text-center">
                    <button type="button" class="btn btn-primary btn-lg btn-action" id="preview-button" disabled>
                        <i class="fa fa-eye"></i> 预览导入数据
                    </button>
                    <button type="reset" class="btn btn-default btn-action">
                        <i class="fa fa-refresh"></i> 重置表单
                    </button>
                    <a href="{:url('resume/index')}" class="btn btn-default btn-action">
                        <i class="fa fa-arrow-left"></i> 返回列表
                    </a>
                </div>
                <div class="help-block text-center">
                    <small class="text-muted">
                        <i class="fa fa-lightbulb-o"></i>
                        请先选择模板和上传文件，然后点击预览按钮查看数据
                    </small>
                </div>
            </div>

            <!-- 预览容器 -->
            <div id="preview-container">
                <div class="preview-header">
                    <div class="row">
                        <div class="col-md-4">
                            <h3 style="margin: 0;">
                                <i class="fa fa-eye"></i> 数据预览
                                <small style="color: white;">验证导入数据的准确性</small>
                            </h3>
                        </div>
                        <div class="col-md-8">
                            <div class="preview-stats">
                                <div class="preview-stat">
                                    <div class="number" id="preview-count">0</div>
                                    <div class="label">简历数</div>
                                </div>
                                <div class="preview-stat">
                                    <div class="number" id="error-count">0</div>
                                    <div class="label">错误数</div>
                                </div>
                                <div class="preview-stat">
                                    <div class="number" id="warning-count">0</div>
                                    <div class="label">警告数</div>
                                </div>
                                <div class="preview-stat" id="success-files-stat" style="display: none;">
                                    <div class="number" id="success-files-count">0</div>
                                    <div class="label">成功文件</div>
                                </div>
                                <div class="preview-stat" id="failed-files-stat" style="display: none;">
                                    <div class="number" id="failed-files-count">0</div>
                                    <div class="label">失败文件</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="preview-content">
                    <!-- 加载状态 -->
                    <div id="preview-loading" class="loading">
                        <div class="spinner">
                            <i class="fa fa-spinner fa-spin"></i>
                        </div>
                        <h4>正在解析Excel数据</h4>
                        <p class="text-muted">请稍候，正在处理您的文件...</p>
                        <div class="progress" style="width: 200px; margin: 20px auto;">
                            <div class="progress-bar progress-bar-striped active" style="width: 100%"></div>
                        </div>
                    </div>

                    <!-- 错误信息 -->
                    <div id="preview-error" class="alert alert-danger" style="display:none; margin: 20px;">
                        <h4><i class="fa fa-exclamation-triangle"></i> 处理失败</h4>
                        <div id="error-message"></div>
                        <hr>
                        <p class="mb-0">
                            <i class="fa fa-lightbulb-o"></i>
                            <strong>建议：</strong>请检查Excel文件格式是否正确，或尝试使用较小的文件。
                        </p>
                    </div>

                    <!-- 预览数据 -->
                    <div id="preview-data"></div>
                </div>

                <!-- 控制按钮 -->
                <div class="preview-controls">
                    <button type="button" class="btn btn-success btn-lg btn-action" id="confirm-import">
                        <i class="fa fa-check"></i> 确认导入
                        <small class="block">将数据保存到系统</small>
                    </button>
                    <button type="button" class="btn btn-warning btn-lg btn-action" id="re-preview">
                        <i class="fa fa-refresh"></i> 重新预览
                        <small class="block">修改文件后重新预览</small>
                    </button>
                    <button type="button" class="btn btn-default btn-lg btn-action" id="cancel-import">
                        <i class="fa fa-times"></i> 取消导入
                        <small class="block">返回上一步</small>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div> 

<script>
(function() {
    // 应用程序状态管理
    const AppState = {
        currentStep: 1,
        importMode: 'single', // 'single' 或 'batch'
        templateSelected: false,
        fileSelected: false,
        previewLoaded: false,
        batchFiles: [], // 批量文件列表

        updateStep: function(step) {
            this.currentStep = step;
            this.updateStepIndicator();
            this.updateButtonStates();
        },

        updateStepIndicator: function() {
            const steps = document.querySelectorAll('.step-item');
            steps.forEach((step, index) => {
                const stepNum = index + 1;
                step.classList.remove('active', 'completed');

                if (stepNum < this.currentStep) {
                    step.classList.add('completed');
                } else if (stepNum === this.currentStep) {
                    step.classList.add('active');
                }
            });
        },

        updateButtonStates: function() {
            const previewBtn = document.getElementById('preview-button');
            let canPreview = false;

            if (this.importMode === 'single') {
                canPreview = this.templateSelected && this.fileSelected;
            } else {
                canPreview = this.templateSelected && this.batchFiles.length > 0;
            }

            previewBtn.disabled = !canPreview;
            previewBtn.classList.toggle('btn-primary', canPreview);
            previewBtn.classList.toggle('btn-default', !canPreview);
        },

        // 批量文件管理
        addBatchFile: function(file) {
            const fileObj = {
                id: Date.now() + Math.random(),
                file: file,
                name: file.name,
                size: file.size,
                status: 'pending', // pending, success, error
                error: null
            };
            this.batchFiles.push(fileObj);
            this.fileSelected = this.batchFiles.length > 0;
            this.updateButtonStates();
            return fileObj;
        },

        removeBatchFile: function(fileId) {
            this.batchFiles = this.batchFiles.filter(f => f.id !== fileId);
            this.fileSelected = this.batchFiles.length > 0;
            this.updateButtonStates();
        },

        clearBatchFiles: function() {
            this.batchFiles = [];
            this.fileSelected = false;
            this.updateButtonStates();
        },

        setImportMode: function(mode) {
            this.importMode = mode;
            this.fileSelected = false;
            this.previewLoaded = false;
            this.updateButtonStates();
            this.updateUploadInterface();
        },

        updateUploadInterface: function() {
            const singleGroup = document.getElementById('single-upload-group');
            const batchGroup = document.getElementById('batch-upload-group');
            const singleFileInput = document.getElementById('excel_file');
            const batchFileInput = document.getElementById('excel_files');

            if (this.importMode === 'single') {
                singleGroup.style.display = 'block';
                batchGroup.style.display = 'none';
                // 设置单文件输入为必填
                if (singleFileInput) {
                    singleFileInput.setAttribute('required', 'required');
                }
                if (batchFileInput) {
                    batchFileInput.removeAttribute('required');
                }
                // 清空批量文件
                this.clearBatchFiles();
            } else {
                singleGroup.style.display = 'none';
                batchGroup.style.display = 'block';
                // 移除单文件输入的必填属性
                if (singleFileInput) {
                    singleFileInput.removeAttribute('required');
                    singleFileInput.value = '';
                }
                if (batchFileInput) {
                    batchFileInput.setAttribute('required', 'required');
                }
                hideFileInfo();
            }
        }
    };

    // 工具函数
    const Utils = {
        formatFileSize: function(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },

        showNotification: function(message, type = 'info') {
            // 简单的通知显示
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} alert-dismissible`;
            notification.innerHTML = `
                <button type="button" class="close" data-dismiss="alert">&times;</button>
                ${message}
            `;
            document.body.insertBefore(notification, document.body.firstChild);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        },

        // Toast消息弹窗
        showToast: function(message, type = 'info', duration = 3000) {
            // 创建Toast容器（如果不存在）
            let toastContainer = document.getElementById('toast-container');
            if (!toastContainer) {
                toastContainer = document.createElement('div');
                toastContainer.id = 'toast-container';
                toastContainer.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    z-index: 10000;
                    max-width: 350px;
                `;
                document.body.appendChild(toastContainer);
            }

            // 创建Toast元素
            const toast = document.createElement('div');
            const toastId = 'toast-' + Date.now();
            toast.id = toastId;

            // 根据类型设置图标和颜色
            const typeConfig = {
                'success': { icon: 'fa-check-circle', color: '#5cb85c', bgColor: '#dff0d8', borderColor: '#d6e9c6' },
                'info': { icon: 'fa-info-circle', color: '#31708f', bgColor: '#d9edf7', borderColor: '#bce8f1' },
                'warning': { icon: 'fa-exclamation-triangle', color: '#8a6d3b', bgColor: '#fcf8e3', borderColor: '#faebcc' },
                'error': { icon: 'fa-times-circle', color: '#a94442', bgColor: '#f2dede', borderColor: '#ebccd1' },
                'danger': { icon: 'fa-times-circle', color: '#a94442', bgColor: '#f2dede', borderColor: '#ebccd1' }
            };

            const config = typeConfig[type] || typeConfig['info'];

            toast.style.cssText = `
                background: ${config.bgColor};
                border: 1px solid ${config.borderColor};
                border-radius: 4px;
                padding: 12px 16px;
                margin-bottom: 10px;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
                font-size: 14px;
                line-height: 1.4;
                position: relative;
                overflow: hidden;
            `;

            toast.innerHTML = `
                <div style="display: flex; align-items: center; color: ${config.color};">
                    <i class="fa ${config.icon}" style="margin-right: 8px; font-size: 16px;"></i>
                    <span style="flex: 1;">${message}</span>
                    <button type="button" style="
                        background: none;
                        border: none;
                        color: ${config.color};
                        font-size: 18px;
                        cursor: pointer;
                        padding: 0;
                        margin-left: 10px;
                        opacity: 0.7;
                        line-height: 1;
                    " onclick="Utils.removeToast('${toastId}')">&times;</button>
                </div>
            `;

            // 添加到容器
            toastContainer.appendChild(toast);

            // 触发动画
            setTimeout(() => {
                toast.style.opacity = '1';
                toast.style.transform = 'translateX(0)';
            }, 10);

            // 自动移除
            setTimeout(() => {
                this.removeToast(toastId);
            }, duration);

            return toastId;
        },

        // 移除Toast
        removeToast: function(toastId) {
            const toast = document.getElementById(toastId);
            if (toast) {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }
        },

        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // 安全的DOM操作
        safeGetElement: function(id) {
            const element = document.getElementById(id);
            if (!element) {
                console.warn(`Element with ID '${id}' not found`);
            }
            return element;
        },

        safeSetContent: function(id, content) {
            const element = this.safeGetElement(id);
            if (element) {
                element.innerHTML = content;
            }
        },

        safeSetText: function(id, text) {
            const element = this.safeGetElement(id);
            if (element) {
                element.textContent = text;
            }
        },

        safeSetStyle: function(id, property, value) {
            const element = this.safeGetElement(id);
            if (element) {
                element.style[property] = value;
            }
        },

        // 检查字段是否真正有效映射
        isFieldMapped: function(config, field) {
            if (!config || !config[field]) {
                return false;
            }

            const value = config[field];

            // 检查是否为空值
            if (value === '' || value === null || value === undefined) {
                return false;
            }

            // 检查是否为空字符串（去除空格后）
            if (typeof value === 'string' && value.trim() === '') {
                return false;
            }

            // 检查是否为空数组
            if (Array.isArray(value) && value.length === 0) {
                return false;
            }

            // 检查是否为空对象
            if (typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0) {
                return false;
            }

            // 对于数组类型，检查是否所有元素都为空
            if (Array.isArray(value)) {
                return value.some(item => {
                    if (typeof item === 'string') {
                        return item.trim() !== '';
                    }
                    return item !== null && item !== undefined && item !== '';
                });
            }

            return true;
        },

        // 批量文件验证
        validateBatchFile: function(file) {
            // 验证文件类型
            const allowedTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel'
            ];

            if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
                return { valid: false, error: '请选择有效的Excel文件（.xlsx或.xls格式）' };
            }

            // 验证文件大小（10MB限制）
            if (file.size > 10 * 1024 * 1024) {
                return { valid: false, error: '文件大小不能超过10MB' };
            }

            // 检查是否已存在同名文件
            const existingFile = AppState.batchFiles.find(f => f.name === file.name);
            if (existingFile) {
                return { valid: false, error: '文件名重复，请选择不同的文件' };
            }

            return { valid: true };
        },

        // 创建文件项HTML
        createFileItemHTML: function(fileObj) {
            return `
                <div class="file-item" data-file-id="${fileObj.id}">
                    <div class="file-info">
                        <div class="file-details">
                            <div class="file-name">${fileObj.name}</div>
                            <div class="file-meta">
                                大小: ${this.formatFileSize(fileObj.size)} |
                                状态: <span class="file-status ${fileObj.status}">${this.getStatusText(fileObj.status)}</span>
                                ${fileObj.error ? `<br><small class="text-danger">${fileObj.error}</small>` : ''}
                            </div>
                        </div>
                        <div class="file-actions">
                            <button type="button" class="btn btn-xs btn-danger" onclick="removeBatchFile('${fileObj.id}')">
                                <i class="fa fa-trash"></i> 移除
                            </button>
                        </div>
                    </div>
                </div>
            `;
        },

        // 获取状态文本
        getStatusText: function(status) {
            const statusMap = {
                'pending': '待处理',
                'success': '成功',
                'error': '失败'
            };
            return statusMap[status] || status;
        },

        // 更新批量文件统计
        updateBatchStats: function() {
            const totalFiles = AppState.batchFiles.length;
            const successFiles = AppState.batchFiles.filter(f => f.status === 'success').length;
            const errorFiles = AppState.batchFiles.filter(f => f.status === 'error').length;
            const pendingFiles = AppState.batchFiles.filter(f => f.status === 'pending').length;

            document.getElementById('file-count').textContent = totalFiles;

            // 更新预览统计（如果存在）
            const previewCount = document.getElementById('preview-count');
            const errorCount = document.getElementById('error-count');
            if (previewCount) previewCount.textContent = successFiles;
            if (errorCount) errorCount.textContent = errorFiles;
        }
    };

    // 初始化应用
    function initializeApp() {
        // 初始化工具提示（检查jQuery是否可用）
        if (typeof $ !== 'undefined' && $.fn.tooltip) {
            $('[title]').tooltip();
        } else {
            // 如果jQuery不可用，使用原生方式添加title属性
            document.querySelectorAll('[title]').forEach(function(element) {
                // 原生tooltip已经通过title属性工作，无需额外处理
            });
        }

        // 添加图片预览功能 - 与detail.html保持一致
        if (typeof $ !== 'undefined') {
            $(document).on('click', '.photo-preview', function() {
                var src = $(this).data('src');
                if (src) {
                    showImageModal(src);
                }
            });
        } else {
            // 如果jQuery不可用，使用原生事件委托
            document.addEventListener('click', function(e) {
                if (e.target.closest('.photo-preview')) {
                    var element = e.target.closest('.photo-preview');
                    var src = element.getAttribute('data-src');
                    if (src && typeof showImageModal === 'function') {
                        showImageModal(src);
                    }
                }
            });
        }

        // 初始化状态
        AppState.updateStep(1);

        // 初始化导入模式为单文件
        AppState.setImportMode('single');

        // 绑定事件
        bindEvents();

        // 触发默认模板选择
        const templateSelect = document.getElementById('template_id');
        if (templateSelect.value) {
            templateSelect.dispatchEvent(new Event('change'));
        }
    }

    // 安全的事件绑定函数
    function safeAddEventListener(elementId, event, handler) {
        const element = document.getElementById(elementId);
        if (element) {
            element.addEventListener(event, handler);
        } else {
            console.warn(`Element with ID '${elementId}' not found`);
        }
    }

    // 事件绑定
    function bindEvents() {
        // 导入模式切换
        safeAddEventListener('single-mode-btn', 'click', () => handleImportModeChange('single'));
        safeAddEventListener('batch-mode-btn', 'click', () => handleImportModeChange('batch'));

        // 模板选择
        safeAddEventListener('template_id', 'change', handleTemplateChange);

        // 单文件上传相关
        safeAddEventListener('browse-button', 'click', (e) => {
            e.stopPropagation(); // 阻止事件冒泡
            const fileInput = document.getElementById('excel_file');
            if (fileInput) fileInput.click();
        });

        safeAddEventListener('change-file', 'click', (e) => {
            e.stopPropagation(); // 阻止事件冒泡
            const fileInput = document.getElementById('excel_file');
            if (fileInput) fileInput.click();
        });

        safeAddEventListener('excel_file', 'change', handleFileChange);

        // 批量文件上传相关
        safeAddEventListener('batch-browse-button', 'click', (e) => {
            e.stopPropagation(); // 阻止事件冒泡
            const fileInput = document.getElementById('excel_files');
            if (fileInput) fileInput.click();
        });

        safeAddEventListener('excel_files', 'change', handleBatchFileChange);
        safeAddEventListener('clear-all-files', 'click', handleClearAllFiles);

        // 单文件拖拽上传
        const uploadArea = document.getElementById('upload-area');
        if (uploadArea) {
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleFileDrop);
            uploadArea.addEventListener('click', () => {
                if (!AppState.fileSelected) {
                    const fileInput = document.getElementById('excel_file');
                    if (fileInput) fileInput.click();
                }
            });
        }

        // 批量文件拖拽上传
        const batchUploadArea = document.getElementById('batch-upload-area');
        if (batchUploadArea) {
            batchUploadArea.addEventListener('dragover', handleDragOver);
            batchUploadArea.addEventListener('dragleave', handleDragLeave);
            batchUploadArea.addEventListener('drop', handleBatchFileDrop);
            batchUploadArea.addEventListener('click', () => {
                const fileInput = document.getElementById('excel_files');
                if (fileInput) fileInput.click();
            });
        }

        // 预览和导入按钮
        safeAddEventListener('preview-button', 'click', handlePreviewClick);
        safeAddEventListener('confirm-import', 'click', handleConfirmImport);
        safeAddEventListener('re-preview', 'click', handleRePreview);
        safeAddEventListener('cancel-import', 'click', handleCancelImport);

        // 模板映射切换
        safeAddEventListener('toggle-mapping', 'click', handleToggleMapping);

        // 表单重置
        const resetButton = document.querySelector('button[type="reset"]');
        if (resetButton) {
            resetButton.addEventListener('click', handleFormReset);
        }
    }

    // 事件处理函数
    function handleImportModeChange(mode) {
        AppState.setImportMode(mode);

        // 更新按钮状态
        const singleBtn = document.getElementById('single-mode-btn');
        const batchBtn = document.getElementById('batch-mode-btn');

        if (mode === 'single') {
            singleBtn.classList.add('active');
            batchBtn.classList.remove('active');
        } else {
            batchBtn.classList.add('active');
            singleBtn.classList.remove('active');
        }

        // 重置预览状态
        AppState.previewLoaded = false;
        document.getElementById('preview-container').style.display = 'none';

        // 使用Toast消息弹窗提示
        const modeText = mode === 'single' ? '单文件导入' : '批量导入';
        const description = mode === 'single' ? '一次导入一个Excel文件' : '一次导入多个Excel文件';
        Utils.showToast(`已切换到${modeText}模式<br><small>${description}</small>`, 'success', 2500);
    }

    function handleBatchFileChange() {
        const files = Array.from(this.files);
        addBatchFiles(files);
    }

    function handleBatchFileDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('dragover');

        const files = Array.from(e.dataTransfer.files);
        addBatchFiles(files);
    }

    function addBatchFiles(files) {
        if (!AppState.templateSelected) {
            Utils.showToast('请先选择导入模板', 'warning');
            return;
        }

        if (files.length === 0) {
            return;
        }

        // 检查文件数量限制
        const totalFiles = AppState.batchFiles.length + files.length;
        if (totalFiles > 20) {
            Utils.showToast('最多只能同时处理20个文件', 'warning');
            return;
        }

        let addedCount = 0;
        let errorCount = 0;

        files.forEach(file => {
            const validation = Utils.validateBatchFile(file);
            if (validation.valid) {
                const fileObj = AppState.addBatchFile(file);
                addFileToList(fileObj);
                addedCount++;
            } else {
                Utils.showToast(`文件 "${file.name}" 添加失败<br><small>${validation.error}</small>`, 'warning', 4000);
                errorCount++;
            }
        });

        if (addedCount > 0) {
            showBatchFileList();
            Utils.updateBatchStats();
            if (errorCount > 0) {
                Utils.showToast(`成功添加 ${addedCount} 个文件，${errorCount} 个文件添加失败`, 'warning', 4000);
            } else {
                Utils.showToast(`成功添加 ${addedCount} 个文件`, 'success');
            }

            if (AppState.batchFiles.length > 0) {
                AppState.updateStep(Math.max(AppState.currentStep, 3));
            }
        }
    }

    function addFileToList(fileObj) {
        const fileItems = document.getElementById('file-items');
        const fileItemHTML = Utils.createFileItemHTML(fileObj);
        fileItems.insertAdjacentHTML('beforeend', fileItemHTML);
    }

    function showBatchFileList() {
        document.getElementById('batch-file-list').style.display = 'block';
    }

    function hideBatchFileList() {
        document.getElementById('batch-file-list').style.display = 'none';
    }

    function handleClearAllFiles() {
        if (AppState.batchFiles.length === 0) {
            return;
        }

        if (confirm('确认要清空所有文件吗？')) {
            AppState.clearBatchFiles();
            document.getElementById('file-items').innerHTML = '';
            hideBatchFileList();
            Utils.updateBatchStats();
            Utils.showToast('已清空文件列表', 'info');
        }
    }

    // 全局函数，供HTML调用
    window.removeBatchFile = function(fileId) {
        AppState.removeBatchFile(fileId);
        const fileItem = document.querySelector(`[data-file-id="${fileId}"]`);
        if (fileItem) {
            fileItem.remove();
        }
        Utils.updateBatchStats();

        if (AppState.batchFiles.length === 0) {
            hideBatchFileList();
        }

        Utils.showToast('文件已移除', 'info');
    };

    // 全局Toast移除函数
    window.removeToast = function(toastId) {
        Utils.removeToast(toastId);
    };

    // 全局错误详情切换函数
    window.toggleErrorDetails = function() {
        const errorSection = document.querySelector('.alert-warning');
        if (errorSection) {
            if (errorSection.style.display === 'none') {
                errorSection.style.display = 'block';
                Utils.showToast('错误详情已展开', 'info');
            } else {
                errorSection.style.display = 'none';
                Utils.showToast('错误详情已收起', 'info');
            }
        } else {
            Utils.showToast('没有找到错误信息', 'warning');
        }
    };



    function handleTemplateChange() {
        const templateId = this.value;
        const templateInfo = document.getElementById('template-info');

        if (templateId) {
            AppState.templateSelected = true;
            AppState.updateStep(2);

            // 显示模板信息区域
            if (templateInfo) {
                templateInfo.style.display = 'block';
            }

            // 获取模板信息
            const xhr = new XMLHttpRequest();
            xhr.open('GET', "{:url('resume_import_template/getTemplateInfo')}?id=" + templateId, true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const res = JSON.parse(xhr.responseText);
                            if (res.code === 1) {
                                const template = res.data;
                                displayTemplateInfo(template);
                            } else {
                                showTemplateError('获取模板信息失败: ' + res.msg);
                            }
                        } catch (e) {
                            showTemplateError('解析模板信息失败: ' + e.message);
                        }
                    } else {
                        showTemplateError('获取模板信息失败: ' + xhr.statusText);
                    }
                }
            };
            xhr.send();
        } else {
            AppState.templateSelected = false;
            AppState.updateStep(1);
            if (templateInfo) {
                templateInfo.style.display = 'none';
            }
            resetTemplateInfo();
        }
    }

    function handleFileChange() {
        const file = this.files[0];
        if (file) {
            // 验证文件类型
            const allowedTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel'
            ];

            if (!allowedTypes.includes(file.type) && !file.name.match(/\.(xlsx|xls)$/i)) {
                Utils.showToast('请选择有效的Excel文件<br><small>支持 .xlsx 或 .xls 格式</small>', 'error', 4000);
                this.value = '';
                return;
            }

            // 验证文件大小（10MB限制）
            if (file.size > 10 * 1024 * 1024) {
                Utils.showToast('文件大小不能超过10MB<br><small>请选择较小的文件</small>', 'warning', 4000);
                this.value = '';
                return;
            }

            AppState.fileSelected = true;
            AppState.updateStep(Math.max(AppState.currentStep, 3));

            // 显示文件信息
            displayFileInfo(file);

            Utils.showToast(`文件选择成功<br><small>${file.name}</small>`, 'success');
        } else {
            AppState.fileSelected = false;
            hideFileInfo();
        }
    }

    // 拖拽事件处理
    function handleDragOver(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.add('dragover');
    }

    function handleDragLeave(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('dragover');
    }

    function handleFileDrop(e) {
        e.preventDefault();
        e.stopPropagation();
        this.classList.remove('dragover');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            const fileInput = document.getElementById('excel_file');
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change'));
        }
    }

    function handlePreviewClick() {
        const templateId = document.getElementById('template_id').value;

        // 表单验证
        if (!templateId) {
            Utils.showToast('请先选择导入模板', 'warning');
            return;
        }

        if (AppState.importMode === 'single') {
            handleSinglePreview();
        } else {
            handleBatchPreview();
        }
    }

    function handleSinglePreview() {
        const form = document.getElementById('import-form');
        const fileInput = document.getElementById('excel_file');

        if (!fileInput.files || fileInput.files.length === 0) {
            Utils.showToast('请先选择要导入的Excel文件', 'warning');
            return;
        }

        // 显示预览容器
        const previewContainer = document.getElementById('preview-container');
        previewContainer.style.display = 'block';

        // 显示加载状态
        showPreviewLoading();

        // 滚动到预览区域
        previewContainer.scrollIntoView({ behavior: 'smooth' });

        // 发送预览请求
        const formData = new FormData(form);
        const xhr = new XMLHttpRequest();

        xhr.open('POST', "{:url('resume/previewImport')}", true);
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4) {
                hidePreviewLoading();

                if (xhr.status === 200) {
                    try {
                        // 检查响应是否为HTML错误页面
                        if (xhr.responseText.trim().startsWith('<!DOCTYPE') ||
                            xhr.responseText.trim().startsWith('<html')) {
                            showPreviewError('服务器返回了错误页面，可能是文件过大或格式错误，请检查您的Excel文件');
                            return;
                        }

                        const res = JSON.parse(xhr.responseText);

                        if (res.code === 1) {
                            AppState.previewLoaded = true;
                            AppState.updateStep(4);

                            // 更新统计信息
                            updatePreviewStats(res.data);

                            // 渲染预览数据
                            renderPreviewData(res.data.data, res.data.errors, res.data.date_conversions);

                            Utils.showToast('数据预览加载成功', 'success');
                        } else {
                            showPreviewError(res.msg || '预览数据失败');
                        }
                    } catch (e) {
                        showPreviewError('解析响应数据失败: ' + e.message + '<br>请检查您的Excel文件格式是否正确');
                        console.error('JSON解析错误:', e);
                    }
                } else {
                    showPreviewError('请求失败: ' + xhr.status + ' ' + xhr.statusText);
                }
            }
        };

        xhr.send(formData);
    }

    function handleBatchPreview() {
        if (AppState.batchFiles.length === 0) {
            Utils.showToast('请先选择要导入的Excel文件', 'warning');
            return;
        }

        // 显示预览容器
        const previewContainer = document.getElementById('preview-container');
        previewContainer.style.display = 'block';

        // 显示加载状态
        showPreviewLoading();

        // 滚动到预览区域
        previewContainer.scrollIntoView({ behavior: 'smooth' });

        // 批量处理文件
        processBatchFiles();
    }

    function processBatchFiles() {
        const templateId = document.getElementById('template_id').value;
        let completedCount = 0;
        let successCount = 0;
        let errorCount = 0;
        const allResults = [];

        AppState.batchFiles.forEach((fileObj, index) => {
            const formData = new FormData();
            formData.append('template_id', templateId);
            formData.append('excel_file', fileObj.file);

            const xhr = new XMLHttpRequest();
            xhr.open('POST', "{:url('resume/previewImport')}", true);

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    completedCount++;

                    if (xhr.status === 200) {
                        try {
                            const res = JSON.parse(xhr.responseText);
                            if (res.code === 1) {
                                fileObj.status = 'success';
                                fileObj.error = null;
                                fileObj.data = res.data;
                                successCount++;
                                allResults.push({
                                    fileName: fileObj.name,
                                    data: res.data.data,
                                    errors: res.data.errors,
                                    date_conversions: res.data.date_conversions
                                });
                            } else {
                                fileObj.status = 'error';
                                fileObj.error = res.msg || '预览失败';
                                errorCount++;
                            }
                        } catch (e) {
                            fileObj.status = 'error';
                            fileObj.error = '解析响应失败: ' + e.message;
                            errorCount++;
                        }
                    } else {
                        fileObj.status = 'error';
                        fileObj.error = '请求失败: ' + xhr.status + ' ' + xhr.statusText;
                        errorCount++;
                    }

                    // 更新文件状态显示
                    updateFileItemStatus(fileObj);

                    // 检查是否所有文件都处理完成
                    if (completedCount === AppState.batchFiles.length) {
                        hidePreviewLoading();

                        if (successCount > 0) {
                            AppState.previewLoaded = true;
                            AppState.updateStep(4);

                            // 更新统计信息
                            updateBatchPreviewStats(successCount, errorCount);

                            // 渲染批量预览数据
                            renderBatchPreviewData(allResults);

                            Utils.showToast(`批量预览完成<br><small>成功 ${successCount} 个，失败 ${errorCount} 个</small>`, 'success', 4000);
                        } else {
                            showPreviewError('所有文件预览都失败了，请检查文件格式和模板配置');
                        }
                    }
                }
            };

            xhr.send(formData);
        });
    }

    function updateFileItemStatus(fileObj) {
        const fileItem = document.querySelector(`[data-file-id="${fileObj.id}"]`);
        if (fileItem) {
            fileItem.className = `file-item ${fileObj.status}`;
            const statusSpan = fileItem.querySelector('.file-status');
            if (statusSpan) {
                statusSpan.className = `file-status ${fileObj.status}`;
                statusSpan.textContent = Utils.getStatusText(fileObj.status);
            }

            if (fileObj.error) {
                const metaDiv = fileItem.querySelector('.file-meta');
                if (metaDiv && !metaDiv.querySelector('.text-danger')) {
                    metaDiv.insertAdjacentHTML('beforeend', `<br><small class="text-danger">${fileObj.error}</small>`);
                }
            }
        }
    }

    function handleConfirmImport() {
        if (!AppState.previewLoaded) {
            Utils.showToast('请先预览数据', 'warning');
            return;
        }

        if (AppState.importMode === 'single') {
            handleSingleImport();
        } else {
            handleBatchImport();
        }
    }

    function handleSingleImport() {
        if (confirm('确认要导入这些数据吗？导入后将无法撤销。')) {
            const form = document.getElementById('import-form');
            form.submit();
        }
    }

    function handleBatchImport() {
        const successFiles = AppState.batchFiles.filter(f => f.status === 'success');
        if (successFiles.length === 0) {
            Utils.showToast('没有可导入的文件<br><small>请先预览数据并确保有成功解析的文件</small>', 'warning', 4000);
            return;
        }

        if (confirm(`确认要导入 ${successFiles.length} 个文件的数据吗？导入后将无法撤销。`)) {
            processBatchImport(successFiles);
        }
    }

    function processBatchImport(successFiles) {
        const templateId = document.getElementById('template_id').value;
        let completedCount = 0;
        let importSuccessCount = 0;
        let importErrorCount = 0;
        const importResults = [];

        // 显示导入进度
        showImportProgress(successFiles.length);

        successFiles.forEach((fileObj, index) => {
            const formData = new FormData();
            formData.append('template_id', templateId);
            formData.append('excel_file', fileObj.file);

            const xhr = new XMLHttpRequest();
            xhr.open('POST', "{:url('resume/importWithTemplate')}", true);

            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    completedCount++;
                    updateImportProgress(completedCount, successFiles.length);

                    if (xhr.status === 200) {
                        try {
                            // 尝试解析JSON响应
                            const res = JSON.parse(xhr.responseText);
                            if (res.code === 1) {
                                // 成功导入
                                importSuccessCount++;

                                // 尝试从消息中提取简历ID
                                let resumeId = null;
                                const idMatch = res.msg.match(/ID:\s*(\d+)/);
                                if (idMatch) {
                                    resumeId = idMatch[1];
                                }

                                importResults.push({
                                    fileName: fileObj.name,
                                    status: 'success',
                                    message: res.msg || '导入成功',
                                    resumeId: resumeId,
                                    details: res.data ? JSON.stringify(res.data) : null
                                });
                            } else {
                                // 导入失败
                                importErrorCount++;
                                importResults.push({
                                    fileName: fileObj.name,
                                    status: 'error',
                                    message: res.msg || '导入失败',
                                    details: res.data ? JSON.stringify(res.data) : null
                                });
                            }
                        } catch (e) {
                            // 如果不是JSON，检查是否是成功的HTML响应
                            if (xhr.responseText.includes('🎉 导入成功') ||
                                xhr.responseText.includes('成功导入') ||
                                xhr.responseText.includes('导入成功')) {

                                importSuccessCount++;

                                // 尝试从HTML中提取简历ID和消息
                                let message = '导入成功';
                                let resumeId = null;

                                // 查找成功消息
                                const successMatch = xhr.responseText.match(/成功导入\s*(\d+)\s*条简历数据/);
                                if (successMatch) {
                                    message = `成功导入 ${successMatch[1]} 条简历数据`;
                                }

                                // 查找简历ID
                                const idMatch = xhr.responseText.match(/ID:\s*(\d+)/);
                                if (idMatch) {
                                    resumeId = idMatch[1];
                                }

                                importResults.push({
                                    fileName: fileObj.name,
                                    status: 'success',
                                    message: message,
                                    resumeId: resumeId
                                });
                            } else {
                                // 导入失败
                                importErrorCount++;

                                // 尝试从HTML中提取错误信息
                                let errorMessage = '导入失败';
                                const errorMatch = xhr.responseText.match(/<div[^>]*class="[^"]*alert-danger[^"]*"[^>]*>(.*?)<\/div>/s);
                                if (errorMatch) {
                                    errorMessage = errorMatch[1].replace(/<[^>]*>/g, '').trim();
                                }

                                importResults.push({
                                    fileName: fileObj.name,
                                    status: 'error',
                                    message: errorMessage,
                                    details: '解析响应失败: ' + e.message
                                });
                            }
                        }
                    } else {
                        importErrorCount++;
                        importResults.push({
                            fileName: fileObj.name,
                            status: 'error',
                            message: '请求失败: ' + xhr.status + ' ' + xhr.statusText
                        });
                    }

                    // 检查是否所有文件都处理完成
                    if (completedCount === successFiles.length) {
                        hideImportProgress();
                        showBatchImportResults(importResults, importSuccessCount, importErrorCount);
                    }
                }
            };

            xhr.send(formData);
        });
    }

    function showImportProgress(totalFiles) {
        const progressHTML = `
            <div id="import-progress-modal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 30px; border-radius: 8px; min-width: 400px; text-align: center;">
                    <h4><i class="fa fa-upload"></i> 批量导入进行中...</h4>
                    <div class="progress" style="margin: 20px 0;">
                        <div class="progress-bar progress-bar-striped active" id="import-progress-bar" style="width: 0%"></div>
                    </div>
                    <p id="import-progress-text">正在导入第 1 个文件，共 ${totalFiles} 个文件</p>
                    <small class="text-muted">请勿关闭页面，导入过程中请耐心等待...</small>
                </div>
            </div>
        `;
        document.body.insertAdjacentHTML('beforeend', progressHTML);
    }

    function updateImportProgress(completed, total) {
        const progressBar = document.getElementById('import-progress-bar');
        const progressText = document.getElementById('import-progress-text');

        if (progressBar && progressText) {
            const percentage = Math.round((completed / total) * 100);
            progressBar.style.width = percentage + '%';
            progressText.textContent = `正在导入第 ${completed + 1} 个文件，共 ${total} 个文件`;
        }
    }

    function hideImportProgress() {
        const progressModal = document.getElementById('import-progress-modal');
        if (progressModal) {
            progressModal.remove();
        }
    }

    function showBatchImportResults(results, successCount, errorCount) {
        // 清空预览数据，显示导入结果
        const container = document.getElementById('preview-data');

        let resultHTML = `
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 style="margin: 0; color: #303133;">
                        <i class="fa fa-${successCount > 0 ? 'check-circle text-success' : 'exclamation-triangle text-danger'}"></i>
                        批量导入完成
                    </h4>
                </div>
                <div class="panel-body">
                    <div class="alert alert-info" style="margin-bottom: 20px;">
                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fa fa-check-circle text-success"></i> 导入成功</h5>
                                <p class="text-success" style="font-size: 24px; font-weight: bold; margin: 0;">
                                    ${successCount} 个文件
                                </p>
                            </div>
                            <div class="col-md-6">
                                <h5><i class="fa fa-times-circle text-danger"></i> 导入失败</h5>
                                <p class="text-danger" style="font-size: 24px; font-weight: bold; margin: 0;">
                                    ${errorCount} 个文件
                                </p>
                            </div>
                        </div>
                    </div>
        `;

        if (results.length > 0) {
            resultHTML += `
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h5 class="panel-title">
                                <i class="fa fa-list"></i> 详细结果
                                <small class="text-muted">(点击展开查看详情)</small>
                            </h5>
                        </div>
                        <div class="panel-body">
                            <div class="panel-group" id="import-results-accordion">
            `;

            results.forEach((result, index) => {
                const isSuccess = result.status === 'success';
                const panelClass = isSuccess ? 'panel-success' : 'panel-danger';
                const iconClass = isSuccess ? 'fa-check-circle' : 'fa-times-circle';
                const statusText = isSuccess ? '导入成功' : '导入失败';

                resultHTML += `
                                <div class="panel ${panelClass}">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" data-parent="#import-results-accordion" href="#result-${index}">
                                                <i class="fa ${iconClass}"></i>
                                                ${result.fileName} - ${statusText}
                                                ${isSuccess && result.resumeId ? `<small class="text-muted">(简历ID: ${result.resumeId})</small>` : ''}
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="result-${index}" class="panel-collapse collapse ${index === 0 ? 'in' : ''}">
                                        <div class="panel-body">
                                            <p><strong>处理结果：</strong>${result.message}</p>
                                            ${result.details ? `<p><strong>详细信息：</strong>${result.details}</p>` : ''}
                                            ${isSuccess ? `
                                                <div class="alert alert-info">
                                                    <i class="fa fa-info-circle"></i>
                                                    该简历已成功保存到数据库，您可以在简历列表中查看。
                                                </div>
                                            ` : `
                                                <div class="alert alert-warning">
                                                    <i class="fa fa-exclamation-triangle"></i>
                                                    请检查文件格式和数据内容，修正后重新导入。
                                                </div>
                                            `}
                                        </div>
                                    </div>
                                </div>
                `;
            });

            resultHTML += `
                            </div>
                        </div>
                    </div>
            `;
        }

        resultHTML += `
                </div>
            </div>
        `;

        // 显示结果
        container.innerHTML = resultHTML;

        if (successCount > 0) {
            Utils.showToast(`批量导入完成<br><small>导入成功 ${successCount} 个文件，导入失败 ${errorCount} 个文件</small><br><small>3秒后自动跳转到简历列表</small>`, 'success', 5000);

            // 3秒后跳转到简历列表页面
            setTimeout(() => {
                window.location.href = "{:url('admin/resume/index')}";
            }, 3000);
        } else {
            Utils.showToast(`批量导入失败<br><small>导入成功 0 个文件，导入失败 ${errorCount} 个文件</small><br><small>请检查文件格式和数据</small>`, 'error', 5000);
        }
    }

    function handleRePreview() {
        AppState.previewLoaded = false;
        AppState.updateStep(3);
        handlePreviewClick();
    }



    function handleCancelImport() {
        document.getElementById('preview-container').style.display = 'none';
        AppState.previewLoaded = false;
        AppState.updateStep(AppState.fileSelected ? 3 : (AppState.templateSelected ? 2 : 1));
    }

    function handleToggleMapping() {
        const mappingDiv = document.getElementById('template-mapping');
        const toggleBtn = document.getElementById('toggle-mapping');

        if (mappingDiv.style.display === 'none') {
            mappingDiv.style.display = 'block';
            toggleBtn.innerHTML = '<i class="fa fa-eye-slash"></i> 隐藏详情';
        } else {
            mappingDiv.style.display = 'none';
            toggleBtn.innerHTML = '<i class="fa fa-eye"></i> 显示详情';
        }
    }

    function handleFormReset() {
        AppState.templateSelected = false;
        AppState.fileSelected = false;
        AppState.previewLoaded = false;
        AppState.updateStep(1);

        // 重置批量文件
        AppState.clearBatchFiles();
        document.getElementById('file-items').innerHTML = '';
        hideBatchFileList();

        document.getElementById('template-info').style.display = 'none';
        document.getElementById('preview-container').style.display = 'none';
        hideFileInfo();
        resetTemplateInfo();

        // 重置导入模式到单文件
        AppState.setImportMode('single');
        document.getElementById('single-mode-btn').classList.add('active');
        document.getElementById('batch-mode-btn').classList.remove('active');

        // 重新选择默认模板
        selectDefaultTemplate();

        Utils.showToast('表单已重置，已自动选择默认模板', 'info');
    }

    // 选择默认模板
    function selectDefaultTemplate() {
        const templateSelect = document.getElementById('template_id');
        if (templateSelect) {
            // 查找默认模板选项
            const defaultOption = templateSelect.querySelector('option[selected]');
            if (defaultOption) {
                // 设置选中默认模板
                templateSelect.value = defaultOption.value;

                // 触发change事件，加载模板信息
                const changeEvent = new Event('change', { bubbles: true });
                templateSelect.dispatchEvent(changeEvent);
            } else {
                // 如果没有默认模板，选择第一个可用模板
                const firstTemplate = templateSelect.querySelector('option[value]:not([value=""])');
                if (firstTemplate) {
                    templateSelect.value = firstTemplate.value;
                    const changeEvent = new Event('change', { bubbles: true });
                    templateSelect.dispatchEvent(changeEvent);
                }
            }
        }
    }

    // 辅助函数
    function displayTemplateInfo(template) {
        const description = template.description || '暂无描述';
        // 格式化创建时间
        let createTimeText = '未知';
        if (template.create_time_text) {
            // 优先使用后端格式化的时间字符串
            createTimeText = template.create_time_text;
        } else if (template.create_time) {
            // 如果没有格式化字符串，但有时间戳，则前端转换
            if (typeof template.create_time === 'number' || (typeof template.create_time === 'string' && /^\d+$/.test(template.create_time))) {
                const timestamp = parseInt(template.create_time) * 1000; // 转换为毫秒
                const date = new Date(timestamp);
                createTimeText = date.getFullYear() + '-' +
                    String(date.getMonth() + 1).padStart(2, '0') + '-' +
                    String(date.getDate()).padStart(2, '0') + ' ' +
                    String(date.getHours()).padStart(2, '0') + ':' +
                    String(date.getMinutes()).padStart(2, '0');
            } else {
                createTimeText = template.create_time;
            }
        }

        Utils.safeSetContent('template-description', `
            <div class="template-desc">
                <p>${description}</p>
                <div class="template-meta">
                    <small class="text-muted">
                        <i class="fa fa-calendar"></i> 创建时间: ${createTimeText}
                        ${template.is_default ? '<span class="label label-primary">默认模板</span>' : ''}
                    </small>
                </div>
            </div>
        `);

        // 显示字段映射概览和详情
        renderMappingSummary(template.config);
        renderMappingInfo(template.config);
    }

    function showTemplateError(message) {
        Utils.safeSetContent('template-description', `
            <div class="alert alert-danger">
                <i class="fa fa-exclamation-triangle"></i> ${message}
            </div>
        `);
        Utils.safeSetContent('template-mapping-summary', `
            <div class="alert alert-danger">${message}</div>
        `);
        Utils.safeSetContent('template-mapping', `
            <div class="alert alert-danger">${message}</div>
        `);
    }

    function resetTemplateInfo() {
        Utils.safeSetContent('template-description', '请先选择导入模板');
        Utils.safeSetContent('template-mapping-summary', `
            <div class="alert alert-info">请先选择导入模板查看字段映射</div>
        `);
        Utils.safeSetContent('template-mapping', `
            <div class="alert alert-info">请先选择导入模板查看字段映射详情</div>
        `);
        Utils.safeSetText('mapping-count', '0');
    }

    function displayFileInfo(file) {
        const uploadArea = document.getElementById('upload-area');
        const uploadContent = uploadArea.querySelector('.upload-content');
        const uploadSuccess = uploadArea.querySelector('.upload-success');

        uploadContent.style.display = 'none';
        uploadSuccess.style.display = 'block';
        uploadArea.classList.add('file-selected');

        document.getElementById('file-name').textContent = file.name;
        document.getElementById('file-size').textContent = Utils.formatFileSize(file.size);
    }

    function hideFileInfo() {
        const uploadArea = document.getElementById('upload-area');
        const uploadContent = uploadArea.querySelector('.upload-content');
        const uploadSuccess = uploadArea.querySelector('.upload-success');

        uploadContent.style.display = 'block';
        uploadSuccess.style.display = 'none';
        uploadArea.classList.remove('file-selected');
    }

    function showPreviewLoading() {
        document.getElementById('preview-loading').style.display = 'block';
        document.getElementById('preview-error').style.display = 'none';
        document.getElementById('preview-data').style.display = 'none';
    }

    function hidePreviewLoading() {
        document.getElementById('preview-loading').style.display = 'none';
    }

    function showPreviewError(message) {
        document.getElementById('preview-error').style.display = 'block';
        document.getElementById('error-message').innerHTML = message;
        document.getElementById('preview-data').style.display = 'none';
    }

    function updatePreviewStats(data, errorCount, warningCount, successFiles, failedFiles) {
        if (typeof data === 'object' && data !== null) {
            // 单文件模式
            document.getElementById('preview-count').textContent = data.count || 0;
            document.getElementById('error-count').textContent = (data.errors && data.errors.length) || 0;
            document.getElementById('warning-count').textContent = 0;

            // 更新标签为单文件模式
            updateStatsLabels('single');

            // 隐藏批量文件统计
            hideBatchFileStats();

            // 隐藏批量统计信息
            hideBatchStatsInHeader();
        } else {
            // 批量模式 - data 是 totalResumes
            document.getElementById('preview-count').textContent = data || 0;
            document.getElementById('error-count').textContent = errorCount || 0;
            document.getElementById('warning-count').textContent = warningCount || 0;

            // 显示并更新批量文件统计
            showBatchFileStats(successFiles, failedFiles);

            // 更新标签为批量模式
            updateStatsLabels('batch');

            // 隐藏原来的批量统计信息（现在已经集成到主统计区域）
            hideBatchStatsInHeader();
        }
    }

    function updateStatsLabels(mode) {
        const previewCountLabel = document.querySelector('#preview-count').nextElementSibling;
        const errorCountLabel = document.querySelector('#error-count').nextElementSibling;
        const warningCountLabel = document.querySelector('#warning-count').nextElementSibling;

        if (mode === 'batch') {
            previewCountLabel.textContent = '总简历数';
            errorCountLabel.textContent = '数据错误';
            warningCountLabel.textContent = '警告数';
        } else {
            previewCountLabel.textContent = '简历数';
            errorCountLabel.textContent = '错误数';
            warningCountLabel.textContent = '警告数';
        }
    }

    function updateBatchStatsInHeader(successFiles, failedFiles) {
        // 检查是否已存在批量统计区域
        let batchStatsContainer = document.getElementById('batch-stats-header');
        if (!batchStatsContainer) {
            // 创建批量统计区域
            const previewHeader = document.querySelector('.preview-header .row');
            if (previewHeader) {
                batchStatsContainer = document.createElement('div');
                batchStatsContainer.id = 'batch-stats-header';
                batchStatsContainer.className = 'col-md-12';
                batchStatsContainer.style.marginTop = '15px';

                previewHeader.appendChild(batchStatsContainer);
            }
        }

        if (batchStatsContainer) {
            batchStatsContainer.innerHTML = `
                <div class="batch-stats-header">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="batch-stat-item-small">
                                <span class="batch-stat-number-small text-primary">${successFiles}</span>
                                <span class="batch-stat-label-small">成功文件</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="batch-stat-item-small">
                                <span class="batch-stat-number-small text-danger">${failedFiles}</span>
                                <span class="batch-stat-label-small">失败文件</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }
    }

    function hideBatchStatsInHeader() {
        const batchStatsContainer = document.getElementById('batch-stats-header');
        if (batchStatsContainer) {
            batchStatsContainer.style.display = 'none';
        }
    }

    function showBatchFileStats(successFiles, failedFiles) {
        // 显示成功文件统计
        const successFilesStat = document.getElementById('success-files-stat');
        const failedFilesStat = document.getElementById('failed-files-stat');

        if (successFilesStat && failedFilesStat) {
            successFilesStat.style.display = 'block';
            failedFilesStat.style.display = 'block';

            document.getElementById('success-files-count').textContent = successFiles || 0;
            document.getElementById('failed-files-count').textContent = failedFiles || 0;
        }
    }

    function hideBatchFileStats() {
        // 隐藏批量文件统计
        const successFilesStat = document.getElementById('success-files-stat');
        const failedFilesStat = document.getElementById('failed-files-stat');

        if (successFilesStat && failedFilesStat) {
            successFilesStat.style.display = 'none';
            failedFilesStat.style.display = 'none';
        }
    }

    function updateBatchPreviewStats(successCount, errorCount) {
        document.getElementById('preview-count').textContent = successCount;
        document.getElementById('error-count').textContent = errorCount;
        document.getElementById('warning-count').textContent = 0;
    }

    function renderBatchPreviewData(allResults) {
        const container = document.getElementById('preview-data');
        let html = '';

        if (allResults && allResults.length > 0) {
            // 计算统计信息
            const totalErrors = allResults.reduce((sum, r) => sum + (r.errors ? r.errors.length : 0), 0);
            const totalResumes = allResults.reduce((sum, r) => sum + (r.data ? r.data.length : 0), 0);
            const failedFiles = AppState.batchFiles.filter(f => f.status === 'error').length;

            // 更新预览头部的统计信息
            updatePreviewStats(totalResumes, totalErrors, 0, allResults.length, failedFiles);

            // 如果有错误，显示错误汇总
            if (totalErrors > 0) {
                html += `
                    <div class="alert alert-warning" style="margin: 20px;">
                        <h4 style="cursor: pointer; margin-bottom: 0;" onclick="toggleErrorDetails()">
                            <i class="fa fa-exclamation-triangle"></i> 发现 ${totalErrors} 个问题
                            <i class="fa fa-chevron-down pull-right" id="error-toggle-icon"></i>
                        </h4>
                        <div class="panel-group" id="error-accordion" style="margin-top: 15px; display: none;">
                `;

                allResults.forEach((result, index) => {
                    if (result.errors && result.errors.length > 0) {
                        html += `
                            <div class="panel panel-warning">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        <a data-toggle="collapse" data-parent="#error-accordion" href="#error-${index}" style="color: #333;">
                                            <i class="fa fa-file-excel-o"></i> <span style="color: #333;">${result.fileName} (${result.errors.length} 个错误)</span>
                                        </a>
                                    </h4>
                                </div>
                                <div id="error-${index}" class="panel-collapse collapse">
                                    <div class="panel-body">
                                        <ul class="list-unstyled" style="color: #333; line-height: 1.6;">
                        `;
                        result.errors.forEach(error => {
                            html += `<li style="margin-bottom: 8px;"><i class="fa fa-exclamation-triangle text-warning"></i> <span style="color: #856404; font-weight: 500;">${error}</span></li>`;
                        });
                        html += `
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                });

                html += `
                        </div>
                        <p class="text-muted" style="margin-top: 15px;">
                            <i class="fa fa-info-circle"></i>
                            这些错误不会阻止导入，但可能影响数据质量。建议修正后重新导入。
                        </p>
                    </div>
                `;
            }

            // 渲染每个文件的预览数据
            allResults.forEach((result, index) => {
                html += `
                    <div class="preview-resume-panel panel panel-default" style="margin-bottom: 20px;">
                        <div class="panel-heading">
                            <h4 class="panel-title" style="cursor: pointer; margin-bottom: 0;" onclick="toggleFilePreview(${index})">
                                <i class="fa fa-file-excel-o text-success"></i>
                                文件 ${index + 1}: ${result.fileName}
                                <small class="text-muted">(${result.data ? result.data.length : 0} 条简历)</small>
                                <i class="fa fa-chevron-down pull-right" id="file-toggle-icon-${index}"></i>
                            </h4>
                        </div>
                        <div class="panel-body" id="file-preview-body-${index}" style="display: none;">
                `;

                if (result.data && result.data.length > 0) {
                    // 渲染简历数据（使用现有的渲染逻辑）
                    result.data.forEach((resume, resumeIndex) => {
                        const resumeId = `batch-${index}-${resumeIndex}`;
                        html += `
                            <div class="preview-resume-panel panel panel-default" style="margin-bottom: 15px;">
                                <div class="panel-heading">
                                    <ul class="nav nav-tabs" role="tablist">
                                        <li class="active"><a href="#${resumeId}-basic" data-toggle="tab"><i class="fa fa-user"></i> 基本信息</a></li>
                                        <li><a href="#${resumeId}-contact" data-toggle="tab"><i class="fa fa-phone"></i> 联系人信息</a></li>
                                        <li><a href="#${resumeId}-education" data-toggle="tab"><i class="fa fa-graduation-cap"></i> 教育经历</a></li>
                                        <li><a href="#${resumeId}-work" data-toggle="tab"><i class="fa fa-briefcase"></i> 工作经历</a></li>
                                        <li><a href="#${resumeId}-photos" data-toggle="tab"><i class="fa fa-image"></i> 照片资料</a></li>
                                    </ul>
                                </div>
                                <div class="panel-body">
                                    <div class="tab-content">
                        `;

                        // 基本信息标签页
                        html += renderBasicInfoTab(resume, resumeId);

                        // 联系人标签页
                        html += renderContactTab(resume, resumeId);

                        // 教育经历标签页
                        html += renderEducationTab(resume, resumeId);

                        // 工作经历标签页
                        html += renderWorkTab(resume, resumeId);

                        // 照片资料标签页
                        html += renderPhotosTab(resume, resumeId);

                        html += `
                                    </div>
                                </div>
                            </div>
                        `;
                    });
                } else {
                    html += `
                        <div class="alert alert-warning">
                            <i class="fa fa-exclamation-triangle"></i> 该文件没有解析到有效的简历数据
                        </div>
                    `;
                }

                html += `
                        </div>
                    </div>
                `;
            });
        } else {
            html += `
                <div class="alert alert-info text-center" style="margin: 20px;">
                    <i class="fa fa-info-circle"></i> 暂无预览数据
                </div>
            `;
        }

        container.innerHTML = html;
        container.style.display = 'block';
    }

    function renderMappingSummary(config) {
        if (typeof config === 'string') {
            try {
                config = JSON.parse(config);
            } catch (e) {
                Utils.safeSetContent('template-mapping-summary', `
                    <div class="alert alert-danger">配置数据格式错误</div>
                `);
                return;
            }
        }

        if (!config) {
            Utils.safeSetContent('template-mapping-summary', `
                <div class="alert alert-warning">该模板未配置字段映射</div>
            `);
            return;
        }

        // 计算所有字段统计
        const allFields = [
            'name', 'intended_position', 'applied_position', 'gender', 'ethnicity', 'height', 'weight', 'id_card', 'age', 'birth_date', 'phone', 'marital_status', 'hukou_location', 'residence_address', 'highest_education', 'cantonese_level', 'mandarin_level', 'english_level', 'hk_macau_passport', 'hk_macau_passport_expiry', 'overseas_experience', 'overseas_region',
            'contact_relation', 'contact_name', 'contact_age', 'contact_job',
            'education_start', 'education_end', 'education_school', 'education_major', 'graduation_education',
            'job_start', 'job_end', 'job_company', 'job_position', 'job_description',
            'special_certificate', 'hobbies', 'self_evaluation', 'contact_person', 'status',
            'avatar', 'full_body_photo', 'id_card_front', 'id_card_back', 'hk_macau_passport_front', 'hk_macau_passport_back', 'additional_photos'
        ];

        // 使用新的字段检查逻辑
        const mappedFields = allFields.filter(field => Utils.isFieldMapped(config, field));
        const unmappedFields = allFields.filter(field => !Utils.isFieldMapped(config, field));
        const totalFields = allFields.length;
        const mappingRate = Math.round((mappedFields.length / totalFields) * 100);

        Utils.safeSetText('mapping-count', mappedFields.length);

        let html = `
            <div class="mapping-summary">
                <div class="summary-stats">
                    <div class="row">
                        <div class="col-xs-3">
                            <div class="stat-box text-center">
                                <div class="stat-number text-success">${mappedFields.length}</div>
                                <div class="stat-label">已映射</div>
                            </div>
                        </div>
                        <div class="col-xs-3">
                            <div class="stat-box text-center">
                                <div class="stat-number text-warning">${unmappedFields.length}</div>
                                <div class="stat-label">未映射</div>
                            </div>
                        </div>
                        <div class="col-xs-3">
                            <div class="stat-box text-center">
                                <div class="stat-number text-info">${totalFields}</div>
                                <div class="stat-label">总字段</div>
                            </div>
                        </div>
                        <div class="col-xs-3">
                            <div class="stat-box text-center">
                                <div class="stat-number ${mappingRate >= 80 ? 'text-success' : mappingRate >= 50 ? 'text-warning' : 'text-danger'}">${mappingRate}%</div>
                                <div class="stat-label">映射率</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mapping-preview">
                    <h5><i class="fa fa-check-circle text-success"></i> 已映射字段预览</h5>
        `;

        let count = 0;
        for (const field of mappedFields) {
            if (count >= 5) {
                html += `<div class="text-muted">... 还有 ${mappedFields.length - 5} 个已映射字段</div>`;
                break;
            }
            html += `
                <div class="mapping-item">
                    <span class="status-indicator status-success"></span>
                    <strong>${getFieldDisplayName(field)}:</strong> ${config[field]}
                </div>
            `;
            count++;
        }

        if (unmappedFields.length > 0) {
            html += `
                    <h5 style="margin-top: 15px;"><i class="fa fa-exclamation-triangle text-warning"></i> 未映射字段预览</h5>
            `;

            const showUnmapped = Math.min(5, unmappedFields.length);
            for (let i = 0; i < showUnmapped; i++) {
                html += `
                    <div class="mapping-item">
                        <span class="status-indicator status-warning"></span>
                        <strong>${getFieldDisplayName(unmappedFields[i])}:</strong> <span class="text-muted">未配置</span>
                    </div>
                `;
            }

            if (unmappedFields.length > 5) {
                html += `<div class="text-muted">... 还有 ${unmappedFields.length - 5} 个未映射字段</div>`;
            }
        }

        html += `
                </div>
            </div>
        `;

        Utils.safeSetContent('template-mapping-summary', html);
    }

    // 获取字段显示名称
    function getFieldDisplayName(field) {
        const fieldNames = {
            'name': '姓名',
            'intended_position': '意向岗位',
            'applied_position': '申请岗位',
            'gender': '性别',
            'ethnicity': '民族',
            'height': '身高',
            'weight': '体重',
            'id_card': '身份证号',
            'age': '年龄',
            'birth_date': '出生日期',
            'phone': '手机号码',
            'marital_status': '婚姻状况',
            'hukou_location': '户口所在地',
            'residence_address': '常住地址',
            'highest_education': '最高学历',
            'cantonese_level': '粤语熟练度',
            'mandarin_level': '国语熟练度',
            'english_level': '英语熟练度',
            'hk_macau_passport': '港澳通行证编号',
            'hk_macau_passport_expiry': '港澳通行证到期时间',
            'overseas_experience': '海外工作经历',
            'overseas_region': '海外工作地区',
            'contact_relation': '联系人关系',
            'contact_name': '联系人姓名',
            'contact_age': '联系人年龄',
            'contact_job': '联系人工作',
            'education_start': '教育开始时间',
            'education_end': '教育结束时间',
            'education_school': '学校名称',
            'education_major': '专业',
            'graduation_education': '学历',
            'job_start': '工作开始时间',
            'job_end': '工作结束时间',
            'job_company': '工作单位',
            'job_position': '工作岗位',
            'job_description': '工作内容',
            'special_certificate': '特殊职业资格证',
            'hobbies': '兴趣爱好',
            'self_evaluation': '自我评价',
            'contact_person': '对接人',
            'status': '状态',
            'avatar': '头像',
            'full_body_photo': '全身照',
            'id_card_front': '身份证正面',
            'id_card_back': '身份证反面',
            'hk_macau_passport_front': '港澳通行证正面',
            'hk_macau_passport_back': '港澳通行证反面',
            'additional_photos': '补充照片'
        };
        return fieldNames[field] || field;
    }

    // 渲染字段映射信息
    function renderMappingInfo(config) {
        // 确保config是对象
        if (typeof config === 'string') {
            try {
                config = JSON.parse(config);
            } catch (e) {
                console.error('配置数据解析失败:', e);
                document.getElementById('template-mapping').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fa fa-exclamation-triangle"></i> 配置数据格式错误，无法解析
                    </div>
                `;
                return;
            }
        }

        if (!config) {
            document.getElementById('template-mapping').innerHTML = `
                <div class="alert alert-warning">
                    <i class="fa fa-info-circle"></i> 该模板未配置字段映射
                </div>
            `;
            return;
        }

        // 按类别组织字段
        const fieldCategories = {
            '基本信息': ['name', 'intended_position', 'applied_position', 'gender', 'ethnicity', 'height', 'weight', 'id_card', 'age', 'birth_date', 'phone', 'marital_status', 'hukou_location', 'residence_address', 'highest_education', 'cantonese_level', 'mandarin_level', 'english_level', 'hk_macau_passport', 'hk_macau_passport_expiry', 'overseas_experience', 'overseas_region'],
            '联系人信息': ['contact_relation', 'contact_name', 'contact_age', 'contact_job'],
            '教育经历': ['education_start', 'education_end', 'education_school', 'education_major', 'graduation_education'],
            '工作经历': ['job_start', 'job_end', 'job_company', 'job_position', 'job_description'],
            '其他信息': ['special_certificate', 'hobbies', 'self_evaluation', 'contact_person', 'status'],
            '图片信息': ['avatar', 'full_body_photo', 'id_card_front', 'id_card_back', 'hk_macau_passport_front', 'hk_macau_passport_back', 'additional_photos']
        };

        let html = '';
        let totalMapped = 0;
        let totalUnmapped = 0;

        for (const [categoryName, fields] of Object.entries(fieldCategories)) {
            // 分离已映射和未映射的字段（使用新的检查逻辑）
            const mappedFields = fields.filter(field => Utils.isFieldMapped(config, field));
            const unmappedFields = fields.filter(field => !Utils.isFieldMapped(config, field));

            totalMapped += mappedFields.length;
            totalUnmapped += unmappedFields.length;

            // 只显示有字段的类别（已映射或未映射）
            if (mappedFields.length > 0 || unmappedFields.length > 0) {
                const categoryId = `mapping-category-${categoryName.replace(/\s+/g, '-')}`;
                html += `
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <i class="fa fa-folder-open text-primary"></i> ${categoryName}
                            <span class="pull-right" style="font-size: 12px; color: #666;">
                                已映射字段：<span class="text-success">${mappedFields.length}</span> |
                                未映射字段：<span class="text-warning">${unmappedFields.length}</span>
                            </span>
                        </div>
                        <div class="panel-body">
                            <table class="table mapping-table">
                                <thead>
                                    <tr>
                                        <th width="40%">字段名称</th>
                                        <th width="30%">Excel单元格</th>
                                        <th width="30%">状态</th>
                                    </tr>
                                </thead>
                                <tbody>
                `;

                // 先显示已映射的字段
                mappedFields.forEach(field => {
                    let cellInfo = config[field];
                    let statusInfo = '已映射';

                    // 对于数组类型的字段，显示更详细的信息
                    if (Array.isArray(config[field])) {
                        const validItems = config[field].filter(item =>
                            item !== null && item !== undefined &&
                            (typeof item === 'string' ? item.trim() !== '' : true)
                        );
                        cellInfo = `[${config[field].join(', ')}]`;
                        statusInfo = `已映射 (${validItems.length}/${config[field].length} 有效)`;
                    }

                    html += `
                        <tr class="success">
                            <td>
                                <strong>${getFieldDisplayName(field)}</strong>
                                <br><small class="text-muted">${field}</small>
                            </td>
                            <td>
                                <code class="text-success">${cellInfo}</code>
                            </td>
                            <td>
                                <span class="label label-success">
                                    <i class="fa fa-check"></i> ${statusInfo}
                                </span>
                            </td>
                        </tr>
                    `;
                });

                // 再显示未映射的字段
                unmappedFields.forEach(field => {
                    let statusReason = '未配置';
                    let statusClass = 'warning';
                    let statusIcon = 'exclamation-triangle';

                    // 检查是否配置了但值为空
                    if (config && config.hasOwnProperty(field)) {
                        const value = config[field];
                        if (value === '' || value === null || value === undefined) {
                            statusReason = '配置为空值';
                        } else if (typeof value === 'string' && value.trim() === '') {
                            statusReason = '配置为空字符串';
                        } else if (Array.isArray(value) && value.length === 0) {
                            statusReason = '配置为空数组';
                        } else if (Array.isArray(value)) {
                            const validItems = value.filter(item =>
                                item !== null && item !== undefined &&
                                (typeof item === 'string' ? item.trim() !== '' : true)
                            );
                            if (validItems.length === 0) {
                                statusReason = `数组无有效值 [${value.join(', ')}]`;
                            }
                        }
                        statusClass = 'danger';
                        statusIcon = 'times-circle';
                    }

                    html += `
                        <tr class="${statusClass === 'danger' ? 'danger' : 'warning'}">
                            <td>
                                <strong>${getFieldDisplayName(field)}</strong>
                                <br><small class="text-muted">${field}</small>
                            </td>
                            <td>
                                <span class="text-muted">
                                    <i class="fa fa-minus"></i> ${statusReason}
                                </span>
                            </td>
                            <td>
                                <span class="label label-${statusClass}">
                                    <i class="fa fa-${statusIcon}"></i> 未映射
                                </span>
                            </td>
                        </tr>
                    `;
                });

                html += `
                                </tbody>
                            </table>
                        </div>
                    </div>
                `;
            }
        }

        // 添加统计信息
        if (html !== '') {
            const totalFields = totalMapped + totalUnmapped;
            const mappingRate = totalFields > 0 ? Math.round((totalMapped / totalFields) * 100) : 0;

            const statsHtml = `
                <div class="alert alert-info mapping-stats">
                    <h4><i class="fa fa-bar-chart"></i> 字段映射统计</h4>
                    <div class="row">
                        <div class="col-md-3">
                            <div class="stat-item">
                                <span class="stat-number text-primary">${totalFields}</span>
                                <span class="stat-label">总字段数</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <span class="stat-number text-success">${totalMapped}</span>
                                <span class="stat-label">已映射</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <span class="stat-number text-warning">${totalUnmapped}</span>
                                <span class="stat-label">未映射</span>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="stat-item">
                                <span class="stat-number ${mappingRate >= 80 ? 'text-success' : mappingRate >= 50 ? 'text-warning' : 'text-danger'}">${mappingRate}%</span>
                                <span class="stat-label">映射率</span>
                            </div>
                        </div>
                    </div>
                    ${totalUnmapped > 0 ? `
                        <div class="alert alert-warning" style="margin-top: 15px; margin-bottom: 0;">
                            <i class="fa fa-info-circle"></i>
                            <strong>提示：</strong>有 ${totalUnmapped} 个字段未配置映射，这些字段在导入时将被忽略。
                            如需导入这些字段，请在模板管理中配置相应的Excel单元格映射。
                        </div>
                    ` : `
                        <div class="alert alert-success" style="margin-top: 15px; margin-bottom: 0;">
                            <i class="fa fa-check-circle"></i>
                            <strong>完美！</strong>所有字段都已配置映射，可以完整导入简历数据。
                        </div>
                    `}
                </div>
            `;

            html = statsHtml + html;
        } else {
            html = `
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> 该模板暂无字段映射配置
                </div>
            `;
        }

        Utils.safeSetContent('template-mapping', html);
    }



    // 渲染预览数据
    function renderPreviewData(data, errors, dateConversions) {
        const container = document.getElementById('preview-data');
        let html = '';

        // 处理错误信息
        if (errors && errors.length > 0) {
            html += `
                <div class="alert alert-warning" style="margin: 20px;">
                    <h4 style="cursor: pointer; margin-bottom: 0;" onclick="toggleSingleErrorDetails()">
                        <i class="fa fa-exclamation-triangle"></i> 发现 ${errors.length} 个问题
                        <i class="fa fa-chevron-down pull-right" id="single-error-toggle-icon"></i>
                    </h4>
                    <ul class="list-unstyled" id="single-error-details" style="margin-top: 15px; display: none;">
            `;
            errors.forEach(error => {
                html += `<li style="margin-bottom: 8px;"><i class="fa fa-exclamation-triangle text-warning"></i> <span style="color: #856404; font-weight: 500;">${error}</span></li>`;
            });
            html += `
                    </ul>
                </div>
            `;
        }

        // 显示日期转换详情
        if (dateConversions && Object.keys(dateConversions).length > 0) {
            html += `
                <div class="alert alert-info" style="margin: 20px;">
                    <h4><i class="fa fa-exchange"></i> 日期转换详情</h4>
                    <div class="table-responsive">
                        <table class="table table-condensed table-bordered">
                            <thead>
                                <tr>
                                    <th>行号</th>
                                    <th>字段</th>
                                    <th>原始值</th>
                                    <th>转换后</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
            `;

            for (const rowIndex in dateConversions) {
                const conversions = dateConversions[rowIndex];
                for (const field in conversions) {
                    const conversion = conversions[field];
                    html += `
                        <tr>
                            <td>${rowIndex}</td>
                            <td>${conversion.field_name}</td>
                            <td><code>${conversion.original}</code></td>
                            <td><strong>${conversion.converted}</strong></td>
                        </tr>
                    `;
                }
            }

            html += `
                            </tbody>
                        </table>
                    </div>
                    <p class="text-muted">
                        <i class="fa fa-info-circle"></i>
                        以上日期字段已自动转换为标准格式，请确认转换结果是否正确。
                    </p>
                </div>
            `;
        }

        // 处理预览数据
        if (data && data.length > 0) {
            data.forEach((resume, index) => {
                const resumeName = resume['姓名'] ? resume['姓名'].replace(/<[^>]*>/g, '') : '未知';
                const resumeId = `resume-${index}`;

                html += `
                    <div class="panel panel-default panel-intro preview-resume-panel" style="margin-bottom: 30px;">
                        <div class="panel-heading">
                            <h4 style="margin: 0; color: #303133;">
                                <i class="fa fa-user text-primary"></i>
                                简历预览 #${index + 1} - ${resumeName}
                            </h4>
                            <ul class="nav nav-tabs" style="margin-top: 15px;">
                                <li class="active"><a href="#${resumeId}-basic" data-toggle="tab"><i class="fa fa-user"></i> 基本信息</a></li>
                                <li><a href="#${resumeId}-contact" data-toggle="tab"><i class="fa fa-address-book"></i> 联系人</a></li>
                                <li><a href="#${resumeId}-education" data-toggle="tab"><i class="fa fa-graduation-cap"></i> 教育经历</a></li>
                                <li><a href="#${resumeId}-work" data-toggle="tab"><i class="fa fa-briefcase"></i> 工作经历</a></li>
                                <li><a href="#${resumeId}-photos" data-toggle="tab"><i class="fa fa-image"></i> 照片资料</a></li>
                            </ul>
                        </div>
                        <div class="panel-body">
                            <div class="tab-content">
                `;

                // 基本信息标签页
                html += renderBasicInfoTab(resume, resumeId);

                // 联系人标签页
                html += renderContactTab(resume, resumeId);

                // 教育经历标签页
                html += renderEducationTab(resume, resumeId);

                // 工作经历标签页
                html += renderWorkTab(resume, resumeId);

                // 照片资料标签页
                html += renderPhotosTab(resume, resumeId);

                html += `
                            </div>
                        </div>
                    </div>
                `;
            });
        } else {
            html += `
                <div class="alert alert-info text-center" style="margin: 20px;">
                    <i class="fa fa-info-circle"></i> 暂无预览数据
                </div>
            `;
        }

        container.innerHTML = html;
        container.style.display = 'block';
    }

    // 渲染基本信息标签页
    function renderBasicInfoTab(resume, resumeId) {
        const avatarSrc = resume['头像'] || '';
        const resumeName = resume['姓名'] || '未知';
        const appliedPosition = resume['申请岗位'] || resume['意向岗位'] || '';

        let html = `
            <div class="tab-pane fade active in" id="${resumeId}-basic">
                <div class="widget-body no-padding">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="profile-user-img" style="width: 150px; height: 150px; margin: 0 auto; overflow: hidden; border-radius: 50%; border: 2px solid #eee;">
        `;

        if (avatarSrc) {
            html += `
                <a href="javascript:;" class="photo-preview" data-src="${avatarSrc}">
                    <img src="${avatarSrc}" class="img-responsive" style="width: 100%; height: 100%; object-fit: cover;">
                </a>
            `;
        } else {
            html += `
                <div style="width: 100%; height: 100%; background: #f5f5f5; display: flex; align-items: center; justify-content: center; color: #999;">
                    <i class="fa fa-user" style="font-size: 48px;"></i>
                </div>
            `;
        }

        html += `
                            </div>
                            <h3 class="profile-username text-center" style="margin-top: 10px;">${resumeName}</h3>
                            <p class="text-muted text-center">${appliedPosition}</p>
                        </div>
                        <div class="col-md-9">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover resume-table">
                                    <tbody>
        `;

        // 个人信息部分
        html += `
                                        <tr class="info">
                                            <th colspan="4" class="text-center">
                                                <i class="fa fa-user"></i> 个人信息
                                            </th>
                                        </tr>
        `;

        const personalFields = [
            ['姓名', '性别'],
            ['年龄', '民族'],
            ['身高(cm)', '体重(kg)'],
            ['身份证号', '出生日期'],
            ['手机号码', '婚姻状况']
        ];

        personalFields.forEach(fieldPair => {
            html += `<tr class="four-columns">`;
            fieldPair.forEach(field => {
                const value = resume[field] || '未填写';
                html += `<th>${field}</th><td>${value}</td>`;
            });
            html += `</tr>`;
        });

        // 地址信息部分
        html += `
                                        <tr class="info">
                                            <th colspan="4" class="text-center">
                                                <i class="fa fa-map-marker"></i> 地址信息
                                            </th>
                                        </tr>
                                        <tr>
                                            <th>户口所在地</th>
                                            <td>${resume['户口所在地'] || '未填写'}</td>
                                            <th>常住地址</th>
                                            <td>${resume['常住地址'] || '未填写'}</td>
                                        </tr>
        `;

        // 教育语言部分
        html += `
                                        <tr class="info">
                                            <th colspan="4" class="text-center">
                                                <i class="fa fa-graduation-cap"></i> 教育语言
                                            </th>
                                        </tr>
                                        <tr>
                                            <th>最高学历</th>
                                            <td>${resume['最高学历'] || '未填写'}</td>
                                            <th>粤语熟练度</th>
                                            <td>${resume['粤语熟练度'] || '未填写'}</td>
                                        </tr>
                                        <tr>
                                            <th>国语熟练度</th>
                                            <td>${resume['国语熟练度'] || '未填写'}</td>
                                            <th>英语熟练度</th>
                                            <td>${resume['英语熟练度'] || '未填写'}</td>
                                        </tr>
        `;

        // 证件信息部分
        html += `
                                        <tr class="info">
                                            <th colspan="4" class="text-center">
                                                <i class="fa fa-id-card"></i> 证件信息
                                            </th>
                                        </tr>
                                        <tr>
                                            <th>港澳通行证编号</th>
                                            <td>${resume['港澳通行证编号'] || '未填写'}</td>
                                            <th>港澳通行证到期时间</th>
                                            <td>${resume['港澳通行证到期时间'] || '未填写'}</td>
                                        </tr>
                                        <tr>
                                            <th>海外工作经历</th>
                                            <td>${resume['海外工作经历'] || '未填写'}</td>
                                            <th>海外工作地区</th>
                                            <td>${resume['海外工作地区'] || '未填写'}</td>
                                        </tr>
        `;

        // 其他信息部分
        html += `
                                        <tr class="info">
                                            <th colspan="4" class="text-center">
                                                <i class="fa fa-star"></i> 其他信息
                                            </th>
                                        </tr>
                                        <tr>
                                            <th>特殊职业资格证</th>
                                            <td colspan="3">${resume['特殊职业资格证'] || '未填写'}</td>
                                        </tr>
                                        <tr>
                                            <th>兴趣爱好</th>
                                            <td colspan="3">${resume['兴趣爱好'] || '未填写'}</td>
                                        </tr>
                                        <tr>
                                            <th>自我评价</th>
                                            <td colspan="3">${resume['自我评价'] || '未填写'}</td>
                                        </tr>
        `;

        // 岗位信息部分
        html += `
                                        <tr class="info">
                                            <th colspan="4" class="text-center">
                                                <i class="fa fa-briefcase"></i> 岗位信息
                                            </th>
                                        </tr>
                                        <tr>
                                            <th>意向岗位</th>
                                            <td>${resume['意向岗位'] || '未填写'}</td>
                                            <th>申请岗位</th>
                                            <td>${resume['申请岗位'] || '未填写'}</td>
                                        </tr>
                                        <tr>
                                            <th>对接人</th>
                                            <td colspan="3">${resume['对接人'] || '未填写'}</td>
                                        </tr>
                                        <tr class="info">
                                            <th colspan="4" class="text-center">
                                                <i class="fa fa-cog"></i> 系统信息
                                            </th>
                                        </tr>
                                        <tr>
                                            <th>状态</th>
                                            <td colspan="3">${resume['状态'] || '未填写'}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        return html;
    }

    // 渲染联系人标签页
    function renderContactTab(resume, resumeId) {
        let html = `
            <div class="tab-pane fade" id="${resumeId}-contact">
                <div class="widget-body no-padding">
        `;

        const contacts = resume['联系人信息'] || [];

        if (contacts && contacts.length > 0) {
            html += `
                <div class="table-responsive">
                    <table class="table table-bordered table-hover resume-table">
                        <thead>
                            <tr class="info">
                                <th class="text-center">序号</th>
                                <th class="text-center">姓名</th>
                                <th class="text-center">关系</th>
                                <th class="text-center">联系电话</th>
                                <th class="text-center">工作单位</th>
                                <th class="text-center">职务</th>
                                <th class="text-center">地址</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            contacts.forEach((contact, index) => {
                html += `
                    <tr>
                        <td class="text-center">${index + 1}</td>
                        <td>${contact['姓名'] || '未填写'}</td>
                        <td>${contact['关系'] || '未填写'}</td>
                        <td>${contact['联系电话'] || '未填写'}</td>
                        <td>${contact['工作单位'] || '未填写'}</td>
                        <td>${contact['职务'] || '未填写'}</td>
                        <td>${contact['地址'] || '未填写'}</td>
                    </tr>
                `;
            });

            html += `
                        </tbody>
                    </table>
                </div>
            `;
        } else {
            html += `
                <div class="alert alert-info text-center" style="margin: 20px;">
                    <i class="fa fa-info-circle"></i> 暂无联系人信息
                </div>
            `;
        }

        html += `
                </div>
            </div>
        `;

        return html;
    }

    // 渲染教育经历标签页
    function renderEducationTab(resume, resumeId) {
        let html = `
            <div class="tab-pane fade" id="${resumeId}-education">
                <div class="widget-body no-padding">
        `;

        const educations = resume['教育经历'] || [];

        if (educations && educations.length > 0) {
            html += `
                <div class="timeline">
            `;

            educations.forEach((education, index) => {
                const startTime = education['开始时间'] || '';
                const endTime = education['结束时间'] || '';
                const school = education['学校'] || '未知学校';
                const major = education['专业'] || '未知专业';
                const degree = education['学历'] || '';

                html += `
                    <div class="timeline-item">
                        <div class="timeline-marker">
                            <div class="timeline-marker-icon">
                                <i class="fa fa-graduation-cap"></i>
                            </div>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-header">
                                <h4 class="timeline-title">${school}</h4>
                            </div>
                            <div class="timeline-body">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong>开始时间:</strong> ${startTime}
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>结束时间:</strong> ${endTime}
                                    </div>
                                </div>
                `;

                // 显示专业和学历信息（如果不为空）
                if (major && major !== '未知专业') {
                    html += `
                        <div style="margin-top: 10px;">
                            <strong>专业:</strong> ${major}
                        </div>
                    `;
                }

                if (degree && degree.trim() !== '') {
                    html += `
                        <div style="margin-top: 10px;">
                            <strong>学历:</strong> ${degree}
                        </div>
                    `;
                }

                // 显示其他字段
                for (const key in education) {
                    if (!['开始时间', '结束时间', '学校', '专业', '学历'].includes(key)) {
                        html += `
                            <div style="margin-top: 10px;">
                                <strong>${key}:</strong> ${education[key] || '未填写'}
                            </div>
                        `;
                    }
                }

                html += `
                            </div>
                        </div>
                    </div>
                `;
            });

            html += `
                </div>
            `;
        } else {
            html += `
                <div class="alert alert-info text-center" style="margin: 20px;">
                    <i class="fa fa-info-circle"></i> 暂无教育经历
                </div>
            `;
        }

        html += `
                </div>
            </div>
        `;

        return html;
    }

    // 渲染工作经历标签页
    function renderWorkTab(resume, resumeId) {
        let html = `
            <div class="tab-pane fade" id="${resumeId}-work">
                <div class="widget-body no-padding">
        `;

        const workExperiences = resume['工作经历'] || [];

        if (workExperiences && workExperiences.length > 0) {
            html += `
                <div class="timeline">
            `;

            workExperiences.forEach((work, index) => {
                const startTime = work['开始时间'] || '';
                const endTime = work['结束时间'] || '';
                const company = work['公司名称'] || work['公司'] || work['工作单位'] || work['单位名称'] || '未知公司';
                const position = work['职位'] || work['岗位'] || work['职务'] || '未知职位';
                const salary = work['薪资'] || work['工资'] || '';

                html += `
                    <div class="timeline-item">
                        <div class="timeline-marker">
                            <div class="timeline-marker-icon">
                                <i class="fa fa-briefcase"></i>
                            </div>
                        </div>
                        <div class="timeline-content">
                            <div class="timeline-header">
                                <h4 class="timeline-title">${company}</h4>
                            </div>
                            <div class="timeline-body">
                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong>开始时间:</strong> ${startTime}
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>结束时间:</strong> ${endTime}
                                    </div>
                                </div>
                `;

                // 显示岗位信息（如果不为空且不是占位符）
                if (position && position !== '未知职位') {
                    html += `
                        <div style="margin-top: 10px;">
                            <strong>岗位:</strong> ${position}
                        </div>
                    `;
                }

                // 显示其他字段
                for (const key in work) {
                    if (!['开始时间', '结束时间', '公司名称', '公司', '工作单位', '职位', '岗位', '职务', '薪资'].includes(key)) {
                        html += `
                            <div style="margin-top: 10px;">
                                <strong>${key}:</strong> ${work[key] || '未填写'}
                            </div>
                        `;
                    }
                }

                html += `
                            </div>
                        </div>
                    </div>
                `;
            });

            html += `
                </div>
            `;
        } else {
            html += `
                <div class="alert alert-info text-center" style="margin: 20px;">
                    <i class="fa fa-info-circle"></i> 暂无工作经历
                </div>
            `;
        }

        html += `
                </div>
            </div>
        `;

        return html;
    }

    // 渲染照片资料标签页
    function renderPhotosTab(resume, resumeId) {
        let html = `
            <div class="tab-pane fade" id="${resumeId}-photos">
                <div class="widget-body no-padding">
                    <div class="row">
        `;

        // 图片字段映射
        const imageFields = {
            '头像': 'avatar',
            '全身照': 'full_body_photo',
            '身份证正面': 'id_card_front',
            '身份证反面': 'id_card_back',
            '港澳通行证正面': 'hk_macau_passport_front',
            '港澳通行证反面': 'hk_macau_passport_back',
            '补充照片': 'additional_photos'
        };

        let hasImages = false;

        for (const [fieldName, fieldKey] of Object.entries(imageFields)) {
            const imageValue = resume[fieldName];

            if (imageValue) {
                hasImages = true;

                if (fieldName === '补充照片') {
                    // 处理补充照片（可能是JSON数组）
                    try {
                        const photos = JSON.parse(imageValue);
                        if (Array.isArray(photos) && photos.length > 0) {
                            html += `
                                <div class="col-md-12" style="margin-bottom: 20px;">
                                    <h4><i class="fa fa-image"></i> ${fieldName}</h4>
                                    <div class="row">
                            `;

                            photos.forEach((photo, index) => {
                                html += `
                                    <div class="col-md-3 col-sm-4 col-xs-6" style="margin-bottom: 15px;">
                                        <div class="photo-card">
                                            <a href="javascript:;" class="photo-preview" data-src="${photo}">
                                                <img src="${photo}" class="img-responsive"
                                                     style="width: 100%; height: 200px; object-fit: cover; cursor: pointer;"
                                                     title="点击查看大图">
                                            </a>
                                            <div class="photo-info">
                                                <small class="text-muted">照片 ${index + 1}</small>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            });

                            html += `
                                    </div>
                                </div>
                            `;
                        }
                    } catch (e) {
                        // 如果不是JSON，当作单张图片处理
                        html += `
                            <div class="col-md-3 col-sm-4 col-xs-6" style="margin-bottom: 15px;">
                                <div class="photo-card">
                                    <h5>${fieldName}</h5>
                                    <a href="javascript:;" class="photo-preview" data-src="${imageValue}">
                                        <img src="${imageValue}" class="img-responsive"
                                             style="width: 100%; height: 200px; object-fit: cover; cursor: pointer;"
                                             title="点击查看大图">
                                    </a>
                                    <div class="photo-info">
                                        <small class="text-success">已上传</small>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                } else {
                    // 处理单张图片
                    html += `
                        <div class="col-md-3 col-sm-4 col-xs-6" style="margin-bottom: 15px;">
                            <div class="photo-card">
                                <h5>${fieldName}</h5>
                                <a href="javascript:;" class="photo-preview" data-src="${imageValue}">
                                    <img src="${imageValue}" class="img-responsive"
                                         style="width: 100%; height: 200px; object-fit: cover; cursor: pointer;"
                                         title="点击查看大图">
                                </a>
                                <div class="photo-info">
                                    <small class="text-success">已上传</small>
                                </div>
                            </div>
                        </div>
                    `;
                }
            }
        }

        if (!hasImages) {
            html += `
                <div class="col-md-12">
                    <div class="alert alert-info text-center" style="margin: 20px;">
                        <i class="fa fa-info-circle"></i> 暂无照片资料
                    </div>
                </div>
            `;
        }

        html += `
                    </div>
                </div>
            </div>
        `;

        return html;
    }

    // 显示图片预览器 - 使用与detail.html相同的预览器
    window.showImageModal = function(imageSrc) {
        if (!imageSrc) return;

        // 使用Layer弹出图片预览
        layer.open({
            type: 1,
            title: false,
            closeBtn: 1,
            shadeClose: true,
            skin: 'layui-layer-photo-transparent',
            area: ['90%', '90%'],
            offset: 'auto',
            maxmin: false,
            moveOut: true,
            resize: false,
            scrollbar: false,
            content: '<div class="image-viewer-container" style="width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:rgba(0,0,0,0.7);overflow:hidden;border-radius:4px;"><img src="' + imageSrc + '" style="max-width:96%;max-height:96%;object-fit:contain;" /></div>',
            success: function(layero, index) {
                // 调整弹出层样式为半透明
                $('.layui-layer').css({
                    'background-color': 'transparent',
                    'box-shadow': 'none'
                });

                // 调整遮罩层为半透明
                $('.layui-layer-shade').css({
                    'background-color': 'rgba(0,0,0,0.3)',
                    'opacity': '1'
                });

                // 在弹出层中添加图片缩放控制
                var $img = $(layero).find('img');
                var $container = $(layero).find('.image-viewer-container');

                // 初始状态
                var scale = 1;
                var rotate = 0;
                var posX = 0;
                var posY = 0;
                var isDragging = false;
                var startX, startY;

                // 添加缩放和旋转按钮
                var $toolbar = $('<div class="image-preview-toolbar"></div>');
                $toolbar.css({
                    'position': 'absolute',
                    'bottom': '20px',
                    'left': '0',
                    'right': '0',
                    'text-align': 'center',
                    'z-index': '19891015',
                    'background-color': 'rgba(0,0,0,0.6)',
                    'border-radius': '30px',
                    'padding': '8px 15px',
                    'box-shadow': '0 2px 10px rgba(0,0,0,0.2)',
                    'width': 'auto',
                    'max-width': '80%',
                    'margin': '0 auto'
                });

                var $zoomIn = $('<button class="btn btn-sm btn-primary" title="放大"><i class="fa fa-search-plus"></i></button>');
                var $zoomOut = $('<button class="btn btn-sm btn-primary" title="缩小"><i class="fa fa-search-minus"></i></button>');
                var $rotateLeft = $('<button class="btn btn-sm btn-info" title="向左旋转"><i class="fa fa-rotate-left"></i></button>');
                var $rotateRight = $('<button class="btn btn-sm btn-info" title="向右旋转"><i class="fa fa-rotate-right"></i></button>');
                var $reset = $('<button class="btn btn-sm btn-default" title="重置"><i class="fa fa-refresh"></i></button>');

                $toolbar.append($zoomOut).append(' ').append($zoomIn).append(' ')
                        .append($rotateLeft).append(' ').append($rotateRight).append(' ')
                        .append($reset);

                $(layero).append($toolbar);

                // 缩放功能
                $zoomIn.on('click', function() {
                    scale += 0.1;
                    updateTransform();
                });

                $zoomOut.on('click', function() {
                    if (scale > 0.2) {
                        scale -= 0.1;
                        updateTransform();
                    }
                });

                // 旋转功能
                $rotateLeft.on('click', function() {
                    rotate -= 90;
                    updateTransform();
                });

                $rotateRight.on('click', function() {
                    rotate += 90;
                    updateTransform();
                });

                // 重置
                $reset.on('click', function() {
                    scale = 1;
                    rotate = 0;
                    posX = 0;
                    posY = 0;
                    updateTransform();
                });

                // 更新变换
                function updateTransform() {
                    $img.css('transform', 'translate(' + posX + 'px, ' + posY + 'px) scale(' + scale + ') rotate(' + rotate + 'deg)');
                }

                // 添加拖动功能
                $img.css('cursor', 'move');

                // 鼠标按下事件
                $img.on('mousedown', function(e) {
                    e.preventDefault();
                    isDragging = true;
                    startX = e.clientX;
                    startY = e.clientY;
                });

                // 鼠标移动事件
                $(document).on('mousemove.imageDrag', function(e) {
                    if (!isDragging) return;
                    var dx = e.clientX - startX;
                    var dy = e.clientY - startY;
                    posX += dx;
                    posY += dy;
                    updateTransform();
                    startX = e.clientX;
                    startY = e.clientY;
                });

                // 鼠标松开事件
                $(document).on('mouseup.imageDrag', function() {
                    isDragging = false;
                });

                // 触摸支持
                $img.on('touchstart', function(e) {
                    e.preventDefault();
                    isDragging = true;
                    startX = e.originalEvent.touches[0].clientX;
                    startY = e.originalEvent.touches[0].clientY;
                });

                $(document).on('touchmove.imageDrag', function(e) {
                    if (!isDragging) return;
                    var dx = e.originalEvent.touches[0].clientX - startX;
                    var dy = e.originalEvent.touches[0].clientY - startY;
                    posX += dx;
                    posY += dy;
                    updateTransform();
                    startX = e.originalEvent.touches[0].clientX;
                    startY = e.originalEvent.touches[0].clientY;
                });

                $(document).on('touchend.imageDrag', function() {
                    isDragging = false;
                });

                // 点击空白区域关闭图片查看器
                $container.on('click', function(e) {
                    // 确保点击的是容器而不是图片
                    if (e.target === this) {
                        layer.close(index);
                    }
                });

                // 阻止图片上的点击事件冒泡到容器
                $img.on('click', function(e) {
                    e.stopPropagation();
                });

                // 阻止工具栏上的点击事件冒泡到弹出层（防止点击工具栏时关闭弹出层）
                $toolbar.on('click', function(e) {
                    e.stopPropagation();
                });

                // 图层关闭时清除事件
                $(document).on('click', '.layui-layer-close', function() {
                    $(document).off('mousemove.imageDrag mouseup.imageDrag touchmove.imageDrag touchend.imageDrag');
                });

                // 鼠标滚轮缩放
                $img.on('wheel', function(e) {
                    e.preventDefault();
                    if (e.originalEvent.deltaY < 0) {
                        // 向上滚动，放大
                        scale += 0.1;
                    } else {
                        // 向下滚动，缩小
                        if (scale > 0.2) {
                            scale -= 0.1;
                        }
                    }
                    updateTransform();
                });

                // 样式调整
                $img.css({
                    'transition': 'none', // 移除过渡效果，使拖动更流畅
                    'max-width': '100%',
                    'max-height': 'calc(100vh - 100px)',
                    'transform-origin': 'center'
                });
            }
        });
    }

    // 折叠展开错误详情（批量导入）
    window.toggleErrorDetails = function() {
        const errorAccordion = document.getElementById('error-accordion');
        const toggleIcon = document.getElementById('error-toggle-icon');

        if (errorAccordion.style.display === 'none') {
            errorAccordion.style.display = 'block';
            toggleIcon.className = 'fa fa-chevron-up pull-right';
        } else {
            errorAccordion.style.display = 'none';
            toggleIcon.className = 'fa fa-chevron-down pull-right';
        }
    };

    // 折叠展开错误详情（单文件导入）
    window.toggleSingleErrorDetails = function() {
        const errorDetails = document.getElementById('single-error-details');
        const toggleIcon = document.getElementById('single-error-toggle-icon');

        if (errorDetails.style.display === 'none') {
            errorDetails.style.display = 'block';
            toggleIcon.className = 'fa fa-chevron-up pull-right';
        } else {
            errorDetails.style.display = 'none';
            toggleIcon.className = 'fa fa-chevron-down pull-right';
        }
    };

    // 折叠展开文件预览（批量导入）
    window.toggleFilePreview = function(index) {
        const previewBody = document.getElementById(`file-preview-body-${index}`);
        const toggleIcon = document.getElementById(`file-toggle-icon-${index}`);

        if (previewBody.style.display === 'none') {
            previewBody.style.display = 'block';
            toggleIcon.className = 'fa fa-chevron-up pull-right';
        } else {
            previewBody.style.display = 'none';
            toggleIcon.className = 'fa fa-chevron-down pull-right';
        }
    };



    // 初始化应用 - 确保DOM完全加载
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeApp);
    } else {
        // DOM已经加载完成
        initializeApp();
    }
})();
</script>

        </form>
    </div>
</div>