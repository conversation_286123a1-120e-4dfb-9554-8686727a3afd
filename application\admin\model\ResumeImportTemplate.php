<?php

namespace app\admin\model;

use think\Model;

class ResumeImportTemplate extends Model
{
    // 表名
    protected $name = 'resume_import_template';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    
    // 追加属性
    protected $append = [
    ];
    
    /**
     * 需要使用分隔符的字段
     */
    protected $arrayFields = [
        'contact_relation', 'contact_name', 'contact_age', 'contact_job', 
        'education_start', 'education_end', 'education_school', 'education_major', 'graduation_education',
        'job_start', 'job_end', 'job_company', 'job_position', 'job_description'
    ];
    
    /**
     * 获取默认模板
     */
    public static function getDefaultTemplate()
    {
        return self::where('is_default', 1)->find();
    }
    
    /**
     * 设置为默认模板
     */
    public function setAsDefault()
    {
        // 先将所有模板设为非默认
        self::where('id', '<>', $this->id)->update(['is_default' => 0]);
        // 将当前模板设为默认
        $this->is_default = 1;
        return $this->save();
    }
    
    /**
     * 获取配置
     */
    public function getConfigAttr($value)
    {
        $config = $value ? json_decode($value, true) : [];
        
        // 处理多数组字段，将管道符分隔的字符串转换为数组
        foreach ($this->arrayFields as $field) {
            if (isset($config[$field]) && is_string($config[$field]) && !empty($config[$field])) {
                $config[$field] = explode('|', $config[$field]);
                \think\Log::write('模型中自动转换字段 '.$field.' 为数组: ' . json_encode($config[$field]), 'debug');
            }
        }
        
        return $config;
    }
    
    /**
     * 设置配置
     */
    public function setConfigAttr($value)
    {
        if (!$value) {
            return '';
        }
        
        if (is_array($value)) {
            // 处理数组字段，将数组转换为分隔符分割的字符串
            foreach ($this->arrayFields as $field) {
                if (isset($value[$field]) && is_array($value[$field])) {
                    // 过滤空值
                    $value[$field] = array_filter($value[$field], function($v) {
                        return $v !== '' && $v !== null;
                    });
                    if (!empty($value[$field])) {
                        $value[$field] = implode('|', $value[$field]);
                    }
                }
            }
            
            return json_encode($value);
        }
        
        return $value;
    }
} 