<?php

namespace app\admin\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\Config;

class UpdateConfig extends Command
{
    protected function configure()
    {
        $this->setName('update_config')
            ->setDescription('Update config from site.php to database');
    }

    protected function execute(Input $input, Output $output)
    {
        $site = Config::get('site');
        
        // 检查配置组是否存在
        $configGroup = Db::name('config')->where('name', 'configgroup')->find();
        if ($configGroup) {
            $content = json_decode($configGroup['value'], true);
            if (!isset($content['contact'])) {
                $content['contact'] = '联系信息';
                Db::name('config')->where('name', 'configgroup')->update(['value' => json_encode($content)]);
                $output->writeln("Added contact group to configgroup");
            }
            if (!isset($content['homepage'])) {
                $content['homepage'] = '首页设置';
                Db::name('config')->where('name', 'configgroup')->update(['value' => json_encode($content)]);
                $output->writeln("Added homepage group to configgroup");
            }
            if (!isset($content['pages'])) {
                $content['pages'] = '页面管理';
                Db::name('config')->where('name', 'configgroup')->update(['value' => json_encode($content)]);
                $output->writeln("Added pages group to configgroup");
            }
            if (!isset($content['social'])) {
                $content['social'] = '社交媒体';
                Db::name('config')->where('name', 'configgroup')->update(['value' => json_encode($content)]);
                $output->writeln("Added social group to configgroup");
            }
        }
        
        // 添加配置项
        $configs = [
            // 联系信息配置项
            [
                'name' => 'phone',
                'group' => 'contact',
                'title' => 'Phone',
                'tip' => '页脚显示的联系电话',
                'type' => 'string',
                'value' => $site['phone'] ?? ''
            ],
            [
                'name' => 'email',
                'group' => 'contact',
                'title' => 'Email address',
                'tip' => '页脚显示的联系邮箱',
                'type' => 'string',
                'value' => $site['email'] ?? ''
            ],
            [
                'name' => 'address',
                'group' => 'contact',
                'title' => 'Address',
                'tip' => '页脚显示的联系地址',
                'type' => 'string',
                'value' => $site['address'] ?? ''
            ],
            // 首页设置配置项
            [
                'name' => 'banner_image',
                'group' => 'homepage',
                'title' => 'Banner image',
                'tip' => '首页轮播图片',
                'type' => 'image',
                'value' => $site['banner_image'] ?? ''
            ],
            [
                'name' => 'about_image',
                'group' => 'homepage',
                'title' => 'About image',
                'tip' => '关于我们图片',
                'type' => 'image',
                'value' => $site['about_image'] ?? ''
            ],
            [
                'name' => 'description',
                'group' => 'homepage',
                'title' => 'Description',
                'tip' => '公司简介',
                'type' => 'text',
                'value' => $site['description'] ?? '我们是一家专业的人力资源服务公司，致力于为企业提供优质的人才招聘服务，为求职者提供理想的职业发展平台。'
            ],
            // 页面管理配置项
            [
                'name' => 'about_title',
                'group' => 'pages',
                'title' => 'About title',
                'tip' => '关于我们页面标题',
                'type' => 'string',
                'value' => $site['about_title'] ?? '关于我们'
            ],
            [
                'name' => 'about_content',
                'group' => 'pages',
                'title' => 'About content',
                'tip' => '关于我们页面内容',
                'type' => 'editor',
                'value' => $site['about_content'] ?? '<h2>公司简介</h2><p>我们是一家专业的人力资源服务公司，致力于为企业提供优质的人才招聘服务，为求职者提供理想的职业发展平台。</p><h2>我们的使命</h2><p>帮助企业找到合适的人才，帮助求职者找到理想的工作，促进人力资源市场的良性发展。</p><h2>我们的优势</h2><ul><li>专业的招聘团队</li><li>广泛的企业资源</li><li>高效的匹配系统</li><li>完善的售后服务</li></ul>'
            ],
            [
                'name' => 'help_title',
                'group' => 'pages',
                'title' => 'Help title',
                'tip' => '帮助中心页面标题',
                'type' => 'string',
                'value' => $site['help_title'] ?? '帮助中心'
            ],
            [
                'name' => 'help_content',
                'group' => 'pages',
                'title' => 'Help content',
                'tip' => '帮助中心页面内容',
                'type' => 'editor',
                'value' => $site['help_content'] ?? '<h2>常见问题</h2><p>这里是一些常见问题的解答，帮助您更好地使用我们的服务。</p><h2>如何注册账号？</h2><p>点击网站右上角的"注册"按钮，填写相关信息即可完成注册。</p><h2>如何投递简历？</h2><p>浏览职位列表，找到心仪的职位后点击"申请职位"按钮，上传您的简历即可。</p><h2>如何修改个人信息？</h2><p>登录后进入个人中心，点击"编辑资料"即可修改个人信息。</p>'
            ],
            [
                'name' => 'privacy_title',
                'group' => 'pages',
                'title' => 'Privacy title',
                'tip' => '隐私政策页面标题',
                'type' => 'string',
                'value' => $site['privacy_title'] ?? '隐私政策'
            ],
            [
                'name' => 'privacy_content',
                'group' => 'pages',
                'title' => 'Privacy content',
                'tip' => '隐私政策页面内容',
                'type' => 'editor',
                'value' => $site['privacy_content'] ?? '<h2>隐私声明</h2><p>本隐私政策说明了我们如何收集、使用、披露、传输和存储您的个人信息。</p><h2>信息收集</h2><p>当您注册账户、发布职位、投递简历时，我们会收集相关的个人信息。</p><h2>信息使用</h2><p>我们使用收集到的信息来提供、维护、保护和改进我们的服务，开发新的服务，并保护我们的用户。</p><h2>信息共享</h2><p>未经您的同意，我们不会与第三方共享您的个人信息，法律法规要求的情况除外。</p>'
            ],
            // 社交媒体配置项
            [
                'name' => 'wechat_title',
                'group' => 'social',
                'title' => 'Wechat title',
                'tip' => '微信弹窗标题',
                'type' => 'string',
                'value' => $site['wechat_title'] ?? '微信公众号'
            ],
            [
                'name' => 'wechat_image',
                'group' => 'social',
                'title' => 'Wechat image',
                'tip' => '微信二维码图片',
                'type' => 'image',
                'value' => $site['wechat_image'] ?? ''
            ],
            [
                'name' => 'wechat_desc',
                'group' => 'social',
                'title' => 'Wechat desc',
                'tip' => '微信二维码描述',
                'type' => 'text',
                'value' => $site['wechat_desc'] ?? '扫描上方二维码关注我们的微信公众号，获取最新招聘信息和职场资讯。'
            ],
            [
                'name' => 'weibo_title',
                'group' => 'social',
                'title' => 'Weibo title',
                'tip' => '微博弹窗标题',
                'type' => 'string',
                'value' => $site['weibo_title'] ?? '官方微博'
            ],
            [
                'name' => 'weibo_url',
                'group' => 'social',
                'title' => 'Weibo url',
                'tip' => '微博链接',
                'type' => 'string',
                'value' => $site['weibo_url'] ?? ''
            ],
            [
                'name' => 'weibo_desc',
                'group' => 'social',
                'title' => 'Weibo desc',
                'tip' => '微博描述',
                'type' => 'text',
                'value' => $site['weibo_desc'] ?? '关注我们的官方微博，了解最新动态和招聘信息。'
            ]
        ];
        
        foreach ($configs as $config) {
            $exists = Db::name('config')->where('name', $config['name'])->find();
            if ($exists) {
                Db::name('config')->where('name', $config['name'])->update([
                    'group' => $config['group'],
                    'title' => $config['title'],
                    'tip' => $config['tip'],
                    'type' => $config['type'],
                    'value' => $config['value']
                ]);
                $output->writeln("Updated config: {$config['name']}");
            } else {
                Db::name('config')->insert([
                    'name' => $config['name'],
                    'group' => $config['group'],
                    'title' => $config['title'],
                    'tip' => $config['tip'],
                    'type' => $config['type'],
                    'value' => $config['value'],
                    'content' => '',
                    'rule' => '',
                    'extend' => '',
                    'setting' => ''
                ]);
                $output->writeln("Added config: {$config['name']}");
            }
        }
        
        $output->writeln("Config update completed");
    }
} 