/*
 FastAdmin Install SQL
 Date: 2024-09-03 15:05:25
*/

SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for fa_admin
-- ----------------------------
CREATE TABLE `fa_admin` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `username` varchar(20) DEFAULT '' COMMENT '用户名',
  `nickname` varchar(50) DEFAULT '' COMMENT '昵称',
  `password` varchar(32) DEFAULT '' COMMENT '密码',
  `salt` varchar(30) DEFAULT '' COMMENT '密码盐',
  `avatar` varchar(255) DEFAULT '' COMMENT '头像',
  `email` varchar(100) DEFAULT '' COMMENT '电子邮箱',
  `mobile` varchar(11) DEFAULT '' COMMENT '手机号码',
  `loginfailure` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '失败次数',
  `logintime` bigint(16) DEFAULT NULL COMMENT '登录时间',
  `loginip` varchar(50) DEFAULT NULL COMMENT '登录IP',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `token` varchar(59) DEFAULT '' COMMENT 'Session标识',
  `status` varchar(30) NOT NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='管理员表';

-- ----------------------------
-- Records of fa_admin
-- ----------------------------
BEGIN;
INSERT INTO `fa_admin` VALUES (1, 'admin', 'Admin', '', '', '/assets/img/avatar.png', '<EMAIL>', '', 0, 1491635035, '127.0.0.1',1491635035, 1491635035, '', 'normal');
COMMIT;

-- ----------------------------
-- Table structure for fa_admin_log
-- ----------------------------
CREATE TABLE `fa_admin_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `admin_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '管理员ID',
  `username` varchar(30) DEFAULT '' COMMENT '管理员名字',
  `url` varchar(1500) DEFAULT '' COMMENT '操作页面',
  `title` varchar(100) DEFAULT '' COMMENT '日志标题',
  `content` longtext NOT NULL COMMENT '内容',
  `ip` varchar(50) DEFAULT '' COMMENT 'IP',
  `useragent` varchar(255) DEFAULT '' COMMENT 'User-Agent',
  `createtime` bigint(16) DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`id`),
  KEY `name` (`username`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='管理员日志表';

-- ----------------------------
-- Table structure for fa_area
-- ----------------------------
CREATE TABLE `fa_area` (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `pid` int(10) DEFAULT NULL COMMENT '父id',
  `shortname` varchar(100) DEFAULT NULL COMMENT '简称',
  `name` varchar(100) DEFAULT NULL COMMENT '名称',
  `mergename` varchar(255) DEFAULT NULL COMMENT '全称',
  `level` tinyint(4) DEFAULT NULL COMMENT '层级:1=省,2=市,3=区/县',
  `pinyin` varchar(100) DEFAULT NULL COMMENT '拼音',
  `code` varchar(100) DEFAULT NULL COMMENT '长途区号',
  `zip` varchar(100) DEFAULT NULL COMMENT '邮编',
  `first` varchar(50) DEFAULT NULL COMMENT '首字母',
  `lng` varchar(100) DEFAULT NULL COMMENT '经度',
  `lat` varchar(100) DEFAULT NULL COMMENT '纬度',
  PRIMARY KEY (`id`),
  KEY `pid` (`pid`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='地区表';

-- ----------------------------
-- Table structure for fa_attachment
-- ----------------------------
CREATE TABLE `fa_attachment` (
  `id` int(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `category` varchar(50) DEFAULT '' COMMENT '类别',
  `admin_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '管理员ID',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '会员ID',
  `url` varchar(255) DEFAULT '' COMMENT '物理路径',
  `imagewidth` int(10) unsigned DEFAULT 0 COMMENT '宽度',
  `imageheight` int(10) unsigned DEFAULT 0 COMMENT '高度',
  `imagetype` varchar(30) DEFAULT '' COMMENT '图片类型',
  `imageframes` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '图片帧数',
  `filename` varchar(100) DEFAULT '' COMMENT '文件名称',
  `filesize` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '文件大小',
  `mimetype` varchar(100) DEFAULT '' COMMENT 'mime类型',
  `extparam` varchar(255) DEFAULT '' COMMENT '透传数据',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建日期',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `uploadtime` bigint(16) DEFAULT NULL COMMENT '上传时间',
  `storage` varchar(100) NOT NULL DEFAULT 'local' COMMENT '存储位置',
  `sha1` varchar(40) DEFAULT '' COMMENT '文件 sha1编码',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='附件表';

-- ----------------------------
-- Records of fa_attachment
-- ----------------------------
BEGIN;
INSERT INTO `fa_attachment` VALUES (1, '', 1, 0, '/assets/img/qrcode.png', '150', '150', 'png', 0, 'qrcode.png', 21859, 'image/png', '', 1491635035, 1491635035, 1491635035, 'local', '17163603d0263e4838b9387ff2cd4877e8b018f6');
COMMIT;

-- ----------------------------
-- Table structure for fa_auth_group
-- ----------------------------
CREATE TABLE `fa_auth_group` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `pid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父组别',
  `name` varchar(100) DEFAULT '' COMMENT '组名',
  `rules` text NOT NULL COMMENT '规则ID',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `status` varchar(30) DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='分组表';

-- ----------------------------
-- Records of fa_auth_group
-- ----------------------------
BEGIN;
INSERT INTO `fa_auth_group` VALUES (1, 0, 'Admin group', '*', 1491635035, 1491635035, 'normal');
INSERT INTO `fa_auth_group` VALUES (2, 1, 'Second group', '13,14,16,15,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,40,41,42,43,44,45,46,47,48,49,50,55,56,57,58,59,60,61,62,63,64,65,1,9,10,11,7,6,8,2,4,5', 1491635035, 1491635035, 'normal');
INSERT INTO `fa_auth_group` VALUES (3, 2, 'Third group', '1,4,9,10,11,13,14,15,16,17,40,41,42,43,44,45,46,47,48,49,50,55,56,57,58,59,60,61,62,63,64,65,5', 1491635035, 1491635035, 'normal');
INSERT INTO `fa_auth_group` VALUES (4, 1, 'Second group 2', '1,4,13,14,15,16,17,55,56,57,58,59,60,61,62,63,64,65', 1491635035, 1491635035, 'normal');
INSERT INTO `fa_auth_group` VALUES (5, 2, 'Third group 2', '1,2,6,7,8,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34', 1491635035, 1491635035, 'normal');
COMMIT;

-- ----------------------------
-- Table structure for fa_auth_group_access
-- ----------------------------
CREATE TABLE `fa_auth_group_access` (
  `uid` int(10) unsigned NOT NULL COMMENT '会员ID',
  `group_id` int(10) unsigned NOT NULL COMMENT '级别ID',
  UNIQUE KEY `uid_group_id` (`uid`,`group_id`),
  KEY `uid` (`uid`),
  KEY `group_id` (`group_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='权限分组表';

-- ----------------------------
-- Records of fa_auth_group_access
-- ----------------------------
BEGIN;
INSERT INTO `fa_auth_group_access` VALUES (1, 1);
COMMIT;

-- ----------------------------
-- Table structure for fa_auth_rule
-- ----------------------------
CREATE TABLE `fa_auth_rule` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `type` enum('menu','file') NOT NULL DEFAULT 'file' COMMENT 'menu为菜单,file为权限节点',
  `pid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父ID',
  `name` varchar(100) DEFAULT '' COMMENT '规则名称',
  `title` varchar(50) DEFAULT '' COMMENT '规则名称',
  `icon` varchar(50) DEFAULT '' COMMENT '图标',
  `url` varchar(255) DEFAULT '' COMMENT '规则URL',
  `condition` varchar(255) DEFAULT '' COMMENT '条件',
  `remark` varchar(255) DEFAULT '' COMMENT '备注',
  `ismenu` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否为菜单',
  `menutype` enum('addtabs','blank','dialog','ajax') DEFAULT NULL COMMENT '菜单类型',
  `extend` varchar(255) DEFAULT '' COMMENT '扩展属性',
  `py` varchar(30) DEFAULT '' COMMENT '拼音首字母',
  `pinyin` varchar(100) DEFAULT '' COMMENT '拼音',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `status` varchar(30) DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`) USING BTREE,
  KEY `pid` (`pid`),
  KEY `weigh` (`weigh`)
) ENGINE=InnoDB AUTO_INCREMENT=66 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='节点表';

-- ----------------------------
-- Records of fa_auth_rule
-- ----------------------------
BEGIN;
INSERT INTO `fa_auth_rule` VALUES(1, 'file', 0, 'dashboard', 'Dashboard', 'fa fa-dashboard', '', '', 'Dashboard tips', 1, NULL, '', 'kzt', 'kongzhitai', 1491635035, 1491635035, 143, 'normal');
INSERT INTO `fa_auth_rule` VALUES(2, 'file', 0, 'general', 'General', 'fa fa-cogs', '', '', '', 1, NULL, '', 'cggl', 'changguiguanli', 1491635035, 1491635035, 137, 'normal');
INSERT INTO `fa_auth_rule` VALUES(3, 'file', 0, 'category', 'Category', 'fa fa-leaf', '', '', 'Category tips', 0, NULL, '', 'flgl', 'fenleiguanli', 1491635035, 1747645872, 119, 'normal');
INSERT INTO `fa_auth_rule` VALUES(4, 'file', 0, 'addon', 'Addon', 'fa fa-rocket', '', '', 'Addon tips', 1, NULL, '', 'cjgl', 'chajianguanli', 1491635035, 1747902977, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(5, 'file', 0, 'auth', 'Auth', 'fa fa-group', '', '', '', 1, NULL, '', 'qxgl', 'quanxianguanli', 1491635035, 1491635035, 50, 'normal');
INSERT INTO `fa_auth_rule` VALUES(6, 'file', 2, 'general/config', 'Config', 'fa fa-cog', '', '', 'Config tips', 1, NULL, '', 'xtpz', 'xitongpeizhi', 1491635035, 1491635035, 60, 'normal');
INSERT INTO `fa_auth_rule` VALUES(7, 'file', 2, 'general/attachment', 'Attachment', 'fa fa-file-image-o', '', '', 'Attachment tips', 1, NULL, '', 'fjgl', 'fujianguanli', 1491635035, 1491635035, 53, 'normal');
INSERT INTO `fa_auth_rule` VALUES(8, 'file', 2, 'general/profile', 'Profile', 'fa fa-user', '', '', '', 1, NULL, '', 'grzl', 'gerenziliao', 1491635035, 1491635035, 34, 'normal');
INSERT INTO `fa_auth_rule` VALUES(9, 'file', 5, 'auth/admin', 'Admin', 'fa fa-user', '', '', 'Admin tips', 1, NULL, '', 'glygl', 'guanliyuanguanli', 1491635035, 1491635035, 118, 'normal');
INSERT INTO `fa_auth_rule` VALUES(10, 'file', 5, 'auth/adminlog', 'Admin log', 'fa fa-list-alt', '', '', 'Admin log tips', 1, NULL, '', 'glyrz', 'guanliyuanrizhi', 1491635035, 1491635035, 113, 'normal');
INSERT INTO `fa_auth_rule` VALUES(11, 'file', 5, 'auth/group', 'Group', 'fa fa-group', '', '', 'Group tips', 1, NULL, '', 'jsz', 'juesezu', 1491635035, 1491635035, 109, 'normal');
INSERT INTO `fa_auth_rule` VALUES(12, 'file', 5, 'auth/rule', 'Rule', 'fa fa-bars', '', '', 'Rule tips', 1, NULL, '', 'cdgz', 'caidanguize', 1491635035, 1491635035, 104, 'normal');
INSERT INTO `fa_auth_rule` VALUES(13, 'file', 1, 'dashboard/index', 'View', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 136, 'normal');
INSERT INTO `fa_auth_rule` VALUES(14, 'file', 1, 'dashboard/add', 'Add', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 135, 'normal');
INSERT INTO `fa_auth_rule` VALUES(15, 'file', 1, 'dashboard/del', 'Delete', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 133, 'normal');
INSERT INTO `fa_auth_rule` VALUES(16, 'file', 1, 'dashboard/edit', 'Edit', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 134, 'normal');
INSERT INTO `fa_auth_rule` VALUES(17, 'file', 1, 'dashboard/multi', 'Multi', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 132, 'normal');
INSERT INTO `fa_auth_rule` VALUES(18, 'file', 6, 'general/config/index', 'View', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 52, 'normal');
INSERT INTO `fa_auth_rule` VALUES(19, 'file', 6, 'general/config/add', 'Add', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 51, 'normal');
INSERT INTO `fa_auth_rule` VALUES(20, 'file', 6, 'general/config/edit', 'Edit', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 50, 'normal');
INSERT INTO `fa_auth_rule` VALUES(21, 'file', 6, 'general/config/del', 'Delete', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 49, 'normal');
INSERT INTO `fa_auth_rule` VALUES(22, 'file', 6, 'general/config/multi', 'Multi', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 48, 'normal');
INSERT INTO `fa_auth_rule` VALUES(23, 'file', 7, 'general/attachment/index', 'View', 'fa fa-circle-o', '', '', 'Attachment tips', 0, NULL, '', '', '', 1491635035, 1491635035, 59, 'normal');
INSERT INTO `fa_auth_rule` VALUES(24, 'file', 7, 'general/attachment/select', 'Select attachment', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 58, 'normal');
INSERT INTO `fa_auth_rule` VALUES(25, 'file', 7, 'general/attachment/add', 'Add', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 57, 'normal');
INSERT INTO `fa_auth_rule` VALUES(26, 'file', 7, 'general/attachment/edit', 'Edit', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 56, 'normal');
INSERT INTO `fa_auth_rule` VALUES(27, 'file', 7, 'general/attachment/del', 'Delete', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 55, 'normal');
INSERT INTO `fa_auth_rule` VALUES(28, 'file', 7, 'general/attachment/multi', 'Multi', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 54, 'normal');
INSERT INTO `fa_auth_rule` VALUES(29, 'file', 8, 'general/profile/index', 'View', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 33, 'normal');
INSERT INTO `fa_auth_rule` VALUES(30, 'file', 8, 'general/profile/update', 'Update profile', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 32, 'normal');
INSERT INTO `fa_auth_rule` VALUES(31, 'file', 8, 'general/profile/add', 'Add', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 31, 'normal');
INSERT INTO `fa_auth_rule` VALUES(32, 'file', 8, 'general/profile/edit', 'Edit', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 30, 'normal');
INSERT INTO `fa_auth_rule` VALUES(33, 'file', 8, 'general/profile/del', 'Delete', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 29, 'normal');
INSERT INTO `fa_auth_rule` VALUES(34, 'file', 8, 'general/profile/multi', 'Multi', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 28, 'normal');
INSERT INTO `fa_auth_rule` VALUES(35, 'file', 3, 'category/index', 'View', 'fa fa-circle-o', '', '', 'Category tips', 0, NULL, '', '', '', 1491635035, 1491635035, 142, 'normal');
INSERT INTO `fa_auth_rule` VALUES(36, 'file', 3, 'category/add', 'Add', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 141, 'normal');
INSERT INTO `fa_auth_rule` VALUES(37, 'file', 3, 'category/edit', 'Edit', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 140, 'normal');
INSERT INTO `fa_auth_rule` VALUES(38, 'file', 3, 'category/del', 'Delete', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 139, 'normal');
INSERT INTO `fa_auth_rule` VALUES(39, 'file', 3, 'category/multi', 'Multi', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 138, 'normal');
INSERT INTO `fa_auth_rule` VALUES(40, 'file', 9, 'auth/admin/index', 'View', 'fa fa-circle-o', '', '', 'Admin tips', 0, NULL, '', '', '', 1491635035, 1491635035, 117, 'normal');
INSERT INTO `fa_auth_rule` VALUES(41, 'file', 9, 'auth/admin/add', 'Add', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 116, 'normal');
INSERT INTO `fa_auth_rule` VALUES(42, 'file', 9, 'auth/admin/edit', 'Edit', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 115, 'normal');
INSERT INTO `fa_auth_rule` VALUES(43, 'file', 9, 'auth/admin/del', 'Delete', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 114, 'normal');
INSERT INTO `fa_auth_rule` VALUES(44, 'file', 10, 'auth/adminlog/index', 'View', 'fa fa-circle-o', '', '', 'Admin log tips', 0, NULL, '', '', '', 1491635035, 1491635035, 112, 'normal');
INSERT INTO `fa_auth_rule` VALUES(45, 'file', 10, 'auth/adminlog/detail', 'Detail', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 111, 'normal');
INSERT INTO `fa_auth_rule` VALUES(46, 'file', 10, 'auth/adminlog/del', 'Delete', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 110, 'normal');
INSERT INTO `fa_auth_rule` VALUES(47, 'file', 11, 'auth/group/index', 'View', 'fa fa-circle-o', '', '', 'Group tips', 0, NULL, '', '', '', 1491635035, 1491635035, 108, 'normal');
INSERT INTO `fa_auth_rule` VALUES(48, 'file', 11, 'auth/group/add', 'Add', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 107, 'normal');
INSERT INTO `fa_auth_rule` VALUES(49, 'file', 11, 'auth/group/edit', 'Edit', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 106, 'normal');
INSERT INTO `fa_auth_rule` VALUES(50, 'file', 11, 'auth/group/del', 'Delete', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 105, 'normal');
INSERT INTO `fa_auth_rule` VALUES(51, 'file', 12, 'auth/rule/index', 'View', 'fa fa-circle-o', '', '', 'Rule tips', 0, NULL, '', '', '', 1491635035, 1491635035, 103, 'normal');
INSERT INTO `fa_auth_rule` VALUES(52, 'file', 12, 'auth/rule/add', 'Add', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 102, 'normal');
INSERT INTO `fa_auth_rule` VALUES(53, 'file', 12, 'auth/rule/edit', 'Edit', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 101, 'normal');
INSERT INTO `fa_auth_rule` VALUES(54, 'file', 12, 'auth/rule/del', 'Delete', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 100, 'normal');
INSERT INTO `fa_auth_rule` VALUES(55, 'file', 4, 'addon/index', 'View', 'fa fa-circle-o', '', '', 'Addon tips', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(56, 'file', 4, 'addon/add', 'Add', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(57, 'file', 4, 'addon/edit', 'Edit', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(58, 'file', 4, 'addon/del', 'Delete', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(59, 'file', 4, 'addon/downloaded', 'Local addon', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(60, 'file', 4, 'addon/state', 'Update state', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(63, 'file', 4, 'addon/config', 'Setting', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(64, 'file', 4, 'addon/refresh', 'Refresh', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(65, 'file', 4, 'addon/multi', 'Multi', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(66, 'file', 0, 'user', '用户管理', 'fa fa-user-circle', '', '', '', 1, 'addtabs', '', 'yhgl', 'yonghuguanli', 1491635035, 1750498364, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(67, 'file', 66, 'user/user', '用户管理', 'fa fa-user', '', '', '', 1, 'addtabs', '', 'yhgl', 'yonghuguanli', 1491635035, 1750498382, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(68, 'file', 67, 'user/user/index', 'View', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(69, 'file', 67, 'user/user/edit', 'Edit', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(70, 'file', 67, 'user/user/add', 'Add', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(71, 'file', 67, 'user/user/del', 'Del', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(72, 'file', 67, 'user/user/multi', 'Multi', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(73, 'file', 66, 'user/group', '用户分组', 'fa fa-users', '', '', '', 1, 'addtabs', '', 'yhfz', 'yonghufenzu', 1491635035, 1750498388, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(74, 'file', 73, 'user/group/add', 'Add', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(75, 'file', 73, 'user/group/edit', 'Edit', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(76, 'file', 73, 'user/group/index', 'View', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(77, 'file', 73, 'user/group/del', 'Del', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(78, 'file', 73, 'user/group/multi', 'Multi', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(79, 'file', 66, 'user/rule', '用户规则', 'fa fa-circle-o', '', '', '', 1, 'addtabs', '', 'yhgz', 'yonghuguize', 1491635035, 1750498395, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(80, 'file', 79, 'user/rule/index', 'View', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(81, 'file', 79, 'user/rule/del', 'Del', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(82, 'file', 79, 'user/rule/add', 'Add', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(83, 'file', 79, 'user/rule/edit', 'Edit', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(84, 'file', 79, 'user/rule/multi', 'Multi', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1491635035, 1491635035, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(220, 'file', 0, 'resume', '简历管理', 'fa fa-book', '', '', '', 1, 'addtabs', '', 'jlgl', 'jianliguanli', 1747199072, 1747533032, 48, 'normal');
INSERT INTO `fa_auth_rule` VALUES(221, 'file', 220, 'resume/index', '简历列表', 'fa fa-book', '', '', '', 1, 'addtabs', '', 'jllb', 'jianliliebiao', 1747199072, 1750498698, 49, 'normal');
INSERT INTO `fa_auth_rule` VALUES(222, 'file', 220, 'resume/add', '添加', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'tj', 'tianjia', 1747199072, 1747555206, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(223, 'file', 220, 'resume/edit', '编辑', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'bj', 'bianji', 1747199072, 1747199072, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(224, 'file', 220, 'resume/del', '删除', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'sc', 'shanchu', 1747199072, 1747199072, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(225, 'file', 220, 'resume/multi', '批量更新', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'plgx', 'pilianggengxin', 1747199072, 1747199072, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(258, 'file', 220, 'resume/exportexcel', '导出Excel', 'fa fa-circle-o', '', '', '', 0, 'addtabs', '', 'dcE', 'daochuExcel', 1747374300, 1748227349, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(259, 'file', 220, 'resume/import', '导入Excel', 'fa fa-file-excel-o', '', '', '', 0, 'addtabs', '', 'drE', 'daoruExcel', 1747453070, 1747647090, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(272, 'file', 220, 'template', '模版管理', 'fa fa-codepen', '', '', '', 1, 'addtabs', '', 'mbgl', 'mobanguanli', 1747532895, 1750498482, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(273, 'file', 272, 'template/index', '模版列表', 'fa fa-circle-o', '', '', '', 1, 'addtabs', '', 'mblb', 'mobanliebiao', 1747533155, 1747547094, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(274, 'file', 272, 'template/add', '添加简历', 'fa fa-circle-o', '', '', '', 0, 'addtabs', '', 'tjjl', 'tianjiajianli', 1747533155, 1747547131, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(275, 'file', 272, 'template/edit', '编辑', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'bj', 'bianji', 1747533155, 1747533155, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(276, 'file', 272, 'template/del', '删除', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'sc', 'shanchu', 1747533155, 1747533155, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(277, 'file', 272, 'template/multi', '批量更新', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'plgx', 'pilianggengxin', 1747533155, 1747533155, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(278, 'file', 272, 'template/download', '下载模版', 'fa fa-circle-o', '', '', '', 0, 'addtabs', '', 'xzmb', 'xiazaimoban', 1747533880, 1747533880, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(279, 'file', 272, 'template/excel', 'Excel模版列表', 'fa fa-circle-o', '', '', '', 1, 'addtabs', '', 'Emblb', 'Excelmobanliebiao', 1747547717, 1747547717, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(280, 'file', 272, 'template/word', 'Word模版列表', 'fa fa-circle-o', '', '', '', 1, 'addtabs', '', 'Wmblb', 'Wordmobanliebiao', 1747547735, 1747547735, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(281, 'file', 220, 'resume/showexporttemplatemodal', '导出Excel模版', 'fa fa-circle-o', '', '', '', 0, 'addtabs', '', 'dcEmb', 'daochuExcelmoban', 1747646984, 1747647143, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(282, 'file', 220, 'resume/showexportwordmodal', '导出word模版', 'fa fa-circle-o', '', '', '', 0, 'addtabs', '', 'dcwmb', 'daochuwordmoban', 1747647169, 1747647169, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(331, 'file', 343, 'jobcategory', '岗位分类管理', 'fa fa-delicious', '', '', '', 1, 'addtabs', '', 'gwflgl', 'gangweifenleiguanli', 1747725493, 1750498450, 46, 'normal');
INSERT INTO `fa_auth_rule` VALUES(332, 'file', 331, 'jobcategory/index', '查看', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'zk', 'zhakan', 1747725493, 1747725493, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(333, 'file', 331, 'jobcategory/add', '添加', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'tj', 'tianjia', 1747725493, 1747725493, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(334, 'file', 331, 'jobcategory/edit', '编辑', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'bj', 'bianji', 1747725493, 1747725493, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(335, 'file', 331, 'jobcategory/del', '删除', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'sc', 'shanchu', 1747725493, 1747725493, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(336, 'file', 331, 'jobcategory/multi', '批量更新', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'plgx', 'pilianggengxin', 1747725493, 1747725493, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(337, 'file', 343, 'jobinterview', '面试管理', 'fa fa-group', '', '', '', 1, 'addtabs', '', 'msgl', 'mianshiguanli', 1747725531, 1750498498, 44, 'normal');
INSERT INTO `fa_auth_rule` VALUES(338, 'file', 337, 'jobinterview/index', '查看', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'zk', 'zhakan', 1747725531, 1747725531, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(339, 'file', 337, 'jobinterview/add', '添加', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'tj', 'tianjia', 1747725531, 1747725531, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(340, 'file', 337, 'jobinterview/edit', '编辑', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'bj', 'bianji', 1747725531, 1747725531, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(341, 'file', 337, 'jobinterview/del', '删除', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'sc', 'shanchu', 1747725531, 1747725531, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(342, 'file', 337, 'jobinterview/multi', '批量更新', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'plgx', 'pilianggengxin', 1747725531, 1747725531, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(343, 'file', 0, 'jobs', '岗位管理', 'fa fa-archive', '', '', '', 1, 'addtabs', '', 'gwgl', 'gangweiguanli', 1747725538, 1750498314, 8, 'normal');
INSERT INTO `fa_auth_rule` VALUES(344, 'file', 343, 'jobs/index', '岗位列表', 'fa fa-th-list', '', '', '', 1, 'addtabs', '', 'gwlb', 'gangweiliebiao', 1747725538, 1750498710, 47, 'normal');
INSERT INTO `fa_auth_rule` VALUES(345, 'file', 343, 'jobs/add', '添加', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'tj', 'tianjia', 1747725538, 1747725538, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(346, 'file', 343, 'jobs/edit', '编辑', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'bj', 'bianji', 1747725538, 1747725538, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(347, 'file', 343, 'jobs/del', '删除', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'sc', 'shanchu', 1747725538, 1747725538, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(348, 'file', 343, 'jobs/multi', '批量更新', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'plgx', 'pilianggengxin', 1747725538, 1747725538, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(433, 'file', 343, 'jobs/import', '导入', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1614567890, 1614567890, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(434, 'file', 337, 'jobinterview/import', '导入', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1614567890, 1614567890, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(435, 'file', 331, 'jobcategory/import', '导入', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1614567890, 1614567890, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(452, 'file', 220, 'resume/exportToTemplate', '导出到Excel模板', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1614567890, 1614567890, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(453, 'file', 220, 'resume/exportToWordTemplate', '导出到Word模板', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1614567890, 1614567890, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(454, 'file', 220, 'resume/batchCopy', '复制简历', 'fa fa-circle-o', '', '', '', 0, NULL, '', '', '', 1614567890, 1614567890, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(493, 'file', 0, 'department', '组织架构', 'fa fa-sitemap', '', '', '', 1, 'addtabs', '', 'zzjg', 'zuzhijiagou', 1750321638, 1750903043, 99, 'normal');
INSERT INTO `fa_auth_rule` VALUES(494, 'file', 493, 'department/admin', 'Employee', 'fa fa-users', '', '', '', 1, NULL, '', 'E', 'Employee', 1750321638, 1750321638, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(495, 'file', 494, 'department/admin/index', 'View', 'fa fa-users', '', '', '', 0, NULL, '', 'zk', 'zhakan', 1750321638, 1750321638, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(496, 'file', 494, 'department/admin/add', 'Add', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'tj', 'tianjia', 1750321638, 1750321638, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(497, 'file', 494, 'department/admin/edit', 'Edit', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'bj', 'bianji', 1750321638, 1750321638, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(498, 'file', 494, 'department/admin/del', 'Del', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'sc', 'shanchu', 1750321638, 1750321638, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(499, 'file', 494, 'department/admin/multi', 'Multi', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'plgx', 'pilianggengxin', 1750321638, 1750321638, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(500, 'file', 494, 'department/admin/principal', 'Principal set', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'Ps', 'Principalset', 1750321638, 1750321638, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(501, 'file', 493, 'department/index', 'Department list', 'fa fa-sliders', '', '', '', 1, NULL, '', 'Dl', 'Departmentlist', 1750321638, 1750321638, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(502, 'file', 501, 'department/index/index', 'View', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'zk', 'zhakan', 1750321638, 1750321638, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(503, 'file', 501, 'department/index/add', 'Add', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'tj', 'tianjia', 1750321638, 1750321638, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(504, 'file', 501, 'department/index/edit', 'Edit', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'bj', 'bianji', 1750321638, 1750321638, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(505, 'file', 501, 'department/index/del', 'Del', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'sc', 'shanchu', 1750321638, 1750321638, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(506, 'file', 501, 'department/index/multi', 'Multi', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'plgx', 'pilianggengxin', 1750321638, 1750321638, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(661, 'file', 0, 'wechat', '公众号管理', 'fa fa-wechat', '', '', '', 1, 'addtabs', '', 'gzhgl', 'gongzhonghaoguanli', 1750498237, 1750498739, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(662, 'file', 661, 'wechat/autoreply', '自动回复管理', 'fa fa-reply-all', '', '', '', 1, NULL, '', 'zdhfgl', 'zidonghuifuguanli', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(663, 'file', 662, 'wechat/autoreply/index', '查看', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'zk', 'zhakan', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(664, 'file', 662, 'wechat/autoreply/add', '添加', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'tj', 'tianjia', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(665, 'file', 662, 'wechat/autoreply/edit', '修改', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'xg', 'xiugai', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(666, 'file', 662, 'wechat/autoreply/del', '删除', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'sc', 'shanchu', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(667, 'file', 662, 'wechat/autoreply/multi', '批量更新', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'plgx', 'pilianggengxin', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(668, 'file', 661, 'wechat/config', '配置管理', 'fa fa-cog', '', '', '', 1, NULL, '', 'pzgl', 'peizhiguanli', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(669, 'file', 668, 'wechat/config/index', '查看', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'zk', 'zhakan', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(670, 'file', 668, 'wechat/config/add', '添加', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'tj', 'tianjia', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(671, 'file', 668, 'wechat/config/edit', '修改', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'xg', 'xiugai', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(672, 'file', 668, 'wechat/config/del', '删除', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'sc', 'shanchu', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(673, 'file', 668, 'wechat/config/multi', '批量更新', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'plgx', 'pilianggengxin', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(674, 'file', 661, 'wechat/menu', '菜单管理', 'fa fa-list', '', '', '', 1, NULL, '', 'cdgl', 'caidanguanli', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(675, 'file', 674, 'wechat/menu/index', '查看', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'zk', 'zhakan', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(676, 'file', 674, 'wechat/menu/add', '添加', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'tj', 'tianjia', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(677, 'file', 674, 'wechat/menu/edit', '修改', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'xg', 'xiugai', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(678, 'file', 674, 'wechat/menu/del', '删除', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'sc', 'shanchu', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(679, 'file', 674, 'wechat/menu/remote', '加载远程菜单', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'jzyccd', 'jiazaiyuanchengcaidan', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(680, 'file', 674, 'wechat/menu/sync', '同步', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'tb', 'tongbu', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(681, 'file', 674, 'wechat/menu/multi', '批量更新', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'plgx', 'pilianggengxin', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(682, 'file', 661, 'wechat/response', '资源管理', 'fa fa-list-alt', '', '', '', 1, NULL, '', 'zygl', 'ziyuanguanli', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(683, 'file', 682, 'wechat/response/index', '查看', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'zk', 'zhakan', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(684, 'file', 682, 'wechat/response/add', '添加', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'tj', 'tianjia', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(685, 'file', 682, 'wechat/response/edit', '修改', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'xg', 'xiugai', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(686, 'file', 682, 'wechat/response/del', '删除', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'sc', 'shanchu', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(687, 'file', 682, 'wechat/response/select', '选择', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'xz', 'xuanze', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(688, 'file', 682, 'wechat/response/multi', '批量更新', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'plgx', 'pilianggengxin', 1750498237, 1750498237, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(708, 'file', 220, 'resume_apply', '简历投递管理', 'fa fa-file-text-o', '', '', '', 1, 'addtabs', '', 'jltdgl', 'jianlitoudiguanli', 1750734891, 1750903099, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(709, 'file', 708, 'resume_apply/index', '简历投递列表', 'fa fa-th-list', '', '', '', 0, 'addtabs', '', 'jltdlb', 'jianlitoudiliebiao', 1750734891, 1750903059, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(710, 'file', 708, 'resume_apply/add', '添加', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'tj', 'tianjia', 1750734891, 1750734891, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(711, 'file', 708, 'resume_apply/edit', '编辑', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'bj', 'bianji', 1750734891, 1750734891, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(712, 'file', 708, 'resume_apply/del', '删除', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'sc', 'shanchu', 1750734891, 1750734891, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(713, 'file', 708, 'resume_apply/viewresume', '查看简历详情', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'zkjlxq', 'zhakanjianlixiangqing', 1750734891, 1750734891, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(714, 'file', 708, 'resume_apply/viewjob', '查看岗位详情', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'zkgwxq', 'zhakangangweixiangqing', 1750734891, 1750734891, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(715, 'file', 708, 'resume_apply/multi', '批量更新状态', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'plgxzt', 'pilianggengxinzhuangtai', 1750734891, 1750734891, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(716, 'file', 708, 'resume_apply/feedback', '更新反馈', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'gxfk', 'gengxinfankui', 1750734891, 1750734891, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(740, 'file', 343, 'jobfavorite', '职位收藏管理', 'fa fa-heart', '', '', '', 1, 'addtabs', '', 'zwscgl', 'zhiweishoucangguanli', 1750820541, 1750903131, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(741, 'file', 740, 'jobfavorite/index', '查看', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'zk', 'zhakan', 1750820541, 1750820586, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(742, 'file', 740, 'jobfavorite/add', '添加', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'tj', 'tianjia', 1750820541, 1750820586, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(743, 'file', 740, 'jobfavorite/edit', '编辑', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'bj', 'bianji', 1750820541, 1750820586, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(744, 'file', 740, 'jobfavorite/del', '删除', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'sc', 'shanchu', 1750820541, 1750820586, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(745, 'file', 740, 'jobfavorite/multi', '批量更新', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'plgx', 'pilianggengxin', 1750820541, 1750820586, 0, 'normal');
INSERT INTO `fa_auth_rule` VALUES(746, 'file', 740, 'jobfavorite/menu', '添加菜单', 'fa fa-circle-o', '', '', '', 0, NULL, '', 'tjcd', 'tianjiacaidan', 1750820586, 1750820586, 0, 'normal');
COMMIT;

-- ----------------------------
-- Table structure for fa_category
-- ----------------------------
CREATE TABLE `fa_category` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `pid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父ID',
  `type` varchar(30) DEFAULT '' COMMENT '栏目类型',
  `name` varchar(30) DEFAULT '',
  `nickname` varchar(50) DEFAULT '',
  `flag` set('hot','index','recommend') DEFAULT '',
  `image` varchar(100) DEFAULT '' COMMENT '图片',
  `keywords` varchar(255) DEFAULT '' COMMENT '关键字',
  `description` varchar(255) DEFAULT '' COMMENT '描述',
  `diyname` varchar(30) DEFAULT '' COMMENT '自定义名称',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `status` varchar(30) DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `weigh` (`weigh`,`id`),
  KEY `pid` (`pid`)
) ENGINE=InnoDB AUTO_INCREMENT=14 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='分类表';

-- ----------------------------
-- Records of fa_category
-- ----------------------------
BEGIN;
INSERT INTO `fa_category` VALUES (1, 0, 'page', '官方新闻', 'news', 'recommend', '/assets/img/qrcode.png', '', '', 'news', 1491635035, 1491635035, 1, 'normal');
INSERT INTO `fa_category` VALUES (2, 0, 'page', '移动应用', 'mobileapp', 'hot', '/assets/img/qrcode.png', '', '', 'mobileapp', 1491635035, 1491635035, 2, 'normal');
INSERT INTO `fa_category` VALUES (3, 2, 'page', '微信公众号', 'wechatpublic', 'index', '/assets/img/qrcode.png', '', '', 'wechatpublic', 1491635035, 1491635035, 3, 'normal');
INSERT INTO `fa_category` VALUES (4, 2, 'page', 'Android开发', 'android', 'recommend', '/assets/img/qrcode.png', '', '', 'android', 1491635035, 1491635035, 4, 'normal');
INSERT INTO `fa_category` VALUES (5, 0, 'page', '软件产品', 'software', 'recommend', '/assets/img/qrcode.png', '', '', 'software', 1491635035, 1491635035, 5, 'normal');
INSERT INTO `fa_category` VALUES (6, 5, 'page', '网站建站', 'website', 'recommend', '/assets/img/qrcode.png', '', '', 'website', 1491635035, 1491635035, 6, 'normal');
INSERT INTO `fa_category` VALUES (7, 5, 'page', '企业管理软件', 'company', 'index', '/assets/img/qrcode.png', '', '', 'company', 1491635035, 1491635035, 7, 'normal');
INSERT INTO `fa_category` VALUES (8, 6, 'page', 'PC端', 'website-pc', 'recommend', '/assets/img/qrcode.png', '', '', 'website-pc', 1491635035, 1491635035, 8, 'normal');
INSERT INTO `fa_category` VALUES (9, 6, 'page', '移动端', 'website-mobile', 'recommend', '/assets/img/qrcode.png', '', '', 'website-mobile', 1491635035, 1491635035, 9, 'normal');
INSERT INTO `fa_category` VALUES (10, 7, 'page', 'CRM系统 ', 'company-crm', 'recommend', '/assets/img/qrcode.png', '', '', 'company-crm', 1491635035, 1491635035, 10, 'normal');
INSERT INTO `fa_category` VALUES (11, 7, 'page', 'SASS平台软件', 'company-sass', 'recommend', '/assets/img/qrcode.png', '', '', 'company-sass', 1491635035, 1491635035, 11, 'normal');
INSERT INTO `fa_category` VALUES (12, 0, 'test', '测试1', 'test1', 'recommend', '/assets/img/qrcode.png', '', '', 'test1', 1491635035, 1491635035, 12, 'normal');
INSERT INTO `fa_category` VALUES (13, 0, 'test', '测试2', 'test2', 'recommend', '/assets/img/qrcode.png', '', '', 'test2', 1491635035, 1491635035, 13, 'normal');
COMMIT;

-- ----------------------------
-- Table structure for fa_config
-- ----------------------------
CREATE TABLE `fa_config` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(30) DEFAULT '' COMMENT '变量名',
  `group` varchar(30) DEFAULT '' COMMENT '分组',
  `title` varchar(100) DEFAULT '' COMMENT '变量标题',
  `tip` varchar(100) DEFAULT '' COMMENT '变量描述',
  `type` varchar(30) DEFAULT '' COMMENT '类型:string,text,int,bool,array,datetime,date,file',
  `visible` varchar(255) DEFAULT '' COMMENT '可见条件',
  `value` text COMMENT '变量值',
  `content` text COMMENT '变量字典数据',
  `rule` varchar(100) DEFAULT '' COMMENT '验证规则',
  `extend` varchar(255) DEFAULT '' COMMENT '扩展属性',
  `setting` varchar(255) DEFAULT '' COMMENT '配置',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='系统配置';

-- ----------------------------
-- Records of fa_config
-- ----------------------------
BEGIN;
INSERT INTO `fa_config` VALUES(1, 'name', 'basic', 'Site name', '请填写站点名称', 'string', '', '我的网站', '', 'required', '', NULL);
INSERT INTO `fa_config` VALUES(2, 'beian', 'basic', 'Beian', '粤ICP备15000000号-1', 'string', '', '粤ICP备15000000号-1', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(3, 'cdnurl', 'basic', 'Cdn url', '如果全站静态资源使用第三方云储存请配置该值', 'string', '', '', '', '', '', '');
INSERT INTO `fa_config` VALUES(4, 'version', 'basic', 'Version', '如果静态资源有变动请重新配置该值', 'string', '', '1.2', '', 'required', '', NULL);
INSERT INTO `fa_config` VALUES(5, 'timezone', 'basic', 'Timezone', '', 'string', '', 'Asia/Shanghai', '', 'required', '', NULL);
INSERT INTO `fa_config` VALUES(6, 'forbiddenip', 'basic', 'Forbidden ip', '一行一条记录', 'text', '', '', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(7, 'languages', 'basic', 'Languages', '', 'array', '', '{\"backend\":\"zh-cn\",\"frontend\":\"zh-cn\"}', '', 'required', '', NULL);
INSERT INTO `fa_config` VALUES(8, 'fixedpage', 'basic', 'Fixed page', '请输入左侧菜单栏存在的链接', 'string', '', 'dashboard', '', 'required', '', NULL);
INSERT INTO `fa_config` VALUES(9, 'categorytype', 'dictionary', 'Category type', '', 'array', '', '{\"default\":\"默认\",\"page\":\"单页\",\"article\":\"文章\",\"test\":\"Test\"}', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(10, 'configgroup', 'dictionary', 'Config group', '', 'array', '', '{\"basic\":\"基础配置\",\"email\":\"邮件配置\",\"dictionary\":\"字典配置\",\"user\":\"会员配置\",\"example\":\"示例分组\",\"baidu_ocr\":\"百度OCR\",\"openai\":\"AI配置\",\"pages\":\"页面设置\",\"social\":\"联系方式设置\",\"wechat\":\"微信小程序设置\"}', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(11, 'mail_type', 'email', 'Mail type', '选择邮件发送方式', 'select', '', '1', '[\"请选择\",\"SMTP\"]', '', '', NULL);
INSERT INTO `fa_config` VALUES(12, 'mail_smtp_host', 'email', 'Mail smtp host', '错误的配置发送邮件会导致服务器超时', 'string', '', 'smtp.qq.com', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(13, 'mail_smtp_port', 'email', 'Mail smtp port', '(不加密默认25,SSL默认465,TLS默认587)', 'string', '', '465', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(14, 'mail_smtp_user', 'email', 'Mail smtp user', '（填写完整用户名）', 'string', '', '', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(15, 'mail_smtp_pass', 'email', 'Mail smtp password', '（填写您的密码或授权码）', 'password', '', '', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(16, 'mail_verify_type', 'email', 'Mail vertify type', '（SMTP验证方式[推荐SSL]）', 'select', '', '', '[\"无\",\"TLS\",\"SSL\"]', '', '', NULL);
INSERT INTO `fa_config` VALUES(17, 'mail_from', 'email', 'Mail from', '', 'string', '', '1', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(18, 'attachmentcategory', 'dictionary', 'Attachment category', '', 'array', '', '{\"category1\":\"分类一\",\"category2\":\"分类二\",\"custom\":\"自定义\"}', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(45, 'baidu_ocr_enabled', 'baidu_ocr', 'OCR功能开关', '是否启用身份证OCR识别功能', 'switch', '', '0', '0:禁用\n1:启用', '', '', NULL);
INSERT INTO `fa_config` VALUES(46, 'baidu_ocr_api_key', 'baidu_ocr', 'API Key', '百度OCR API Key', 'string', '', '', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(47, 'baidu_ocr_secret_key', 'baidu_ocr', 'Secret Key', '百度OCR Secret Key', 'string', '', '', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(48, 'openai_enabled', 'openai', '启用AI', '是否启用AI功能', 'switch', '', '0', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(49, 'openai_api_url', 'openai', 'AI接口地址', 'AI接口地址（使用OpenAI通用接口）', 'string', '', 'https://api.hunyuan.cloud.tencent.com/v1', '', 'required', '', NULL);
INSERT INTO `fa_config` VALUES(50, 'openai_api_key', 'openai', 'SecretKey', 'API密钥Key', 'string', '', '', '', 'required', '', NULL);
INSERT INTO `fa_config` VALUES(51, 'openai_model', 'openai', 'AI模型', 'AI模型名称', 'string', '', 'hunyuan-lite', '', 'required', '', NULL);
INSERT INTO `fa_config` VALUES(52, 'phone', 'pages', 'Phone', '页脚显示的联系电话', 'string', '', '1', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(53, 'email', 'pages', 'Email address', '页脚显示的联系邮箱', 'string', '', '1', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(54, 'address', 'pages', 'Address', '页脚显示的联系地址', 'string', '', '1', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(55, 'banner_image', 'pages', 'Banner image', '首页轮播图片', 'images', '', '', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(56, 'about_image', 'pages', 'About image', '关于我们图片', 'image', '', '', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(57, 'description', 'pages', 'Description', '公司简介', 'text', '', '我们是一家专业的人力资源服务公司，致力于为企业提供优质的人才招聘服务，为求职者提供理想的职业发展平台。', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(58, 'about_title', 'pages', 'About title', '关于我们页面标题', 'string', '', '关于我们', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(59, 'about_content', 'pages', 'About content', '关于我们页面内容', 'editor', '', '<h2>公司简介</h2><p>我们是一家专业的人力资源服务公司，致力于为企业提供优质的人才招聘服务，为求职者提供理想的职业发展平台。</p><h2>我们的使命</h2><p>帮助企业找到合适的人才，帮助求职者找到理想的工作，促进人力资源市场的良性发展。</p><h2>我们的优势</h2><ul><li>专业的招聘团队</li><li>广泛的企业资源</li><li>高效的匹配系统</li><li>完善的售后服务</li></ul>', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(60, 'help_title', 'pages', 'Help title', '帮助中心页面标题', 'string', '', '帮助中心', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(61, 'help_content', 'pages', 'Help content', '帮助中心页面内容', 'editor', '', '<h2>如何注册账号？</h2><h2></h2><p>点击网站右上角的\"注册\"按钮，填写相关信息即可完成注册。</p><h2>如何投递简历？</h2><p>浏览职位列表，找到心仪的职位后点击\"申请职位\"按钮，上传您的简历即可。</p><h2>如何修改个人信息？</h2><p>登录后进入个人中心，点击\"编辑资料\"即可修改个人信息。</p>', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(62, 'privacy_title', 'pages', 'Privacy title', '隐私政策页面标题', 'string', '', '隐私政策', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(63, 'privacy_content', 'pages', 'Privacy content', '隐私政策页面内容', 'editor', '', '<h2>隐私声明</h2><p>本隐私政策说明了我们如何收集、使用、披露、传输和存储您的个人信息。</p><h2>信息收集</h2><p>当您注册账户、发布职位、投递简历时，我们会收集相关的个人信息。</p><h2>信息使用</h2><p>我们使用收集到的信息来提供、维护、保护和改进我们的服务，开发新的服务，并保护我们的用户。</p><h2>信息共享</h2><p>未经您的同意，我们不会与第三方共享您的个人信息，法律法规要求的情况除外。</p>', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(64, 'wechat_title', 'social', 'Wechat title', '微信弹窗标题', 'string', '', '微信公众号', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(65, 'wechat_image', 'social', 'Wechat image', '微信二维码图片', 'image', '', '', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(66, 'wechat_desc', 'social', 'Wechat desc', '微信二维码描述', 'text', '', '扫描上方二维码关注我们的微信公众号，获取最新招聘信息和职场资讯。', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(67, 'weibo_title', 'social', 'Weibo title', '微博弹窗标题', 'string', '', '官方微博', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(68, 'weibo_url', 'social', 'Weibo url', '微博链接', 'string', '', '', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(69, 'weibo_desc', 'social', 'Weibo desc', '微博描述', 'text', '', '关注我们的官方微博，了解最新动态和招聘信息。', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(70, 'wechat_miniapp_appid', 'wechat', '小程序AppID', '微信小程序的AppID，在微信开发者平台获取\r\n', 'string', '', '', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(71, 'wechat_miniapp_appsecret', 'wechat', '小程序AppSecret', '微信小程序的AppSecret，在微信开发者平台获取\r\n', 'string', '', '', '', '', '', NULL);
INSERT INTO `fa_config` VALUES(80, 'logo', 'wechat', '小程序LOGO', '小程序登录页LOGO', 'image', '', '', '', '', '', NULL);

COMMIT;

-- ----------------------------
-- Table structure for fa_ems
-- ----------------------------
CREATE TABLE `fa_ems`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `event` varchar(30) DEFAULT '' COMMENT '事件',
  `email` varchar(100) DEFAULT '' COMMENT '邮箱',
  `code` varchar(10) DEFAULT '' COMMENT '验证码',
  `times` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '验证次数',
  `ip` varchar(30) DEFAULT '' COMMENT 'IP',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='邮箱验证码表';

-- ----------------------------
-- Table structure for fa_sms
-- ----------------------------
CREATE TABLE `fa_sms` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `event` varchar(30) DEFAULT '' COMMENT '事件',
  `mobile` varchar(20) DEFAULT '' COMMENT '手机号',
  `code` varchar(10) DEFAULT '' COMMENT '验证码',
  `times` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '验证次数',
  `ip` varchar(30) DEFAULT '' COMMENT 'IP',
  `createtime` bigint(16) unsigned DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='短信验证码表';

-- ----------------------------
-- Table structure for fa_test
-- ----------------------------
CREATE TABLE `fa_test` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) DEFAULT '0' COMMENT '会员ID',
  `admin_id` int(10) DEFAULT '0' COMMENT '管理员ID',
  `category_id` int(10) unsigned DEFAULT '0' COMMENT '分类ID(单选)',
  `category_ids` varchar(100) COMMENT '分类ID(多选)',
  `tags` varchar(255) DEFAULT '' COMMENT '标签',
  `week` enum('monday','tuesday','wednesday') COMMENT '星期(单选):monday=星期一,tuesday=星期二,wednesday=星期三',
  `flag` set('hot','index','recommend') DEFAULT '' COMMENT '标志(多选):hot=热门,index=首页,recommend=推荐',
  `genderdata` enum('male','female') DEFAULT 'male' COMMENT '性别(单选):male=男,female=女',
  `hobbydata` set('music','reading','swimming') COMMENT '爱好(多选):music=音乐,reading=读书,swimming=游泳',
  `title` varchar(100) DEFAULT '' COMMENT '标题',
  `content` text COMMENT '内容',
  `image` varchar(100) DEFAULT '' COMMENT '图片',
  `images` varchar(1500) DEFAULT '' COMMENT '图片组',
  `attachfile` varchar(100) DEFAULT '' COMMENT '附件',
  `keywords` varchar(255) DEFAULT '' COMMENT '关键字',
  `description` varchar(255) DEFAULT '' COMMENT '描述',
  `city` varchar(100) DEFAULT '' COMMENT '省市',
  `array` varchar(255) DEFAULT '' COMMENT '数组:value=值',
  `json` varchar(255) DEFAULT '' COMMENT '配置:key=名称,value=值',
  `multiplejson` varchar(1500) DEFAULT '' COMMENT '二维数组:title=标题,intro=介绍,author=作者,age=年龄',
  `price` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '价格',
  `views` int(10) unsigned DEFAULT '0' COMMENT '点击',
  `workrange` varchar(100) DEFAULT '' COMMENT '时间区间',
  `startdate` date DEFAULT NULL COMMENT '开始日期',
  `activitytime` datetime DEFAULT NULL COMMENT '活动时间(datetime)',
  `year` year(4) DEFAULT NULL COMMENT '年',
  `times` time DEFAULT NULL COMMENT '时间',
  `refreshtime` bigint(16) DEFAULT NULL COMMENT '刷新时间',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) DEFAULT NULL COMMENT '删除时间',
  `weigh` int(10) DEFAULT '0' COMMENT '权重',
  `switch` tinyint(1) DEFAULT '0' COMMENT '开关',
  `status` enum('normal','hidden') DEFAULT 'normal' COMMENT '状态',
  `state` enum('0','1','2') DEFAULT '1' COMMENT '状态值:0=禁用,1=正常,2=推荐',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='测试表';

-- ----------------------------
-- Records of fa_test
-- ----------------------------
BEGIN;
INSERT INTO `fa_test` VALUES (1, 1, 1, 12, '12,13', '互联网,计算机', 'monday', 'hot,index', 'male', 'music,reading', '我是一篇测试文章', '<p>我是测试内容</p>', '/assets/img/avatar.png', '/assets/img/avatar.png,/assets/img/qrcode.png', '/assets/img/avatar.png', '关键字', '我是一篇测试文章描述，内容过多时将自动隐藏', '广西壮族自治区/百色市/平果县', '[\"a\",\"b\"]', '{\"a\":\"1\",\"b\":\"2\"}', '[{\"title\":\"标题一\",\"intro\":\"介绍一\",\"author\":\"小明\",\"age\":\"21\"}]', 0.00, 0, '2020-10-01 00:00:00 - 2021-10-31 23:59:59', '2017-07-10', '2017-07-10 18:24:45', 2017, '18:24:45', 1491635035, 1491635035, 1491635035, NULL, 0, 1, 'normal', '1');
COMMIT;

-- ----------------------------
-- Table structure for fa_user
-- ----------------------------
CREATE TABLE `fa_user` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `group_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '组别ID',
  `username` varchar(32) DEFAULT '' COMMENT '用户名',
  `nickname` varchar(50) DEFAULT '' COMMENT '昵称',
  `realname` varchar(50) DEFAULT '' COMMENT '真实姓名',
  `password` varchar(32) DEFAULT '' COMMENT '密码',
  `salt` varchar(30) DEFAULT '' COMMENT '密码盐',
  `email` varchar(100) DEFAULT '' COMMENT '电子邮箱',
  `mobile` varchar(11) DEFAULT '' COMMENT '手机号',
  `avatar` varchar(255) DEFAULT '' COMMENT '头像',
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '等级',
  `gender` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '性别',
  `birthday` date DEFAULT NULL COMMENT '生日',
  `bio` varchar(100) DEFAULT '' COMMENT '格言',
  `money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额',
  `score` int(10) NOT NULL DEFAULT '0' COMMENT '积分',
  `successions` int(10) UNSIGNED NOT NULL DEFAULT '1' COMMENT '连续登录天数',
  `maxsuccessions` int(10) UNSIGNED NOT NULL DEFAULT '1' COMMENT '最大连续登录天数',
  `prevtime` bigint(16) DEFAULT NULL COMMENT '上次登录时间',
  `logintime` bigint(16) DEFAULT NULL COMMENT '登录时间',
  `loginip` varchar(50) DEFAULT '' COMMENT '登录IP',
  `loginfailure` tinyint(1) UNSIGNED NOT NULL DEFAULT '0' COMMENT '失败次数',
  `loginfailuretime` bigint(16) DEFAULT NULL COMMENT '最后登录失败时间',
  `joinip` varchar(50) DEFAULT '' COMMENT '加入IP',
  `jointime` bigint(16) DEFAULT NULL COMMENT '加入时间',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `token` varchar(50) DEFAULT '' COMMENT 'Token',
  `status` varchar(30) DEFAULT '' COMMENT '状态',
  `is_first_login` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否首次登录',
  `verification` varchar(255) DEFAULT '' COMMENT '验证',
  `wx_openid` varchar(50) DEFAULT NULL COMMENT '微信openid',
  `wx_unionid` varchar(50) DEFAULT NULL COMMENT '微信unionid',
  `wx_session_key` varchar(128) DEFAULT NULL COMMENT '微信会话密钥',
  PRIMARY KEY (`id`),
  KEY `username` (`username`),
  KEY `email` (`email`),
  KEY `mobile` (`mobile`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='会员表';

-- ----------------------------
-- Records of fa_user
-- ----------------------------
BEGIN;
INSERT INTO `fa_user` VALUES(1, 1, 'admin', 'admin', '', '', '', '<EMAIL>', '13000000000', '', 0, 0, '2017-04-08', '', '0.00', 0, 1, 1, 1491635035, 1491635035, '127.0.0.1', 0, 1491635035, '127.0.0.1', 1491635035, 0, 1491635035, '', 'normal', 0, '', NULL, NULL, NULL);
COMMIT;

-- ----------------------------
-- Table structure for fa_user_group
-- ----------------------------
CREATE TABLE `fa_user_group` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `name` varchar(50) DEFAULT '' COMMENT '组名',
  `rules` text COMMENT '权限节点',
  `createtime` bigint(16) DEFAULT NULL COMMENT '添加时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `status` enum('normal','hidden') DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='会员组表';

-- ----------------------------
-- Records of fa_user_group
-- ----------------------------
BEGIN;
INSERT INTO `fa_user_group` VALUES (1, '默认组', '1,2,3,4,5,6,7,8,9,10,11,12', 1491635035, 1491635035, 'normal');
COMMIT;

-- ----------------------------
-- Table structure for fa_user_money_log
-- ----------------------------
CREATE TABLE `fa_user_money_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '会员ID',
  `money` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更余额',
  `before` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更前余额',
  `after` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '变更后余额',
  `memo` varchar(255) DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='会员余额变动表';

-- ----------------------------
-- Table structure for fa_user_rule
-- ----------------------------
CREATE TABLE `fa_user_rule` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `pid` int(10) DEFAULT NULL COMMENT '父ID',
  `name` varchar(50) DEFAULT NULL COMMENT '名称',
  `title` varchar(50) DEFAULT '' COMMENT '标题',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注',
  `ismenu` tinyint(1) DEFAULT NULL COMMENT '是否菜单',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) DEFAULT '0' COMMENT '权重',
  `status` enum('normal','hidden') DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='会员规则表';

-- ----------------------------
-- Records of fa_user_rule
-- ----------------------------
BEGIN;
INSERT INTO `fa_user_rule` VALUES (1, 0, 'index', 'Frontend', '', 1, 1491635035, 1491635035, 1, 'normal');
INSERT INTO `fa_user_rule` VALUES (2, 0, 'api', 'API Interface', '', 1, 1491635035, 1491635035, 2, 'normal');
INSERT INTO `fa_user_rule` VALUES (3, 1, 'user', 'User Module', '', 1, 1491635035, 1491635035, 12, 'normal');
INSERT INTO `fa_user_rule` VALUES (4, 2, 'user', 'User Module', '', 1, 1491635035, 1491635035, 11, 'normal');
INSERT INTO `fa_user_rule` VALUES (5, 3, 'index/user/login', 'Login', '', 0, 1491635035, 1491635035, 5, 'normal');
INSERT INTO `fa_user_rule` VALUES (6, 3, 'index/user/register', 'Register', '', 0, 1491635035, 1491635035, 7, 'normal');
INSERT INTO `fa_user_rule` VALUES (7, 3, 'index/user/index', 'User Center', '', 0, 1491635035, 1491635035, 9, 'normal');
INSERT INTO `fa_user_rule` VALUES (8, 3, 'index/user/profile', 'Profile', '', 0, 1491635035, 1491635035, 4, 'normal');
INSERT INTO `fa_user_rule` VALUES (9, 4, 'api/user/login', 'Login', '', 0, 1491635035, 1491635035, 6, 'normal');
INSERT INTO `fa_user_rule` VALUES (10, 4, 'api/user/register', 'Register', '', 0, 1491635035, 1491635035, 8, 'normal');
INSERT INTO `fa_user_rule` VALUES (11, 4, 'api/user/index', 'User Center', '', 0, 1491635035, 1491635035, 10, 'normal');
INSERT INTO `fa_user_rule` VALUES (12, 4, 'api/user/profile', 'Profile', '', 0, 1491635035, 1491635035, 3, 'normal');
COMMIT;

-- ----------------------------
-- Table structure for fa_user_score_log
-- ----------------------------
CREATE TABLE `fa_user_score_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '会员ID',
  `score` int(10) NOT NULL DEFAULT '0' COMMENT '变更积分',
  `before` int(10) NOT NULL DEFAULT '0' COMMENT '变更前积分',
  `after` int(10) NOT NULL DEFAULT '0' COMMENT '变更后积分',
  `memo` varchar(255) DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='会员积分变动表';

-- ----------------------------
-- Table structure for fa_user_token
-- ----------------------------
CREATE TABLE `fa_user_token` (
  `token` varchar(50) NOT NULL COMMENT 'Token',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '会员ID',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `expiretime` bigint(16) DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`token`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='会员Token表';

-- ----------------------------
-- Table structure for fa_version
-- ----------------------------
CREATE TABLE `fa_version`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `oldversion` varchar(30) DEFAULT '' COMMENT '旧版本号',
  `newversion` varchar(30) DEFAULT '' COMMENT '新版本号',
  `packagesize` varchar(30) DEFAULT '' COMMENT '包大小',
  `content` varchar(500) DEFAULT '' COMMENT '升级内容',
  `downloadurl` varchar(255) DEFAULT '' COMMENT '下载地址',
  `enforce` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '强制更新',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` varchar(30) DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE utf8mb4_general_ci COMMENT='版本表';

--
-- 表的结构 `fa_jobs`
--

CREATE TABLE IF NOT EXISTS `fa_jobs` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(200) NOT NULL COMMENT '岗位标题',
  `image` varchar(255) DEFAULT NULL COMMENT '岗位图片',
  `company` varchar(200) NOT NULL COMMENT '公司名称',
  `job_code` varchar(50) NOT NULL COMMENT '岗位编号',
  `job_name` varchar(100) NOT NULL COMMENT '岗位名称',
  `category` varchar(200) DEFAULT NULL COMMENT '岗位分类',
  `salary_range` varchar(100) NOT NULL COMMENT '薪资范围',
  `age_requirement` varchar(50) DEFAULT NULL COMMENT '年龄要求',
  `gender_requirement` varchar(20) DEFAULT NULL COMMENT '性别要求',
  `language_requirement` varchar(100) DEFAULT NULL COMMENT '语言要求',
  `education_requirement` varchar(50) DEFAULT NULL COMMENT '学历要求',
  `experience_requirement` varchar(100) DEFAULT NULL COMMENT '经验要求',
  `working_hours` varchar(100) DEFAULT NULL COMMENT '工作时间',
  `accommodation` varchar(255) DEFAULT NULL COMMENT '食宿情况',
  `job_description` text COMMENT '岗位内容',
  `total_fee` varchar(200) DEFAULT NULL COMMENT '总费用',
  `remark` text COMMENT '备注',
  `job_summary` text COMMENT '岗位汇总',
  `resume_count` int(11) DEFAULT '0' COMMENT '应聘简历数',
  `weight` int(11) NOT NULL DEFAULT '0' COMMENT '权重',
  `create_time` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间',
  `status` enum('上架','暂停','下架') DEFAULT '上架' COMMENT '状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8mb4 COMMENT='岗位表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_job_category`
--

CREATE TABLE IF NOT EXISTS `fa_job_category` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `image` varchar(255) DEFAULT NULL COMMENT '分类图片',
  `create_time` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间',
  `status` enum('展示','隐藏') DEFAULT '展示' COMMENT '状态',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8mb4 COMMENT='岗位分类表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_job_favorite`
--

CREATE TABLE IF NOT EXISTS `fa_job_favorite` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户ID',
  `job_id` int(11) NOT NULL COMMENT '职位ID',
  `create_time` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_job` (`user_id`,`job_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='职位收藏表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_job_interview`
--

CREATE TABLE IF NOT EXISTS `fa_job_interview` (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(200) DEFAULT NULL COMMENT '面试标题',
  `interview_time` datetime DEFAULT NULL COMMENT '面试时间',
  `job_code` varchar(200) DEFAULT NULL COMMENT '岗位编号',
  `job` varchar(200) NOT NULL COMMENT '面试岗位',
  `interview_type` enum('线上面试','现场面试','入港面试') DEFAULT NULL COMMENT '面试类型',
  `interview_materials` text COMMENT '面试资料',
  `resume_id` text COMMENT '面试人员简历ID',
  `interviewee` text COMMENT '面试人员',
  `progress` text COMMENT '面试人员进度',
  `status` enum('未开始','进行中','已结束','已取消') NOT NULL DEFAULT '未开始' COMMENT '面试状态',
  `create_time` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '修改时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=16 DEFAULT CHARSET=utf8mb4 COMMENT='面试管理表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_resume`
--

CREATE TABLE IF NOT EXISTS `fa_resume` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户ID',
  `name` varchar(50) NOT NULL COMMENT '姓名',
  `intended_position` varchar(100) DEFAULT NULL COMMENT '意向岗位',
  `applied_position` varchar(100) DEFAULT NULL COMMENT '申请岗位',
  `ethnicity` varchar(20) DEFAULT NULL COMMENT '民族',
  `height` decimal(5,2) DEFAULT NULL COMMENT '身高(cm)',
  `weight` decimal(5,2) DEFAULT NULL COMMENT '体重(kg)',
  `id_card` varchar(18) DEFAULT NULL COMMENT '身份证号',
  `gender` enum('未知','男','女') DEFAULT '未知' COMMENT '性别',
  `age` tinyint(3) DEFAULT NULL COMMENT '年龄',
  `birth_date` text COMMENT '出生日期',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号码',
  `marital_status` enum('未婚','已婚','离异') DEFAULT '未婚' COMMENT '婚姻状况',
  `hukou_location` text COMMENT '户口所在地',
  `residence_address` text COMMENT '常住地址',
  `highest_education` varchar(20) DEFAULT NULL COMMENT '最高学历',
  `cantonese_level` enum('不会','一般','熟练') DEFAULT '不会' COMMENT '粤语熟练度',
  `mandarin_level` enum('不会','一般','熟练') DEFAULT '不会' COMMENT '国语熟练度',
  `english_level` enum('不会','一般','熟练') DEFAULT '不会' COMMENT '英语熟练度',
  `hk_macau_passport` varchar(50) DEFAULT NULL COMMENT '港澳通行证编号',
  `hk_macau_passport_expiry` text COMMENT '澳通行证到期时间',
  `special_certificate` varchar(255) DEFAULT NULL COMMENT '特殊职业资格证',
  `overseas_experience` enum('无','有') DEFAULT '无' COMMENT '海外工作经历',
  `overseas_region` varchar(100) DEFAULT NULL COMMENT '海外工作地区',
  `hobbies` text COMMENT '兴趣爱好',
  `self_evaluation` text COMMENT '自我评价',
  `contact_relation` text COMMENT '第一联系人关系',
  `contact_name` text COMMENT '第一联系人姓名',
  `contact_age` text COMMENT '第一联系人年龄',
  `contact_job` text COMMENT '第一联系人工作',
  `education_start` text COMMENT '读书开始时间',
  `education_end` text COMMENT '读书结束时间',
  `education_school` text COMMENT '学校名称',
  `education_major` text COMMENT '选修专业',
  `graduation_education` text COMMENT '毕业学历',
  `job_start` text COMMENT '工作开始时间',
  `job_end` text COMMENT '工作结束时间',
  `job_company` text COMMENT '工作单位',
  `job_position` text COMMENT '工作岗位',
  `job_description` text COMMENT '工作内容',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `full_body_photo` varchar(255) DEFAULT NULL COMMENT '全身照',
  `id_card_front` varchar(255) DEFAULT NULL COMMENT '身份证正面',
  `id_card_back` varchar(255) DEFAULT NULL COMMENT '身份证反面',
  `hk_macau_passport_front` varchar(255) DEFAULT NULL COMMENT '港澳通行证正面',
  `hk_macau_passport_back` varchar(255) DEFAULT NULL COMMENT '港澳通行证反面',
  `additional_photos` text COMMENT '补充照片',
  `contact_person` varchar(50) DEFAULT NULL COMMENT '对接人',
  `status` enum('草稿','已发布','官网提交') DEFAULT '草稿' COMMENT '状态:draft=草稿,published=已发布',
  `create_time` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(11) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=67 DEFAULT CHARSET=utf8mb4 COMMENT='简历表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_resume_apply`
--

CREATE TABLE IF NOT EXISTS `fa_resume_apply` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) UNSIGNED DEFAULT '0' COMMENT '用户ID',
  `resume_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '简历ID',
  `resume_name` varchar(100) DEFAULT NULL COMMENT '简历姓名',
  `job_id` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '职位ID',
  `job_code` varchar(50) NOT NULL DEFAULT '' COMMENT '岗位编号',
  `job_name` varchar(100) NOT NULL DEFAULT '' COMMENT '职位名称',
  `status` enum('待处理','未选中','已选中','已取消') NOT NULL DEFAULT '待处理' COMMENT '状态',
  `feedback` text COMMENT '反馈',
  `create_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `update_time` int(10) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=18 DEFAULT CHARSET=utf8mb4 COMMENT='简历投递表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_wechat_autoreply`
--

CREATE TABLE IF NOT EXISTS `fa_wechat_autoreply` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '标题',
  `text` varchar(100) NOT NULL DEFAULT '' COMMENT '触发文本',
  `eventkey` varchar(50) NOT NULL DEFAULT '' COMMENT '响应事件',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
  `createtime` bigint(16) UNSIGNED NOT NULL DEFAULT '0' COMMENT '添加时间',
  `updatetime` bigint(16) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间',
  `status` varchar(30) NOT NULL DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `eventkey` (`eventkey`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COMMENT='微信自动回复表';

--
-- 转存表中的数据 `fa_wechat_autoreply`
--
BEGIN;
INSERT INTO `fa_wechat_autoreply` VALUES(1, '输入hello', 'hello', '58c7d908c4570', '', 1, 1493366855, 1493366855, 'normal');
INSERT INTO `fa_wechat_autoreply` VALUES(2, '输入你好', '你好', '58fdfaa9e1965', '', 2, 1493704976, 1493704976, 'normal');
COMMIT;
-- --------------------------------------------------------

--
-- 表的结构 `fa_wechat_captcha`
--

CREATE TABLE IF NOT EXISTS `fa_wechat_captcha` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `event` varchar(100) DEFAULT '' COMMENT '事件',
  `openid` varchar(255) DEFAULT NULL COMMENT '用户openid',
  `context` varchar(20) DEFAULT NULL COMMENT '上下文',
  `code` varchar(30) DEFAULT NULL COMMENT '验证码',
  `times` int(10) UNSIGNED DEFAULT '0' COMMENT '验证次数',
  `ip` varchar(50) DEFAULT '' COMMENT 'IP',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `ip` (`ip`,`event`) USING BTREE,
  KEY `openid` (`openid`(191),`event`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信公众号验证码';

-- --------------------------------------------------------

--
-- 表的结构 `fa_wechat_config`
--

CREATE TABLE IF NOT EXISTS `fa_wechat_config` (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '配置名称',
  `title` varchar(50) NOT NULL DEFAULT '' COMMENT '配置标题',
  `value` text NOT NULL COMMENT '配置值',
  `createtime` bigint(16) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updatetime` bigint(16) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8mb4 COMMENT='微信配置表';

--
-- 转存表中的数据 `fa_wechat_config`
--
BEGIN;
INSERT INTO `fa_wechat_config` VALUES(1, 'menu', '公众号菜单', '[{\"name\":\"外部链接\",\"sub_button\":[{\"name\":\"百度\",\"type\":\"view\",\"url\":\"https:\\/\\/www.baidu.com\"},{\"name\":\"微信开放平台\",\"type\":\"view\",\"url\":\"https:\\/\\/open.weixin.qq.com\"}]},{\"name\":\"在线客服\",\"type\":\"click\",\"key\":\"58cb852984970\"},{\"name\":\"关于我们\",\"type\":\"click\",\"key\":\"58bf944aa0777\"}]', 1497398820, 1500538185);
INSERT INTO `fa_wechat_config` VALUES(2, 'service', '客服配置', '{\"onlinetime\":\"09:00-18:00\",\"offlinemsg\":\"请在工作时间联系客服！\",\"nosessionmsg\":\"当前没有客服在线！请稍后重试！\",\"waitformsg\":\"请问有什么可以帮到您？\"}', 1497429674, 1497429674);
INSERT INTO `fa_wechat_config` VALUES(3, 'signin', '连续登录配置', '{\"s1\":\"100\",\"s2\":\"200\",\"s3\":\"300\",\"sn\":\"500\"}', 1497429711, 1497429711);
COMMIT;
-- --------------------------------------------------------

--
-- 表的结构 `fa_wechat_context`
--

CREATE TABLE IF NOT EXISTS `fa_wechat_context` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `openid` varchar(64) NOT NULL DEFAULT '',
  `type` varchar(30) NOT NULL DEFAULT '' COMMENT '类型',
  `eventkey` varchar(64) NOT NULL DEFAULT '',
  `command` varchar(64) NOT NULL DEFAULT '',
  `message` varchar(255) NOT NULL DEFAULT '' COMMENT '内容',
  `refreshtime` bigint(16) UNSIGNED NOT NULL DEFAULT '0' COMMENT '最后刷新时间',
  `createtime` bigint(16) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updatetime` bigint(16) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `openid` (`openid`,`eventkey`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='微信上下文表';

-- --------------------------------------------------------

--
-- 表的结构 `fa_wechat_response`
--

CREATE TABLE IF NOT EXISTS `fa_wechat_response` (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) NOT NULL DEFAULT '' COMMENT '资源名',
  `eventkey` varchar(128) NOT NULL DEFAULT '' COMMENT '事件',
  `type` enum('text','image','news','voice','video','music','link','app') NOT NULL DEFAULT 'text' COMMENT '类型',
  `content` text NOT NULL COMMENT '内容',
  `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) UNSIGNED NOT NULL DEFAULT '0' COMMENT '创建时间',
  `updatetime` bigint(16) UNSIGNED NOT NULL DEFAULT '0' COMMENT '更新时间',
  `status` varchar(30) NOT NULL DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`),
  UNIQUE KEY `eventkey` (`eventkey`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COMMENT='微信资源表';

--
-- 转存表中的数据 `fa_wechat_response`
--
BEGIN;
INSERT INTO `fa_wechat_response` VALUES(1, '签到送积分', '58adaf7876aab', 'app', '{\"app\":\"signin\"}', '', 1487777656, 1487777656, 'normal');
INSERT INTO `fa_wechat_response` VALUES(2, '关于我们', '58bf944aa0777', 'app', '{\"app\":\"page\",\"id\":\"1\"}', '', 1488950346, 1488950346, 'normal');
INSERT INTO `fa_wechat_response` VALUES(3, '自动回复1', '58c7d908c4570', 'text', '{\"content\":\"world\"}', '', 1489492232, 1489492232, 'normal');
INSERT INTO `fa_wechat_response` VALUES(5, '自动回复2', '58fdfaa9e1965', 'text', '{\"content\":\"你好!我是自动回复！\"}', '', 1493039785, 1493039785, 'normal');
COMMIT;



SET FOREIGN_KEY_CHECKS = 1;





