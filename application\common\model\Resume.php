<?php

namespace app\common\model;

use think\Model;


class Resume extends Model
{

    

    

    // 表名
    protected $name = 'resume';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'gender_text',
        'marital_status_text',
        'cantonese_level_text',
        'mandarin_level_text',
        'english_level_text',
        'overseas_experience_text',
        'status_text'
    ];
    

    
    public function getGenderList()
    {
        return ['man' => __('Man'), 'wuman' => __('Wuman')];
    }

    public function getMaritalStatusList()
    {
        return ['unmarried' => __('Unmarried'), 'married' => __('Married'), 'divorced' => __('Divorced')];
    }

    public function getCantoneseLevelList()
    {
        return ['cannot' => __('Cannot'), 'general' => __('General'), 'skilled' => __('Skilled')];
    }

    public function getMandarinLevelList()
    {
        return ['cannot' => __('Cannot'), 'general' => __('General'), 'skilled' => __('Skilled')];
    }

    public function getEnglishLevelList()
    {
        return ['cannot' => __('Cannot'), 'general' => __('General'), 'skilled' => __('Skilled')];
    }

    public function getOverseasExperienceList()
    {
        return ['no' => __('No'), 'yes' => __('Yes')];
    }

    public function getStatusList()
    {
        return ['draft' => __('Status draft'), 'published' => __('Status published')];
    }


    public function getGenderTextAttr($value, $data)
    {
        $value = $value ?: ($data['gender'] ?? '');
        $list = $this->getGenderList();
        return $list[$value] ?? '';
    }


    public function getMaritalStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['marital_status'] ?? '');
        $list = $this->getMaritalStatusList();
        return $list[$value] ?? '';
    }


    public function getCantoneseLevelTextAttr($value, $data)
    {
        $value = $value ?: ($data['cantonese_level'] ?? '');
        $list = $this->getCantoneseLevelList();
        return $list[$value] ?? '';
    }


    public function getMandarinLevelTextAttr($value, $data)
    {
        $value = $value ?: ($data['mandarin_level'] ?? '');
        $list = $this->getMandarinLevelList();
        return $list[$value] ?? '';
    }


    public function getEnglishLevelTextAttr($value, $data)
    {
        $value = $value ?: ($data['english_level'] ?? '');
        $list = $this->getEnglishLevelList();
        return $list[$value] ?? '';
    }


    public function getOverseasExperienceTextAttr($value, $data)
    {
        $value = $value ?: ($data['overseas_experience'] ?? '');
        $list = $this->getOverseasExperienceList();
        return $list[$value] ?? '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }




}
