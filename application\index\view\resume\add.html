<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>添加简历</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <!-- 预加载关键资源 -->
    <link rel="preload" href="https://cdn.staticfile.org/jquery/3.6.0/jquery.min.js" as="script">
    <link rel="preload" href="https://cdn.staticfile.org/bootstrap/5.1.3/css/bootstrap.min.css" as="style">
    <link rel="preload" href="https://cdn.staticfile.org/font-awesome/6.0.0/css/all.min.css" as="style">
    
    <!-- 使用更快的CDN源 -->
    <link href="https://cdn.staticfile.org/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/sweetalert2/11.7.32/sweetalert2.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <script src="https://cdn.staticfile.org/jquery/3.6.0/jquery.min.js"></script>
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-bg: #f8f9fa;
            --dark-bg: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f1c40f;
            --border-radius: 12px;
            --box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }
        
        body {
            background-color: var(--light-bg);
            color: var(--primary-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        .navbar {
        background-color: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px);
        box-shadow: var(--box-shadow);
        padding: 1rem 0;
        transition: var(--transition);
    }

    .navbar.scrolled {
        padding: 0.5rem 0;
    }

    .navbar-brand {
        font-weight: 700;
        color: var(--primary-color) !important;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
        letter-spacing: -0.5px;
    }

    .navbar-brand i {
        color: var(--primary-color);
        margin-right: 0.5rem;
        font-size: 1.8rem;
    }

    .nav-link {
        color: var(--text-primary) !important;
        font-weight: 500;
        padding: 0.75rem 1.25rem !important;
        margin: 0 0.25rem;
        border-radius: var(--border-radius);
        transition: var(--transition);
        position: relative;
    }

    .nav-link::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 2px;
        background-color: var(--primary-color);
        transition: var(--transition);
    }

    .nav-link:hover::after {
        width: 80%;
    }

    .nav-link:hover {
        background-color: rgba(26, 115, 232, 0.08);
        color: var(--primary-color) !important;
    }

    .nav-link.active {
        background-color: var(--primary-color) !important;
        color: white !important;
    }

    .nav-link.active::after {
        display: none;
    }

    @media (max-width: 991.98px) {
        .navbar-collapse {
            background-color: rgba(255, 255, 255, 0.98);
            padding: 1rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-top: 1rem;
            backdrop-filter: blur(10px);
        }

        .nav-link {
            padding: 1rem !important;
            margin: 0.5rem 0;
        }

        .navbar-buttons {
            margin-top: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .navbar-buttons .btn {
            width: 100%;
        }
    }
        .card {
            border: none;
            box-shadow: var(--box-shadow);
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 1.5rem;
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
        }

        .card-body {
            padding: 2rem;
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--secondary-color);
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 0.75rem;
            color: var(--secondary-color);
        }

        .form-label {
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border-radius: var(--border-radius);
            border: 1px solid #e0e0e0;
            padding: 0.75rem;
            transition: var(--transition);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn {
            border-radius: var(--border-radius);
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .btn-primary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
            transform: translateY(-1px);
        }

        .btn-outline-primary {
            color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--secondary-color);
            color: white;
            transform: translateY(-1px);
        }

        .btn-danger {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }

        .btn-danger:hover {
            background-color: #c0392b;
            border-color: #c0392b;
        }

        .file-upload {
            position: relative;
            overflow: hidden;
            border-radius: var(--border-radius);
        }

        .file-upload input[type="file"] {
            position: absolute;
            top: 0;
            right: 0;
            min-width: 100%;
            min-height: 100%;
            font-size: 100px;
            text-align: right;
            filter: alpha(opacity=0);
            opacity: 0;
            outline: none;
            cursor: pointer;
            display: block;
        }

        .file-upload-label {
            display: block;
            padding: 1rem;
            background-color: var(--light-bg);
            border: 2px dashed #dee2e6;
            border-radius: var(--border-radius);
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
        }

        .file-upload-label:hover {
            background-color: #e9ecef;
            border-color: var(--secondary-color);
        }

        .preview-container {
            position: relative;
            display: inline-block;
            margin-top: 10px;
        }

        .preview-container img {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius);
        }

        .delete-image {
            position: absolute;
            top: -10px;
            right: -10px;
            background-color: var(--accent-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 1;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .delete-image:hover {
            background-color: #c0392b;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3rem;
            position: relative;
            padding: 0 2rem;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        .progress-steps::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        .progress-step {
            flex: 0 0 auto;
            text-align: center;
            position: relative;
            z-index: 1;
            min-width: 80px;
            padding: 0 10px;
        }

        .progress-step::before {
            content: '';
            width: 32px;
            height: 32px;
            background-color: var(--light-bg);
            border: 2px solid #dee2e6;
            border-radius: 50%;
            display: block;
            margin: 0 auto 0.5rem;
            line-height: 28px;
            color: white;
            transition: var(--transition);
        }

        .progress-step.active::before {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .progress-step span {
            font-weight: 500;
            color: var(--primary-color);
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .progress-line {
            position: absolute;
            top: 16px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: #dee2e6;
            z-index: 0;
        }

        .education-item, .work-item, .contact-item {
            margin-bottom: 1.5rem;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .education-item .card-body, .work-item .card-body, .contact-item .card-body {
            padding: 1.5rem;
        }

        .required-field::after {
            content: ' *';
            color: var(--accent-color);
        }

        @media (max-width: 768px) {
            .card-body {
                padding: 1.5rem;
            }

            .progress-steps {
                padding: 0 1rem;
                margin-bottom: 2rem;
            }

            .progress-step {
                min-width: 70px;
            }

            .progress-step::before {
                width: 28px;
                height: 28px;
                margin-bottom: 0.3rem;
            }

            .progress-step span {
                font-size: 0.8rem;
            }

            .progress-line {
                top: 14px;
            }
        }

        @media (max-width: 576px) {
            .progress-steps {
                padding: 0 0.5rem;
            }

            .progress-step {
                min-width: 60px;
            }

            .progress-step::before {
                width: 24px;
                height: 24px;
                margin-bottom: 0.2rem;
            }

            .progress-step span {
                font-size: 0.75rem;
            }

            .progress-line {
                top: 12px;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
<nav class="navbar navbar-expand-lg navbar-light fixed-top">
    <div class="container">
        <a class="navbar-brand" href="{:url('index/index/index')}">
            <i class="fas fa-briefcase"></i>{$site.name|htmlentities}
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='Index'}active{/eq}" href="{:url('index/index/index')}">
                        <i class="fas fa-home"></i>首页
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='Job'}active{/eq}" href="{:url('index/job/list')}">
                        <i class="fas fa-list me-1"></i>职位列表
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='News'}active{/eq}" href="{:url('index/resume/list')}">
                        <i class="fas fa-newspaper me-1"></i>我的简历
                    </a>
                </li>
            </ul>
            <div class="navbar-buttons">
                {if $user}
                <!-- 已登录状态 -->
                <a href="{:url('index/user/index')}" class="btn btn-outline-primary me-2">
                    <i class="fas fa-user me-1"></i>个人中心
                </a>
                <a href="javascript:;" class="btn btn-outline-danger" id="btn-logout">
                    <i class="fas fa-sign-out-alt me-1"></i>退出登录
                </a>
                <!-- 退出登录表单 -->
                <form id="logout-form" action="{:url('index/user/logout')}" method="post" style="display: none;">
                    {:token()}
                </form>
                {else}
                <!-- 未登录状态 -->
                <a href="{:url('index/user/login')}" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-1"></i>登录/注册
                </a>
                {/if}
            </div>
        </div>
    </div>
</nav>

    <!-- 主要内容 -->
    <div class="container mt-5 pt-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-paper-plane me-2"></i>添加简历
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- 进度步骤 -->
                        <div class="progress-steps">
                            <div class="progress-line"></div>
                            <div class="progress-step active">
                                <span>基本信息</span>
                            </div>
                            <div class="progress-step">
                                <span>语言能力</span>
                            </div>
                            <div class="progress-step">
                                <span>联系人信息</span>
                            </div>
                            <div class="progress-step">
                                <span>教育背景</span>
                            </div>
                            <div class="progress-step">
                                <span>工作经历</span>
                            </div>
                            <div class="progress-step">
                                <span>证件信息</span>
                            </div>
                            <div class="progress-step">
                                <span>照片上传</span>
                            </div>
                        </div>

                        <form action="" method="post" enctype="multipart/form-data">
                            <!-- 基本信息 -->
                            <div class="form-section" id="section1">
                                <h5 class="section-title">
                                    <i class="fas fa-user"></i>基本信息
                                </h5>
                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <label class="form-label required-field">姓名</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control {if $ocr_enabled != '1'}rounded-end{/if}" name="name" required>
                                            {if $ocr_enabled == '1'}
                                            <button type="button" class="btn btn-outline-primary" onclick="scanIdCard('front')" style="border-top-left-radius: 0; border-bottom-left-radius: 0;">
                                                <i class="fas fa-camera me-1"></i>扫描证件
                                            </button>
                                            {/if}
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">身份证号</label>
                                        <input type="text" class="form-control" name="id_card" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">性别</label>
                                        <select class="form-select" name="gender" required>
                                            <option value="">请选择</option>
                                            <option value="男">男</option>
                                            <option value="女">女</option>
                                        </select>
                                    </div>
                                </div>



                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <label class="form-label required-field">年龄</label>
                                        <input type="number" class="form-control" name="age" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">民族</label>
                                        <input type="text" class="form-control" name="ethnicity" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">身高(cm)</label>
                                        <input type="number" step="0.01" class="form-control" name="height" required>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <label class="form-label required-field">体重(kg)</label>
                                        <input type="number" step="0.01" class="form-control" name="weight" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">出生日期</label>
                                        <input type="date" class="form-control" name="birth_date" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">手机号码</label>
                                        <input type="tel" class="form-control" name="phone" required>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <label class="form-label required-field">婚姻状况</label>
                                        <select class="form-select" name="marital_status" required>
                                            <option value="">请选择</option>
                                            <option value="未婚">未婚</option>
                                            <option value="已婚">已婚</option>
                                            <option value="离异">离异</option>
                                        </select>
                                    </div>

                                        <div class="col-md-4">
                                            <label class="form-label required-field">意向岗位</label>
                                            <input type="text" class="form-control" name="intended_position" required>
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label required-field">应聘岗位</label>
                                            <input type="text" class="form-control" name="applied_position" required>
                                        </div>
                                </div>

                                
                                <div class="col-md-12">
                                    <label class="form-label">特殊职业资格证</label>
                                    <textarea type="text" class="form-control" name="special_certificate"></textarea>
                                </div>

                                <!-- 自我评价AI功能 -->
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <label class="form-label required-field">自我评价</label>
                                        {if condition="config('site.openai_enabled') eq '1'"}
                                        <div class="input-group mb-2">
                                            <select class="form-select" style="max-width: 150px;" id="selfEvaluationWordCount">
                                                <option value="50">50字</option>
                                                <option value="100">100字</option>
                                                <option value="150">150字</option>
                                                <option value="200">200字</option>
                                                <option value="300">300字</option>
                                            </select>
                                            <button type="button" class="btn btn-primary" onclick="generateSelfEvaluation()">
                                                <i class="fas fa-magic me-1"></i>AI润色
                                            </button>
                                        </div>
                                        {/if}
                                        <textarea class="form-control" name="self_evaluation" rows="3" required></textarea>
                                    </div>
                                </div>

                                <!-- 兴趣爱好AI功能 -->
                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <label class="form-label required-field">兴趣爱好</label>
                                        {if condition="config('site.openai_enabled') eq '1'"}
                                        <div class="input-group mb-2">
                                            <select class="form-select" style="max-width: 150px;" id="hobbiesWordCount">
                                                <option value="30">30字</option>
                                                <option value="50">50字</option>
                                                <option value="80">80字</option>
                                                <option value="100">100字</option>
                                                <option value="150">150字</option>
                                            </select>
                                            <button type="button" class="btn btn-primary" onclick="generateHobbies()">
                                                <i class="fas fa-magic me-1"></i>AI润色
                                            </button>
                                        </div>
                                        {/if}
                                        <textarea class="form-control" name="hobbies" rows="2" required></textarea>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label class="form-label required-field">户口所在地</label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <select class="form-select" name="hukou_province" id="hukouProvince" required>
                                                    <option value="">请选择省份</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <select class="form-select" name="hukou_city" id="hukouCity" required>
                                                    <option value="">请选择城市</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label required-field">常住地址</label>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <select class="form-select" name="residence_province" id="residenceProvince" required>
                                                    <option value="">请选择省份</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <select class="form-select" name="residence_city" id="residenceCity" required>
                                                    <option value="">请选择城市</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <select class="form-select" name="residence_district" id="residenceDistrict" required>
                                                    <option value="">请选择区县</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end mt-4">
                                    <button type="button" class="btn btn-primary" onclick="nextSection(1)">
                                        <i class="fas fa-arrow-right me-2"></i>下一步
                                    </button>
                                </div>
                            </div>

                            <!-- 语言能力 -->
                            <div class="form-section" id="section2" style="display: none;">
                                <h5 class="section-title">
                                    <i class="fas fa-language"></i>语言能力
                                </h5>
                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <label class="form-label required-field">粤语水平</label>
                                        <select class="form-select" name="cantonese_level" required>
                                            <option value="">请选择</option>
                                            <option value="不会">不会</option>
                                            <option value="一般">一般</option>
                                            <option value="熟练">熟练</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">国语水平</label>
                                        <select class="form-select" name="mandarin_level" required>
                                            <option value="">请选择</option>
                                            <option value="不会">不会</option>
                                            <option value="一般">一般</option>
                                            <option value="熟练">熟练</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">英语水平</label>
                                        <select class="form-select" name="english_level" required>
                                            <option value="">请选择</option>
                                            <option value="不会">不会</option>
                                            <option value="一般">一般</option>
                                            <option value="熟练">熟练</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="text-end mt-4">
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="prevSection(2)">
                                        <i class="fas fa-arrow-left me-2"></i>上一步
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="nextSection(2)">
                                        <i class="fas fa-arrow-right me-2"></i>下一步
                                    </button>
                                </div>
                            </div>

                            <!-- 联系人信息 -->
                            <div class="form-section" id="section3" style="display: none;">
                                <h5 class="section-title">
                                    <i class="fas fa-address-book"></i>联系人信息
                                </h5>
                                <div id="contactList">
                                    <div class="contact-item card">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label class="form-label required-field">联系人姓名</label>
                                                    <input type="text" class="form-control" name="contact_name[]" required>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label required-field">与本人关系</label>
                                                    <select class="form-select" name="contact_relation[]" required>
                                                        <option value="父母">父母</option>
                                                        <option value="配偶">配偶</option>
                                                        <option value="子女">子女</option>
                                                        <option value="兄弟姐妹">兄弟姐妹</option>
                                                        <option value="其他亲属">其他亲属</option>
                                                        <option value="朋友">朋友</option>
                                                    </select>
                                                </div>
                                            </div>
                                            <div class="row mt-5">
                                                <div class="col-md-6">
                                                    <label class="form-label required-field">联系人年龄</label>
                                                    <input type="number" class="form-control" name="contact_age[]" required>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label required-field">联系人工作</label>
                                                    <input type="text" class="form-control" name="contact_job[]" required>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center mb-4">
                                    <button type="button" class="btn btn-outline-primary" onclick="addContact()">
                                        <i class="fas fa-plus me-2"></i>添加联系人
                                    </button>
                                </div>
                                <div class="text-end mt-4">
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="prevSection(3)">
                                        <i class="fas fa-arrow-left me-2"></i>上一步
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="nextSection(3)">
                                        <i class="fas fa-arrow-right me-2"></i>下一步
                                    </button>
                                </div>
                            </div>

                            <!-- 教育背景 -->
                            <div class="form-section" id="section4" style="display: none;">
                                <h5 class="section-title">
                                    <i class="fas fa-graduation-cap"></i>教育背景
                                </h5>
                                <div id="educationList">
                                    <div class="education-item card">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <label class="form-label required-field">学历</label>
                                                    <select class="form-select" name="graduation_education[]" required>
                                                        <option value="">请选择</option>
                                                        <option value="小学">小学</option>
                                                        <option value="初中">初中</option>
                                                        <option value="高中">高中</option>
                                                        <option value="中专">中专</option>
                                                        <option value="大专">大专</option>
                                                        <option value="本科">本科</option>
                                                        <option value="硕士">硕士</option>
                                                        <option value="博士">博士</option>
                                                    </select>
                                                </div>
                                                <div class="col-md-4">
                                                    <label class="form-label required-field">学校名称</label>
                                                    <input type="text" class="form-control" name="education_school[]" required>
                                                </div>
                                                <div class="col-md-4">
                                                    <label class="form-label">专业</label>
                                                    <input type="text" class="form-control" name="education_major[]">
                                                </div>
                                            </div>
                                            <div class="row mt-3">
                                                <div class="col-md-6">
                                                    <label class="form-label required-field">入学时间</label>
                                                    <input type="month" class="form-control" name="education_start[]" required>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label required-field">毕业时间</label>
                                                    <input type="month" class="form-control" name="education_end[]" required>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center mb-4">
                                    <button type="button" class="btn btn-outline-primary" onclick="addEducation()">
                                        <i class="fas fa-plus me-2"></i>添加教育经历
                                    </button>
                                </div>
                                <div class="text-end mt-4">
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="prevSection(4)">
                                        <i class="fas fa-arrow-left me-2"></i>上一步
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="nextSection(4)">
                                        <i class="fas fa-arrow-right me-2"></i>下一步
                                    </button>
                                </div>
                            </div>

                            <!-- 工作经历 -->
                            <div class="form-section" id="section5" style="display: none;">
                                <h5 class="section-title">
                                    <i class="fas fa-briefcase"></i>工作经历
                                </h5>
                                <div id="workList">
                                    <div class="work-item card">
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <label class="form-label required-field">工作单位</label>
                                                    <input type="text" class="form-control" name="job_company[]" required>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label required-field">工作岗位</label>
                                                    <input type="text" class="form-control" name="job_position[]" required>
                                                </div>
                                            </div>
                                            <div class="row mt-3">
                                                <div class="col-md-6">
                                                    <label class="form-label required-field">入职时间</label>
                                                    <input type="month" class="form-control" name="job_start[]" required>
                                                </div>
                                                <div class="col-md-6">
                                                    <label class="form-label required-field">离职时间</label>
                                                    <input type="month" class="form-control" name="job_end[]" required>
                                                </div>
                                            </div>
                                            <div class="row mt-3">
                                                <div class="col-md-12">
                                                    <label class="form-label required-field">工作内容</label>
                                                    {if condition="config('site.openai_enabled') eq '1'"}
                                                    <div class="input-group mb-2">
                                                        <select class="form-select" style="max-width: 150px;" data-index="1">
                                                            <option value="20">20字</option>
                                                            <option value="30">30字</option>
                                                            <option value="50">50字</option>
                                                            <option value="80">80字</option>
                                                            <option value="100">100字</option>
                                                            <option value="120">120字</option>
                                                            <option value="150">150字</option>
                                                        </select>
                                                        <button type="button" class="btn btn-primary" onclick="generateJobDescription(1)">
                                                            <i class="fas fa-magic me-1"></i>AI生成
                                                        </button>
                                                    </div>
                                                    {/if}
                                                    <textarea class="form-control" name="job_description[]" rows="3" required></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-center mb-4">
                                    <button type="button" class="btn btn-outline-primary" onclick="addWork()">
                                        <i class="fas fa-plus me-2"></i>添加工作经历
                                    </button>
                                </div>
                                <div class="text-end mt-4">
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="prevSection(5)">
                                        <i class="fas fa-arrow-left me-2"></i>上一步
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="nextSection(5)">
                                        <i class="fas fa-arrow-right me-2"></i>下一步
                                    </button>
                                </div>
                            </div>

                            <!-- 证件信息 -->
                            <div class="form-section" id="section6" style="display: none;">
                                <h5 class="section-title">
                                    <i class="fas fa-id-card"></i>证件信息
                                </h5>
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label class="form-label">港澳通行证编号</label>
                                        <input type="text" class="form-control" name="hk_macau_passport">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">港澳通行证到期时间</label>
                                        <input type="date" class="form-control" name="hk_macau_passport_expiry">
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label class="form-label required-field">身份证正面</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload me-2"></i>点击上传
                                            </label>
                                            <input type="file" name="id_card_front" accept="image/*" required onchange="previewIdCard(this, 'front')">
                                        </div>
                                        <div id="idCardFrontPreview" class="preview-container" style="display: none;">
                                            <img src="" alt="身份证正面预览">
                                            <button type="button" class="delete-image" onclick="deleteImage('idCardFrontPreview', 'id_card_front')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label required-field">身份证反面</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload me-2"></i>点击上传
                                            </label>
                                            <input type="file" name="id_card_back" accept="image/*" required onchange="previewIdCard(this, 'back')">
                                        </div>
                                        <div id="idCardBackPreview" class="preview-container" style="display: none;">
                                            <img src="" alt="身份证反面预览">
                                            <button type="button" class="delete-image" onclick="deleteImage('idCardBackPreview', 'id_card_back')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label class="form-label">港澳通行证正面</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload me-2"></i>点击上传
                                            </label>
                                            <input type="file" name="hk_macau_passport_front" accept="image/*" onchange="previewImage(this, 'hkMacauPassportFrontPreview')">
                                        </div>
                                        <div id="hkMacauPassportFrontPreview" class="preview-container" style="display: none;">
                                            <img src="" alt="港澳通行证正面预览">
                                            <button type="button" class="delete-image" onclick="deleteImage('hkMacauPassportFrontPreview', 'hk_macau_passport_front')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">港澳通行证反面</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload me-2"></i>点击上传
                                            </label>
                                            <input type="file" name="hk_macau_passport_back" accept="image/*" onchange="previewImage(this, 'hkMacauPassportBackPreview')">
                                        </div>
                                        <div id="hkMacauPassportBackPreview" class="preview-container" style="display: none;">
                                            <img src="" alt="港澳通行证反面预览">
                                            <button type="button" class="delete-image" onclick="deleteImage('hkMacauPassportBackPreview', 'hk_macau_passport_back')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end mt-4">
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="prevSection(6)">
                                        <i class="fas fa-arrow-left me-2"></i>上一步
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="nextSection(6)">
                                        <i class="fas fa-arrow-right me-2"></i>下一步
                                    </button>
                                </div>
                            </div>

                            <!-- 照片上传 -->
                            <div class="form-section" id="section7" style="display: none;">
                                <h5 class="section-title">
                                    <i class="fas fa-camera"></i>照片上传
                                </h5>
                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <label class="form-label required-field">头像</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload me-2"></i>点击上传
                                            </label>
                                            <input type="file" name="avatar" accept="image/*" required onchange="previewImage(this, 'avatarPreview')">
                                        </div>
                                        <div id="avatarPreview" class="preview-container" style="display: none;">
                                            <img src="" alt="头像预览">
                                            <button type="button" class="delete-image" onclick="deleteImage('avatarPreview', 'avatar')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">全身照</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload me-2"></i>点击上传
                                            </label>
                                            <input type="file" name="full_body_photo" accept="image/*" required onchange="previewImage(this, 'fullBodyPhotoPreview')">
                                        </div>
                                        <div id="fullBodyPhotoPreview" class="preview-container" style="display: none;">
                                            <img src="" alt="全身照预览">
                                            <button type="button" class="delete-image" onclick="deleteImage('fullBodyPhotoPreview', 'full_body_photo')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">补充照片</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload me-2"></i>点击上传
                                            </label>
                                            <input type="file" name="additional_photos[]" accept="image/*" multiple onchange="previewAdditionalPhotos(this)">
                                        </div>
                                        <div id="additionalPhotosPreview" class="preview-container" style="display: none;">
                                            <img src="" alt="补充照片预览">
                                            <button type="button" class="delete-image" onclick="deleteImage('additionalPhotosPreview', 'additional_photos')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end mt-4">
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="prevSection(7)">
                                        <i class="fas fa-arrow-left me-2"></i>上一步
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-2"></i>提交申请
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 将JavaScript文件放在body结束标签前 -->
    <script src="https://cdn.staticfile.org/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.staticfile.org/sweetalert2/11.7.32/sweetalert2.all.min.js"></script>
    <script>
        // 地区数据
        let areaData = null;

        // 获取地区数据
        async function getAreaData() {
            try {
                const response = await fetch('{:url("index/area/getAreaData")}');
                areaData = await response.json();
                initAreaSelectors();
            } catch (error) {
                console.error('获取地区数据失败:', error);
            }
        }

        // 初始化地区选择器
        function initAreaSelectors() {
            // 初始化户口所在地选择器
            initHukouSelector();
            // 初始化常住地址选择器
            initResidenceSelector();
        }

        // 初始化户口所在地选择器
        function initHukouSelector() {
            const provinceSelect = document.getElementById('hukouProvince');
            const citySelect = document.getElementById('hukouCity');

            // 加载省份数据
            const provinces = areaData.filter(item => item.level === 1);
            provinces.forEach(province => {
                const option = new Option(province.name, province.id);
                provinceSelect.add(option);
            });

            // 省份选择事件
            provinceSelect.addEventListener('change', function() {
                const provinceId = this.value;
                citySelect.innerHTML = '<option value="">请选择城市</option>';
                
                if (provinceId) {
                    const cities = areaData.filter(item => item.pid === parseInt(provinceId));
                    cities.forEach(city => {
                        const option = new Option(city.name, city.id);
                        citySelect.add(option);
                    });
                }
            });
        }

        // 初始化常住地址选择器
        function initResidenceSelector() {
            const provinceSelect = document.getElementById('residenceProvince');
            const citySelect = document.getElementById('residenceCity');
            const districtSelect = document.getElementById('residenceDistrict');

            // 加载省份数据
            const provinces = areaData.filter(item => item.level === 1);
            provinces.forEach(province => {
                const option = new Option(province.name, province.id);
                provinceSelect.add(option);
            });

            // 省份选择事件
            provinceSelect.addEventListener('change', function() {
                const provinceId = this.value;
                citySelect.innerHTML = '<option value="">请选择城市</option>';
                districtSelect.innerHTML = '<option value="">请选择区县</option>';
                
                if (provinceId) {
                    const cities = areaData.filter(item => item.pid === parseInt(provinceId));
                    cities.forEach(city => {
                        const option = new Option(city.name, city.id);
                        citySelect.add(option);
                    });
                }
            });

            // 城市选择事件
            citySelect.addEventListener('change', function() {
                const cityId = this.value;
                districtSelect.innerHTML = '<option value="">请选择区县</option>';
                
                if (cityId) {
                    const districts = areaData.filter(item => item.pid === parseInt(cityId));
                    districts.forEach(district => {
                        const option = new Option(district.name, district.id);
                        districtSelect.add(option);
                    });
                }
            });
        }

        // 页面加载完成后获取地区数据
        document.addEventListener('DOMContentLoaded', getAreaData);

        // 身份证号自动识别
        document.querySelector('input[name="id_card"]').addEventListener('input', function(e) {
            const idCard = e.target.value;
            if (idCard.length === 18) {
                // 获取性别
                const gender = parseInt(idCard.substr(16, 1)) % 2 === 0 ? '女' : '男';
                document.querySelector('select[name="gender"]').value = gender;

                // 获取出生日期
                const year = idCard.substr(6, 4);
                const month = idCard.substr(10, 2);
                const day = idCard.substr(12, 2);
                const birthDate = `${year}-${month}-${day}`;
                document.querySelector('input[name="birth_date"]').value = birthDate;

                // 计算年龄
                const today = new Date();
                const birth = new Date(birthDate);
                let age = today.getFullYear() - birth.getFullYear();
                const monthDiff = today.getMonth() - birth.getMonth();
                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                    age--;
                }
                document.querySelector('input[name="age"]').value = age;
            }
        });

        // 文件上传预览
        document.querySelectorAll('input[type="file"]').forEach(input => {
            input.addEventListener('change', function(e) {
                const label = this.previousElementSibling;
                if (this.files && this.files[0]) {
                    label.innerHTML = `<i class="fas fa-check me-2"></i>${this.files[0].name}`;
                } else {
                    label.innerHTML = `<i class="fas fa-upload me-2"></i>点击上传`;
                }
            });
        });

        // 联系人相关
        let contactCount = 1;

        function addContact() {
            const template = `
                <div class="contact-item card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">联系人 #${contactCount + 1}</h6>
                            <button type="button" class="btn btn-danger btn-sm" onclick="removeContact(this)">
                                <i class="fas fa-times me-1"></i>删除
                            </button>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label required-field">联系人姓名</label>
                                <input type="text" class="form-control" name="contact_name[]" required>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label required-field">与本人关系</label>
                                <select class="form-select" name="contact_relation[]" required>
                                    <option value="父母">父母</option>
                                    <option value="配偶">配偶</option>
                                    <option value="子女">子女</option>
                                    <option value="兄弟姐妹">兄弟姐妹</option>
                                    <option value="其他亲属">其他亲属</option>
                                    <option value="朋友">朋友</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <label class="form-label">联系人年龄</label>
                                <input type="number" class="form-control" name="contact_age[]">
                            </div>
                            <div class="col-md-8">
                                <label class="form-label">联系人工作</label>
                                <input type="text" class="form-control" name="contact_job[]">
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('contactList').insertAdjacentHTML('beforeend', template);
            contactCount++;
        }

        function removeContact(button) {
            const contactItem = button.closest('.contact-item');
            contactItem.remove();
        }

        // 教育经历相关
        let educationCount = 1;

        function addEducation() {
            const template = `
                <div class="education-item card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">教育经历 #${educationCount + 1}</h6>
                            <button type="button" class="btn btn-danger btn-sm" onclick="removeEducation(this)">
                                <i class="fas fa-times me-1"></i>删除
                            </button>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">学历</label>
                                <select class="form-select" name="graduation_education[]">
                                    <option value="">请选择</option>
                                    <option value="小学">小学</option>
                                    <option value="初中">初中</option>
                                    <option value="高中">高中</option>
                                    <option value="中专">中专</option>
                                    <option value="大专">大专</option>
                                    <option value="本科">本科</option>
                                    <option value="硕士">硕士</option>
                                    <option value="博士">博士</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">学校名称</label>
                                <input type="text" class="form-control" name="education_school[]">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">专业</label>
                                <input type="text" class="form-control" name="education_major[]">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">入学时间</label>
                                <input type="month" class="form-control" name="education_start[]">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">毕业时间</label>
                                <input type="month" class="form-control" name="education_end[]">
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('educationList').insertAdjacentHTML('beforeend', template);
            educationCount++;
        }

        function removeEducation(button) {
            const educationItem = button.closest('.education-item');
            educationItem.remove();
        }

        // 工作经历相关
        let workCount = 1;

        function addWork() {
            const template = `
                <div class="work-item card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">工作经历 #${workCount + 1}</h6>
                            <button type="button" class="btn btn-danger btn-sm" onclick="removeWork(this)">
                                <i class="fas fa-times me-1"></i>删除
                            </button>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">工作单位</label>
                                <input type="text" class="form-control" name="job_company[]">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">工作岗位</label>
                                <input type="text" class="form-control" name="job_position[]">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">入职时间</label>
                                <input type="month" class="form-control" name="job_start[]">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">离职时间</label>
                                <input type="month" class="form-control" name="job_end[]">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <label class="form-label">工作内容</label>
                                {if condition="config('site.openai_enabled') eq '1'"}
                                <div class="input-group mb-2">
                                    <select class="form-select" style="max-width: 150px;" data-index="${workCount + 1}">
                                        <option value="20">20字</option>
                                        <option value="30">30字</option>
                                        <option value="50">50字</option>
                                        <option value="80">80字</option>
                                        <option value="100">100字</option>
                                        <option value="120">120字</option>
                                        <option value="150">150字</option>
                                    </select>
                                    <button type="button" class="btn btn-primary" onclick="generateJobDescription(${workCount + 1})">
                                        <i class="fas fa-magic me-1"></i>AI生成
                                    </button>
                                </div>
                                {/if}
                                <textarea class="form-control" name="job_description[]" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('workList').insertAdjacentHTML('beforeend', template);
            workCount++;
        }

        function removeWork(button) {
            const workItem = button.closest('.work-item');
            workItem.remove();
        }

        // 分步表单控制
        function nextSection(currentSection) {
            // 验证当前部分
            const currentSectionElement = document.getElementById('section' + currentSection);
            const requiredFields = currentSectionElement.querySelectorAll('[required]');
            let isValid = true;

            requiredFields.forEach(field => {
                if (!field.value) {
                    isValid = false;
                    field.classList.add('is-invalid');
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                alert('请填写所有必填项');
                return;
            }

            // 隐藏当前部分
            currentSectionElement.style.display = 'none';
            // 显示下一部分
            document.getElementById('section' + (currentSection + 1)).style.display = 'block';
            // 更新进度条
            updateProgress(currentSection + 1);
        }

        function prevSection(currentSection) {
            // 隐藏当前部分
            document.getElementById('section' + currentSection).style.display = 'none';
            // 显示上一部分
            document.getElementById('section' + (currentSection - 1)).style.display = 'block';
            // 更新进度条
            updateProgress(currentSection - 1);
        }

        function updateProgress(currentSection) {
            const steps = document.querySelectorAll('.progress-step');
            steps.forEach((step, index) => {
                if (index < currentSection) {
                    step.classList.add('active');
                } else {
                    step.classList.remove('active');
                }
            });
        }

        // 检查登录状态
        function checkLoginStatus() {
            $.ajax({
                url: '{:url("index/user/check")}',
                type: 'GET',
                success: function(res) {
                    if (!res.code) {
                        Swal.fire({
                            title: '提示',
                            text: '当前未注册/登录账号，是否继续填写？未注册/登录账号，后续将无法管理填写的简历。',
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonText: '继续填写',
                            cancelButtonText: '去登录',
                            confirmButtonColor: '#1a73e8',
                            cancelButtonColor: '#5f6368'
                        }).then((result) => {
                            if (result.isDismissed) {
                                // 如果用户点击"去登录"，跳转到登录页面
                                window.location.href = '{:url("index/user/login")}';
                            }
                        });
                    }
                }
            });
        }

        // 页面加载完成后检查登录状态
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
            document.getElementById('section1').style.display = 'block';
            updateProgress(1);
        });

        // 预览图片
        function previewImage(input, previewId) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                const preview = document.getElementById(previewId);
                
                reader.onload = function(e) {
                    const img = preview.querySelector('img');
                    img.src = e.target.result;
                    preview.style.display = 'block';
                };
                
                reader.readAsDataURL(input.files[0]);
            }
        }

        // 删除图片
        function deleteImage(previewId, inputName) {
            const preview = document.getElementById(previewId);
            const input = document.querySelector(`input[name="${inputName}"]`);
            const oldInput = document.querySelector(`input[name="old_${inputName}"]`);
            const label = input.previousElementSibling;
            
            // 清除预览
            preview.style.display = 'none';
            preview.querySelector('img').src = '';
            
            // 清除文件输入
            input.value = '';
            
            // 清除旧图片路径
            if (oldInput) {
                oldInput.value = '';
            }
            
            // 重置上传按钮文本
            label.innerHTML = `<i class="fas fa-upload me-2"></i>点击上传`;
            
            // 如果是必填字段，添加验证
            if (input.hasAttribute('required')) {
                input.closest('.file-upload').classList.add('is-invalid');
            }
        }

        // 预览身份证图片
        function previewIdCard(input, type) {
            if (input.files && input.files[0]) {
                const file = input.files[0];
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    const previewId = type === 'front' ? 'idCardFrontPreview' : 'idCardBackPreview';
                    const preview = document.getElementById(previewId);
                    if (preview) {
                        const img = preview.querySelector('img');
                        if (img) {
                            img.src = e.target.result;
                            preview.style.display = 'block';
                        }
                    }
                };
                
                reader.readAsDataURL(file);
            }
        }

        // 预览补充照片
        function previewAdditionalPhotos(input) {
            if (input.files && input.files.length > 0) {
                const preview = document.getElementById('additionalPhotosPreview');
                const img = preview.querySelector('img');
                
                // 只显示第一张图片的预览
                const reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // 引入AI相关JS方法
        async function generateJobDescription(index) {
            const workItems = document.querySelectorAll('.work-item');
            const workItem = workItems[index - 1];
            if (!workItem) {
                Swal.fire({ title: '提示', text: '未找到对应的工作经历', icon: 'error', confirmButtonText: '确定' });
                return;
            }
            const companyInput = workItem.querySelector('input[name="job_company[]"]');
            const positionInput = workItem.querySelector('input[name="job_position[]"]');
            const wordCountSelect = workItem.querySelector('select[data-index="' + index + '"]');
            if (!companyInput || !positionInput || !wordCountSelect) {
                Swal.fire({ title: '提示', text: '表单元素未找到', icon: 'error', confirmButtonText: '确定' });
                return;
            }
            const company = companyInput.value;
            const position = positionInput.value;
            const wordCount = wordCountSelect.value;
            if (!company || !position) {
                Swal.fire({ title: '提示', text: '请先填写工作单位和岗位', icon: 'warning', confirmButtonText: '确定' });
                return;
            }
            try {
                Swal.fire({ title: '正在生成...', text: '请稍候', allowOutsideClick: false, didOpen: () => { Swal.showLoading(); } });
                const response = await fetch('{:url("index/resume/generateJobDescription")}', {
                    method: 'POST', headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ company: company, position: position, word_count: wordCount })
                });
                const result = await response.json();
                if (result.code === 1) {
                    const textarea = workItem.querySelector('textarea[name="job_description[]"]');
                    if (textarea) {
                        textarea.value = result.data;
                        Swal.fire({ title: '生成成功', icon: 'success', timer: 1500 });
                    } else {
                        throw new Error('未找到工作内容文本框');
                    }
                } else {
                    throw new Error(result.msg || '生成失败');
                }
            } catch (error) {
                Swal.fire({ title: '生成失败', text: error.message || '请稍后重试', icon: 'error' });
            }
        }
        async function generateSelfEvaluation() {
            const textarea = document.querySelector('textarea[name="self_evaluation"]');
            const wordCount = document.getElementById('selfEvaluationWordCount').value;
            if (!textarea.value) {
                Swal.fire({ title: '提示', text: '请先输入自我评价内容', icon: 'warning', confirmButtonText: '确定' });
                return;
            }
            try {
                Swal.fire({ title: '正在润色...', text: '请稍候', allowOutsideClick: false, didOpen: () => { Swal.showLoading(); } });
                const response = await fetch('{:url("index/resume/generateSelfEvaluation")}', {
                    method: 'POST', headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ content: textarea.value, word_count: wordCount })
                });
                const result = await response.json();
                if (result.code === 1) {
                    textarea.value = result.data;
                    Swal.fire({ title: '润色成功', icon: 'success', timer: 1500 });
                } else {
                    throw new Error(result.msg || '润色失败');
                }
            } catch (error) {
                Swal.fire({ title: '润色失败', text: error.message || '请稍后重试', icon: 'error' });
            }
        }
        async function generateHobbies() {
            const textarea = document.querySelector('textarea[name="hobbies"]');
            const wordCount = document.getElementById('hobbiesWordCount').value;
            if (!textarea.value) {
                Swal.fire({ title: '提示', text: '请先输入兴趣爱好内容', icon: 'warning', confirmButtonText: '确定' });
                return;
            }
            try {
                Swal.fire({ title: '正在润色...', text: '请稍候', allowOutsideClick: false, didOpen: () => { Swal.showLoading(); } });
                const response = await fetch('{:url("index/resume/generateHobbies")}', {
                    method: 'POST', headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ content: textarea.value, word_count: wordCount })
                });
                const result = await response.json();
                if (result.code === 1) {
                    textarea.value = result.data;
                    Swal.fire({ title: '润色成功', icon: 'success', timer: 1500 });
                } else {
                    throw new Error(result.msg || '润色失败');
                }
            } catch (error) {
                Swal.fire({ title: '润色失败', text: error.message || '请稍后重试', icon: 'error' });
            }
        }

        // 扫描身份证
        function scanIdCard(type) {
            // 创建文件输入元素
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.capture = 'environment'; // 使用后置摄像头
            
            input.onchange = async function(e) {
                if (e.target.files && e.target.files[0]) {
                    const file = e.target.files[0];
                    
                    // 显示预览
                    previewIdCard(e.target, type);
                    
                    // 创建 FormData
                    const formData = new FormData();
                    formData.append('id_card', file);
                    formData.append('type', type);
                    
                    try {
                        // 显示加载提示
                        Swal.fire({
                            title: '正在识别...',
                            text: '请稍候',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });
                        
                        // 发送到后端进行识别
                        const response = await fetch('{:url("index/resume/scanIdCard")}', {
                            method: 'POST',
                            body: formData
                        });
                        
                        if (!response.ok) {
                            throw new Error('网络请求失败');
                        }
                        
                        const contentType = response.headers.get('content-type');
                        if (!contentType || !contentType.includes('application/json')) {
                            throw new Error('服务器返回格式错误');
                        }
                        
                        const result = await response.json();
                
                        if (result.code === 1) {
                            // 识别成功，填充表单
                            const data = result.data;
                            if (type === 'front') {
                                document.querySelector('input[name="name"]').value = data.name || '';
                                document.querySelector('select[name="gender"]').value = data.gender || '';
                                document.querySelector('input[name="age"]').value = data.age || '';
                                document.querySelector('input[name="id_card"]').value = data.id_card || '';
                                
                                // 处理出生日期
                                if (data.id_card && data.id_card.length === 18) {
                                    const year = data.id_card.substr(6, 4);
                                    const month = data.id_card.substr(10, 2);
                                    const day = data.id_card.substr(12, 2);
                                    const birthDate = `${year}-${month}-${day}`;
                                    document.querySelector('input[name="birth_date"]').value = birthDate;
                                }

                                // 自动填充身份证照片
                                const idCardFrontInput = document.querySelector('input[name="id_card_front"]');
                                if (idCardFrontInput) {
                                    // 直接触发文件选择
                                    const fileInput = document.createElement('input');
                                    fileInput.type = 'file';
                                    fileInput.accept = 'image/*';
                                    fileInput.style.display = 'none';
                                    document.body.appendChild(fileInput);
                                    
                                    // 创建一个新的 FileList 对象
                                    const dataTransfer = new ClipboardEvent('').clipboardData || new DataTransfer();
                                    dataTransfer.items.add(file);
                                    fileInput.files = dataTransfer.files;
                                    
                                    // 触发文件选择
                                    idCardFrontInput.files = fileInput.files;
                                    
                                    // 更新预览
                                    const preview = document.getElementById('idCardFrontPreview');
                                    const previewImg = preview.querySelector('img');
                                    const reader = new FileReader();
                                    reader.onload = function(e) {
                                        previewImg.src = e.target.result;
                                        preview.style.display = 'block';
                                    };
                                    reader.readAsDataURL(file);
                                    
                                    // 更新上传按钮文本
                                    const label = idCardFrontInput.previousElementSibling;
                                    if (label) {
                                        label.innerHTML = `<i class="fas fa-check me-2"></i>${file.name}`;
                                    }
                                    
                                    // 清理临时元素
                                    document.body.removeChild(fileInput);
                                }
                            }
                            
                            Swal.fire({
                                title: '识别成功',
                                icon: 'success',
                                timer: 1500
                            });
                        } else {
                            throw new Error(result.msg || '识别失败');
                        }
                    } catch (error) {
                        console.error('身份证识别错误:', error);
                        Swal.fire({
                            title: '识别失败',
                            text: error.message || '请稍后重试',
                            icon: 'error'
                        });
                    }
                }
            };
            
            input.click();
        }
    </script>
    <script>
        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
        
        // 退出登录按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const logoutBtn = document.getElementById('btn-logout');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function() {
                    document.getElementById('logout-form').submit();
                });
            }
        });
    </script> 
</body>
</html> 