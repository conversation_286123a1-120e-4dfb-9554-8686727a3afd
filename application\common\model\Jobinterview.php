<?php

namespace app\common\model;

use think\Model;


class Jobinterview extends Model
{

    

    

    // 表名
    protected $name = 'job_interview';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'interview_type_text',
        'progress_text',
        'status_text',
        'interview_time_text',
        'create_time_text',
        'update_time_text'
    ];
    

    
    public function getInterviewTypeList()
    {
        return ['线上面试' => __('线上面试'), '现场面试' => __('现场面试'), '入港面试' => __('入港面试')];
    }

    /**
     * 获取进度列表
     */
    public function getProgressList()
    {
        return [
            '未面试' => __('未面试'),
            '已面试' => __('已面试'),
            '未通过' => __('未通过'),
            '已通过' => __('已通过'),
            '已被录取' => __('已被录取')
        ];
    }

    public function getStatusList()
    {
        return ['未开始' => __('未开始'), '进行中' => __('进行中'), '已结束' => __('已结束'), '已取消' => __('已取消')];
    }


    public function getInterviewTypeTextAttr($value, $data)
    {
        $value = $value ?: ($data['interview_type'] ?? '');
        $list = $this->getInterviewTypeList();
        return $list[$value] ?? '';
    }


    public function getProgressTextAttr($value, $data)
    {
        $value = $value ?: ($data['progress'] ?? '');
        $list = $this->getProgressList();
        return $list[$value] ?? '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }


    public function getCreateTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['create_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }


    public function getUpdateTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['update_time'] ?? '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }

    protected function setCreateTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setUpdateTimeAttr($value)
    {
        return $value === '' ? null : ($value && !is_numeric($value) ? strtotime($value) : $value);
    }

    protected function setInterviewTimeAttr($value)
    {
        if ($value === '' || $value === null) {
            return null;
        }
        
        // 如果是时间戳格式，转换为日期时间格式
        if (is_numeric($value)) {
            return date('Y-m-d H:i:s', $value);
        }
        
        // 尝试解析为日期时间格式
        try {
            $date = new \DateTime($value);
            return $date->format('Y-m-d H:i:s');
        } catch (\Exception $e) {
            // 如果解析失败，返回原值
            return $value;
        }
    }

    public function getInterviewTimeTextAttr($value, $data)
    {
        $value = $value ?: ($data['interview_time'] ?? '');
        return $value;
    }

}
