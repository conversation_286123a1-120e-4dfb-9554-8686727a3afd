<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use think\Db;
use think\Exception;

class Job extends Frontend
{
    protected $noNeedLogin = ['*'];
    protected $noNeedRight = ['*'];

    public function _initialize()
    {
        parent::_initialize();
    }

    /**
     * 职位首页
     */
    public function index()
    {
        $this->redirect('index/job/list');
    }

    /**
     * 职位列表
     */
    public function list()
    {
        $where = [];
        $where['status'] = '上架';

        // 搜索条件
        $keyword = $this->request->get('keyword', '');
        if ($keyword) {
            $where['title'] = ['like', "%{$keyword}%"];
        }

        // 分类筛选
        $category = $this->request->get('category', '');
        if ($category) {
            $where['category'] = $category;
        }

        // 学历筛选
        $education = $this->request->get('education', '');
        if ($education) {
            $where['education_requirement'] = $education;
        }

        // 年龄筛选
        $age = $this->request->get('age', '');
        if ($age) {
            $where['age_requirement'] = $age;
        }

        // 获取所有职位分类
        $categories = Db::name('jobs')->where('status', '上架')->column('DISTINCT category');

        // 获取职位列表
        $jobs = Db::name('jobs')
            ->where($where)
            ->order('weight', 'desc')
            ->order('id', 'desc')
            ->paginate(10);

        $this->assign([
            'jobs' => $jobs->items(),
            'page' => $jobs->render(),
            'categories' => $categories,
            'keyword' => $keyword,
            'category' => $category,
            'education' => $education,
            'age' => $age
        ]);

        return $this->view->fetch();
    }

    /**
     * 职位详情
     */
    public function detail($id = null)
    {
        if (!$id) {
            $this->error('参数错误');
        }

        $job = Db::name('jobs')->where('id', $id)->find();
        if (!$job) {
            $this->error('职位不存在');
        }

        // 更新浏览次数
        Db::name('jobs')->where('id', $id)->setInc('resume_count');

        // 获取相似职位推荐
        $similarJobs = Db::name('jobs')
            ->where('id', '<>', $id)
            ->where('status', '上架')
            ->where(function ($query) use ($job) {
                $query->where('category', $job['category'])
                    ->whereOr('company', $job['company'])
                    ->whereOr('education_requirement', $job['education_requirement'])
                    ->whereOr('experience_requirement', $job['experience_requirement']);
            })
            ->order('create_time', 'desc')
            ->limit(5)
            ->select();

        $this->assign([
            'job' => $job,
            'similarJobs' => $similarJobs
        ]);

        return $this->view->fetch();
    }

    /**
     * 职位搜索
     */
    public function search()
    {
        $where = [];
        $where['status'] = '上架';

        // 搜索条件
        $keyword = $this->request->get('keyword', '');
        if ($keyword) {
            $where['title'] = ['like', "%{$keyword}%"];
        }

        // 分类筛选
        $category = $this->request->get('category', '');
        if ($category) {
            $where['category'] = $category;
        }

        // 学历筛选
        $education = $this->request->get('education', '');
        if ($education) {
            $where['education_requirement'] = $education;
        }

        // 年龄筛选
        $age = $this->request->get('age', '');
        if ($age) {
            $where['age_requirement'] = $age;
        }

        // 获取所有职位分类
        $categories = Db::name('jobs')->where('status', '上架')->column('DISTINCT category');

        // 获取职位列表
        $jobs = Db::name('jobs')
            ->where($where)
            ->order('weight', 'desc')
            ->order('id', 'desc')
            ->paginate(10);

        $this->assign([
            'jobs' => $jobs->items(),
            'page' => $jobs->render(),
            'categories' => $categories,
            'keyword' => $keyword,
            'category' => $category,
            'education' => $education,
            'age' => $age,
            'isSearch' => true  // 标记为搜索结果页
        ]);

        return $this->view->fetch('list');  // 直接使用list模板
    }
} 