<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>批量更新投递状态</em></div>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <form id="batch-update-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
                        <input type="hidden" name="ids" id="batch-ids" value="">
                        
                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-2">{:__('批量更新状态')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <select id="c-status" data-rule="required" class="form-control selectpicker" name="status">
                                    <option value="">{:__('请选择状态')}</option>
                                    {foreach name="statusList" item="vo" key="key"}
                                    <option value="{$key}">{$vo}</option>
                                    {/foreach}
                                </select>
                                <span class="help-block text-muted">选中的投递记录将被批量更新为以上状态</span>
                            </div>
                        </div>
                        
                        <div class="form-group layer-footer">
                            <label class="control-label col-xs-12 col-sm-2"></label>
                            <div class="col-xs-12 col-sm-8">
                                <button type="submit" class="btn btn-success btn-embossed">{:__('确认更新')}</button>
                                <button type="button" class="btn btn-default btn-embossed" onclick="Layer.closeAll();">{:__('取消')}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div> 