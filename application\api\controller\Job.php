<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;

/**
 * 职位接口
 */
class Job extends Api
{
    protected $noNeedLogin = ['hot', 'latest', 'list', 'detail', 'similar', 'categories', 'search'];
    protected $noNeedRight = '*';

    /**
     * 获取热门职位
     */
    public function hot()
    {
        $jobs = Db::name('jobs')
            ->where('status', '上架')
            ->order('weight', 'desc')
            ->order('resume_count', 'desc')
            ->limit(6)
            ->select();

        $this->success('获取成功', $jobs);
    }

    /**
     * 获取最新职位
     */
    public function latest()
    {
        $jobs = Db::name('jobs')
            ->where('status', '上架')
            ->order('create_time', 'desc')
            ->limit(6)
            ->select();

        $this->success('获取成功', $jobs);
    }

    /**
     * 获取职位列表
     */
    public function list()
    {
        $params = $this->request->get();
        
        $where = [];
        $where['status'] = '上架';

        // 搜索条件
        if (!empty($params['keyword'])) {
            $where['title'] = ['like', "%{$params['keyword']}%"];
        }

        // 分类筛选
        if (!empty($params['category'])) {
            $where['category'] = $params['category'];
        }

        // 学历筛选
        if (!empty($params['education'])) {
            $where['education_requirement'] = $params['education'];
        }

        // 年龄筛选
        if (!empty($params['age'])) {
            $where['age_requirement'] = $params['age'];
        }

        // 分页参数
        $page = !empty($params['page']) ? intval($params['page']) : 1;
        $limit = !empty($params['limit']) ? intval($params['limit']) : 10;

        // 获取职位列表
        $jobs = Db::name('jobs')
            ->where($where)
            ->order('weight', 'desc')
            ->order('id', 'desc')
            ->page($page, $limit)
            ->select();

        $this->success('获取成功', $jobs);
    }

    /**
     * 获取职位详情
     * 
     * @param int $id 职位ID
     */
    public function detail($id = null)
    {
        if (!$id) {
            $this->error('参数错误');
        }

        // 获取所有字段数据
        $job = Db::name('jobs')
            ->field('id, title, image, job_code, job_name, category, salary_range, 
                    age_requirement, gender_requirement, language_requirement, education_requirement, 
                    experience_requirement, working_hours, accommodation, job_description, 
                    total_fee, remark, job_summary, resume_count, weight, 
                    create_time, update_time, status')
            ->where('id', $id)
            ->find();
            
        if (!$job) {
            $this->error('职位不存在');
        }

        // 处理图片路径
        if ($job['image']) {
            $job['image'] = cdnurl($job['image'], true);
        }

        // 格式化时间
        $job['create_time_text'] = date('Y-m-d H:i:s', $job['create_time']);
        $job['update_time_text'] = date('Y-m-d H:i:s', $job['update_time']);


        $this->success('获取成功', $job);
    }
    
    /**
     * 获取相似职位
     * 
     * @param int $id 职位ID
     */
    public function similar($id = null)
    {
        if (!$id) {
            $this->error('参数错误');
        }

        $job = Db::name('jobs')->where('id', $id)->find();
        if (!$job) {
            $this->error('职位不存在');
        }

        // 获取相似职位推荐
        $similarJobs = Db::name('jobs')
            ->where('id', '<>', $id)
            ->where('status', '上架')
            ->where(function ($query) use ($job) {
                $query->where('category', $job['category'])
                    ->whereOr('education_requirement', $job['education_requirement'])
                    ->whereOr('experience_requirement', $job['experience_requirement']);
            })
            ->order('create_time', 'desc')
            ->limit(5)
            ->select();

        $this->success('获取成功', $similarJobs);
    }

    /**
     * 获取职位分类
     */
    public function categories()
    {
        // 查询分类及其关联的岗位数量
        $categories = \app\common\model\Jobcategory::alias('c')
            ->join('jobs j', 'j.category = c.name AND j.status = "上架"', 'LEFT')
            ->where('c.status', '展示')
            ->field('c.id, c.name, c.image, COUNT(j.id) as job_count')
            ->group('c.id')
            ->order('job_count DESC, c.id DESC')
            ->select();
            
        // 处理图片路径为完整URL
        foreach ($categories as &$category) {
            $category['image'] = $category['image'] ? cdnurl($category['image'], true) : '/assets/img/blank.gif';
        }

        $this->success('获取成功', $categories);
    }
    
    /**
     * 搜索职位
     */
    public function search()
    {
        $params = $this->request->get();
        
        // 构建查询条件
        $where = [];
        $where['status'] = '上架';

        // 关键词搜索（支持职位名称、职位分类、公司名称和地点）
        if (!empty($params['keyword'])) {
            $keyword = $params['keyword'];
            $where['title|category|job_name|job_code|salary_range|age_requirement|gender_requirement|language_requirement|education_requirement|experience_requirement|working_hours|accommodation|job_description|remark'] = ['like', "%{$keyword}%"];
        }
        
        // 分类筛选
        if (!empty($params['category'])) {
            $where['category'] = $params['category'];
        }

        // 学历筛选
        if (!empty($params['education'])) {
            $where['education_requirement'] = $params['education'];
        }

        // 年龄筛选
        if (!empty($params['age'])) {
            $where['age_requirement'] = $params['age'];
        }

        // 经验筛选
        if (!empty($params['experience'])) {
            $where['experience_requirement'] = $params['experience'];
        }

        // 分页参数
        $page = !empty($params['page']) ? intval($params['page']) : 1;
        $size = !empty($params['size']) ? intval($params['size']) : 10;

        // 查询职位总数
        $total = Db::name('jobs')->where($where)->count();
        
        // 获取职位列表
        $jobs = Db::name('jobs')
            ->where($where)
            ->order('weight', 'desc')
            ->order('create_time', 'desc')
            ->page($page, $size)
            ->select();
            
        // 处理图片路径
        foreach ($jobs as &$job) {
            if (!empty($job['image'])) {
                $job['image'] = cdnurl($job['image'], true);
            }
        }

        $result = [
            'total' => $total,
            'rows' => $jobs,
            'page' => $page,
            'size' => $size,
            'keyword' => isset($params['keyword']) ? $params['keyword'] : ''
        ];

        $this->success('搜索成功', $result);
    }

    /**
     * 收藏职位
     */
    public function favorite()
    {
        // 检查登录状态
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }
        
        // 获取参数
        $job_id = $this->request->post('job_id', 0, 'int');
        
        if (!$job_id) {
            $this->error('参数错误');
        }
        
        // 获取当前用户
        $user = $this->auth->getUser();
        
        // 检查职位是否存在
        $job = Db::name('jobs')->where('id', $job_id)->find();
        if (!$job) {
            $this->error('职位不存在');
        }
        
        // 检查是否已收藏
        $exists = Db::name('job_favorite')
            ->where('user_id', $user->id)
            ->where('job_id', $job_id)
            ->find();
            
        if ($exists) {
            $this->error('已收藏该职位');
        }
        
        // 添加收藏记录
        $data = [
            'user_id' => $user->id,
            'job_id' => $job_id,
            'create_time' => time()
        ];
        
        $result = Db::name('job_favorite')->insert($data);
        
        if ($result) {
            $this->success('收藏成功');
        } else {
            $this->error('收藏失败');
        }
    }
    
    /**
     * 取消收藏职位
     */
    public function unfavorite()
    {
        // 检查登录状态
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }
        
        // 获取参数
        $job_id = $this->request->post('job_id', 0, 'int');
        
        if (!$job_id) {
            $this->error('参数错误');
        }
        
        // 获取当前用户
        $user = $this->auth->getUser();
        
        // 删除收藏记录
        $result = Db::name('job_favorite')
            ->where('user_id', $user->id)
            ->where('job_id', $job_id)
            ->delete();
            
        if ($result) {
            $this->success('取消收藏成功');
        } else {
            $this->error('取消收藏失败');
        }
    }
    
    /**
     * 检查职位收藏状态
     */
    public function checkFavorite($job_id = null)
    {
        // 检查登录状态
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }
        
        if (!$job_id) {
            $this->error('参数错误');
        }
        
        // 获取当前用户
        $user = $this->auth->getUser();
        
        // 检查是否已收藏
        $exists = Db::name('job_favorite')
            ->where('user_id', $user->id)
            ->where('job_id', $job_id)
            ->find();
            
        $this->success('获取成功', ['is_favorite' => !empty($exists)]);
    }
    
    /**
     * 获取我的收藏职位列表
     */
    public function myFavorites()
    {
        // 检查登录状态
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }
        
        // 获取参数
        $page = $this->request->get('page', 1, 'int');
        $limit = $this->request->get('limit', 10, 'int');
        
        // 获取当前用户
        $user = $this->auth->getUser();
        
        // 查询收藏记录
        $count = Db::name('job_favorite')
            ->alias('f')
            ->join('jobs j', 'f.job_id = j.id')
            ->where('f.user_id', $user->id)
            ->count();
            
        $list = Db::name('job_favorite')
            ->alias('f')
            ->join('jobs j', 'f.job_id = j.id')
            ->where('f.user_id', $user->id)
            ->field('f.id as favorite_id, f.create_time, j.*')
            ->order('f.create_time', 'desc')
            ->page($page, $limit)
            ->select();
            
        // 格式化数据
        foreach ($list as &$item) {
            $item['create_time_text'] = date('Y-m-d H:i', $item['create_time']);
            
            // 处理图片URL
            if (!empty($item['image']) && !preg_match('/^https?:\/\//', $item['image'])) {
                $item['image'] = request()->domain() . $item['image'];
            }
        }
        
        $this->success('获取成功', [
            'total' => $count,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($count / $limit),
            'list' => $list
        ]);
    }
} 