<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead"><em>编辑导入模板</em></div>
    </div>
    <style>
        .mb-10 { margin-bottom: 15px; }
        .panel-heading .btn-xs { margin-top: -3px; }
        .section-title {
            border-left: 3px solid #18bc9c;
            padding-left: 10px;
            margin-bottom: 20px;
            font-weight: 600;
            color: #333;
        }
        .form-section {
            background: #f9f9f9;
            border: 1px solid #e7e7e7;
            border-radius: 3px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .form-group label {
            font-weight: 600;
            color: #555;
        }
        .form-control:focus {
            border-color: #18bc9c;
            box-shadow: 0 0 5px rgba(24,188,156,0.2);
        }
        .panel-default > .panel-heading {
            background-color: #f5f5f5;
            border-color: #e7e7e7;
            font-weight: 600;
        }
        .dynamic-item {
            border: 1px solid #e7e7e7;
            border-radius: 3px;
            margin-bottom: 15px;
            background: #fff;
            transition: all 0.2s;
        }
        .dynamic-item:hover {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .dynamic-item .panel-heading {
            padding: 10px 15px;
            background: #f8f8f8;
            border-bottom: 1px solid #e7e7e7;
        }
        .btn-add-item {
            margin-bottom: 15px;
        }
        .field-section {
            border-bottom: 1px dashed #eee;
            padding-bottom: 15px;
            margin-bottom: 15px;
        }
        .field-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .alert-info {
            background-color: #e8f6ff;
            border-color: #d1e9ff;
            color: #31708f;
        }
        .field-group-title {
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 16px;
            color: #18bc9c;
            font-weight: 600;
        }
        .nav-tabs {
            margin-bottom: 20px;
            border-bottom: 2px solid #18bc9c;
        }
        .nav-tabs > li.active > a,
        .nav-tabs > li.active > a:focus,
        .nav-tabs > li.active > a:hover {
            border: 1px solid #18bc9c;
            border-bottom-color: transparent;
            background-color: #fff;
            color: #18bc9c;
            font-weight: 600;
        }

        /* 优化input标签的左右边距 */
        .form-group {
            margin-bottom: 20px;
            padding: 0 15px;
        }

        .input-group {
            margin-bottom: 8px;
        }

        .input-group .form-control {
            padding-left: 18px;
            padding-right: 18px;
        }

        .input-group-addon {
            padding: 10px 18px;
            min-width: 55px;
            text-align: center;
            background-color: #f8f9fa;
            border-color: #e9ecef;
        }

        /* 优化列间距 */
        .row {
            margin-left: -15px;
            margin-right: -15px;
        }

        .col-sm-4, .col-sm-6, .col-sm-12 {
            padding-left: 15px;
            padding-right: 15px;
        }

        /* 优化表单控件的内边距 */
        .form-control {
            padding: 12px 18px;
            border-radius: 4px;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        /* 优化textarea的边距 */
        textarea.form-control {
            padding: 15px 18px;
            resize: vertical;
        }

        /* 优化单选按钮的间距 */
        .radio-inline {
            margin-right: 20px;
            padding-left: 30px;
        }

        /* 增加表单区域的整体边距 */
        .form-section {
            padding: 25px;
            margin-bottom: 25px;
        }

        /* 优化标签的间距 */
        .form-group label {
            margin-bottom: 8px;
            padding-left: 5px;
        }
    </style>
    <div class="panel-body">
        <form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
            <!-- 隐藏字段，用于存储模板配置数据 -->
            <input type="hidden" id="template-data" value="{:htmlspecialchars(json_encode($row))}">
            
            <div class="form-section">
                <h4 class="section-title">基本信息</h4>
                <div class="row">
                    <div class="col-xs-12 col-sm-6">
                        <div class="form-group">
                            <label for="name" class="control-label">模板名称</label>
                            <input type="text" class="form-control" id="name" name="row[name]" value="{$row.name|htmlentities}" data-rule="required" placeholder="请输入模板名称" />
                        </div>
                    </div>
                    <div class="col-xs-12 col-sm-6">
                        <div class="form-group">
                            <label for="is_default" class="control-label">是否默认</label>
                            <div>
                                <label class="radio-inline">
                                    <input type="radio" name="row[is_default]" value="1" {eq name="$row.is_default" value="1"}checked{/eq}> 是
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" name="row[is_default]" value="0" {eq name="$row.is_default" value="0"}checked{/eq}> 否
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <div class="form-group">
                            <label for="description" class="control-label">模板描述</label>
                            <textarea class="form-control" id="description" name="row[description]" rows="3" placeholder="请输入模板描述信息">{$row.description|htmlentities}</textarea>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-section">
                <h4 class="section-title">字段映射配置</h4>
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> 请设置Excel中的单元格与简历字段的映射关系。单元格坐标示例：A1, B5, C10 等，表示第几列第几行。
                </div>
                
                <!-- 使用选项卡切换不同部分 -->
                <ul class="nav nav-tabs" role="tablist">
                    <li role="presentation" class="active"><a href="#basic-info" aria-controls="basic-info" role="tab" data-toggle="tab">基本信息</a></li>
                    <li role="presentation"><a href="#contact-info" aria-controls="contact-info" role="tab" data-toggle="tab">联系人信息</a></li>
                    <li role="presentation"><a href="#education-info" aria-controls="education-info" role="tab" data-toggle="tab">教育经历</a></li>
                    <li role="presentation"><a href="#job-info" aria-controls="job-info" role="tab" data-toggle="tab">工作经历</a></li>
                    <li role="presentation"><a href="#image-info" aria-controls="image-info" role="tab" data-toggle="tab">图片信息</a></li>
                    <li role="presentation"><a href="#other-info" aria-controls="other-info" role="tab" data-toggle="tab">其他信息</a></li>
                </ul>
                
                <div class="tab-content">
                    <!-- 基本信息选项卡 -->
                    <div role="tabpanel" class="tab-pane active" id="basic-info">
                        <div class="field-section">
                            <h5 class="field-group-title"><i class="fa fa-user"></i> 个人基本信息</h5>
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>姓名</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-user"></i></span>
                                            <input type="text" class="form-control" name="row[config][name]" value="{$row.config.name|default=''}" placeholder="例如：B2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>意向岗位</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-briefcase"></i></span>
                                            <input type="text" class="form-control" name="row[config][intended_position]" value="{$row.config.intended_position|default=''}" placeholder="例如：C2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>申请岗位</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-briefcase"></i></span>
                                            <input type="text" class="form-control" name="row[config][applied_position]" value="{$row.config.applied_position|default=''}" placeholder="例如：D2">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>性别</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-venus-mars"></i></span>
                                            <input type="text" class="form-control" name="row[config][gender]" value="{$row.config.gender|default=''}" placeholder="例如：E2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>民族</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-flag"></i></span>
                                            <input type="text" class="form-control" name="row[config][ethnicity]" value="{$row.config.ethnicity|default=''}" placeholder="例如：F2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>年龄</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                            <input type="text" class="form-control" name="row[config][age]" value="{$row.config.age|default=''}" placeholder="例如：G2">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>出生日期</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-birthday-cake"></i></span>
                                            <input type="text" class="form-control" name="row[config][birth_date]" value="{$row.config.birth_date|default=''}" placeholder="例如：H2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>身高(cm)</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-arrows-v"></i></span>
                                            <input type="text" class="form-control" name="row[config][height]" value="{$row.config.height|default=''}" placeholder="例如：I2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>体重(kg)</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-balance-scale"></i></span>
                                            <input type="text" class="form-control" name="row[config][weight]" value="{$row.config.weight|default=''}" placeholder="例如：J2">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>身份证号</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-id-card"></i></span>
                                            <input type="text" class="form-control" name="row[config][id_card]" value="{$row.config.id_card|default=''}" placeholder="例如：K2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>手机号码</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-phone"></i></span>
                                            <input type="text" class="form-control" name="row[config][phone]" value="{$row.config.phone|default=''}" placeholder="例如：L2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>婚姻状况</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-heart"></i></span>
                                            <input type="text" class="form-control" name="row[config][marital_status]" value="{$row.config.marital_status|default=''}" placeholder="例如：M2">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>户口所在地</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-home"></i></span>
                                            <input type="text" class="form-control" name="row[config][hukou_location]" value="{$row.config.hukou_location|default=''}" placeholder="例如：N2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>常住地址</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-map-marker"></i></span>
                                            <input type="text" class="form-control" name="row[config][residence_address]" value="{$row.config.residence_address|default=''}" placeholder="例如：O2">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>最高学历</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-graduation-cap"></i></span>
                                            <input type="text" class="form-control" name="row[config][highest_education]" value="{$row.config.highest_education|default=''}" placeholder="例如：P2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>粤语熟练度</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-language"></i></span>
                                            <input type="text" class="form-control" name="row[config][cantonese_level]" value="{$row.config.cantonese_level|default=''}" placeholder="例如：Q2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>国语熟练度</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-language"></i></span>
                                            <input type="text" class="form-control" name="row[config][mandarin_level]" value="{$row.config.mandarin_level|default=''}" placeholder="例如：R2">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>英语熟练度</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-language"></i></span>
                                            <input type="text" class="form-control" name="row[config][english_level]" value="{$row.config.english_level|default=''}" placeholder="例如：S2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>港澳通行证编号</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-id-card-o"></i></span>
                                            <input type="text" class="form-control" name="row[config][hk_macau_passport]" value="{$row.config.hk_macau_passport|default=''}" placeholder="例如：T2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>港澳通行证到期时间</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-calendar-times-o"></i></span>
                                            <input type="text" class="form-control" name="row[config][hk_macau_passport_expiry]" value="{$row.config.hk_macau_passport_expiry|default=''}" placeholder="例如：U2">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>海外工作经历</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-globe"></i></span>
                                            <input type="text" class="form-control" name="row[config][overseas_experience]" value="{$row.config.overseas_experience|default=''}" placeholder="例如：V2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>海外工作地区</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-map"></i></span>
                                            <input type="text" class="form-control" name="row[config][overseas_region]" value="{$row.config.overseas_region|default=''}" placeholder="例如：W2">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="field-section">
                            <h5 class="field-group-title"><i class="fa fa-id-card"></i> 身份信息</h5>
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>体重(kg)</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-balance-scale"></i></span>
                                            <input type="text" class="form-control" name="row[config][weight]" value="{$row.config.weight|default=''}" placeholder="例如：H2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>身份证号</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-id-card-o"></i></span>
                                            <input type="text" class="form-control" name="row[config][id_card]" value="{$row.config.id_card|default=''}" placeholder="例如：I2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>年龄</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-birthday-cake"></i></span>
                                            <input type="text" class="form-control" name="row[config][age]" value="{$row.config.age|default=''}" placeholder="例如：J2">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>出生日期</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-calendar"></i></span>
                                            <input type="text" class="form-control" name="row[config][birth_date]" value="{$row.config.birth_date|default=''}" placeholder="例如：K2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>手机号码</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-phone"></i></span>
                                            <input type="text" class="form-control" name="row[config][phone]" value="{$row.config.phone|default=''}" placeholder="例如：L2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>婚姻状况</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-heart"></i></span>
                                            <input type="text" class="form-control" name="row[config][marital_status]" value="{$row.config.marital_status|default=''}" placeholder="例如：M2">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="field-section">
                            <h5 class="field-group-title"><i class="fa fa-home"></i> 住址与语言能力</h5>
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>户口所在地</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-map-marker"></i></span>
                                            <input type="text" class="form-control" name="row[config][hukou_location]" value="{$row.config.hukou_location|default=''}" placeholder="例如：N2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>常住地址</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-home"></i></span>
                                            <input type="text" class="form-control" name="row[config][residence_address]" value="{$row.config.residence_address|default=''}" placeholder="例如：O2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>最高学历</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-graduation-cap"></i></span>
                                            <input type="text" class="form-control" name="row[config][highest_education]" value="{$row.config.highest_education|default=''}" placeholder="例如：P2">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>粤语熟练度</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-language"></i></span>
                                            <input type="text" class="form-control" name="row[config][cantonese_level]" value="{$row.config.cantonese_level|default=''}" placeholder="例如：Q2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>国语熟练度</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-language"></i></span>
                                            <input type="text" class="form-control" name="row[config][mandarin_level]" value="{$row.config.mandarin_level|default=''}" placeholder="例如：R2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>英语熟练度</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-language"></i></span>
                                            <input type="text" class="form-control" name="row[config][english_level]" value="{$row.config.english_level|default=''}" placeholder="例如：S2">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 联系人信息选项卡 -->
                    <div role="tabpanel" class="tab-pane" id="contact-info">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 联系人信息支持多条记录，点击"添加联系人"按钮可添加更多记录。
                        </div>
                        
                        <div id="contact-items">
                            <!-- 联系人项目将在这里动态生成 -->
                        </div>
                        
                        <div class="row">
                            <div class="col-sm-12">
                                <a href="javascript:;" class="btn btn-success btn-sm btn-add-item" id="add-contact"><i class="fa fa-plus"></i> 添加联系人</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 教育经历选项卡 -->
                    <div role="tabpanel" class="tab-pane" id="education-info">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 教育经历支持多条记录，点击"添加教育经历"按钮可添加更多记录。
                        </div>
                        
                        <div id="education-items">
                            <!-- 教育经历项目将在这里动态生成 -->
                        </div>
                        
                        <div class="row">
                            <div class="col-sm-12">
                                <a href="javascript:;" class="btn btn-success btn-sm btn-add-item" id="add-education"><i class="fa fa-plus"></i> 添加教育经历</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 工作经历选项卡 -->
                    <div role="tabpanel" class="tab-pane" id="job-info">
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 工作经历支持多条记录，点击"添加工作经历"按钮可添加更多记录。
                        </div>
                        
                        <div id="job-items">
                            <!-- 工作经历项目将在这里动态生成 -->
                        </div>
                        
                        <div class="row">
                            <div class="col-sm-12">
                                <a href="javascript:;" class="btn btn-success btn-sm btn-add-item" id="add-job"><i class="fa fa-plus"></i> 添加工作经历</a>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 图片信息选项卡 -->
                    <div role="tabpanel" class="tab-pane" id="image-info">
                        <div class="field-section">
                            <h5 class="field-group-title"><i class="fa fa-image"></i> 图片字段配置</h5>
                            <div class="alert alert-warning">
                                <i class="fa fa-exclamation-triangle"></i> 
                                <strong>注意：</strong>图片字段应填入图片的相对路径或文件名，系统会自动处理为完整路径。
                                <br>例如：/uploads/20241201/abc123.jpg 或 abc123.jpg
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>头像</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-user-circle"></i></span>
                                            <input type="text" class="form-control" name="row[config][avatar]" value="{$row.config.avatar|default=''}" placeholder="例如：X2">
                                        </div>
                                        <small class="help-block">个人头像照片路径</small>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>全身照</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-male"></i></span>
                                            <input type="text" class="form-control" name="row[config][full_body_photo]" value="{$row.config.full_body_photo|default=''}" placeholder="例如：Y2">
                                        </div>
                                        <small class="help-block">全身照片路径</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>身份证正面</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-id-card"></i></span>
                                            <input type="text" class="form-control" name="row[config][id_card_front]" value="{$row.config.id_card_front|default=''}" placeholder="例如：Z2">
                                        </div>
                                        <small class="help-block">身份证正面照片路径</small>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>身份证反面</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-id-card-o"></i></span>
                                            <input type="text" class="form-control" name="row[config][id_card_back]" value="{$row.config.id_card_back|default=''}" placeholder="例如：AA2">
                                        </div>
                                        <small class="help-block">身份证反面照片路径</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>港澳通行证正面</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-credit-card"></i></span>
                                            <input type="text" class="form-control" name="row[config][hk_macau_passport_front]" value="{$row.config.hk_macau_passport_front|default=''}" placeholder="例如：AB2">
                                        </div>
                                        <small class="help-block">港澳通行证正面照片路径</small>
                                    </div>
                                </div>
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label>港澳通行证反面</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-credit-card-alt"></i></span>
                                            <input type="text" class="form-control" name="row[config][hk_macau_passport_back]" value="{$row.config.hk_macau_passport_back|default=''}" placeholder="例如：AC2">
                                        </div>
                                        <small class="help-block">港澳通行证反面照片路径</small>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="row">
                                <div class="col-sm-12">
                                    <div class="form-group">
                                        <label>补充照片</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-images"></i></span>
                                            <input type="text" class="form-control" name="row[config][additional_photos]" value="{$row.config.additional_photos|default=''}" placeholder="例如：AD2">
                                        </div>
                                        <small class="help-block">补充照片路径，多张照片用逗号分隔</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 其他信息选项卡 -->
                    <div role="tabpanel" class="tab-pane" id="other-info">
                        <div class="field-section">
                            <h5 class="field-group-title"><i class="fa fa-star"></i> 其他信息</h5>
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>特殊职业资格证</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-certificate"></i></span>
                                            <input type="text" class="form-control" name="row[config][special_certificate]" value="{$row.config.special_certificate|default=''}" placeholder="例如：X2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>兴趣爱好</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-heart-o"></i></span>
                                            <input type="text" class="form-control" name="row[config][hobbies]" value="{$row.config.hobbies|default=''}" placeholder="例如：Y2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>自我评价</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-comment-o"></i></span>
                                            <input type="text" class="form-control" name="row[config][self_evaluation]" value="{$row.config.self_evaluation|default=''}" placeholder="例如：Z2">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>对接人</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-user-o"></i></span>
                                            <input type="text" class="form-control" name="row[config][contact_person]" value="{$row.config.contact_person|default=''}" placeholder="例如：AO2">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-sm-4">
                                    <div class="form-group">
                                        <label>状态</label>
                                        <div class="input-group">
                                            <span class="input-group-addon"><i class="fa fa-info-circle"></i></span>
                                            <input type="text" class="form-control" name="row[config][status]" value="{$row.config.status|default=''}" placeholder="例如：AX2">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="form-group layer-footer">
                <div class="col-xs-12 text-center">
                    <button type="submit" class="btn btn-success btn-lg btn-embossed disabled"><i class="fa fa-save"></i> 保存</button>
                    <button type="reset" class="btn btn-default btn-lg btn-embossed"><i class="fa fa-refresh"></i> 重置</button>
                </div>
            </div>
        </form>
    </div>
</div>

<script type="text/javascript">
// 使用字符串形式的模板语法，避免IDE语法检查报错
var configJson = '{:addslashes(json_encode(["row" => $row]))}';
// 解析JSON字符串
var Config = JSON.parse(configJson.replace(/\\/g, ''));

if (Config && Config.row && Config.row.config) {
    // 检查多数组字段是否为数组格式
    var arrayFields = [
        'contact_relation', 'contact_name', 'contact_age', 'contact_job',
        'education_start', 'education_end', 'education_school', 'education_major', 'graduation_education',
        'job_start', 'job_end', 'job_company', 'job_position', 'job_description'
    ];

    // 检查所有字段
    arrayFields.forEach(function(field) {
        if (Config.row.config[field]) {
            // 如果是字符串，自动转换为数组
            if (typeof Config.row.config[field] === 'string' && Config.row.config[field].indexOf('|') !== -1) {
                Config.row.config[field] = Config.row.config[field].split('|');
            }
        } else {
            Config.row.config[field] = [];
        }
    });
}


</script> 

