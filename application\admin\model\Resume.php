<?php

namespace app\admin\model;

use think\Model;


class Resume extends Model
{

    

    

    // 表名
    protected $name = 'resume';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'integer';

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'gender_text',
        'marital_status_text',
        'cantonese_level_text',
        'mandarin_level_text',
        'english_level_text',
        'overseas_experience_text',
        'status_text'
    ];
    

    
    public function getGenderList()
    {
        return ['未知' => __('未知'), '男' => __('男'), '女' => __('女')];
    }

    public function getMaritalStatusList()
    {
        return ['未婚' => __('未婚'), '已婚' => __('已婚'), '离异' => __('离异')];
    }

    public function getCantoneseLevelList()
    {
        return ['不会' => __('不会'), '一般' => __('一般'), '熟练' => __('熟练')];
    }

    public function getMandarinLevelList()
    {
        return ['不会' => __('不会'), '一般' => __('一般'), '熟练' => __('熟练')];
    }

    public function getEnglishLevelList()
    {
        return ['不会' => __('不会'), '一般' => __('一般'), '熟练' => __('熟练')];
    }

    public function getOverseasExperienceList()
    {
        return ['无' => __('无'), '有' => __('有')];
    }

    public function getStatusList()
    {
        return ['已发布' => __('已发布'), '草稿' => __('草稿'),'官网提交' => __('官网提交')];
    }


    public function getGenderTextAttr($value, $data)
    {
        $value = $value ?: ($data['gender'] ?? '');
        $list = $this->getGenderList();
        return $list[$value] ?? '';
    }


    public function getMaritalStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['marital_status'] ?? '');
        $list = $this->getMaritalStatusList();
        return $list[$value] ?? '';
    }


    public function getCantoneseLevelTextAttr($value, $data)
    {
        $value = $value ?: ($data['cantonese_level'] ?? '');
        $list = $this->getCantoneseLevelList();
        return $list[$value] ?? '';
    }


    public function getMandarinLevelTextAttr($value, $data)
    {
        $value = $value ?: ($data['mandarin_level'] ?? '');
        $list = $this->getMandarinLevelList();
        return $list[$value] ?? '';
    }


    public function getEnglishLevelTextAttr($value, $data)
    {
        $value = $value ?: ($data['english_level'] ?? '');
        $list = $this->getEnglishLevelList();
        return $list[$value] ?? '';
    }


    public function getOverseasExperienceTextAttr($value, $data)
    {
        $value = $value ?: ($data['overseas_experience'] ?? '');
        $list = $this->getOverseasExperienceList();
        return $list[$value] ?? '';
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }




}
