{include file="common/header" /}

<!-- 搜索区域 -->
<div class="search-section py-5 bg-light mt-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-body">
                        <form action="{:url('index/job/search')}" method="get">
                            <div class="row g-3">
                                <div class="col-md-5">
                                    <input type="text" class="form-control" name="keyword" placeholder="输入职位名称" value="{$keyword|default=''}">
                                </div>
                                <div class="col-md-5">
                                    <select class="form-select" name="category">
                                        <option value="">选择岗位分类</option>
                                        {volist name="categories" id="cat"}
                                        <option value="{$cat}" {if isset($category) && $category==$cat}selected{/if}>{$cat}</option>
                                        {/volist}
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary w-100">搜索</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 搜索结果 -->
<div class="container py-5">
    <div class="row">
        <!-- 左侧筛选栏 -->
        <div class="col-md-3">
            <div class="filter-card">
                <div class="card-header" onclick="toggleFilter()">
                    <i class="fas fa-filter me-2"></i>筛选条件
                </div>
                <div class="card-body">
                    <form action="{:url('index/job/search')}" method="get">
                        <input type="hidden" name="keyword" value="{$keyword|default=''}">
                        <input type="hidden" name="category" value="{$category|default=''}">
                        <div class="mb-4">
                            <label class="form-label">年龄要求</label>
                            <select class="form-select" name="age">
                                <option value="">不限</option>
                                <option value="18-25" {if isset($age) && $age=='18-25'}selected{/if}>18-25岁</option>
                                <option value="25-35" {if isset($age) && $age=='25-35'}selected{/if}>25-35岁</option>
                                <option value="35-45" {if isset($age) && $age=='35-45'}selected{/if}>35-45岁</option>
                                <option value="45+" {if isset($age) && $age=='45+'}selected{/if}>45岁以上</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-check me-2"></i>应用筛选
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- 右侧职位列表 -->
        <div class="col-md-9">
            <div class="alert alert-info mb-4">
                <i class="fas fa-search me-2"></i>
                搜索关键词：<strong>{$keyword|default='全部'}</strong>
                {if isset($category) && $category}
                <span class="badge bg-primary ms-2">{$category}</span>
                {/if}
                {if isset($age) && $age}
                <span class="badge bg-secondary ms-2">{$age}</span>
                {/if}
                <span class="ms-2">共找到 <strong>{:count($jobs)}</strong> 个职位</span>
            </div>

            {volist name="jobs" id="job"}
            <div class="job-card">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3 text-center">
                            <div class="d-flex flex-column align-items-center">
                                <img src="{$job.image|default='__CDN__/assets/img/default-job.jpg'}" class="company-logo" alt="{$job.title}">
                            </div>
                        </div>
                        <div class="col-md-9 job-content">
                            <h5 class="job-title">
                                <a href="{:url('index/job/detail', ['id' => $job.id])}">{$job.title}</a>
                                <span class="badge bg-primary ms-2">{$job.job_code}</span>
                                {if $job.weight > 0}
                                <span class="badge bg-warning position-absolute top-0 end-0 m-3">
                                    <i class="fas fa-star"></i> 推荐
                                </span>
                                {/if}
                            </h5>
                            <div class="job-info">
                                <i class="fas fa-yen-sign"></i> {$job.salary_range}
                            </div>
                            <div class="job-info">
                                <i class="fas fa-graduation-cap"></i> {$job.education_requirement|default='不限'}
                            </div>
                            <div class="job-info">
                                <i class="fas fa-briefcase"></i> {$job.experience_requirement|default='不限'}
                            </div>
                            <div class="job-info">
                                <i class="fas fa-clock"></i> 更新时间：{:date('Y-m-d H:i', $job['update_time'])}
                            </div>
                            <div class="d-flex justify-content-between align-items-center mt-3">
                                <div>
                                    <span class="badge bg-info">{$job.category}</span>
                                    {if $job.gender_requirement}
                                    <span class="badge bg-secondary">{$job.gender_requirement}</span>
                                    {/if}
                                </div>
                            </div>
                            <a href="{:url('index/job/detail', ['id' => $job.id])}" class="btn btn-outline-primary btn-detail">
                                <i class="fas fa-eye me-1"></i>查看详情
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {/volist}

            <!-- 分页 -->
            <div class="pagination-container">
                <div class="d-flex justify-content-center">
                    {$page}
                </div>
            </div>
        </div>
    </div>
</div>

{include file="common/footer" /} 