<?php

namespace app\api\controller;

use app\common\controller\Api;
use think\Db;
use think\Exception;

/**
 * 首页相关API接口
 */
class Index extends Api
{
    // 无需登录的接口
    protected $noNeedLogin = ['*'];
    // 无需鉴权的接口
    protected $noNeedRight = ['*'];

    /**
     * 获取首页数据
     *
     * @ApiTitle    (获取首页数据)
     * @ApiSummary  (获取首页热门职位、最新职位和职位分类)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/index/index)
     * @ApiReturn   ({"code":1,"msg":"获取成功","time":1627884064,"data":{"hot_jobs":[],"latest_jobs":[],"categories":[]}})
     */
    public function index()
    {
        try {
            // 获取热门职位
            $hotJobs = Db::name('jobs')
                ->where('status', '上架')
                ->field('id, title, company, image, category, salary_range, education_requirement, resume_count, create_time')
                ->order('weight', 'desc')
                ->order('resume_count', 'desc')
                ->limit(6)
                ->select();

            // 获取最新职位
            $latestJobs = Db::name('jobs')
                ->where('status', '上架')
                ->field('id, title, company, image, category, salary_range, education_requirement, create_time')
                ->order('create_time', 'desc')
                ->limit(6)
                ->select();

            // 获取职位分类
            $categories = Db::name('jobs')
                ->where('status', '上架')
                ->column('DISTINCT category');

            $result = [
                'hot_jobs' => $hotJobs,
                'latest_jobs' => $latestJobs,
                'categories' => $categories
            ];
            
            $this->success('获取成功', $result);
        } catch (Exception $e) {
            $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取网站统计数据
     *
     * @ApiTitle    (获取网站统计数据)
     * @ApiSummary  (获取网站的职位数量、企业数量、简历数量等统计数据)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/index/stats)
     * @ApiReturn   ({"code":1,"msg":"获取成功","time":1627884064,"data":{"job_count":0,"company_count":0,"resume_count":0,"user_count":0}})
     */
    public function stats()
    {
        try {
            // 职位数量
            $jobCount = Db::name('jobs')
                ->where('status', '上架')
                ->count();

            // 企业数量
            $companyCount = Db::name('jobs')
                ->where('status', '上架')
                ->distinct(true)
                ->column('company');
            $companyCount = count($companyCount);

            // 简历数量
            $resumeCount = Db::name('resume')
                ->count();

            // 用户数量
            $userCount = Db::name('user')
                ->count();

            $result = [
                'job_count' => $jobCount,
                'company_count' => $companyCount,
                'resume_count' => $resumeCount,
                'user_count' => $userCount
            ];
            
            $this->success('获取成功', $result);
        } catch (Exception $e) {
            $this->error('获取失败：' . $e->getMessage());
        }
    }

    /**
     * 获取Banner轮播图
     *
     * @ApiTitle    (获取Banner轮播图)
     * @ApiSummary  (获取首页Banner轮播图数据)
     * @ApiMethod   (GET)
     * @ApiRoute    (/api/index/banners)
     * @ApiReturn   ({"code":1,"msg":"获取成功","time":1627884064,"data":[{"id":1,"image":"http://example.com/banner1.jpg","url":"http://example.com","title":"Banner标题"}]})
     */
    public function banners()
    {
        try {
            // 假设有一个banners表存储轮播图数据
            // 如果没有，可以根据实际情况修改此处代码
            $banners = Db::name('banners')
                ->where('status', 'normal')
                ->field('id, image, url, title')
                ->order('weigh', 'desc')
                ->select();
                
            // 处理图片URL
            foreach ($banners as &$banner) {
                $banner['image'] = cdnurl($banner['image'], true);
            }
            
            $this->success('获取成功', $banners);
        } catch (Exception $e) {
            // 如果没有banners表，返回空数组
            $this->success('获取成功', []);
        }
    }
} 