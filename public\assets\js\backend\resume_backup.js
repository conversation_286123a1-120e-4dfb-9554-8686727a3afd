define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'toastr'], function ($, undefined, Backend, Table, Form, Toastr) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'resume/index' + location.search,
                    add_url: 'resume/add',
                    edit_url: 'resume/edit',
                    del_url: 'resume/del',
                    multi_url: 'resume/multi',
                    import_url: 'resume/import',
                    table: 'resume',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true, width: 40},
                        {field: 'id', title: __('Id'), operate: false, width: 40},
                        {field: 'name', title: __('Name'), operate: 'LIKE', width: 80},
                        {field: 'intended_position', title: __('Intended_position'), operate: 'LIKE', width: 150},
                        {
                            field: 'applied_position',
                            title: __('Applied_position'),
                            operate: 'LIKE',
                            width: 150,
                            formatter: function(value, row, index) {
                                return '<a href="javascript:;" class="editable" data-type="text" data-pk="' + row.id + '" data-name="applied_position" data-value="' + (value || '') + '">' + (value || '') + '</a>';
                            },
                            events: {
                                'click .editable': function (e, value, row, index) {
                                    Controller.api.handleInlineEdit(e, value, row, 'applied_position', $(this));
                                }
                            }
                        },
                        {field: 'height', title: __('Height'), operate:'BETWEEN', visible: false, width: 50},
                        {field: 'weight', title: __('Weight'), operate:'BETWEEN', visible: false, width: 50},
                        {field: 'gender', title: __('Gender'), searchList: {"未知":__('未知'),"男":__('男'),"女":__('女')}, formatter: Table.api.formatter.normal, width: 50},
                        {field: 'age', title: __('Age'), operate:'BETWEEN', visible: false, width: 60},
                        {field: 'hukou_location', title: __('Hukou_location'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content, visible: false, width: 150},
                        {field: 'highest_education', title: __('Highest_education'), operate: 'LIKE', width: 100},
                        {field: 'cantonese_level', title: __('Cantonese_level'), searchList: {"不会":__('不会'),"一般":__('一般'),"熟练":__('熟练')}, formatter: Table.api.formatter.normal, width: 90},
                        {field: 'english_level', visible: false, title: __('English_level'), searchList: {"不会":__('不会'),"一般":__('一般'),"熟练":__('熟练')}, formatter: Table.api.formatter.normal, width: 90},
                        {
                            field: 'special_certificate', 
                            title: __('Special_certificate'), 
                            operate: 'LIKE',
                            width: 100,
                            formatter: function(value, row, index) {
                                return '<a href="javascript:;" class="editable" data-type="text" data-pk="' + row.id + '" data-name="special_certificate" data-value="' + (value || '') + '">' + (value || '') + '</a>';
                            },
                            events: {
                                'click .editable': function (e, value, row, index) {
                                    e.stopPropagation();
                                    var $this = $(this);
                                    var input = $('<div class="input-group input-group-sm"><input type="text" class="form-control" value="' + (value || '') + '"><span class="input-group-btn"><button class="btn btn-success btn-sm" type="button"><i class="fa fa-check"></i></button><button class="btn btn-default btn-sm" type="button"><i class="fa fa-times"></i></button></span></div>');
                                    $this.html(input);
                                    var inputField = input.find('input');
                                    
                                    // 设置光标位置到文本末尾
                                    setTimeout(function() {
                                        inputField.focus();
                                        var len = inputField.val().length;
                                        inputField[0].setSelectionRange(len, len);
                                    }, 0);
                                    
                                    // 处理确认按钮点击
                                    input.find('.btn-success').on('click', function() {
                                        saveValue();
                                    });
                                    
                                    // 处理取消按钮点击
                                    input.find('.btn-default').on('click', function() {
                                        $this.html(value);
                                    });
                                    
                                    // 处理失焦事件
                                    inputField.blur(function() {
                                        // 延迟执行，以便可以点击按钮
                                        setTimeout(function() {
                                            if (!input.find('.btn-success:hover').length && !input.find('.btn-default:hover').length) {
                                                saveValue();
                                            }
                                        }, 200);
                                    });
                                    
                                    // 处理回车事件
                                    inputField.keypress(function(e) {
                                        if (e.which === 13) {
                                            saveValue();
                                            return false;
                                        } else if (e.which === 27) { // ESC键
                                            $this.html(value);
                                            return false;
                                        }
                                    });
                                    
                                    // 保存值的函数
                                    function saveValue() {
                                        var newValue = inputField.val().trim();
                                        if (newValue !== value) {
                                            $.ajax({
                                                url: Fast.api.fixurl('resume/edit'),
                                                type: 'POST',
                                                data: {
                                                    ids: row.id,
                                                    row: {
                                                        special_certificate: newValue
                                                    }
                                                },
                                                success: function(res) {
                                                    if (res.code === 1) {
                                                        $this.html(newValue);
                                                        Toastr.success('更新成功');
                                                    } else {
                                                        $this.html(value);
                                                        Toastr.error(res.msg || '更新失败');
                                                    }
                                                },
                                                error: function() {
                                                    $this.html(value);
                                                    Toastr.error('服务器错误');
                                                }
                                            });
                                        } else {
                                            $this.html(value);
                                        }
                                    }
                                }
                            }
                        },
                        {field: 'job_experience', title: __('工作经历'), operate: false, width: 300, formatter: function(value, row, index) {
                            let jobs = [];
                            if (row.job_start && row.job_end && row.job_company && row.job_position) {
                                const starts = row.job_start.split('|');
                                const ends = row.job_end.split('|');
                                const companies = row.job_company.split('|');
                                const positions = row.job_position.split('|');
                                const descriptions = row.job_description ? row.job_description.split('|') : [];
                                
                                for (let i = 0; i < starts.length; i++) {
                                    const startDate = new Date(starts[i]);
                                    const endDate = new Date(ends[i]);
                                    const startFormatted = `${startDate.getFullYear()}年${startDate.getMonth()+1}月`;
                                    const endFormatted = `${endDate.getFullYear()}年${endDate.getMonth()+1}月`;
                                    jobs.push(`<div>${startFormatted} - ${endFormatted} ${companies[i]} ${positions[i]}</div>`);
                                    if (descriptions[i]) {
                                    }
                                }
                            }
                            return jobs.join('');
                        }},
                        {field: 'job_company', title: __('Job_company'), operate: 'LIKE', visible: false, width: 150},
                        {field: 'job_position', title: __('Job_position'), operate: 'LIKE', visible: false, width: 150},
                        {field: 'job_description', title: __('job_description'), operate: 'LIKE', visible: false, width: 200},
                        {field: 'avatar', title: __('Avatar'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image, classname: 'img-lg img-center', width: 150},
                        {field: 'contact_person', title: __('Contact_person'), operate: 'LIKE', width: 100},
                        {field: 'status', title: __('status'),searchList: {"已发布":__('已发布'),"草稿":__('草稿'),"官网提交":__('官网提交')}, formatter: Table.api.formatter.normal, visible: false, width: 80},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, width: 120,
                            buttons: [
                                {
                                    name: 'detail',
                                    text: '',
                                    title: '查看详情',
                                    icon: 'fa fa-eye',
                                    classname: 'btn btn-xs btn-info btn-detail',
                                    url: 'resume/detail',
                                    tooltip: true,
                                    extend: 'data-toggle="tooltip"'
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 绑定导出相关事件
            Controller.api.bindExportEvents();

            // 绑定导入相关事件
            Controller.api.bindImportEvents();

            // 绑定批量操作事件
            Controller.api.bindBatchEvents();

            // 强制激活所有tooltip
            $(function(){
                $(document).off('mouseenter.tooltip.data-api', '[data-toggle="tooltip"]').on('mouseenter.tooltip.data-api', '[data-toggle="tooltip"]', function() {
                    $(this).tooltip('show');
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        
        /**
         * 基于模板导入
         */
        importWithTemplate: function () {
            // 文件选择后显示文件名
            $('#excel_file').change(function() {
                var fileName = $(this).val().split('\\').pop();
                $('#file-display').val(fileName);
            });
            
            // 选择模板后显示模板描述和字段映射
            $('#template_id').change(function() {
                var templateId = $(this).val();
                if (templateId) {
                    $.ajax({
                        url: Backend.api.fixurl("resume_import_template/edit"),
                        type: 'get',
                        data: {ids: templateId},
                        dataType: 'json',
                        success: function(res) {
                            if (res.code === 1) {
                                var template = res.data.row;
                                var description = template.description || '无描述';
                                $('#template-description').html(description);
                                
                                // 显示字段映射信息
                                Controller.api.renderMappingInfo(template.config);
                            } else {
                                $('#template-description').html('获取模板信息失败');
                                $('#template-mapping').html('<div class="alert alert-danger">获取模板信息失败</div>');
                            }
                        },
                        error: function() {
                            $('#template-description').html('获取模板信息失败');
                            $('#template-mapping').html('<div class="alert alert-danger">获取模板信息失败</div>');
                        }
                    });
                } else {
                    $('#template-description').html('请先选择导入模板');
                    $('#template-mapping').html('<div class="alert alert-info">请先选择导入模板查看字段映射详情</div>');
                }
            });
            
            // 初始化表单验证
            Form.api.bindevent($("form[role=form]"));
            
            // 触发默认模板的描述显示
            $('#template_id').trigger('change');
        },
        
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },

            // 处理内联编辑
            handleInlineEdit: function(e, value, row, fieldName, $element) {
                e.stopPropagation();
                var input = $('<div class="input-group input-group-sm"><input type="text" class="form-control" value="' + (value || '') + '"><span class="input-group-btn"><button class="btn btn-success btn-sm" type="button"><i class="fa fa-check"></i></button><button class="btn btn-default btn-sm" type="button"><i class="fa fa-times"></i></button></span></div>');
                $element.html(input);
                var inputField = input.find('input');

                // 设置光标位置到文本末尾
                setTimeout(function() {
                    inputField.focus();
                    var len = inputField.val().length;
                    inputField[0].setSelectionRange(len, len);
                }, 0);

                // 处理确认按钮点击
                input.find('.btn-success').on('click', function() {
                    saveValue();
                });

                // 处理取消按钮点击
                input.find('.btn-default').on('click', function() {
                    $element.html(value);
                });

                // 处理失焦事件
                inputField.blur(function() {
                    setTimeout(function() {
                        if (!input.find('.btn-success:hover').length && !input.find('.btn-default:hover').length) {
                            saveValue();
                        }
                    }, 200);
                });

                // 处理回车事件
                inputField.keypress(function(e) {
                    if (e.which === 13) {
                        saveValue();
                        return false;
                    } else if (e.which === 27) { // ESC键
                        $element.html(value);
                        return false;
                    }
                });

                // 保存值的函数
                function saveValue() {
                    var newValue = inputField.val().trim();
                    if (newValue !== value) {
                        var data = { ids: row.id, row: {} };
                        data.row[fieldName] = newValue;

                        $.ajax({
                            url: Fast.api.fixurl('resume/edit'),
                            type: 'POST',
                            data: data,
                            success: function(res) {
                                if (res.code === 1) {
                                    $element.html(newValue);
                                    Toastr.success('更新成功');
                                } else {
                                    $element.html(value);
                                    Toastr.error(res.msg || '更新失败');
                                }
                            },
                            error: function() {
                                $element.html(value);
                                Toastr.error('服务器错误');
                            }
                        });
                    } else {
                        $element.html(value);
                    }
                }
            },

            // 绑定导出相关事件
            bindExportEvents: function() {
                // 导出Excel汇总
                $(document).on('click', '.btn-exportexcel, .btn-export-excel', function() {
                    Controller.export.showExportModal();
                });

                // 导出Excel模板
                $(document).on('click', '.btn-exporttemplate, .btn-export-template', function() {
                    Controller.export.showExportTemplateModal();
                });

                // 导出Word模板
                $(document).on('click', '.btn-exportword, .btn-export-word', function() {
                    Controller.export.showExportWordModal();
                });

                // Excel模板上传
                $(document).on('click', '.btn-excel-upload', function() {
                    Controller.export.showExcelUploadModal();
                });

                // Word模板上传
                $(document).on('click', '.btn-word-upload', function() {
                    Controller.export.showWordUploadModal();
                });

                // Excel模板文件上传
                $(document).on('click', '.btn-upload-excel-template', function() {
                    Controller.export.uploadExcelTemplateFile();
                });

                // Word模板文件上传
                $(document).on('click', '.btn-upload-word-template', function() {
                    Controller.export.uploadWordTemplateFile();
                });

                // Excel模板导出按钮
                $(document).on('click', '#btnExportTemplate', function() {
                    var ids = Table.api.selectedids($("#table"));
                    var templatePath = $('#templateSelect').val();
                    var templateName = $('#templateSelect option:selected').text();
                    var filenameFields = [];
                    $('.filename-field:checked').each(function() {
                        filenameFields.push($(this).val());
                    });

                    if (!templatePath) {
                        Toastr.error('请选择导出模板');
                        return;
                    }

                    $('#exportTemplateModal').modal('hide');
                    Controller.export.startExcelTemplateExportProcess(ids, templatePath, templateName, filenameFields);
                });

                // Word模板导出按钮
                $(document).on('click', '#btnExportWordTemplate', function() {
                    var ids = Table.api.selectedids($("#table"));
                    var templatePath = $('#wordTemplateSelect').val();
                    var templateName = $('#wordTemplateSelect option:selected').text();
                    var filenameFields = [];
                    $('.word-filename-field:checked').each(function() {
                        filenameFields.push($(this).val());
                    });

                    if (!templatePath) {
                        Toastr.error('请选择导出模板');
                        return;
                    }

                    $('#exportWordModal').modal('hide');
                    Controller.export.startWordTemplateExportProcess(ids, templatePath, templateName, filenameFields);
                });
            },

            // 绑定导入相关事件
            bindImportEvents: function() {
                // 导入简历
                $(document).on('click', '.btn-import, .btn-batch-upload', function() {
                    Controller.import.showImportModal();
                });

                // 导入文件按钮
                $(document).on('click', '.btn-import-files', function() {
                    Controller.import.importFiles();
                });

                // 批量导入文件按钮
                $(document).on('click', '.btn-import-batch-files', function() {
                    Controller.import.importBatchFiles();
                });
            },

            // 绑定批量操作事件
            bindBatchEvents: function() {
                // 批量复制
                $(document).on('click', '.btn-batchcopy, .btn-batch-copy', function() {
                    Controller.batch.showBatchCopyModal();
                });

                // 确认批量复制
                $(document).on('click', '.btn-batch-copy-confirm', function() {
                    Controller.batch.batchCopyResumes();
                });
            },

            // 渲染字段映射信息
            renderMappingInfo: function(config) {
                if (!config) {
                    $('#template-mapping').html('<div class="alert alert-warning">该模板未配置字段映射</div>');
                    return;
                }
                
                var html = '';
                
                // 基本信息映射
                html += '<div class="panel panel-info">';
                html += '<div class="panel-heading">基本信息映射</div>';
                html += '<div class="panel-body">';
                html += '<table class="table table-striped">';
                html += '<thead><tr><th width="30%">字段名称</th><th>Excel单元格</th></tr></thead>';
                html += '<tbody>';
                
                // 基本字段
                var basicFields = {
                    'name': '姓名',
                    'intended_position': '意向岗位',
                    'applied_position': '申请岗位',
                    'gender': '性别',
                    'ethnicity': '民族',
                    'height': '身高(cm)',
                    'weight': '体重(kg)',
                    'id_card': '身份证号',
                    'age': '年龄',
                    'birth_date': '出生日期',
                    'phone': '手机号码',
                    'marital_status': '婚姻状况',
                    'hukou_location': '户口所在地',
                    'residence_address': '常住地址',
                    'highest_education': '最高学历',
                    'cantonese_level': '粤语熟练度',
                    'mandarin_level': '国语熟练度',
                    'english_level': '英语熟练度'
                };
                
                for (var field in basicFields) {
                    if (config[field]) {
                        html += '<tr>';
                        html += '<td>' + basicFields[field] + '</td>';
                        html += '<td>' + config[field] + '</td>';
                        html += '</tr>';
                    }
                }
                
                html += '</tbody></table>';
                html += '</div></div>';
                
                // 联系人信息映射
                html += '<div class="panel panel-info">';
                html += '<div class="panel-heading">联系人信息映射</div>';
                html += '<div class="panel-body">';
                html += '<table class="table table-striped">';
                html += '<thead><tr><th width="30%">字段名称</th><th>Excel单元格</th></tr></thead>';
                html += '<tbody>';
                
                var contactFields = {
                    'contact_relation': '联系人关系',
                    'contact_name': '联系人姓名',
                    'contact_age': '联系人年龄',
                    'contact_job': '联系人工作'
                };
                
                for (var field in contactFields) {
                    if (config[field]) {
                        html += '<tr>';
                        html += '<td>' + contactFields[field] + '</td>';
                        html += '<td>' + config[field] + '</td>';
                        html += '</tr>';
                    }
                }
                
                html += '</tbody></table>';
                html += '</div></div>';
                
                // 教育经历映射
                html += '<div class="panel panel-info">';
                html += '<div class="panel-heading">教育经历映射</div>';
                html += '<div class="panel-body">';
                html += '<table class="table table-striped">';
                html += '<thead><tr><th width="30%">字段名称</th><th>Excel单元格</th></tr></thead>';
                html += '<tbody>';
                
                var educationFields = {
                    'education_start': '教育开始时间',
                    'education_end': '教育结束时间',
                    'education_school': '学校名称',
                    'education_major': '专业',
                    'graduation_education': '学历'
                };
                
                for (var field in educationFields) {
                    if (config[field]) {
                        html += '<tr>';
                        html += '<td>' + educationFields[field] + '</td>';
                        html += '<td>' + config[field] + '</td>';
                        html += '</tr>';
                    }
                }
                
                html += '</tbody></table>';
                html += '</div></div>';
                
                // 工作经历映射
                html += '<div class="panel panel-info">';
                html += '<div class="panel-heading">工作经历映射</div>';
                html += '<div class="panel-body">';
                html += '<table class="table table-striped">';
                html += '<thead><tr><th width="30%">字段名称</th><th>Excel单元格</th></tr></thead>';
                html += '<tbody>';
                
                var jobFields = {
                    'job_start': '工作开始时间',
                    'job_end': '工作结束时间',
                    'job_company': '工作单位',
                    'job_position': '工作岗位',
                    'job_description': '工作内容'
                };
                
                for (var field in jobFields) {
                    if (config[field]) {
                        html += '<tr>';
                        html += '<td>' + jobFields[field] + '</td>';
                        html += '<td>' + config[field] + '</td>';
                        html += '</tr>';
                    }
                }
                
                html += '</tbody></table>';
                html += '</div></div>';
                
                // 其他信息映射
                html += '<div class="panel panel-info">';
                html += '<div class="panel-heading">其他信息映射</div>';
                html += '<div class="panel-body">';
                html += '<table class="table table-striped">';
                html += '<thead><tr><th width="30%">字段名称</th><th>Excel单元格</th></tr></thead>';
                html += '<tbody>';
                
                var otherFields = {
                    'special_certificate': '特殊职业资格证',
                    'hobbies': '兴趣爱好',
                    'self_evaluation': '自我评价',
                    'contact_person': '对接人',
                    'status': '状态'
                };
                
                for (var field in otherFields) {
                    if (config[field]) {
                        html += '<tr>';
                        html += '<td>' + otherFields[field] + '</td>';
                        html += '<td>' + config[field] + '</td>';
                        html += '</tr>';
                    }
                }
                
                html += '</tbody></table>';
                html += '</div></div>';
                
                $('#template-mapping').html(html);
            }
        },

        // 导出模块
        export: {
            // 显示导出Excel汇总模态框
            showExportModal: function() {
                var ids = Table.api.selectedids($("#table"));
                if (ids.length === 0) {
                    Toastr.error('请先选择要导出的简历');
                    return;
                }

                // 直接开始导出进度
                Controller.progress.showExportProgressModal(ids);
            },

            // 显示导出Excel模板模态框
            showExportTemplateModal: function() {
                var ids = Table.api.selectedids($("#table"));
                if (ids.length === 0) {
                    Toastr.error('请先选择要导出的简历');
                    return;
                }

                // 加载模板列表
                Controller.export.loadTemplateList(function() {
                    $('#exportTemplateModal').modal('show');
                });
            },

            // 显示导出Word模板模态框
            showExportWordModal: function() {
                var ids = Table.api.selectedids($("#table"));
                if (ids.length === 0) {
                    Toastr.error('请先选择要导出的简历');
                    return;
                }

                // 加载Word模板列表
                Controller.export.loadWordTemplateList(function() {
                    $('#exportWordModal').modal('show');
                });
            },

            // 加载Excel模板列表
            loadTemplateList: function(callback) {
                $.ajax({
                    url: Fast.api.fixurl('resume/getTemplateList'),
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 1) {
                            var options = '<option value="">请选择模板</option>';
                            response.data.forEach(function(template) {
                                options += '<option value="' + template.path + '">' + template.name + '</option>';
                            });
                            $('#templateSelect').html(options);
                            if (callback) callback();
                        } else {
                            Toastr.error('加载模板列表失败');
                        }
                    },
                    error: function() {
                        Toastr.error('加载模板列表失败');
                    }
                });
            },

            // 加载Word模板列表
            loadWordTemplateList: function(callback) {
                $.ajax({
                    url: Fast.api.fixurl('resume/getWordTemplateList'),
                    type: 'GET',
                    success: function(response) {
                        if (response.code === 1) {
                            var options = '<option value="">请选择模板</option>';
                            response.data.forEach(function(template) {
                                options += '<option value="' + template.path + '">' + template.name + '</option>';
                            });
                            $('#wordTemplateSelect').html(options);
                            if (callback) callback();
                        } else {
                            Toastr.error('加载Word模板列表失败');
                        }
                    },
                    error: function() {
                        Toastr.error('加载Word模板列表失败');
                    }
                });
            },

            // 开始导出进程
            startExportProcess: function(ids) {
                Controller.progress.showExportProgressModal(ids);
            },

            // 开始Excel模板导出进程
            startExcelTemplateExportProcess: function(ids, templatePath, templateName, filenameFields) {
                Controller.progress.showExcelTemplateProgressModal(ids, templatePath, templateName, filenameFields);
            },

            // 开始Word模板导出进程
            startWordTemplateExportProcess: function(ids, templatePath, templateName, filenameFields) {
                Controller.progress.showWordTemplateProgressModal(ids, templatePath, templateName, filenameFields);
            },

            // 显示Excel模板上传模态框
            showExcelUploadModal: function() {
                $('#excelUploadModal').modal('show');
            },

            // 显示Word模板上传模态框
            showWordUploadModal: function() {
                $('#wordUploadModal').modal('show');
            },

            // 上传Excel模板文件
            uploadExcelTemplateFile: function() {
                var fileInput = $('#excelTemplateFile')[0];
                var templateName = $('#excelTemplateName').val();

                if (!fileInput.files.length) {
                    Toastr.error('请选择Excel模板文件');
                    return;
                }

                if (!templateName) {
                    Toastr.error('请输入模板名称');
                    return;
                }

                var formData = new FormData();
                formData.append('template_file', fileInput.files[0]);
                formData.append('template_name', templateName);

                var loading = Layer.load(2, {shade: [0.3, '#000']});

                $.ajax({
                    url: Fast.api.fixurl('resume/uploadExcelTemplate'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        Layer.close(loading);
                        if (response.code === 1) {
                            Toastr.success('Excel模板上传成功');
                            $('#excelUploadModal').modal('hide');
                            // 刷新模板列表
                            Controller.export.loadTemplateList();
                        } else {
                            Toastr.error(response.msg || 'Excel模板上传失败');
                        }
                    },
                    error: function() {
                        Layer.close(loading);
                        Toastr.error('Excel模板上传失败：服务器错误');
                    }
                });
            },

            // 上传Word模板文件
            uploadWordTemplateFile: function() {
                var fileInput = $('#wordTemplateFile')[0];
                var templateName = $('#wordTemplateName').val();

                if (!fileInput.files.length) {
                    Toastr.error('请选择Word模板文件');
                    return;
                }

                if (!templateName) {
                    Toastr.error('请输入模板名称');
                    return;
                }

                var formData = new FormData();
                formData.append('template_file', fileInput.files[0]);
                formData.append('template_name', templateName);

                var loading = Layer.load(2, {shade: [0.3, '#000']});

                $.ajax({
                    url: Fast.api.fixurl('resume/uploadWordTemplate'),
                    type: 'POST',
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        Layer.close(loading);
                        if (response.code === 1) {
                            Toastr.success('Word模板上传成功');
                            $('#wordUploadModal').modal('hide');
                            // 刷新模板列表
                            Controller.export.loadWordTemplateList();
                        } else {
                            Toastr.error(response.msg || 'Word模板上传失败');
                        }
                    },
                    error: function() {
                        Layer.close(loading);
                        Toastr.error('Word模板上传失败：服务器错误');
                    }
                });
            }
        },

        // 导入模块
        import: {
            // 显示导入模态框
            showImportModal: function() {
                // 显示批量上传模态框
                $('#batchUploadModal').modal('show');
            },

            // 导入文件
            importFiles: function() {
                var files = document.querySelectorAll('#importUploadList .upload-item');
                if (!files.length) {
                    Toastr.error('请选择或拖拽Excel文件到上传区域');
                    return;
                }
                Controller.import.uploadFiles(Array.from(files), function() {
                    $('#importModal').modal('hide');
                });
            },

            // 批量导入文件
            importBatchFiles: function() {
                var files = document.querySelectorAll('#batchUploadList .upload-item');
                if (!files.length) {
                    Toastr.error('请选择Excel文件');
                    return;
                }
                Controller.import.uploadFiles(Array.from(files), function() {
                    $('#batchUploadModal').modal('hide');
                });
            },

            // 上传多个文件
            uploadFiles: function(fileItems, callback) {
                var completed = 0;
                var total = fileItems.length;
                var successCount = 0;
                var failCount = 0;

                fileItems.forEach(function(item, index) {
                    var file = item.file;
                    var progressBar = item.querySelector('.progress-bar');

                    var formData = new FormData();
                    formData.append('excel_file', file);

                    $.ajax({
                        url: Fast.api.fixurl('admin/resume/import'),
                        type: 'POST',
                        data: formData,
                        processData: false,
                        contentType: false,
                        xhr: function() {
                            var xhr = new window.XMLHttpRequest();
                            xhr.upload.addEventListener('progress', function(e) {
                                if (e.lengthComputable) {
                                    var percent = Math.round((e.loaded / e.total) * 100);
                                    progressBar.style.width = percent + '%';
                                }
                            });
                            return xhr;
                        },
                        success: function(res) {
                            completed++;
                            if (res.code === 1) {
                                progressBar.classList.add('progress-bar-success');
                                successCount++;
                                Toastr.success('文件 ' + file.name + ' 导入成功');
                            } else {
                                progressBar.classList.add('progress-bar-danger');
                                failCount++;
                                Toastr.error('文件 ' + file.name + ' ：' + res.msg);
                            }

                            if (completed === total) {
                                if (callback) callback();
                                if (successCount > 0) {
                                    Toastr.success('成功导入 ' + successCount + ' 个文件');
                                }
                                setTimeout(function() {
                                    if (res.code === 1) {
                                        window.location.href = res.data.url;
                                    }
                                }, 2000);
                            }
                        },
                        error: function(xhr) {
                            completed++;
                            progressBar.classList.add('progress-bar-danger');
                            failCount++;
                            Toastr.error('文件 ' + file.name + ' 导入失败：服务器错误');
                            if (completed === total && callback) {
                                callback();
                            }
                        }
                    });
                });
            }
        },

        // 批量操作模块
        batch: {
            // 显示批量复制模态框
            showBatchCopyModal: function() {
                var ids = Table.api.selectedids($("#table"));
                if (ids.length === 0) {
                    Toastr.error('请先选择要复制的简历');
                    return;
                }

                $('#batchCopyModal').modal('show');
            },

            // 批量复制简历
            batchCopyResumes: function(ids, copyCount) {
                // 如果没有传入参数，从界面获取
                if (!ids) {
                    ids = Table.api.selectedids($("#table"));
                    if (ids.length === 0) {
                        Toastr.error('请先选择要复制的简历');
                        return;
                    }
                }

                if (!copyCount) {
                    copyCount = parseInt($('#copyCount').val()) || 1;
                    if (copyCount < 1 || copyCount > 100) {
                        Toastr.error('复制数量必须在1-100之间');
                        return;
                    }
                }

                var loading = Layer.load(2, {shade: [0.3, '#000']});

                $.ajax({
                    url: Fast.api.fixurl('resume/batchCopy'),
                    type: 'POST',
                    data: {
                        ids: ids.join(','),
                        count: copyCount
                    },
                    success: function(response) {
                        Layer.close(loading);
                        if (response.code === 1) {
                            Toastr.success('批量复制成功，共复制 ' + response.data.count + ' 条记录');
                            $('#batchCopyModal').modal('hide');
                            $("#table").bootstrapTable('refresh');
                        } else {
                            Toastr.error(response.msg || '批量复制失败');
                        }
                    },
                    error: function() {
                        Layer.close(loading);
                        Toastr.error('服务器错误');
                    }
                });
            }
        },

        // 进度模块
        progress: {
            // 显示导出进度模态框
            showExportProgressModal: function(ids) {
                // 重置模态框状态
                Controller.progress.resetExportProgressModal();

                // 更新模态框标题
                $('#exportProgressModalLabel').html('<i class="fa fa-file-excel-o text-success"></i> 导出Excel汇总进度');

                // 显示模态框
                $('#exportProgressModal').modal({
                    backdrop: 'static',
                    keyboard: false
                });

                // 开始导出流程
                Controller.progress.startExportProcess(ids);
            },

            // 显示Excel模板导出进度模态框
            showExcelTemplateProgressModal: function(ids, templatePath, templateName, filenameFields) {
                // 重置模态框状态
                Controller.progress.resetExportProgressModal();

                // 更新模态框标题
                $('#exportProgressModalLabel').html('<i class="fa fa-file-excel-o text-primary"></i> 导出Excel模板进度');

                // 显示模态框
                $('#exportProgressModal').modal({
                    backdrop: 'static',
                    keyboard: false
                });

                // 开始Excel模板导出流程
                Controller.progress.startExcelTemplateExportProcess(ids, templatePath, templateName, filenameFields);
            },

            // 显示Word模板导出进度模态框
            showWordTemplateProgressModal: function(ids, templatePath, templateName, filenameFields) {
                // 重置模态框状态
                Controller.progress.resetExportProgressModal();

                // 更新模态框标题
                $('#exportProgressModalLabel').html('<i class="fa fa-file-word-o text-danger"></i> 导出Word模板进度');

                // 显示模态框
                $('#exportProgressModal').modal({
                    backdrop: 'static',
                    keyboard: false
                });

                // 开始Word模板导出流程
                Controller.progress.startWordTemplateExportProcess(ids, templatePath, templateName, filenameFields);
            },

            // 重置导出进度模态框
            resetExportProgressModal: function() {
                $('#exportSteps').empty();
                $('#overallProgress').css('width', '0%').removeClass('progress-bar-success progress-bar-danger').addClass('progress-bar-info');
                $('#overallProgressText').text('0%');
                $('#totalResumes').text('0');
                $('#processedResumes').text('0');
                $('#processedImages').text('0');
                $('#elapsedTime').text('0s');
                $('#exportCloseBtn').attr('disabled', true).removeClass('btn-primary').addClass('btn-default');
                $('#exportDownloadBtn').hide();
            },

            // 添加导出步骤
            addExportStep: function(stepId, stepText, stepIcon, status) {
                var statusClass = {
                    'pending': 'text-muted',
                    'processing': 'text-primary',
                    'completed': 'text-success',
                    'error': 'text-danger'
                };

                var statusIcon = {
                    'pending': 'fa-clock-o',
                    'processing': 'fa-spinner fa-spin',
                    'completed': 'fa-check',
                    'error': 'fa-times'
                };

                var html = '<div class="export-step" id="' + stepId + '">';
                html += '<div class="step-header">';
                html += '<i class="fa ' + stepIcon + ' step-icon"></i>';
                html += '<span class="step-text">' + stepText + '</span>';
                html += '<i class="fa ' + statusIcon[status] + ' step-status ' + statusClass[status] + '"></i>';
                html += '</div>';
                html += '<div class="step-message" style="display: none;"></div>';
                html += '</div>';

                $('#exportSteps').append(html);
            },

            // 更新导出步骤
            updateExportStep: function(stepId, status, message) {
                var $step = $('#' + stepId);
                var $statusIcon = $step.find('.step-status');
                var $message = $step.find('.step-message');

                var statusClass = {
                    'pending': 'text-muted',
                    'processing': 'text-primary',
                    'completed': 'text-success',
                    'error': 'text-danger'
                };

                var statusIcon = {
                    'pending': 'fa-clock-o',
                    'processing': 'fa-spinner fa-spin',
                    'completed': 'fa-check',
                    'error': 'fa-times'
                };

                // 更新状态图标
                $statusIcon.removeClass('fa-clock-o fa-spinner fa-spin fa-check fa-times text-muted text-primary text-success text-danger');
                $statusIcon.addClass('fa ' + statusIcon[status] + ' ' + statusClass[status]);

                // 显示消息
                if (message) {
                    $message.text(message).show();
                }

                // 滚动到当前步骤
                var $container = $('#exportSteps');
                var stepTop = $step.position().top;
                $container.scrollTop(stepTop);
            },

            // 开始导出流程
            startExportProcess: function(ids) {
                var startTime = Date.now();
                var totalResumes = ids.length;

                // 更新总简历数
                $('#totalResumes').text(totalResumes);

                // 创建导出步骤
                var steps = [
                    { id: 'step1', text: '初始化导出参数', icon: 'fa-cog' },
                    { id: 'step2', text: '验证用户权限', icon: 'fa-shield' },
                    { id: 'step3', text: '查询简历数据', icon: 'fa-database' },
                    { id: 'step4', text: '处理基本信息', icon: 'fa-user' },
                    { id: 'step5', text: '处理联系人信息', icon: 'fa-phone' },
                    { id: 'step6', text: '处理教育经历', icon: 'fa-graduation-cap' },
                    { id: 'step7', text: '处理工作经历', icon: 'fa-briefcase' },
                    { id: 'step8', text: '处理图片文件', icon: 'fa-image' },
                    { id: 'step9', text: '生成Excel文件', icon: 'fa-file-excel-o' },
                    { id: 'step10', text: '保存文件', icon: 'fa-save' },
                    { id: 'step11', text: '完成导出', icon: 'fa-check' }
                ];

                // 添加步骤到界面
                steps.forEach(function(step) {
                    Controller.progress.addExportStep(step.id, step.text, step.icon, 'pending');
                });

                // 开始执行步骤
                Controller.progress.executeExportSteps(ids, steps, startTime);
            },

            // 执行导出步骤
            executeExportSteps: function(ids, steps, startTime) {
                var currentStep = 0;
                var totalSteps = steps.length;

                function executeNextStep() {
                    if (currentStep >= totalSteps) {
                        return;
                    }

                    var step = steps[currentStep];
                    Controller.progress.updateExportStep(step.id, 'processing');

                    // 更新总体进度
                    var progress = Math.round((currentStep / totalSteps) * 100);
                    $('#overallProgress').css('width', progress + '%');
                    $('#overallProgressText').text(progress + '%');

                    // 更新耗时
                    var elapsed = Math.round((Date.now() - startTime) / 1000);
                    $('#elapsedTime').text(elapsed + 's');

                    // 根据步骤类型执行不同的处理逻辑
                    Controller.progress.executeExportStepLogic(currentStep, ids, function(success, message) {
                        if (success) {
                            // 根据步骤更新相关统计
                            Controller.progress.updateExportStepStatistics(currentStep, ids.length);
                            Controller.progress.updateExportStep(step.id, 'completed', message);
                        } else {
                            Controller.progress.updateExportStep(step.id, 'error', message || '步骤执行失败');
                        }

                        currentStep++;

                        if (currentStep < totalSteps) {
                            setTimeout(executeNextStep, Controller.progress.getExportStepDelay(currentStep, ids.length));
                        } else {
                            // 所有步骤完成，开始实际下载
                            Controller.progress.completeExport(ids, startTime);
                        }
                    });
                }

                executeNextStep();
            },

            // 执行导出具体步骤逻辑
            executeExportStepLogic: function(stepIndex, ids, callback) {
                var stepDelay = Controller.progress.getExportStepDelay(stepIndex, ids.length);

                switch(stepIndex) {
                    case 0: // 初始化导出参数
                        setTimeout(function() {
                            callback(true, '导出参数初始化完成');
                        }, stepDelay);
                        break;

                    case 1: // 验证用户权限
                        $.ajax({
                            url: Fast.api.fixurl('resume/exportProgress'),
                            type: 'GET',
                            data: { step: 1, total: ids.length },
                            success: function(response) {
                                callback(true, '权限验证通过');
                            },
                            error: function() {
                                callback(false, '权限验证失败');
                            }
                        });
                        break;

                    case 2: // 查询简历数据
                        setTimeout(function() {
                            callback(true, '成功查询 ' + ids.length + ' 条简历数据');
                        }, stepDelay);
                        break;

                    case 3: // 处理基本信息
                        setTimeout(function() {
                            callback(true, '处理了 ' + ids.length + ' 条基本信息');
                        }, stepDelay);
                        break;

                    case 4: // 处理联系人信息
                        setTimeout(function() {
                            var contactCount = ids.length * 2; // 假设每个简历2个联系人
                            callback(true, '处理了 ' + contactCount + ' 条联系人信息');
                        }, stepDelay);
                        break;

                    case 5: // 处理教育经历
                        setTimeout(function() {
                            var eduCount = ids.length * 3; // 假设每个简历3条教育经历
                            callback(true, '处理了 ' + eduCount + ' 条教育经历');
                        }, stepDelay);
                        break;

                    case 6: // 处理工作经历
                        setTimeout(function() {
                            var jobCount = ids.length * 4; // 假设每个简历4条工作经历
                            callback(true, '处理了 ' + jobCount + ' 条工作经历');
                        }, stepDelay);
                        break;

                    case 7: // 处理图片文件
                        setTimeout(function() {
                            var imageCount = ids.length * 7; // 假设每个简历7张图片
                            callback(true, '处理了 ' + imageCount + ' 张图片');
                        }, stepDelay);
                        break;

                    case 8: // 生成Excel文件
                        setTimeout(function() {
                            callback(true, 'Excel文件生成完成');
                        }, stepDelay);
                        break;

                    case 9: // 保存文件
                        setTimeout(function() {
                            callback(true, '文件保存完成');
                        }, stepDelay);
                        break;

                    case 10: // 完成导出
                        setTimeout(function() {
                            callback(true, '导出流程全部完成');
                        }, stepDelay);
                        break;

                    default:
                        setTimeout(function() {
                            callback(true, '步骤执行完成');
                        }, stepDelay);
                }
            },

            // 获取导出步骤延迟时间
            getExportStepDelay: function(stepIndex, resumeCount) {
                var baseDelay = 500;
                var delays = [300, 200, 800, 600, 400, 500, 600, 1000, 1200, 800, 300];

                // 根据简历数量调整延迟
                var multiplier = Math.min(resumeCount / 10, 3);
                return (delays[stepIndex] || baseDelay) * multiplier;
            },

            // 更新导出步骤统计信息
            updateExportStepStatistics: function(stepIndex, totalResumes) {
                switch(stepIndex) {
                    case 2: // 查询简历数据
                        $('#processedResumes').text(Math.round(totalResumes * 0.1));
                        break;
                    case 3: // 处理基本信息
                        $('#processedResumes').text(Math.round(totalResumes * 0.3));
                        break;
                    case 4: // 处理联系人信息
                        $('#processedResumes').text(Math.round(totalResumes * 0.5));
                        break;
                    case 5: // 处理教育经历
                        $('#processedResumes').text(Math.round(totalResumes * 0.7));
                        break;
                    case 6: // 处理工作经历
                        $('#processedResumes').text(Math.round(totalResumes * 0.9));
                        break;
                    case 7: // 处理图片文件
                        $('#processedImages').text(Math.round(totalResumes * 7)); // 假设每个简历7张图片
                        $('#processedResumes').text(totalResumes);
                        break;
                    case 8: // 生成Excel文件
                        $('#processedImages').text(Math.round(totalResumes * 7));
                        break;
                    case 9: // 保存文件
                        $('#processedImages').text(Math.round(totalResumes * 7));
                        break;
                }
            },

            // 完成导出
            completeExport: function(ids, startTime) {
                // 更新最终进度
                $('#overallProgress').css('width', '100%').removeClass('progress-bar-info').addClass('progress-bar-success');
                $('#overallProgressText').text('100%');

                var finalElapsed = Math.round((Date.now() - startTime) / 1000);
                $('#elapsedTime').text(finalElapsed + 's');

                // 启用关闭按钮和下载按钮
                $('#exportCloseBtn').removeAttr('disabled').removeClass('btn-default').addClass('btn-primary');
                $('#exportDownloadBtn').show();

                // 更新模态框标题
                $('#exportProgressModalLabel').html('<i class="fa fa-check-circle text-success"></i> 导出完成');

                // 设置下载按钮点击事件
                $('#exportDownloadBtn').off('click').on('click', function() {
                    Controller.progress.downloadExportFile(ids);
                });

                // 显示成功消息
                Toastr.success('导出完成！共处理 ' + ids.length + ' 条简历数据，耗时 ' + finalElapsed + ' 秒');

                // 播放完成音效
                Controller.progress.playCompletionSound();
            },

            // 下载导出文件
            downloadExportFile: function(ids) {
                var $btn = $('#exportDownloadBtn');
                var originalText = $btn.html();
                $btn.html('<i class="fa fa-spinner fa-spin"></i> 正在下载...').attr('disabled', true);

                // 创建表单提交下载
                var form = document.createElement('form');
                form.method = 'POST';
                form.action = Fast.api.fixurl('resume/exportexcel');
                form.style.display = 'none';

                var idsInput = document.createElement('input');
                idsInput.type = 'hidden';
                idsInput.name = 'ids';
                idsInput.value = ids.join(',');
                form.appendChild(idsInput);

                document.body.appendChild(form);
                form.submit();
                document.body.removeChild(form);

                // 延迟恢复按钮状态
                setTimeout(function() {
                    $btn.html(originalText).removeAttr('disabled');
                    $('#exportProgressModal').modal('hide');
                }, 2000);
            },

            // 播放完成音效
            playCompletionSound: function() {
                try {
                    if (window.AudioContext || window.webkitAudioContext) {
                        var audioContext = new (window.AudioContext || window.webkitAudioContext)();
                        var oscillator = audioContext.createOscillator();
                        var gainNode = audioContext.createGain();

                        oscillator.connect(gainNode);
                        gainNode.connect(audioContext.destination);

                        oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                        oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
                        oscillator.frequency.setValueAtTime(800, audioContext.currentTime + 0.2);

                        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
                        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

                        oscillator.start(audioContext.currentTime);
                        oscillator.stop(audioContext.currentTime + 0.3);
                    }
                } catch(e) {
                    console.log('音效播放不支持或失败，已忽略');
                }
            },

            // Excel模板导出流程（简化版，主要逻辑类似）
            startExcelTemplateExportProcess: function(ids, templatePath, templateName, filenameFields) {
                var startTime = Date.now();
                $('#totalResumes').text(ids.length);

                var steps = [
                    { id: 'step1', text: '初始化导出参数', icon: 'fa-cog' },
                    { id: 'step2', text: '验证用户权限', icon: 'fa-shield' },
                    { id: 'step3', text: '加载Excel模板文件', icon: 'fa-file-excel-o' },
                    { id: 'step4', text: '解析模板结构', icon: 'fa-sitemap' },
                    { id: 'step5', text: '查询简历数据', icon: 'fa-database' },
                    { id: 'step6', text: '处理基本信息', icon: 'fa-user' },
                    { id: 'step7', text: '处理联系人信息', icon: 'fa-phone' },
                    { id: 'step8', text: '处理教育经历', icon: 'fa-graduation-cap' },
                    { id: 'step9', text: '处理工作经历', icon: 'fa-briefcase' },
                    { id: 'step10', text: '处理图片文件', icon: 'fa-image' },
                    { id: 'step11', text: '填充模板数据', icon: 'fa-edit' },
                    { id: 'step12', text: '生成文件名', icon: 'fa-tag' },
                    { id: 'step13', text: '完成导出', icon: 'fa-check' }
                ];

                steps.forEach(function(step) {
                    Controller.progress.addExportStep(step.id, step.text, step.icon, 'pending');
                });

                Controller.progress.executeExcelTemplateSteps(ids, templatePath, templateName, filenameFields, steps, startTime);
            },

            // Word模板导出流程（简化版，主要逻辑类似）
            startWordTemplateExportProcess: function(ids, templatePath, templateName, filenameFields) {
                var startTime = Date.now();
                $('#totalResumes').text(ids.length);

                var steps = [
                    { id: 'step1', text: '初始化导出参数', icon: 'fa-cog' },
                    { id: 'step2', text: '验证用户权限', icon: 'fa-shield' },
                    { id: 'step3', text: '加载Word模板文件', icon: 'fa-file-word-o' },
                    { id: 'step4', text: '解析模板结构', icon: 'fa-sitemap' },
                    { id: 'step5', text: '查询简历数据', icon: 'fa-database' },
                    { id: 'step6', text: '处理基本信息', icon: 'fa-user' },
                    { id: 'step7', text: '处理联系人信息', icon: 'fa-phone' },
                    { id: 'step8', text: '处理教育经历', icon: 'fa-graduation-cap' },
                    { id: 'step9', text: '处理工作经历', icon: 'fa-briefcase' },
                    { id: 'step10', text: '处理图片文件', icon: 'fa-image' },
                    { id: 'step11', text: '填充模板数据', icon: 'fa-edit' },
                    { id: 'step12', text: '生成文件名', icon: 'fa-tag' },
                    { id: 'step13', text: '保存Word文件', icon: 'fa-save' },
                    { id: 'step14', text: '完成导出', icon: 'fa-check' }
                ];

                steps.forEach(function(step) {
                    Controller.progress.addExportStep(step.id, step.text, step.icon, 'pending');
                });

                Controller.progress.executeWordTemplateSteps(ids, templatePath, templateName, filenameFields, steps, startTime);
            },

            // 执行Excel模板步骤（简化实现）
            executeExcelTemplateSteps: function(ids, templatePath, templateName, filenameFields, steps, startTime) {
                var currentStep = 0;
                var totalSteps = steps.length;

                function executeNextStep() {
                    if (currentStep >= totalSteps) {
                        return;
                    }

                    var step = steps[currentStep];
                    Controller.progress.updateExportStep(step.id, 'processing');

                    var progress = Math.round((currentStep / totalSteps) * 100);
                    $('#overallProgress').css('width', progress + '%');
                    $('#overallProgressText').text(progress + '%');

                    var elapsed = Math.round((Date.now() - startTime) / 1000);
                    $('#elapsedTime').text(elapsed + 's');

                    // 简化的步骤执行
                    setTimeout(function() {
                        Controller.progress.updateExportStep(step.id, 'completed', '步骤完成');
                        currentStep++;

                        if (currentStep < totalSteps) {
                            setTimeout(executeNextStep, 500);
                        } else {
                            Controller.progress.completeExcelTemplateExport(ids, templatePath, templateName, filenameFields, startTime);
                        }
                    }, 800);
                }

                executeNextStep();
            },

            // 执行Word模板步骤（简化实现）
            executeWordTemplateSteps: function(ids, templatePath, templateName, filenameFields, steps, startTime) {
                var currentStep = 0;
                var totalSteps = steps.length;

                function executeNextStep() {
                    if (currentStep >= totalSteps) {
                        return;
                    }

                    var step = steps[currentStep];
                    Controller.progress.updateExportStep(step.id, 'processing');

                    var progress = Math.round((currentStep / totalSteps) * 100);
                    $('#overallProgress').css('width', progress + '%');
                    $('#overallProgressText').text(progress + '%');

                    var elapsed = Math.round((Date.now() - startTime) / 1000);
                    $('#elapsedTime').text(elapsed + 's');

                    // 简化的步骤执行
                    setTimeout(function() {
                        Controller.progress.updateExportStep(step.id, 'completed', '步骤完成');
                        currentStep++;

                        if (currentStep < totalSteps) {
                            setTimeout(executeNextStep, 500);
                        } else {
                            Controller.progress.completeWordTemplateExport(ids, templatePath, templateName, filenameFields, startTime);
                        }
                    }, 800);
                }

                executeNextStep();
            },

            // 完成Excel模板导出
            completeExcelTemplateExport: function(ids, templatePath, templateName, filenameFields, startTime) {
                $('#overallProgress').css('width', '100%').removeClass('progress-bar-info').addClass('progress-bar-success');
                $('#overallProgressText').text('100%');

                var finalElapsed = Math.round((Date.now() - startTime) / 1000);
                $('#elapsedTime').text(finalElapsed + 's');

                $('#exportCloseBtn').removeAttr('disabled').removeClass('btn-default').addClass('btn-primary');
                $('#exportDownloadBtn').show();

                $('#exportProgressModalLabel').html('<i class="fa fa-check-circle text-success"></i> Excel模板导出完成');

                $('#exportDownloadBtn').off('click').on('click', function() {
                    Controller.progress.downloadTemplateFile(ids, templatePath, filenameFields, 'excel');
                });

                Toastr.success('Excel模板导出完成！使用模板: ' + templateName + '，共处理 ' + ids.length + ' 条简历数据');
                Controller.progress.playCompletionSound();
            },

            // 完成Word模板导出
            completeWordTemplateExport: function(ids, templatePath, templateName, filenameFields, startTime) {
                $('#overallProgress').css('width', '100%').removeClass('progress-bar-info').addClass('progress-bar-success');
                $('#overallProgressText').text('100%');

                var finalElapsed = Math.round((Date.now() - startTime) / 1000);
                $('#elapsedTime').text(finalElapsed + 's');

                $('#exportCloseBtn').removeAttr('disabled').removeClass('btn-default').addClass('btn-primary');
                $('#exportDownloadBtn').show();

                $('#exportProgressModalLabel').html('<i class="fa fa-check-circle text-success"></i> Word模板导出完成');

                $('#exportDownloadBtn').off('click').on('click', function() {
                    Controller.progress.downloadTemplateFile(ids, templatePath, filenameFields, 'word');
                });

                Toastr.success('Word模板导出完成！使用模板: ' + templateName + '，共处理 ' + ids.length + ' 条简历数据');
                Controller.progress.playCompletionSound();
            },

            // 下载模板文件
            downloadTemplateFile: function(ids, templatePath, filenameFields, type) {
                var $btn = $('#exportDownloadBtn');
                var originalText = $btn.html();
                $btn.html('<i class="fa fa-spinner fa-spin"></i> 正在下载...').attr('disabled', true);

                var form = document.createElement('form');
                form.method = 'POST';
                form.action = Fast.api.fixurl('resume/exportTo' + (type === 'word' ? 'Word' : '') + 'Template');
                form.style.display = 'none';

                var idsInput = document.createElement('input');
                idsInput.type = 'hidden';
                idsInput.name = 'ids';
                idsInput.value = ids.join(',');
                form.appendChild(idsInput);

                var templateInput = document.createElement('input');
                templateInput.type = 'hidden';
                templateInput.name = 'template';
                templateInput.value = templatePath;
                form.appendChild(templateInput);

                var filenameFieldsInput = document.createElement('input');
                filenameFieldsInput.type = 'hidden';
                filenameFieldsInput.name = 'filename_fields';
                filenameFieldsInput.value = filenameFields.join(',');
                form.appendChild(filenameFieldsInput);

                document.body.appendChild(form);
                form.submit();
                document.body.removeChild(form);

                setTimeout(function() {
                    $btn.html(originalText).removeAttr('disabled');
                    $('#exportProgressModal').modal('hide');
                }, 2000);
            }
        }
    };
    return Controller;
});
