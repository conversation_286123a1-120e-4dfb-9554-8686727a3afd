<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{$job.title} - {$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #34a853;
            --accent-color: #ea4335;
            --light-bg: #f8f9fa;
            --dark-bg: #202124;
            --text-primary: #202124;
            --text-secondary: #5f6368;
            --border-radius: 16px;
            --box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --gradient-primary: linear-gradient(135deg, #1a73e8, #4285f4);
            --gradient-success: linear-gradient(135deg, #34a853, #0f9d58);
        }
        
        body {
            background-color: var(--light-bg);
            color: var(--text-primary);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
        }

        .navbar {
            background-color: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: var(--box-shadow);
            padding: 1rem 0;
            transition: var(--transition);
        }

        .navbar.scrolled {
            padding: 0.5rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            letter-spacing: -0.5px;
        }

        .navbar-brand i {
            color: var(--primary-color);
            margin-right: 0.5rem;
            font-size: 1.8rem;
        }

        .nav-link {
            color: var(--text-primary) !important;
            font-weight: 500;
            padding: 0.75rem 1.25rem !important;
            margin: 0 0.25rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
            position: relative;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 2px;
            background-color: var(--primary-color);
            transition: var(--transition);
        }

        .nav-link:hover::after {
            width: 80%;
        }

        .nav-link:hover {
            background-color: rgba(26, 115, 232, 0.08);
            color: var(--primary-color) !important;
        }

        .nav-link.active {
            background-color: var(--primary-color) !important;
            color: white !important;
        }

        .nav-link.active::after {
            display: none;
        }

        .btn {
            border-radius: var(--border-radius);
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .btn:hover::after {
            width: 300px;
            height: 300px;
        }

        .btn-primary {
            background: var(--gradient-primary);
            border: none;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(26, 115, 232, 0.3);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--gradient-primary);
            color: white;
            border-color: transparent;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(26, 115, 232, 0.3);
        }

        .card {
            border: none;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            border-radius: 10px;
            margin-bottom: 1.5rem;
        }

        .card-body {
            padding: 1.5rem;
        }

        .job-title {
            font-size: 1.75rem;
            font-weight: 600;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .badge {
            padding: 0.5em 1em;
            font-weight: 500;
            margin-right: 0.5rem;
        }

        .job-info {
            display: flex;
            align-items: center;
            margin-bottom: 0.5rem;
            color: var(--primary-color);
        }

        .job-info i {
            width: 20px;
            margin-right: 0.5rem;
            color: var(--secondary-color);
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--secondary-color);
        }

        .similar-job {
            padding: 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
        }

        .similar-job:hover {
            background-color: #f8f9fa;
        }

        .similar-job h6 {
            margin-bottom: 0.5rem;
        }

        .similar-job a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .similar-job a:hover {
            color: var(--secondary-color);
        }

        .footer {
            background-color: var(--dark-bg);
            color: white;
            padding: 3rem 0;
            margin-top: 3rem;
        }

        .footer h5 {
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        .footer a {
            color: white;
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer a:hover {
            color: var(--secondary-color);
        }

        .social-links a {
            margin-right: 1rem;
            font-size: 1.5rem;
        }
    </style>
</head>

<body>
    <!-- 顶部导航栏 -->
<nav class="navbar navbar-expand-lg navbar-light fixed-top">
    <div class="container">
        <a class="navbar-brand" href="{:url('index/index/index')}">
            <i class="fas fa-briefcase"></i>{$site.name|htmlentities}
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='Index'}active{/eq}" href="{:url('index/index/index')}">
                        <i class="fas fa-home"></i>首页
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='Job'}active{/eq}" href="{:url('index/job/list')}">
                        <i class="fas fa-list me-1"></i>职位列表
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='News'}active{/eq}" href="{:url('index/resume/list')}">
                        <i class="fas fa-newspaper me-1"></i>我的简历
                    </a>
                </li>
            </ul>
            <div class="navbar-buttons">
                {if $user}
                <!-- 已登录状态 -->
                <a href="{:url('index/user/index')}" class="btn btn-outline-primary me-2">
                    <i class="fas fa-user me-1"></i>个人中心
                </a>
                <a href="javascript:;" class="btn btn-outline-danger" id="btn-logout">
                    <i class="fas fa-sign-out-alt me-1"></i>退出登录
                </a>
                <!-- 退出登录表单 -->
                <form id="logout-form" action="{:url('index/user/logout')}" method="post" style="display: none;">
                    {:token()}
                </form>
                {else}
                <!-- 未登录状态 -->
                <a href="{:url('index/user/login')}" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-1"></i>登录/注册
                </a>
                {/if}
            </div>
        </div>
    </div>
</nav>

    <!-- 职位详情 -->
    <div class="container py-5 mt-5">
        <div class="row">
            <!-- 左侧职位信息 -->
            <div class="col-md-8">
                <div class="card">
                    <div class="card-body">
                        <h1 class="job-title">{$job.title}</h1>
                        <div class="d-flex align-items-center mb-4">
                            <span class="badge bg-primary">{$job.job_code}</span>
                            <span class="badge bg-info">{$job.category}</span>
                            {if $job.gender_requirement}
                            <span class="badge bg-secondary">{$job.gender_requirement}</span>
                            {/if}
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <div class="job-info">
                                    <i class="fas fa-yen-sign"></i>
                                    <span>薪资范围：{$job.salary_range}</span>
                                </div>
                                <div class="job-info">
                                    <i class="fas fa-graduation-cap"></i>
                                    <span>学历要求：{$job.education_requirement|default='不限'}</span>
                                </div>
                                <div class="job-info">
                                    <i class="fas fa-clock"></i>
                                    <span>更新时间：{:date('Y-m-d H:i', $job['update_time'])}</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="job-info">
                                    <i class="fas fa-briefcase"></i>
                                    <span>经验要求：{$job.experience_requirement|default='不限'}</span>
                                </div>
                                <div class="job-info">
                                    <i class="fas fa-clock"></i>
                                    <span>工作时间：{$job.working_hours|default='标准工作时间'}</span>
                                </div>
                                <div class="job-info">
                                    <i class="fas fa-user"></i>
                                    <span>年龄要求：{$job.age_requirement|default='不限'}</span>
                                </div>
                            </div>
                        </div>

                        <h4 class="section-title">
                            <i class="fas fa-file-alt me-2"></i>职位描述
                        </h4>
                        <div class="job-description mb-4">
                            {$job.job_description|nl2br}
                        </div>

                        {if $job.job_summary}
                        <h4 class="section-title">
                            <i class="fas fa-list me-2"></i>岗位汇总
                        </h4>
                        <div class="job-summary mb-4">
                            {$job.job_summary|nl2br}
                        </div>
                        {/if}

                        {if $job.accommodation}
                        <h4 class="section-title">
                            <i class="fas fa-home me-2"></i>食宿情况
                        </h4>
                        <div class="accommodation mb-4">
                            {$job.accommodation|nl2br}
                        </div>
                        {/if}

                        {if $job.remark}
                        <h4 class="section-title">
                            <i class="fas fa-info-circle me-2"></i>备注
                        </h4>
                        <div class="remark mb-4">
                            {$job.remark|nl2br}
                        </div>
                        {/if}

                        {if $job.total_fee}
                        <h4 class="section-title">
                            <i class="fas fa-money-bill-wave me-2"></i>总费用
                        </h4>
                        <div class="total-fee mb-4">
                            {$job.total_fee}
                        </div>
                        {/if}
                    </div>
                </div>
            </div>

            <!-- 右侧操作栏 -->
            <div class="col-md-4">
                <div class="card">
                    <div class="card-body">
                        <h5 class="section-title">
                            <i class="fas fa-paper-plane me-2"></i>申请职位
                        </h5>
                        <div class="text-center mb-4">
                            <div class="job-info justify-content-center mb-3">
                                <i class="fas fa-users"></i>
                                <span>已有 {$job.resume_count|default='0'} 人申请</span>
                            </div>
                            <a href="{:url('index/resume/apply', ['job_id' => $job.id, 'job_name' => urlencode($job.title)])}" class="btn btn-primary btn-lg w-100">
                                <i class="fas fa-paper-plane me-2"></i>立即申请
                            </a>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-body">
                        <h5 class="section-title">
                            <i class="fas fa-thumbs-up me-2"></i>相似职位推荐
                        </h5>
                        {volist name="similarJobs" id="similar"}
                        <div class="similar-job">
                            <h6>
                                <a href="{:url('index/job/detail', ['id' => $similar.id])}">
                                    <i class="fas fa-angle-right me-2"></i>{$similar.title}
                                </a>
                            </h6>
                            <div class="job-info">
                                <i class="fas fa-building"></i>
                                <span>{$similar.company}</span>
                            </div>
                            <div class="job-info">
                                <i class="fas fa-yen-sign"></i>
                                <span>{$similar.salary_range}</span>
                            </div>
                        </div>
                        {/volist}
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- 底部信息 -->
<footer class="footer bg-dark text-light py-4">
    <div class="container">
        <div class="row">
            <div class="col-md-4">
                <h5>联系我们</h5>
                <p>
                    <i class="fas fa-phone"></i> {$site.phone}<br>
                    <i class="fas fa-envelope"></i> {$site.email}<br>
                    <i class="fas fa-map-marker-alt"></i> {$site.address}
                </p>
            </div>
            <div class="col-md-4">
                <h5>快速链接</h5>
                <ul class="list-unstyled">
                    <li><a href="{:url('index/about/index')}" class="text-light">关于我们</a></li>
                    <li><a href="{:url('index/help/index')}" class="text-light">帮助中心</a></li>
                    <li><a href="{:url('index/privacy/index')}" class="text-light">隐私政策</a></li>
                </ul>
            </div>
            <div class="col-md-4">
                <h5>关注我们</h5>
                <div class="social-links">
                    <a href="javascript:;" class="text-light me-2" data-bs-toggle="modal" data-bs-target="#wechatModal"><i class="fab fa-weixin fa-2x"></i></a>
                    <a href="javascript:;" class="text-light me-2" data-bs-toggle="modal" data-bs-target="#weiboModal"><i class="fab fa-weibo fa-2x"></i></a>
                </div>
            </div>
        </div>
        <hr class="mt-4">
        <div class="text-center">
            <p>Copyright © {$site.name|htmlentities} {:date('Y',time())} 版权所有 
                <a href="https://beian.miit.gov.cn" target="_blank" class="text-light">{$site.beian|htmlentities}</a>
            </p>
        </div>
    </div>
</footer>

<!-- 微信弹窗 -->
<div class="modal fade" id="wechatModal" tabindex="-1" aria-labelledby="wechatModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="wechatModalLabel">{$site.wechat_title|default='微信公众号'}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img src="{$site.wechat_image}" alt="微信二维码" class="img-fluid mb-3" style="max-width: 200px;">
                <p class="mb-0">{$site.wechat_desc|default='扫描上方二维码关注我们的微信公众号，获取最新招聘信息和职场资讯。'}</p>
            </div>
        </div>
    </div>
</div>

<!-- 微博弹窗 -->
<div class="modal fade" id="weiboModal" tabindex="-1" aria-labelledby="weiboModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="weiboModalLabel">{$site.weibo_title|default='官方微博'}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{$site.weibo_desc|default='关注我们的官方微博，了解最新动态和招聘信息。'}</p>
                <div class="d-grid gap-2">
                    <a href="{$site.weibo_url}" target="_blank" class="btn btn-danger">
                        <i class="fab fa-weibo me-2"></i>访问官方微博
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script>
        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
        
        // 退出登录按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const logoutBtn = document.getElementById('btn-logout');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function() {
                    document.getElementById('logout-form').submit();
                });
            }
        });
    </script> 
</body>
</html> 