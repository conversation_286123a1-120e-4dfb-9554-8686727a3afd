<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>职位列表 - {$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #1a73e8;
            --secondary-color: #34a853;
            --accent-color: #ea4335;
            --light-bg: #f8f9fa;
            --dark-bg: #202124;
            --text-primary: #202124;
            --text-secondary: #5f6368;
            --border-radius: 16px;
            --box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            --gradient-primary: linear-gradient(135deg, #1a73e8, #4285f4);
            --gradient-success: linear-gradient(135deg, #34a853, #0f9d58);
        }
        
        body {
            background-color: var(--light-bg);
            color: var(--text-primary);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
        }

        .navbar {
            background-color: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: var(--box-shadow);
            padding: 1rem 0;
            transition: var(--transition);
        }

        .navbar.scrolled {
            padding: 0.5rem 0;
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
            font-size: 1.5rem;
            display: flex;
            align-items: center;
            letter-spacing: -0.5px;
        }

        .navbar-brand i {
            color: var(--primary-color);
            margin-right: 0.5rem;
            font-size: 1.8rem;
        }

        .nav-link {
            color: var(--text-primary) !important;
            font-weight: 500;
            padding: 0.75rem 1.25rem !important;
            margin: 0 0.25rem;
            border-radius: var(--border-radius);
            transition: var(--transition);
            position: relative;
        }

        .nav-link::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 2px;
            background-color: var(--primary-color);
            transition: var(--transition);
        }

        .nav-link:hover::after {
            width: 80%;
        }

        .nav-link:hover {
            background-color: rgba(26, 115, 232, 0.08);
            color: var(--primary-color) !important;
        }

        .nav-link.active {
            background-color: var(--primary-color) !important;
            color: white !important;
        }

        .nav-link.active::after {
            display: none;
        }

        .btn {
            border-radius: var(--border-radius);
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .btn::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background-color: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }

        .btn:hover::after {
            width: 300px;
            height: 300px;
        }

        .btn-primary {
            background: var(--gradient-primary);
            border: none;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(26, 115, 232, 0.3);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border: 2px solid var(--primary-color);
            background: transparent;
        }

        .btn-outline-primary:hover {
            background: var(--gradient-primary);
            color: white;
            border-color: transparent;
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(26, 115, 232, 0.3);
        }

        @media (max-width: 991.98px) {
            .navbar-collapse {
                background-color: rgba(255, 255, 255, 0.98);
                padding: 1rem;
                border-radius: var(--border-radius);
                box-shadow: var(--box-shadow);
                margin-top: 1rem;
                backdrop-filter: blur(10px);
            }

            .nav-link {
                padding: 1rem !important;
                margin: 0.5rem 0;
            }

            .navbar-buttons {
                margin-top: 1rem;
                display: flex;
                flex-direction: column;
                gap: 0.75rem;
            }

            .navbar-buttons .btn {
                width: 100%;
            }
        }

        /* 搜索区域样式 */
        .search-section {
            background: var(--gradient-primary);
            padding: 5rem 0;
            position: relative;
            z-index: 1;
            overflow: hidden;
            margin-top: 76px;
        }

        .search-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.1;
        }

        .search-section .card {
            border-radius: var(--border-radius);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        .search-section .form-control,
        .search-section .form-select {
            border-radius: var(--border-radius);
            padding: 1rem 1.25rem;
            border: 2px solid #e0e0e0;
            transition: var(--transition);
            font-size: 1rem;
        }

        .search-section .form-control:focus,
        .search-section .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 4px rgba(26, 115, 232, 0.1);
        }

        /* 筛选栏样式 */
        .filter-card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            background: white;
            margin-bottom: 1.5rem;
        }

        .filter-card .card-header {
            background: var(--gradient-primary);
            color: white;
            border-radius: var(--border-radius) var(--border-radius) 0 0;
            padding: 1rem 1.5rem;
            font-weight: 600;
        }

        .filter-card .card-body {
            padding: 1.5rem;
        }

        .filter-card .form-label {
            color: var(--text-secondary);
            font-weight: 500;
            margin-bottom: 0.5rem;
        }

        .filter-card .form-select {
            border-radius: var(--border-radius);
            padding: 0.75rem 1rem;
            border: 2px solid #e0e0e0;
            transition: var(--transition);
        }

        .filter-card .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(26, 115, 232, 0.1);
        }

        /* 职位卡片样式 */
        .job-card {
            border: none;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            transition: var(--transition);
            background: white;
            margin-bottom: 1.5rem;
            overflow: hidden;
        }

        .job-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
        }

        .job-card .card-body {
            padding: 1.5rem;
            position: relative;
            min-height: 200px;
        }

        .job-card .company-logo {
            width: 160px;
            height: 160px;
            object-fit: cover;
            border-radius: var(--border-radius);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: var(--transition);
            margin-right: 2rem;
        }

        .job-card .job-content {
            flex: 1;
            min-width: 0; /* 防止内容溢出 */
        }

        .job-card .job-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .job-card .job-info {
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .job-card .btn-detail {
            position: absolute;
            bottom: 1.5rem;
            right: 1.5rem;
        }

        .job-card:hover .company-logo {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .job-card .job-title a {
            color: var(--text-primary);
            text-decoration: none;
            transition: var(--transition);
        }

        .job-card .job-title a:hover {
            color: var(--primary-color);
            text-decoration: none;
        }

        .job-card .badge {
            padding: 0.5em 1em;
            font-weight: 500;
            border-radius: 30px;
            font-size: 0.85rem;
            letter-spacing: 0.3px;
        }

        .job-card .badge.bg-primary {
            background: var(--gradient-primary) !important;
        }

        .job-card .badge.bg-info {
            background: linear-gradient(135deg, #4285f4, #5c9ce6) !important;
        }

        .job-card .badge.bg-secondary {
            background: linear-gradient(135deg, #5f6368, #80868b) !important;
        }

        /* 分页样式 */
        .pagination {
            margin-top: 2rem;
            display: flex;
            justify-content: center;
            gap: 8px;
        }

        .pagination .page-link {
            border: none;
            color: var(--text-primary);
            padding: 0.6rem 0.5rem;
            margin: 0;
            border-radius: var(--border-radius);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
            z-index: 1;
            font-weight: 500;
            min-width: 42px;
            height: 42px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.95rem;
            transform-origin: center;
        }

        .pagination .page-link::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: var(--gradient-primary);
            opacity: 0;
            z-index: -1;
            transition: opacity 0.3s ease;
        }

        .pagination .page-link:hover {
            color: var(--primary-color);
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 5px 15px rgba(26, 115, 232, 0.2);
        }

        .pagination .page-link:hover::before {
            opacity: 0.1;
        }

        .pagination .page-item.active .page-link {
            background: var(--gradient-primary);
            color: white;
            font-weight: 600;
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 5px 15px rgba(26, 115, 232, 0.3);
            animation: pulseActive 2s infinite;
        }

        @keyframes pulseActive {
            0% {
                box-shadow: 0 5px 15px rgba(26, 115, 232, 0.3);
            }
            50% {
                box-shadow: 0 5px 20px rgba(26, 115, 232, 0.5);
            }
            100% {
                box-shadow: 0 5px 15px rgba(26, 115, 232, 0.3);
            }
        }

        .pagination .page-item.active .page-link::before {
            opacity: 1;
        }

        .pagination .page-item.disabled .page-link {
            color: var(--text-secondary);
            background: rgba(0,0,0,0.03);
            pointer-events: none;
            box-shadow: none;
            opacity: 0.6;
        }

        /* 前后按钮特殊样式 */
        .pagination .page-item:first-child .page-link,
        .pagination .page-item:last-child .page-link {
            min-width: auto;
            padding: 0.6rem 1rem;
            background-color: rgba(26, 115, 232, 0.08);
            font-size: 0.9rem;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
            width: auto;
            min-width: 80px;
        }

        .pagination .page-item:first-child .page-link i,
        .pagination .page-item:last-child .page-link i {
            font-size: 0.85rem;
            transition: transform 0.3s ease;
        }

        .pagination .page-item:first-child .page-link:hover i {
            transform: translateX(-3px);
        }

        .pagination .page-item:last-child .page-link:hover i {
            transform: translateX(3px);
        }

        .pagination .page-item:first-child .page-link:hover,
        .pagination .page-item:last-child .page-link:hover {
            background-color: rgba(26, 115, 232, 0.15);
        }

        /* 添加分页容器样式 */
        .pagination-container {
            margin-top: 3rem;
            margin-bottom: 2rem;
            position: relative;
            padding: 1.5rem 0;
        }

        .pagination-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 120px;
            height: 3px;
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
        }

        .pagination-container::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 2px;
            background: linear-gradient(90deg, transparent, var(--primary-color), transparent);
            opacity: 0.5;
        }

        /* 移动端响应式样式 */
        @media (max-width: 576px) {
            .pagination {
                gap: 5px;
            }
            
            .pagination .page-link {
                padding: 0.5rem;
                min-width: 36px;
                height: 36px;
                font-size: 0.85rem;
            }
            
            .pagination .page-item:first-child .page-link,
            .pagination .page-item:last-child .page-link {
                padding: 0.5rem 0.8rem;
            }
            
            .pagination-container {
                margin-top: 2rem;
                margin-bottom: 1.5rem;
                padding: 1rem 0;
            }
            
            .pagination-container::before {
                width: 80px;
                height: 2px;
            }
            
            .pagination-container::after {
                width: 40px;
                height: 1px;
            }
        }

        @media (max-width: 991.98px) {
            .search-section {
                padding: 3rem 0;
            }

            .job-card .company-logo {
                width: 140px;
                height: 140px;
                margin-right: 1.5rem;
                margin-bottom: 1.5rem;
            }

            .job-card .card-body {
                min-height: auto;
            }
            
            .job-card .btn-detail {
                position: static;
                margin-top: 1rem;
                width: 100%;
            }

            .job-card .row {
                flex-direction: column;
                align-items: center;
            }

            .job-card .job-content {
                width: 100%;
                text-align: center;
            }

            .job-card .job-title {
                margin-top: 1rem;
            }

            /* 移动端筛选条件样式 */
            .filter-card {
                border-radius: var(--border-radius);
            }

            .filter-card .card-header {
                cursor: pointer;
                user-select: none;
                border-radius: var(--border-radius);
                border-bottom: none;
            }

            .filter-card .card-header.collapsed {
                border-radius: var(--border-radius);
            }

            .filter-card .card-header::after {
                content: '\f107';
                font-family: 'Font Awesome 5 Free';
                font-weight: 900;
                float: right;
                transition: transform 0.3s ease;
            }

            .filter-card .card-header.collapsed::after {
                transform: rotate(-180deg);
            }

            .filter-card .card-body {
                display: none;
                border-radius: 0 0 var(--border-radius) var(--border-radius);
            }

            .filter-card .card-body.show {
                display: block;
            }
        }
    </style>
</head>

<body>
    <!-- 顶部导航栏 -->
<nav class="navbar navbar-expand-lg navbar-light fixed-top">
    <div class="container">
        <a class="navbar-brand" href="{:url('index/index/index')}">
            <i class="fas fa-briefcase"></i>{$site.name|htmlentities}
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='Index'}active{/eq}" href="{:url('index/index/index')}">
                        <i class="fas fa-home"></i>首页
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='Job'}active{/eq}" href="{:url('index/job/list')}">
                        <i class="fas fa-list me-1"></i>职位列表
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='News'}active{/eq}" href="{:url('index/resume/list')}">
                        <i class="fas fa-newspaper me-1"></i>我的简历
                    </a>
                </li>
            </ul>
            <div class="navbar-buttons">
                {if $user}
                <!-- 已登录状态 -->
                <a href="{:url('index/user/index')}" class="btn btn-outline-primary me-2">
                    <i class="fas fa-user me-1"></i>个人中心
                </a>
                <a href="javascript:;" class="btn btn-outline-danger" id="btn-logout">
                    <i class="fas fa-sign-out-alt me-1"></i>退出登录
                </a>
                <!-- 退出登录表单 -->
                <form id="logout-form" action="{:url('index/user/logout')}" method="post" style="display: none;">
                    {:token()}
                </form>
                {else}
                <!-- 未登录状态 -->
                <a href="{:url('index/user/login')}" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-1"></i>登录/注册
                </a>
                {/if}
            </div>
        </div>
    </div>
</nav>

    <!-- 搜索区域 -->
    <div class="search-section py-5 bg-light mt-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-10">
                    <div class="card shadow">
                        <div class="card-body">
                            <form action="{:url('index/job/search')}" method="get">
                                <div class="row g-3">
                                    <div class="col-md-5">
                                        <input type="text" class="form-control" name="keyword" placeholder="输入职位名称" value="{$keyword|default=''}">
                                    </div>
                                    <div class="col-md-5">
                                        <select class="form-select" name="category">
                                            <option value="">选择岗位分类</option>
                                            {volist name="categories" id="cat"}
                                            <option value="{$cat}" {if isset($category) && $category==$cat}selected{/if}>{$cat}</option>
                                            {/volist}
                                        </select>
                                    </div>
                                    <div class="col-md-2">
                                        <button type="submit" class="btn btn-primary w-100">搜索</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 职位列表 -->
    <div class="container py-5">
        <div class="row">
            <!-- 左侧筛选栏 -->
            <div class="col-md-3">
                <div class="filter-card">
                    <div class="card-header" onclick="toggleFilter()">
                        <i class="fas fa-filter me-2"></i>筛选条件
                    </div>
                    <div class="card-body">
                        <form action="{if isset($isSearch)}{:url('index/job/search')}{else}{:url('index/job/list')}{/if}" method="get">
                            {if isset($isSearch)}
                            <input type="hidden" name="keyword" value="{$keyword|default=''}">
                            {if isset($category) && $category}
                            <input type="hidden" name="category" value="{$category}">
                            {/if}
                            {/if}
                            <div class="mb-4">
                                <label class="form-label">年龄要求</label>
                                <select class="form-select" name="age">
                                    <option value="">不限</option>
                                    <option value="18-25" {if isset($age) && $age=='18-25'}selected{/if}>18-25岁</option>
                                    <option value="25-35" {if isset($age) && $age=='25-35'}selected{/if}>25-35岁</option>
                                    <option value="35-45" {if isset($age) && $age=='35-45'}selected{/if}>35-45岁</option>
                                    <option value="45+" {if isset($age) && $age=='45+'}selected{/if}>45岁以上</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-check me-2"></i>应用筛选
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- 右侧职位列表 -->
            <div class="col-md-9">
                {if isset($isSearch) && $isSearch}
                <div class="alert alert-info mb-4">
                    <i class="fas fa-search me-2"></i>
                    搜索关键词：<strong>{$keyword|default='全部'}</strong>
                    {if isset($category) && $category}
                    <span class="badge bg-primary ms-2">{$category}</span>
                    {/if}
                    {if isset($age) && $age}
                    <span class="badge bg-secondary ms-2">{$age}</span>
                    {/if}
                    <span class="ms-2">共找到 <strong>{:count($jobs)}</strong> 个职位</span>
                </div>
                {/if}
                
                {volist name="jobs" id="job"}
                <div class="job-card">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-3 text-center">
                                <div class="d-flex flex-column align-items-center">
                                    <img src="{$job.image|default='__CDN__/assets/img/default-job.jpg'}" class="company-logo" alt="{$job.title}">
                                </div>
                            </div>
                            <div class="col-md-9 job-content">
                                <h5 class="job-title">
                                    <a href="{:url('index/job/detail', ['id' => $job.id])}">{$job.title}</a>
                                    <span class="badge bg-primary ms-2">{$job.job_code}</span>
                                    {if $job.weight > 0}
                                    <span class="badge bg-warning position-absolute top-0 end-0 m-3">
                                        <i class="fas fa-star"></i> 推荐
                                    </span>
                                    {/if}
                                </h5>
                                <div class="job-info">
                                    <i class="fas fa-yen-sign"></i> {$job.salary_range}
                                </div>
                                <div class="job-info">
                                    <i class="fas fa-graduation-cap"></i> {$job.education_requirement|default='不限'}
                                </div>
                                <div class="job-info">
                                    <i class="fas fa-briefcase"></i> {$job.experience_requirement|default='不限'}
                                </div>
                                <div class="job-info">
                                    <i class="fas fa-clock"></i> 更新时间：{:date('Y-m-d H:i', $job['update_time'])}
                                </div>
                                <div class="d-flex justify-content-between align-items-center mt-3">
                                    <div>
                                        <span class="badge bg-info">{$job.category}</span>
                                        {if $job.gender_requirement}
                                        <span class="badge bg-secondary">{$job.gender_requirement}</span>
                                        {/if}
                                    </div>
                                </div>
                                <a href="{:url('index/job/detail', ['id' => $job.id])}" class="btn btn-outline-primary btn-detail">
                                    <i class="fas fa-eye me-1"></i>查看详情
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                {/volist}

                <!-- 分页 -->
                <div class="pagination-container">
                    <div class="d-flex justify-content-center">
                        {$page}
                    </div>
                </div>
            </div>
        </div>
    </div>

<!-- 底部信息 -->
<footer class="footer bg-dark text-light py-4">
    <div class="container">
        <div class="row">
            <div class="col-md-4">
                <h5>联系我们</h5>
                <p>
                    <i class="fas fa-phone"></i> {$site.phone}<br>
                    <i class="fas fa-envelope"></i> {$site.email}<br>
                    <i class="fas fa-map-marker-alt"></i> {$site.address}
                </p>
            </div>
            <div class="col-md-4">
                <h5>快速链接</h5>
                <ul class="list-unstyled">
                    <li><a href="{:url('index/about/index')}" class="text-light">关于我们</a></li>
                    <li><a href="{:url('index/help/index')}" class="text-light">帮助中心</a></li>
                    <li><a href="{:url('index/privacy/index')}" class="text-light">隐私政策</a></li>
                </ul>
            </div>
            <div class="col-md-4">
                <h5>关注我们</h5>
                <div class="social-links">
                    <a href="javascript:;" class="text-light me-2" data-bs-toggle="modal" data-bs-target="#wechatModal"><i class="fab fa-weixin fa-2x"></i></a>
                    <a href="javascript:;" class="text-light me-2" data-bs-toggle="modal" data-bs-target="#weiboModal"><i class="fab fa-weibo fa-2x"></i></a>
                </div>
            </div>
        </div>
        <hr class="mt-4">
        <div class="text-center">
            <p>Copyright © {$site.name|htmlentities} {:date('Y',time())} 版权所有 
                <a href="https://beian.miit.gov.cn" target="_blank" class="text-light">{$site.beian|htmlentities}</a>
            </p>
        </div>
    </div>
</footer>

<!-- 微信弹窗 -->
<div class="modal fade" id="wechatModal" tabindex="-1" aria-labelledby="wechatModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="wechatModalLabel">{$site.wechat_title|default='微信公众号'}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img src="{$site.wechat_image}" alt="微信二维码" class="img-fluid mb-3" style="max-width: 200px;">
                <p class="mb-0">{$site.wechat_desc|default='扫描上方二维码关注我们的微信公众号，获取最新招聘信息和职场资讯。'}</p>
            </div>
        </div>
    </div>
</div>

<!-- 微博弹窗 -->
<div class="modal fade" id="weiboModal" tabindex="-1" aria-labelledby="weiboModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="weiboModalLabel">{$site.weibo_title|default='官方微博'}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{$site.weibo_desc|default='关注我们的官方微博，了解最新动态和招聘信息。'}</p>
                <div class="d-grid gap-2">
                    <a href="{$site.weibo_url}" target="_blank" class="btn btn-danger">
                        <i class="fab fa-weibo me-2"></i>访问官方微博
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

    <script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script>
        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // 筛选条件展开/折叠
        function toggleFilter() {
            if (window.innerWidth <= 991.98) {
                const header = document.querySelector('.filter-card .card-header');
                const body = document.querySelector('.filter-card .card-body');
                header.classList.toggle('collapsed');
                body.classList.toggle('show');
            }
        }

        // 页面加载时，在移动端下默认折叠筛选条件
        window.addEventListener('load', function() {
            if (window.innerWidth <= 991.98) {
                const header = document.querySelector('.filter-card .card-header');
                const body = document.querySelector('.filter-card .card-body');
                header.classList.add('collapsed');
                body.classList.remove('show');
            }
        });
    </script>
    <script>
        // 替换分页按钮符号
        document.addEventListener('DOMContentLoaded', function() {
            // 替换上一页按钮
            const prevButtons = document.querySelectorAll('.pagination .page-item:first-child .page-link');
            prevButtons.forEach(function(btn) {
                if (btn.textContent.includes('«')) {
                    btn.innerHTML = '<i class="fas fa-chevron-left"></i><span>上一页</span>';
                }
            });
            
            // 替换下一页按钮
            const nextButtons = document.querySelectorAll('.pagination .page-item:last-child .page-link');
            nextButtons.forEach(function(btn) {
                if (btn.textContent.includes('»')) {
                    btn.innerHTML = '<span>下一页</span><i class="fas fa-chevron-right"></i>';
                }
            });
        });
        
        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
        
        // 退出登录按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const logoutBtn = document.getElementById('btn-logout');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function() {
                    document.getElementById('logout-form').submit();
                });
            }
        });
    </script> 
</body>
</html> 