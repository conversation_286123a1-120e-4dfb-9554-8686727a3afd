<?php

namespace app\admin\controller;

use app\common\controller\Backend;

/**
 * 岗位分类管理
 *
 * @icon fa fa-circle-o
 */
class Jobcategory extends Backend
{

    /**
     * Jobcategory模型对象
     * @var \app\common\model\Jobcategory
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\common\model\Jobcategory;
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 查看
     */
    public function index()
    {
        if ($this->request->isAjax()) {
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();
            
            $total = $this->model
                ->where($where)
                ->count();
            $result = array("total" => $total, "rows" => $list);
            return json($result);
        }
        
        // 如果没有指定状态，默认显示"展示"状态
        if (!$this->request->get('status')) {
            $this->request->get(['status' => '展示']);
        }
        
        return $this->view->fetch();
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */


}
