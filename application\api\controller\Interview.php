<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\Jobinterview;
use app\common\model\Resume;
use think\Db;

/**
 * 面试接口
 */
class Interview extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

    /**
     * 格式化时间戳为日期字符串
     * 
     * @param mixed $timestamp 时间戳
     * @param string $format 日期格式
     * @return string 格式化后的日期字符串
     */
    protected function formatTimestamp($timestamp, $format = 'Y-m-d H:i')
    {
        if (empty($timestamp)) {
            return '';
        }
        
        if (is_string($timestamp) && !is_numeric($timestamp)) {
            return '';
        }
        
        try {
            return date($format, intval($timestamp));
        } catch (\Exception $e) {
            \think\Log::write('时间格式化错误: ' . $e->getMessage() . ', 时间戳: ' . var_export($timestamp, true), 'error');
            return '';
        }
    }

    /**
     * 获取用户面试列表
     * 步骤：1.获取用户ID 2.查询用户的简历ID 3.查询面试表中resume_id包含这些简历ID的记录
     *
     * @ApiMethod (GET)
     * @param int $page 页码
     * @param int $limit 每页数量
     * @param string $status 面试状态
     * @param string $interview_type 面试类型
     */
    public function my_interviews()
    {
        // 步骤1: 获取当前登录用户ID
        $user = $this->auth->getUser();
        $userId = $user->id;
        
        // 获取分页和筛选参数
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $status = $this->request->get('status', '');
        $interview_type = $this->request->get('interview_type', '');

        // 记录前端传递的所有参数
        \think\Log::write('前端传递的完整参数: ' . json_encode($this->request->get()), 'debug');

        // 处理状态参数，简化逻辑，减少错误
        if (!empty($status)) {
            $status = urldecode($status);
            \think\Log::write('状态参数解码后: ' . $status, 'debug');
            
            // 构建有效状态列表
            $validStatuses = ['未开始', '进行中', '已结束', '已取消'];
            \think\Log::write('有效状态列表: ' . implode(',', $validStatuses), 'debug');
            
            // 状态必须在有效范围内
            if (!in_array($status, $validStatuses)) {
                \think\Log::write('无效的状态参数: ' . $status . '，忽略筛选', 'error');
                $status = ''; // 重置无效状态
            } else {
                \think\Log::write('有效的状态参数: ' . $status, 'debug');
            }
        }

        // 记录日志，便于调试
        \think\Log::write('步骤1: 获取用户ID: ' . $userId, 'debug');

        // 步骤2: 查询用户的简历ID
        $resumes = Resume::where('user_id', $userId)->select();
        if (empty($resumes)) {
            $this->success('暂无面试记录', ['total' => 0, 'items' => []]);
            return;
        }

        // 提取简历ID数组
        $resumeIds = [];
        foreach ($resumes as $resume) {
            $resumeIds[] = $resume->id;
        }
        
        \think\Log::write('步骤2: 查询到的用户简历IDs: ' . implode(',', $resumeIds), 'debug');

        // 步骤3: 直接使用SQL查询，确保只返回resume_id字段中包含用户简历ID的记录
        // 构建SQL查询条件
        $conditions = [];
        foreach ($resumeIds as $resumeId) {
            $conditions[] = "FIND_IN_SET('{$resumeId}', resume_id) > 0";
        }
        
        if (empty($conditions)) {
            $this->success('暂无面试记录', ['total' => 0, 'items' => []]);
            return;
        }
        
        // 合并查询条件
        $whereStr = "(" . implode(' OR ', $conditions) . ")";
        
        // 构建基础查询
        $query = Db::name('job_interview')->whereRaw($whereStr);
        \think\Log::write('步骤3: 精确查询条件: ' . $whereStr, 'debug');

        // 应用额外的筛选条件
        if (!empty($status)) {
            \think\Log::write('尝试应用状态筛选: ' . $status, 'debug');
            
            // 使用最简单的状态筛选
            $query->where('status', '=', $status);
            \think\Log::write('状态筛选SQL: ' . $query->getLastSql(), 'debug');
        }

        if (!empty($interview_type)) {
            $query->where('interview_type', $interview_type);
            \think\Log::write('应用类型筛选: ' . $interview_type, 'debug');
        }

        // 获取总数
        $total = $query->count();
        \think\Log::write('查询到的面试记录总数: ' . $total, 'debug');
        
        // 获取分页数据
        $list = $query->order('interview_time', 'desc')
            ->page($page, $limit)
            ->select();
            
        // 验证每条记录是否确实包含用户的简历ID
        $validRecords = [];
        foreach ($list as $interview) {
            $interviewResumeIds = explode(',', $interview['resume_id']);
            $found = false;
            foreach ($resumeIds as $resumeId) {
                if (in_array($resumeId, $interviewResumeIds)) {
                    $found = true;
                    break;
                }
            }
            
            if ($found) {
                $validRecords[] = $interview;
            } else {
                \think\Log::write('警告: 发现不符合条件的记录ID: ' . $interview['id'] . ', resume_id: ' . $interview['resume_id'], 'error');
            }
        }
        
        // 使用验证后的记录
        $list = $validRecords;
        \think\Log::write('验证后的记录数量: ' . count($list), 'debug');
            
        // 处理面试数据
        foreach ($list as &$interview) {
            // 处理面试进度
            if ($interview['progress'] && is_string($interview['progress'])) {
                $progressArray = explode(',', $interview['progress']);
                $userProgress = [];
                foreach ($progressArray as $progress) {
                    $parts = explode(':', $progress);
                    if (count($parts) == 2) {
                        $userProgress[$parts[0]] = $parts[1];
                    }
                }
                $interview['progress_array'] = $userProgress;
                
                // 获取当前用户的面试进度
                $interview['user_progress'] = isset($userProgress[$user->nickname]) ? $userProgress[$user->nickname] : '未面试';
            } else {
                $interview['progress_array'] = [];
                $interview['user_progress'] = '未面试';
            }
            
            // 格式化时间
            $interview['interview_time_text'] = $this->formatTimestamp($interview['interview_time']);
            $interview['create_time_text'] = $this->formatTimestamp($interview['create_time']);
            $interview['update_time_text'] = $this->formatTimestamp($interview['update_time']);
        }

        $this->success('获取成功', [
            'total' => count($list),
            'items' => $list
        ]);
    }

    /**
     * 获取面试详情
     * 步骤：1.获取用户ID 2.查询用户的简历ID 3.检查面试记录的resume_id是否包含用户的简历ID
     *
     * @ApiMethod (GET)
     * @param int $id 面试ID
     */
    public function detail($id = null)
    {
        if (!$id) {
            $this->error('参数错误');
        }

        // 步骤1: 获取当前登录用户ID
        $user = $this->auth->getUser();
        $userId = $user->id;
        \think\Log::write('步骤1: 获取用户ID: ' . $userId, 'debug');
        
        // 步骤2: 查询用户的简历ID
        $resumes = Resume::where('user_id', $userId)->select();
        $resumeIds = [];
        foreach ($resumes as $resume) {
            $resumeIds[] = $resume->id;
        }
        \think\Log::write('步骤2: 查询到的用户简历IDs: ' . implode(',', $resumeIds), 'debug');

        // 获取面试记录
        $interview = Jobinterview::get($id);
        if (!$interview) {
            $this->error('面试不存在');
        }
        \think\Log::write('获取面试记录ID: ' . $id . ', resume_id: ' . $interview->resume_id, 'debug');

        // 步骤3: 严格检查面试记录的resume_id是否包含用户的简历ID
        $hasPermission = false;
        $userResumeId = null;
        
        if (!empty($resumeIds) && !empty($interview->resume_id)) {
            $interviewResumeIds = explode(',', $interview->resume_id);
            foreach ($resumeIds as $resumeId) {
                if (in_array($resumeId, $interviewResumeIds)) {
                    $hasPermission = true;
                    $userResumeId = $resumeId;
                    break;
                }
            }
        }
        \think\Log::write('步骤3: 权限检查结果: ' . ($hasPermission ? '有权限' : '无权限') . ', 用户简历ID: ' . $userResumeId . ', 面试简历IDs: ' . $interview->resume_id, 'debug');

        if (!$hasPermission) {
            $this->error('无权查看此面试');
        }
        
        // 记录用户简历ID
        $interview->user_resume_id = $userResumeId;

        // 处理面试进度
        if ($interview->progress && is_string($interview->progress)) {
            $progressArray = explode(',', $interview->progress);
            $userProgress = [];
            foreach ($progressArray as $progress) {
                $parts = explode(':', $progress);
                if (count($parts) == 2) {
                    $userProgress[$parts[0]] = $parts[1];
                }
            }
            $interview->progress_array = $userProgress;
            
            // 获取当前用户的面试进度
            $interview->user_progress = isset($userProgress[$user->nickname]) ? $userProgress[$user->nickname] : '未面试';
        } else {
            $interview->progress_array = [];
            $interview->user_progress = '未面试';
        }
        
        // 格式化时间
        $interview->interview_time_text = $this->formatTimestamp($interview->interview_time);
        $interview->create_time_text = $this->formatTimestamp($interview->create_time);
        $interview->update_time_text = $this->formatTimestamp($interview->update_time);

        $this->success('获取成功', $interview);
    }

    /**
     * 确认参加面试
     * 步骤：1.获取用户ID 2.查询用户的简历ID 3.检查面试记录的resume_id是否包含用户的简历ID
     *
     * @ApiMethod (POST)
     * @param int $id 面试ID
     */
    public function confirm()
    {
        $id = $this->request->post('id');
        if (!$id) {
            $this->error('参数错误');
        }

        // 步骤1: 获取当前登录用户ID
        $user = $this->auth->getUser();
        $userId = $user->id;
        \think\Log::write('步骤1: 获取用户ID: ' . $userId, 'debug');
        
        // 步骤2: 查询用户的简历ID
        $resumes = Resume::where('user_id', $userId)->select();
        $resumeIds = [];
        foreach ($resumes as $resume) {
            $resumeIds[] = $resume->id;
        }
        \think\Log::write('步骤2: 查询到的用户简历IDs: ' . implode(',', $resumeIds), 'debug');

        // 获取面试记录
        $interview = Jobinterview::get($id);
        if (!$interview) {
            $this->error('面试不存在');
        }
        \think\Log::write('获取面试记录ID: ' . $id . ', resume_id: ' . $interview->resume_id, 'debug');

        // 步骤3: 严格检查面试记录的resume_id是否包含用户的简历ID
        $hasPermission = false;
        $userResumeId = null;
        
        if (!empty($resumeIds) && !empty($interview->resume_id)) {
            $interviewResumeIds = explode(',', $interview->resume_id);
            foreach ($resumeIds as $resumeId) {
                if (in_array($resumeId, $interviewResumeIds)) {
                    $hasPermission = true;
                    $userResumeId = $resumeId;
                    break;
                }
            }
        }
        \think\Log::write('步骤3: 权限检查结果: ' . ($hasPermission ? '有权限' : '无权限') . ', 用户简历ID: ' . $userResumeId . ', 面试简历IDs: ' . $interview->resume_id, 'debug');

        if (!$hasPermission) {
            $this->error('无权操作此面试');
        }

        // 更新面试进度
        $progressArray = [];
        if (!empty($interview->progress) && is_string($interview->progress)) {
            $progressArray = explode(',', $interview->progress);
        }
        
        $updatedProgress = [];
        $userUpdated = false;

        foreach ($progressArray as $progress) {
            if (is_string($progress) && strpos($progress, $user->nickname . ':') === 0) {
                $updatedProgress[] = $user->nickname . ':已面试';
                $userUpdated = true;
            } else {
                $updatedProgress[] = $progress;
            }
        }

        // 如果用户不在进度列表中，添加用户
        if (!$userUpdated) {
            $updatedProgress[] = $user->nickname . ':已面试';
        }

        $interview->progress = implode(',', $updatedProgress);
        \think\Log::write('更新面试进度: ' . $interview->progress, 'debug');
        
        $interview->save();

        $this->success('已确认参加面试');
    }

    /**
     * 取消面试
     * 步骤：1.获取用户ID 2.查询用户的简历ID 3.检查面试记录的resume_id是否包含用户的简历ID
     *
     * @ApiMethod (POST)
     * @param int $id 面试ID
     * @param string $reason 取消原因
     */
    public function cancel()
    {
        $id = $this->request->post('id');
        $reason = $this->request->post('reason');
        
        if (!$id) {
            $this->error('参数错误');
        }

        // 步骤1: 获取当前登录用户ID
        $user = $this->auth->getUser();
        $userId = $user->id;
        \think\Log::write('步骤1: 获取用户ID: ' . $userId, 'debug');
        
        // 步骤2: 查询用户的简历ID
        $resumes = Resume::where('user_id', $userId)->select();
        $resumeIds = [];
        foreach ($resumes as $resume) {
            $resumeIds[] = $resume->id;
        }
        \think\Log::write('步骤2: 查询到的用户简历IDs: ' . implode(',', $resumeIds), 'debug');

        // 获取面试记录
        $interview = Jobinterview::get($id);
        if (!$interview) {
            $this->error('面试不存在');
        }
        \think\Log::write('获取面试记录ID: ' . $id . ', resume_id: ' . $interview->resume_id, 'debug');

        // 步骤3: 严格检查面试记录的resume_id是否包含用户的简历ID
        $hasPermission = false;
        $userResumeId = null;
        
        if (!empty($resumeIds) && !empty($interview->resume_id)) {
            $interviewResumeIds = explode(',', $interview->resume_id);
            foreach ($resumeIds as $resumeId) {
                if (in_array($resumeId, $interviewResumeIds)) {
                    $hasPermission = true;
                    $userResumeId = $resumeId;
                    break;
                }
            }
        }
        \think\Log::write('步骤3: 权限检查结果: ' . ($hasPermission ? '有权限' : '无权限') . ', 用户简历ID: ' . $userResumeId . ', 面试简历IDs: ' . $interview->resume_id, 'debug');

        if (!$hasPermission) {
            $this->error('无权操作此面试');
        }

        // 更新面试进度
        $progressArray = [];
        if (!empty($interview->progress) && is_string($interview->progress)) {
            $progressArray = explode(',', $interview->progress);
        }
        
        $updatedProgress = [];
        $userUpdated = false;

        foreach ($progressArray as $progress) {
            if (is_string($progress) && strpos($progress, $user->nickname . ':') === 0) {
                $updatedProgress[] = $user->nickname . ':未通过';
                $userUpdated = true;
            } else {
                $updatedProgress[] = $progress;
            }
        }

        // 如果用户不在进度列表中，添加用户
        if (!$userUpdated) {
            $updatedProgress[] = $user->nickname . ':未通过';
        }

        $interview->progress = implode(',', $updatedProgress);
        \think\Log::write('更新面试进度: ' . $interview->progress, 'debug');
        
        // 添加取消原因到面试资料
        $cancelNote = "\n\n【取消记录】\n用户 {$user->nickname} 于 " . date('Y-m-d H:i:s') . " 取消面试\n原因：{$reason}";
        $interview->interview_materials = $interview->interview_materials . $cancelNote;
        
        $interview->save();

        $this->success('已取消面试');
    }

    /**
     * 获取面试统计数据
     * 步骤：1.获取用户ID 2.查询用户的简历ID 3.统计面试表中resume_id包含这些简历ID的记录
     *
     * @ApiMethod (GET)
     */
    public function stats()
    {
        // 步骤1: 获取当前登录用户ID
        $user = $this->auth->getUser();
        $userId = $user->id;
        \think\Log::write('步骤1: 获取用户ID: ' . $userId, 'debug');
        
        // 步骤2: 查询用户的简历ID
        $resumes = Resume::where('user_id', $userId)->select();
        
        if (empty($resumes)) {
            $this->success('获取成功', [
                'total' => 0,
                'upcoming' => 0,
                'completed' => 0,
                'canceled' => 0
            ]);
            return;
        }
        
        // 提取简历ID数组
        $resumeIds = [];
        foreach ($resumes as $resume) {
            $resumeIds[] = $resume->id;
        }
        \think\Log::write('步骤2: 查询到的用户简历IDs: ' . implode(',', $resumeIds), 'debug');
        
        // 步骤3: 统计面试表中resume_id包含这些简历ID的记录
        // 构建FIND_IN_SET查询条件，确保只统计包含用户简历ID的记录
        $conditions = [];
        foreach ($resumeIds as $resumeId) {
            $conditions[] = "FIND_IN_SET('{$resumeId}', resume_id) > 0";
        }
        
        if (empty($conditions)) {
            $this->success('获取成功', [
                'total' => 0,
                'upcoming' => 0,
                'completed' => 0,
                'canceled' => 0
            ]);
            return;
        }
        
        // 合并查询条件
        $whereStr = "(" . implode(' OR ', $conditions) . ")";
        \think\Log::write('步骤3: 精确统计查询条件: ' . $whereStr, 'debug');

        // 获取各状态的数量，使用Db直接查询确保SQL语句准确执行
        $total = Db::name('job_interview')->whereRaw($whereStr)->count();
        $upcoming = Db::name('job_interview')->whereRaw($whereStr)->where('status', 'in', ['未开始', '进行中'])->count();
        $completed = Db::name('job_interview')->whereRaw($whereStr)->where('status', '已结束')->count();
        $canceled = Db::name('job_interview')->whereRaw($whereStr)->where('status', '已取消')->count();
        
        \think\Log::write('精确统计结果: 总数=' . $total . ', 即将=' . $upcoming . ', 已完成=' . $completed . ', 已取消=' . $canceled, 'debug');

        $this->success('获取成功', [
            'total' => $total,
            'upcoming' => $upcoming,
            'completed' => $completed,
            'canceled' => $canceled
        ]);
    }

    /**
     * 获取面试状态列表（诊断接口）
     */
    public function status_list()
    {
        // 直接查询数据库中的状态值
        $statusList = Db::name('job_interview')
            ->field('DISTINCT status as status_value')
            ->select();
        
        // 返回状态列表
        $this->success('获取成功', [
            'db_status_list' => $statusList,
            'valid_status_list' => ['未开始', '进行中', '已结束', '已取消'],
            'model_status_list' => (new \app\common\model\Jobinterview())->getStatusList()
        ]);
    }
} 