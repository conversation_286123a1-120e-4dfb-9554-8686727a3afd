<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="viewport" content="width=device-width, initial-scale=1">

<title>{$site.name|htmlentities} - {$title|default='专业人力资源招聘平台'}</title>
<link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>

<!-- 引入公共CSS -->
<link href="__CDN__/assets/css/index.css" rel="stylesheet">
<link href="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
<link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
<style>
    /* 全局变量 */
:root {
    --primary-color: #1a73e8;
    --secondary-color: #34a853;
    --accent-color: #ea4335;
    --light-bg: #f8f9fa;
    --dark-bg: #202124;
    --text-primary: #202124;
    --text-secondary: #5f6368;
    --border-radius: 16px;
    --box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --gradient-primary: linear-gradient(135deg, #1a73e8, #4285f4);
    --gradient-success: linear-gradient(135deg, #34a853, #0f9d58);
}

/* 基础样式 */
body {
    background-color: var(--light-bg);
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:hover::after {
    width: 300px;
    height: 300px;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(26, 115, 232, 0.3);
}

.btn-outline-primary {
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--gradient-primary);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(26, 115, 232, 0.3);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    background: white;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.card-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.card-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
}

.card-title a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.card-title a:hover {
    color: var(--primary-color);
}

.card-title a:hover::after {
    width: 100%;
}

/* 标签样式 */
.badge {
    padding: 0.5em 1em;
    font-weight: 500;
    border-radius: 30px;
    font-size: 0.85rem;
    letter-spacing: 0.3px;
}

.badge.bg-primary {
    background: var(--gradient-primary) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #4285f4, #5c9ce6) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #5f6368, #80868b) !important;
}

/* 标题样式 */
.section-title {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 2.5rem;
    position: relative;
    padding-bottom: 1rem;
    text-align: center;
    font-size: 2rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* 内容区域通用样式 */
.main-content {
    margin-top: 76px; /* 为固定顶部导航栏留出空间 */
    min-height: calc(100vh - 300px); /* 最小高度，减去导航栏和底部的高度 */
}

/* 响应式调整 */
@media (max-width: 991.98px) {
    .section-title {
        font-size: 1.75rem;
    }
    
    .btn {
        padding: 0.6rem 1.2rem;
    }
}
    .carousel {
        margin-top: 60px;
        max-height: 300px;
        overflow: hidden;
    }

    .carousel-item {
        height: 300px;
    }

    .carousel-item img {
        object-fit: cover;
        height: 100%;
        width: 100%;
        filter: brightness(0.8);
        max-height: 300px;
    }

    .carousel-caption {
        background: rgba(0, 0, 0, 0.6);
        padding: 1rem;
        border-radius: var(--border-radius);
        backdrop-filter: blur(10px);
        max-width: 450px;
        margin: 0 auto;
        bottom: 20px;
    }

    .carousel-caption h2 {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    }

    .carousel-caption p {
        font-size: 1rem;
        margin-bottom: 0;
        text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
    }

    .search-section {
        background: var(--gradient-primary);
        padding: 2rem 0;
        position: relative;
        z-index: 1;
        overflow: hidden;
        margin-top: -20px;
    }

    .search-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect width="1" height="1" fill="rgba(255,255,255,0.1)"/></svg>');
        opacity: 0.1;
    }

    .search-section .card {
        border-radius: var(--border-radius);
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }

    .search-section .form-control,
    .search-section .form-select {
        border-radius: var(--border-radius);
        padding: 0.75rem 1rem;
        border: 2px solid #e0e0e0;
        transition: var(--transition);
        font-size: 1rem;
    }

    .search-section .form-control:focus,
    .search-section .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 4px rgba(26, 115, 232, 0.1);
    }

    .about-image {
        max-height: 300px;
        object-fit: cover;
        width: 100%;
        border-radius: var(--border-radius);
    }

    @media (max-width: 991.98px) {
        .search-section {
            padding: 2rem 0;
        }

        .carousel-item {
            height: 250px;
        }
        
        .carousel {
            max-height: 250px;
        }
        
        .carousel-item img {
            max-height: 250px;
        }

        .carousel-caption {
            padding: 0.75rem;
            max-width: 90%;
            bottom: 10px;
        }

        .carousel-caption h2 {
            font-size: 1.5rem;
            margin-bottom: 0.25rem;
        }

        .carousel-caption p {
            font-size: 0.85rem;
        }
        
        .about-image {
            max-height: 250px;
        }
    }
    
    @media (max-width: 576px) {
        .carousel-item {
            height: 200px;
        }
        
        .carousel {
            max-height: 200px;
        }
        
        .carousel-item img {
            max-height: 200px;
        }
        
        .carousel-caption {
            padding: 0.5rem;
        }
        
        .carousel-caption h2 {
            font-size: 1.25rem;
        }
        
        .carousel-caption p {
            font-size: 0.75rem;
        }
    }
    
    .lazy-load {
        opacity: 0;
        transition: opacity 0.3s ease-in;
    }
    
    .lazy-load.loaded {
        opacity: 1;
    }
    .navbar {
        background-color: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px);
        box-shadow: var(--box-shadow);
        padding: 1rem 0;
        transition: var(--transition);
    }

    .navbar.scrolled {
        padding: 0.5rem 0;
    }

    .navbar-brand {
        font-weight: 700;
        color: var(--primary-color) !important;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
        letter-spacing: -0.5px;
    }

    .navbar-brand i {
        color: var(--primary-color);
        margin-right: 0.5rem;
        font-size: 1.8rem;
    }

    .nav-link {
        color: var(--text-primary) !important;
        font-weight: 500;
        padding: 0.75rem 1.25rem !important;
        margin: 0 0.25rem;
        border-radius: var(--border-radius);
        transition: var(--transition);
        position: relative;
    }

    .nav-link::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 2px;
        background-color: var(--primary-color);
        transition: var(--transition);
    }

    .nav-link:hover::after {
        width: 80%;
    }

    .nav-link:hover {
        background-color: rgba(26, 115, 232, 0.08);
        color: var(--primary-color) !important;
    }

    .nav-link.active {
        background-color: var(--primary-color) !important;
        color: white !important;
    }

    .nav-link.active::after {
        display: none;
    }

    @media (max-width: 991.98px) {
        .navbar-collapse {
            background-color: rgba(255, 255, 255, 0.98);
            padding: 1rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-top: 1rem;
            backdrop-filter: blur(10px);
        }

        .nav-link {
            padding: 1rem !important;
            margin: 0.5rem 0;
        }

        .navbar-buttons {
            margin-top: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .navbar-buttons .btn {
            width: 100%;
        }
    }
    .footer {
        background-color: var(--dark-bg);
        color: white;
        padding: 5rem 0 2rem;
        position: relative;
        overflow: hidden;
    }

    .footer::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient-primary);
    }

    .footer h5 {
        color: white;
        font-weight: 600;
        margin-bottom: 1.5rem;
        font-size: 1.25rem;
    }

    .footer a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: var(--transition);
        display: inline-block;
        margin-bottom: 0.5rem;
    }

    .footer a:hover {
        color: white;
        transform: translateX(5px);
    }

    .social-links a {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(255,255,255,0.1);
        margin-right: 1rem;
        transition: var(--transition);
        cursor: pointer;
    }

    .social-links a:hover {
        background: var(--primary-color);
        transform: translateY(-3px);
    }
</style>
<!-- 顶部导航栏 -->
<nav class="navbar navbar-expand-lg navbar-light fixed-top">
    <div class="container">
        <a class="navbar-brand" href="{:url('index/index/index')}">
            <i class="fas fa-briefcase"></i>{$site.name|htmlentities}
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='Index'}active{/eq}" href="{:url('index/index/index')}">
                        <i class="fas fa-home"></i>首页
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='Job'}active{/eq}" href="{:url('index/job/list')}">
                        <i class="fas fa-list me-1"></i>职位列表
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='News'}active{/eq}" href="{:url('index/resume/list')}">
                        <i class="fas fa-newspaper me-1"></i>我的简历
                    </a>
                </li>
            </ul>
            <div class="navbar-buttons">
                {if $user}
                <!-- 已登录状态 -->
                <a href="{:url('index/user/index')}" class="btn btn-outline-primary me-2">
                    <i class="fas fa-user me-1"></i>个人中心
                </a>
                <a href="javascript:;" class="btn btn-outline-danger" id="btn-logout">
                    <i class="fas fa-sign-out-alt me-1"></i>退出登录
                </a>
                <!-- 退出登录表单 -->
                <form id="logout-form" action="{:url('index/user/logout')}" method="post" style="display: none;">
                    {:token()}
                </form>
                {else}
                <!-- 未登录状态 -->
                <a href="{:url('index/user/login')}" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-1"></i>登录/注册
                </a>
                {/if}
            </div>
        </div>
    </div>
</nav>
<!-- 轮播图 -->
<div id="mainCarousel" class="carousel slide" data-bs-ride="carousel">
    <div class="carousel-inner">
        {if is_array($site.banner_image)}
            {volist name="$site.banner_image" id="banner" key="bannerIndex"}
            <div class="carousel-item {if $bannerIndex == 1}active{/if}">
                <img src="{$banner}" class="d-block w-100 lazy-load" alt="招聘banner" loading="lazy">
                <div class="carousel-caption">
                    <h2>找到理想的工作</h2>
                    <p>数千个优质职位等你来选</p>
                </div>
            </div>
            {/volist}
        {else}
        <div class="carousel-item active">
            <img src="{$site.banner_image}" class="d-block w-100 lazy-load" alt="招聘banner" loading="lazy">
            <div class="carousel-caption">
                <h2>找到理想的工作</h2>
                <p>数千个优质职位等你来选</p>
            </div>
        </div>
        {/if}
    </div>
    {if is_array($site.banner_image) && count($site.banner_image) > 1}
    <button class="carousel-control-prev" type="button" data-bs-target="#mainCarousel" data-bs-slide="prev">
        <span class="carousel-control-prev-icon" aria-hidden="true"></span>
        <span class="visually-hidden">Previous</span>
    </button>
    <button class="carousel-control-next" type="button" data-bs-target="#mainCarousel" data-bs-slide="next">
        <span class="carousel-control-next-icon" aria-hidden="true"></span>
        <span class="visually-hidden">Next</span>
    </button>
    <div class="carousel-indicators">
        {volist name="$site.banner_image" id="banner" key="i"}
            <button type="button" data-bs-target="#mainCarousel" data-bs-slide-to="{$i-1}" {if $i == 1}class="active" aria-current="true"{/if} aria-label="Slide {$i}"></button>
        {/volist}
    </div>
    {/if}
</div>

<!-- 搜索区域 -->
<div class="search-section py-4 bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-body py-3">
                        <form action="{:url('index/job/search')}" method="get">
                            <div class="row g-2">
                                <div class="col-md-5">
                                    <input type="text" class="form-control" name="keyword" placeholder="输入职位名称">
                                </div>
                                <div class="col-md-5">
                                    <select class="form-select" name="category">
                                        <option value="">选择岗位分类</option>
                                        {volist name="categories" id="cat"}
                                        <option value="{$cat}">{$cat}</option>
                                        {/volist}
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary w-100">搜索</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 热门职位 -->
<div class="container py-5">
    <h2 class="text-center section-title">热门职位</h2>
    <div class="row">
        {volist name="hotJobs" id="job"}
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <a href="{:url('index/job/detail', ['id' => $job.id])}">{$job.title}</a>
                        <span class="badge bg-primary ms-2">{$job.job_code}</span>
                    </h5>
                    <p class="card-text text-secondary">
                        <i class="fas fa-yen-sign"></i> {$job.salary_range}<br>
                        <i class="fas fa-graduation-cap"></i> {$job.education_requirement|default='不限'}<br>
                        <i class="fas fa-briefcase"></i> {$job.experience_requirement|default='不限'}
                    </p>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <div>
                            <span class="badge bg-info">{$job.category}</span>
                            {if $job.gender_requirement}
                            <span class="badge bg-secondary">{$job.gender_requirement}</span>
                            {/if}
                        </div>
                        <a href="{:url('index/job/detail', ['id' => $job.id])}" class="btn btn-outline-primary">查看详情</a>
                    </div>
                </div>
            </div>
        </div>
        {/volist}
    </div>
    <div class="text-center mt-4">
        <a href="{:url('index/job/list')}" class="btn btn-outline-primary">
            <i class="fas fa-list me-2"></i>查看更多岗位
        </a>
    </div>
</div>

<!-- 最新职位 -->
<div class="bg-light py-5">
    <div class="container">
        <h2 class="text-center section-title">最新职位</h2>
        <div class="row">
            {volist name="latestJobs" id="job"}
            <div class="col-md-4 mb-4">
                <div class="card h-100">
                    <div class="card-body">
                        <h5 class="card-title">
                            <a href="{:url('index/job/detail', ['id' => $job.id])}">{$job.title}</a>
                            <span class="badge bg-primary ms-2">{$job.job_code}</span>
                        </h5>
                        <p class="card-text text-secondary">
                            <i class="fas fa-yen-sign"></i> {$job.salary_range}<br>
                            <i class="fas fa-graduation-cap"></i> {$job.education_requirement|default='不限'}<br>
                            <i class="fas fa-briefcase"></i> {$job.experience_requirement|default='不限'}
                        </p>
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <span class="badge bg-info">{$job.category}</span>
                                {if $job.gender_requirement}
                                <span class="badge bg-secondary">{$job.gender_requirement}</span>
                                {/if}
                            </div>
                            <a href="{:url('index/job/detail', ['id' => $job.id])}" class="btn btn-outline-primary">查看详情</a>
                        </div>
                    </div>
                </div>
            </div>
            {/volist}
        </div>
        <div class="text-center mt-4">
            <a href="{:url('index/job/list')}" class="btn btn-outline-primary">
                <i class="fas fa-list me-2"></i>查看更多岗位
            </a>
        </div>
    </div>
</div>

<!-- 公司简介 -->
<div class="container py-5">
    <div class="row align-items-center">
        <div class="col-md-6">
            <h2 class="section-title">关于我们</h2>
            <p class="lead text-secondary">{$site.description|default='我们是一家专业的人力资源服务公司，致力于为企业提供优质的人才招聘服务，为求职者提供理想的职业发展平台。'}</p>
            <a href="{:url('index/about/index')}" class="btn btn-primary">了解更多</a>
        </div>
        <div class="col-md-6">
            <img src="{$site.about_image}" class="img-fluid rounded shadow about-image lazy-load" alt="关于我们" loading="lazy">
        </div>
    </div>
</div>

<!-- 底部信息 -->
<footer class="footer bg-dark text-light py-4">
    <div class="container">
        <div class="row">
            <div class="col-md-4">
                <h5>联系我们</h5>
                <p>
                    <i class="fas fa-phone"></i> {$site.phone}<br>
                    <i class="fas fa-envelope"></i> {$site.email}<br>
                    <i class="fas fa-map-marker-alt"></i> {$site.address}
                </p>
            </div>
            <div class="col-md-4">
                <h5>快速链接</h5>
                <ul class="list-unstyled">
                    <li><a href="{:url('index/about/index')}" class="text-light">关于我们</a></li>
                    <li><a href="{:url('index/help/index')}" class="text-light">帮助中心</a></li>
                    <li><a href="{:url('index/privacy/index')}" class="text-light">隐私政策</a></li>
                </ul>
            </div>
            <div class="col-md-4">
                <h5>关注我们</h5>
                <div class="social-links">
                    <a href="javascript:;" class="text-light me-2" data-bs-toggle="modal" data-bs-target="#wechatModal"><i class="fab fa-weixin fa-2x"></i></a>
                    <a href="javascript:;" class="text-light me-2" data-bs-toggle="modal" data-bs-target="#weiboModal"><i class="fab fa-weibo fa-2x"></i></a>
                </div>
            </div>
        </div>
        <hr class="mt-4">
        <div class="text-center">
            <p>Copyright © {$site.name|htmlentities} {:date('Y',time())} 版权所有 
                <a href="https://beian.miit.gov.cn" target="_blank" class="text-light">{$site.beian|htmlentities}</a>
            </p>
        </div>
    </div>
</footer>

<!-- 微信弹窗 -->
<div class="modal fade" id="wechatModal" tabindex="-1" aria-labelledby="wechatModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="wechatModalLabel">{$site.wechat_title|default='微信公众号'}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img src="{$site.wechat_image}" alt="微信二维码" class="img-fluid mb-3" style="max-width: 200px;">
                <p class="mb-0">{$site.wechat_desc|default='扫描上方二维码关注我们的微信公众号，获取最新招聘信息和职场资讯。'}</p>
            </div>
        </div>
    </div>
</div>

<!-- 微博弹窗 -->
<div class="modal fade" id="weiboModal" tabindex="-1" aria-labelledby="weiboModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="weiboModalLabel">{$site.weibo_title|default='官方微博'}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{$site.weibo_desc|default='关注我们的官方微博，了解最新动态和招聘信息。'}</p>
                <div class="d-grid gap-2">
                    <a href="{$site.weibo_url}" target="_blank" class="btn btn-danger">
                        <i class="fab fa-weibo me-2"></i>访问官方微博
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 公共JavaScript库 -->
<script src="https://cdn.bootcdn.net/ajax/libs/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>

<!-- 公共脚本 -->
<script>
// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 初始化弹出框
    var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
});

// 全局函数
function showMessage(message, type) {
    var alertClass = 'alert-info';
    var icon = 'fa-info-circle';
    
    if (type === 'success') {
        alertClass = 'alert-success';
        icon = 'fa-check-circle';
    } else if (type === 'warning') {
        alertClass = 'alert-warning';
        icon = 'fa-exclamation-triangle';
    } else if (type === 'danger') {
        alertClass = 'alert-danger';
        icon = 'fa-times-circle';
    }
    
    var alertHtml = 
        '<div class="alert ' + alertClass + ' alert-dismissible fade show" role="alert">' +
        '<i class="fas ' + icon + ' me-2"></i>' + message +
        '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' +
        '</div>';
    
    var alertContainer = document.getElementById('alert-container');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alert-container';
        alertContainer.style.position = 'fixed';
        alertContainer.style.top = '20px';
        alertContainer.style.right = '20px';
        alertContainer.style.zIndex = '9999';
        alertContainer.style.maxWidth = '400px';
        document.body.appendChild(alertContainer);
    }
    
    var alertElement = document.createElement('div');
    alertElement.innerHTML = alertHtml;
    alertContainer.appendChild(alertElement.firstChild);
    
    // 3秒后自动关闭
    setTimeout(function() {
        var alerts = alertContainer.getElementsByClassName('alert');
        if (alerts.length > 0) {
            alerts[0].remove();
        }
    }, 3000);
}
</script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    var lazyImages = document.querySelectorAll('.lazy-load');
    
    if ('IntersectionObserver' in window) {
        let lazyImageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    let lazyImage = entry.target;
                    lazyImage.classList.add('loaded');
                    lazyImageObserver.unobserve(lazyImage);
                }
            });
        });

        lazyImages.forEach(function(lazyImage) {
            lazyImageObserver.observe(lazyImage);
        });
    } else {
        lazyImages.forEach(function(img) {
            img.classList.add('loaded');
        });
    }
});
</script>
</body>
</html> 
