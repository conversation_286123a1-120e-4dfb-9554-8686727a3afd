<?php
// 简单的PhpWord测试
require_once __DIR__ . '/../vendor/autoload.php';

echo "PhpWord 加载测试结果：<br>";

if (class_exists('\PhpOffice\PhpWord\PhpWord')) {
    echo "✅ 成功！PhpWord 类已正确加载<br>";
    
    try {
        $phpWord = new \PhpOffice\PhpWord\PhpWord();
        echo "✅ 成功！PhpWord 对象创建成功<br>";
        echo "🎉 PhpWord 现在可以正常使用了！<br>";
    } catch (Exception $e) {
        echo "❌ 错误：" . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ 失败：PhpWord 类无法加载<br>";
}
?>
