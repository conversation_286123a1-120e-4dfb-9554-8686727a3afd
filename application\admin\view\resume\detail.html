<!-- 确保 require.js 加载 -->
<script src="__CDN__/assets/js/require.min.js" data-main="__CDN__/assets/js/require-backend{$Think.config.app_debug?'':'.min'}.js?v={$site.version|htmlentities}"></script>
<!-- 确保 jQuery 可用 -->
<script src="__CDN__/assets/libs/jquery/dist/jquery.min.js"></script>

<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs">
            <li class="active"><a href="#t-basic" data-toggle="tab"><i class="fa fa-user"></i> 基本信息</a></li>
            <li><a href="#t-contact" data-toggle="tab"><i class="fa fa-address-book"></i> 联系人</a></li>
            <li><a href="#t-education" data-toggle="tab"><i class="fa fa-graduation-cap"></i> 教育经历</a></li>
            <li><a href="#t-work" data-toggle="tab"><i class="fa fa-briefcase"></i> 工作经历</a></li>
            <li><a href="#t-photos" data-toggle="tab"><i class="fa fa-image"></i> 照片资料</a></li>
        </ul>
    </div>
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <!-- 基本信息 -->
            <div class="tab-pane fade active in" id="t-basic">
                <div class="widget-body no-padding">
                    <div class="row">
                        <div class="col-md-3 text-center">
                            <div class="profile-user-img" style="width: 150px; height: 150px; margin: 0 auto; overflow: hidden; border-radius: 50%; border: 2px solid #eee;">
                                <a href="javascript:;" class="photo-preview" data-src="{$row.avatar|cdnurl}">
                                    <img src="{$row.avatar|cdnurl}" class="img-responsive" style="width: 100%; height: 100%; object-fit: cover;">
                                </a>
                            </div>
                            <h3 class="profile-username text-center" style="margin-top: 10px;">{$row.name}</h3>
                            <p class="text-muted text-center">{$row.applied_position}</p>
                        </div>
                        <div class="col-md-9">
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover resume-table">
                                    <tbody>
                                        <tr class="info">
                                            <th colspan="4" class="text-center">
                                                <i class="fa fa-user"></i> 个人信息
                                            </th>
                                        </tr>
                                        <tr class="four-columns">
                                            <th>姓名</th>
                                            <td>{$row.name}</td>
                                            <th>性别</th>
                                            <td>{$row.gender}</td>
                                        </tr>
                                        <tr class="four-columns">
                                            <th>年龄</th>
                                            <td>{$row.age}</td>
                                            <th>民族</th>
                                            <td>{$row.ethnicity}</td>
                                        </tr>
                                        <tr class="four-columns">
                                            <th>身高</th>
                                            <td>{$row.height} cm</td>
                                            <th>体重</th>
                                            <td>{$row.weight} kg</td>
                                        </tr>
                                        <tr class="four-columns">
                                            <th>身份证号</th>
                                            <td>{$row.id_card}</td>
                                            <th>出生日期</th>
                                            <td>{$row.birth_date|datetime='Y年m月d日'}</td>
                                        </tr>
                                        <tr class="four-columns">
                                            <th>手机号码</th>
                                            <td>{$row.phone}</td>
                                            <th>婚姻状况</th>
                                            <td>{$row.marital_status}</td>
                                        </tr>
                                        <tr class="info">
                                            <th colspan="4" class="text-center">
                                                <i class="fa fa-map-marker"></i> 地址信息
                                            </th>
                                        </tr>
                                        <tr>
                                            <th>户口所在地</th>
                                            <td>{$row.hukou_location}</td>
                                            <th>常住地址</th>
                                            <td>{$row.residence_address}</td>
                                        </tr>
                                        <tr class="info">
                                            <th colspan="4" class="text-center">
                                                <i class="fa fa-graduation-cap"></i> 教育语言
                                            </th>
                                        </tr>
                                        <tr>
                                            <th>最高学历</th>
                                            <td>{$row.highest_education}</td>
                                            <th>粤语</th>
                                            <td>{$row.cantonese_level}</td>
                                        </tr>
                                        <tr>
                                            <th>国语</th>
                                            <td>{$row.mandarin_level}</td>
                                            <th>英语</th>
                                            <td>{$row.english_level}</td>
                                        </tr>
                                        <tr class="info">
                                            <th colspan="4" class="text-center">
                                                <i class="fa fa-id-card"></i> 证件信息
                                            </th>
                                        </tr>
                                        <tr>
                                            <th>港澳通行证编号</th>
                                            <td>{$row.hk_macau_passport}</td>
                                            <th>港澳通行证到期</th>
                                            <td>{$row.hk_macau_passport_expiry|datetime='Y年m月d日'}</td>
                                        </tr>
                                        <tr>
                                            <th>海外经历</th>
                                            <td>{$row.overseas_experience}</td>
                                            <th>海外地区</th>
                                            <td>{$row.overseas_region}</td>
                                        </tr>
                                        <tr class="info">
                                            <th colspan="4" class="text-center">
                                                <i class="fa fa-star"></i> 其他信息
                                            </th>
                                        </tr>
                                        <tr>
                                            <th>特殊职业资格证</th>
                                            <td colspan="3">{$row.special_certificate}</td>
                                        </tr>
                                        <tr>
                                            <th>兴趣爱好</th>
                                            <td colspan="3">{$row.hobbies}</td>
                                        </tr>
                                        <tr>
                                            <th>自我评价</th>
                                            <td colspan="3">{$row.self_evaluation}</td>
                                        </tr>
                                        <tr class="info">
                                            <th colspan="4" class="text-center">
                                                <i class="fa fa-briefcase"></i> 岗位信息
                                            </th>
                                        </tr>
                                        <tr>
                                            <th>意向岗位</th>
                                            <td>{$row.intended_position}</td>
                                            <th>申请岗位</th>
                                            <td>{$row.applied_position}</td>
                                        </tr>
                                        <tr>
                                            <th>对接人</th>
                                            <td colspan="3">{$row.contact_person}</td>
                                        </tr>
                                        <tr class="info">
                                            <th colspan="4" class="text-center">
                                                <i class="fa fa-cog"></i> 系统信息
                                            </th>
                                        </tr>
                                        <tr>
                                            <th>状态</th>
                                            <td>{$row.status}</td>
                                            <th>创建时间</th>
                                            <td>{$row.create_time|datetime='Y年m月d日 H:i'}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 照片资料 -->
            <div class="tab-pane fade" id="t-photos">
                <div class="widget-body no-padding">
                    <div class="photos-container">
                        <!-- 主要照片 -->
                        <div class="row">
                            <!-- 全身照 -->
                            {if $row.full_body_photo}
                            <div class="col-md-4 col-sm-6 photo-item">
                                <div class="photo-card">
                                    <div class="photo-header">
                                        <h4><i class="fa fa-user"></i> 全身照</h4>
                                    </div>
                                    <div class="photo-body">
                                        <a href="javascript:;" class="photo-preview" data-src="{$row.full_body_photo|cdnurl}">
                                            <img src="{$row.full_body_photo|cdnurl}" class="img-responsive">
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {/if}
                            
                            <!-- 身份证正面 -->
                            {if $row.id_card_front}
                            <div class="col-md-4 col-sm-6 photo-item">
                                <div class="photo-card">
                                    <div class="photo-header">
                                        <h4><i class="fa fa-id-card"></i> 身份证正面</h4>
                                    </div>
                                    <div class="photo-body">
                                        <a href="javascript:;" class="photo-preview" data-src="{$row.id_card_front|cdnurl}">
                                            <img src="{$row.id_card_front|cdnurl}" class="img-responsive">
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {/if}
                            
                            <!-- 身份证反面 -->
                            {if $row.id_card_back}
                            <div class="col-md-4 col-sm-6 photo-item">
                                <div class="photo-card">
                                    <div class="photo-header">
                                        <h4><i class="fa fa-id-card"></i> 身份证反面</h4>
                                    </div>
                                    <div class="photo-body">
                                        <a href="javascript:;" class="photo-preview" data-src="{$row.id_card_back|cdnurl}">
                                            <img src="{$row.id_card_back|cdnurl}" class="img-responsive">
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {/if}
                        </div>
                        
                        <div class="row">
                            <!-- 港澳通行证正面 -->
                            {if $row.hk_macau_passport_front}
                            <div class="col-md-4 col-sm-6 photo-item">
                                <div class="photo-card">
                                    <div class="photo-header">
                                        <h4><i class="fa fa-passport"></i> 港澳通行证正面</h4>
                                    </div>
                                    <div class="photo-body">
                                        <a href="javascript:;" class="photo-preview" data-src="{$row.hk_macau_passport_front|cdnurl}">
                                            <img src="{$row.hk_macau_passport_front|cdnurl}" class="img-responsive">
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {/if}
                            
                            <!-- 港澳通行证反面 -->
                            {if $row.hk_macau_passport_back}
                            <div class="col-md-4 col-sm-6 photo-item">
                                <div class="photo-card">
                                    <div class="photo-header">
                                        <h4><i class="fa fa-passport"></i> 港澳通行证反面</h4>
                                    </div>
                                    <div class="photo-body">
                                        <a href="javascript:;" class="photo-preview" data-src="{$row.hk_macau_passport_back|cdnurl}">
                                            <img src="{$row.hk_macau_passport_back|cdnurl}" class="img-responsive">
                                        </a>
                                    </div>
                                </div>
                            </div>
                            {/if}
                        </div>
                        
                        <!-- 补充照片 -->
                        {if $row.additional_photos}
                        <div class="additional-photos">
                            <h3 class="section-title"><i class="fa fa-images"></i> 补充照片</h3>
                            <div class="row">
                                {php}
                                $additionalPhotos = explode(',', $row['additional_photos']);
                                {/php}
                                {foreach name="additionalPhotos" item="photo" key="index"}
                                <div class="col-md-3 col-sm-4 col-xs-6 photo-item">
                                    <div class="photo-card">
                                        <div class="photo-header">
                                            <h4>照片 {$index+1}</h4>
                                        </div>
                                        <div class="photo-body">
                                            <a href="javascript:;" class="photo-preview" data-src="{$photo|cdnurl}">
                                                <img src="{$photo|cdnurl}" class="img-responsive">
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                {/foreach}
                            </div>
                        </div>
                        {/if}
                        
                        {if !$row.full_body_photo && !$row.id_card_front && !$row.id_card_back && !$row.hk_macau_passport_front && !$row.hk_macau_passport_back && !$row.additional_photos}
                        <div class="alert alert-info">
                            <i class="fa fa-info-circle"></i> 暂无照片资料
                        </div>
                        {/if}
                    </div>
                </div>
            </div>
            <!-- 联系人 -->
            <div class="tab-pane fade" id="t-contact">
                <div class="widget-body no-padding">
                    {if $row.contact_relation && $row.contact_name}
                    <div class="timeline">
                        {php}
                        $relations = explode('|', $row['contact_relation'] ?? '');
                        $names = explode('|', $row['contact_name'] ?? '');
                        $ages = explode('|', $row['contact_age'] ?? '');
                        $jobs = explode('|', $row['contact_job'] ?? '');
                        
                        // 确保所有数组长度一致
                        $maxLength = max(count($relations), count($names), count($ages), count($jobs));
                        $relations = array_pad($relations, $maxLength, '');
                        $names = array_pad($names, $maxLength, '');
                        $ages = array_pad($ages, $maxLength, '');
                        $jobs = array_pad($jobs, $maxLength, '');
                        {/php}
                        {for start="0" end="count($relations)" name="i"}
                        {if condition="$relations[$i] || $names[$i]"}
                        <div class="timeline-item">
                            <div class="timeline-point timeline-point-info">
                                <i class="fa fa-user"></i>
                            </div>
                            <div class="timeline-event">
                                <div class="timeline-heading">
                                    <h4>{$names[$i]}</h4>
                                </div>
                                <div class="timeline-body">
                                    <p><strong>关系</strong>{$relations[$i]}</p>
                                    {if condition="!empty($ages[$i])"}
                                    <p><strong>年龄</strong>{$ages[$i]}岁</p>
                                    {/if}
                                    {if condition="!empty($jobs[$i])"}
                                    <p><strong>职业</strong>{$jobs[$i]}</p>
                                    {/if}
                                </div>
                            </div>
                        </div>
                        {/if}
                        {/for}
                    </div>
                    {else}
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> 暂无联系人信息
                    </div>
                    {/if}
                </div>
            </div>
            <!-- 教育经历 -->
            <div class="tab-pane fade" id="t-education">
                <div class="widget-body no-padding">
                    {if $row.education_school}
                    <div class="timeline">
                        {php}
                        $starts = explode('|', $row['education_start'] ?? '');
                        $ends = explode('|', $row['education_end'] ?? '');
                        $schools = explode('|', $row['education_school'] ?? '');
                        $majors = explode('|', $row['education_major'] ?? '');
                        $degrees = explode('|', $row['graduation_education'] ?? '');

                        // 确保所有数组长度一致
                        $maxLength = max(count($starts), count($ends), count($schools), count($majors), count($degrees));
                        $starts = array_pad($starts, $maxLength, '');
                        $ends = array_pad($ends, $maxLength, '');
                        $schools = array_pad($schools, $maxLength, '');
                        $majors = array_pad($majors, $maxLength, '');
                        $degrees = array_pad($degrees, $maxLength, '');
                        {/php}
                        {for start="0" end="count($starts)" name="i"}
                        {if condition="$starts[$i] || $schools[$i]"}
                        <div class="timeline-item">
                            <div class="timeline-point timeline-point-success">
                                <i class="fa fa-graduation-cap"></i>
                            </div>
                            <div class="timeline-event">
                                <div class="timeline-heading">
                                    <h4>{$schools[$i]}</h4>
                                </div>
                                <div class="timeline-body">
                                    <p><strong>时间段</strong>{$starts[$i]|datetime='Y年m月'} - {$ends[$i]|datetime='Y年m月'}</p>
                                    {if condition="!empty($majors[$i])"}
                                    <p><strong>专业</strong>{$majors[$i]}</p>
                                    {/if}
                                    {if condition="!empty($degrees[$i])"}
                                    <p><strong>学历</strong><span class="timeline-tag">{$degrees[$i]}</span></p>
                                    {/if}
                                </div>
                            </div>
                        </div>
                        {/if}
                        {/for}
                    </div>
                    {else}
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> 暂无教育经历信息
                    </div>
                    {/if}
                </div>
            </div>
            <!-- 工作经历 -->
            <div class="tab-pane fade" id="t-work">
                <div class="widget-body no-padding">
                    {if $row.job_company}
                    <div class="timeline">
                        {php}
                        $companies = explode('|', $row['job_company'] ?? '');
                        $positions = explode('|', $row['job_position'] ?? '');
                        $descriptions = explode('|', $row['job_description'] ?? '');
                        $starts = explode('|', $row['job_start'] ?? '');
                        $ends = explode('|', $row['job_end'] ?? '');
                        
                        // 确保所有数组长度一致
                        $maxLength = max(count($companies), count($positions), count($descriptions), count($starts), count($ends));
                        $companies = array_pad($companies, $maxLength, '');
                        $positions = array_pad($positions, $maxLength, '');
                        $descriptions = array_pad($descriptions, $maxLength, '');
                        $starts = array_pad($starts, $maxLength, '');
                        $ends = array_pad($ends, $maxLength, '');
                        {/php}
                        {for start="0" end="count($companies)" name="i"}
                        {if condition="$companies[$i] || $positions[$i]"}
                        <div class="timeline-item">
                            <div class="timeline-point timeline-point-warning">
                                <i class="fa fa-briefcase"></i>
                            </div>
                            <div class="timeline-event">
                                <div class="timeline-heading">
                                    <h4>{$companies[$i]}</h4>
                                </div>
                                <div class="timeline-body">
                                    <p><strong>时间段</strong>{$starts[$i]|datetime='Y年m月'} - {$ends[$i]|datetime='Y年m月'}</p>
                                    {if condition="!empty($positions[$i])"}
                                    <p>
                                        <strong>工作岗位</strong>
                                        <span class="position-tag">
                                            <i class="fa fa-user-tie"></i>
                                            {$positions[$i]}
                                        </span>
                                    </p>
                                    {/if}
                                    {if condition="!empty($descriptions[$i])"}
                                    <p><strong>工作内容</strong></p>
                                    <div style="margin-top: 8px; padding: 12px; background: #f8f9fa; border-radius: 4px; line-height: 1.6;">
                                        {$descriptions[$i]}
                                    </div>
                                    {/if}
                                </div>
                            </div>
                        </div>
                        {/if}
                        {/for}
                    </div>
                    {else}
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> 暂无工作经历信息
                    </div>
                    {/if}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 可拖动悬浮返回按钮 -->
<div id="draggable-back-btn" class="btn btn-default btn-bback" title="返回" style="position: fixed; bottom: 30px; left: 30px; z-index: 1000; cursor: move;">
    <i class="fa fa-reply"></i> 返回
</div>

<style>
    /* 返回按钮样式 */
    .btn-bback {
        margin-right: 15px; /* 基础右侧间距 */
    }

    /* 顶部标签样式 */
    .nav-tabs {
        border-bottom: 1px solid #e4e7ed;
        margin-bottom: 15px;
    }
    .nav-tabs > li {
        margin-bottom: -1px;
    }
    .nav-tabs > li > a {
        color: #606266;
        padding: 10px 15px;
        border: 1px solid transparent;
        border-radius: 4px 4px 0 0;
        transition: all 0.3s;
    }
    .nav-tabs > li > a:hover {
        color: #409eff;
        background: #f5f7fa;
        border-color: #e4e7ed;
    }
    .nav-tabs > li.active > a,
    .nav-tabs > li.active > a:hover,
    .nav-tabs > li.active > a:focus {
        color: #409eff;
        background: #fff;
        border: 1px solid #e4e7ed;
        border-bottom-color: transparent;
    }
    
    /* 移动端标签样式 */
    @media screen and (max-width: 768px) {
        .panel-heading {
            display: grid;
            grid-template-columns: repeat(2, 1fr); /* 两列 */
            gap: 10px; /* 与标签之间的间距一致 */
            padding: 10px; /* 调整内边距 */
            align-items: start; /* 顶部对齐 */
        }

        /* 返回按钮移动端样式 */
        .btn-bback {
            display: block;
            width: auto;
            margin-right: 0;
            margin-bottom: 0; /* 间距由 gap 控制 */
            text-align: center;
            grid-column: 1 / span 2; /* 跨越两列 */
            grid-row: 1; /* 放在第一行 */
        }

        /* 调整 build_heading 的位置，如果它在移动端显示的话 */
        .panel-heading > *:not(.btn-bback):not(.nav-tabs) {
            grid-column: 1 / span 2; /* 也跨越两列 */
            grid-row: 2; /* 放在第二行 */
            margin-bottom: 10px; /* 与下方标签的间距 */
        }

        .nav-tabs {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            grid-template-rows: auto auto auto; /* 三行布局 */
            gap: 10px;
            padding: 0;
            border-bottom: none;
            margin-bottom: 0; /* 间距由父容器控制 */
            grid-column: 1 / span 2; /* 跨越两列 */
            grid-row: 3; /* 放在第三行 */
        }
        .nav-tabs > li {
            margin: 0;
            text-align: center;
        }
        .nav-tabs > li:nth-child(1) { grid-area: 1 / 1; } /* 基本信息 - 左上 */
        .nav-tabs > li:nth-child(2) { grid-area: 1 / 2; } /* 联系人 - 右上 */
        .nav-tabs > li:nth-child(3) { grid-area: 2 / 1; } /* 教育经历 - 左中 */
        .nav-tabs > li:nth-child(4) { grid-area: 2 / 2; } /* 工作经历 - 右中 */
        .nav-tabs > li:nth-child(5) { grid-area: 3 / 1 / 4 / 3; } /* 照片资料 - 底部横跨两列 */

        .nav-tabs > li > a {
            display: block;
            padding: 10px 5px;
            margin: 0;
            border: 1px solid #e4e7ed;
            border-radius: 4px;
            font-size: 13px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            height: 100%; /* Ensure full height in grid cell */
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .nav-tabs > li.active > a {
            color: #fff;
            background: #409eff;
            border-color: #409eff;
        }
        .nav-tabs > li.active > a:hover,
        .nav-tabs > li.active > a:focus {
            color: #fff;
            background: #66b1ff;
            border-color: #66b1ff;
        }
        .nav-tabs > li > a:hover {
            color: #409eff;
            background: #ecf5ff;
            border-color: #d9ecff;
        }

        /* 调整面板内边距 */
        .panel-body {
            padding: 10px;
        }
        .panel-heading {
            padding: 10px;
        }

        /* H5 基本信息表格行样式 */
        #t-basic .resume-table tr:not(.info) {
            display: grid;
            grid-template-columns: 120px 1fr; /* 固定左列宽度，右列自适应 */
            gap: 8px; /* 标题和内容之间的间距 */
            border-bottom: 1px solid #eee;
            padding: 8px 0;
        }
        #t-basic .resume-table tr:not(.info) th,
        #t-basic .resume-table tr:not(.info) td {
            border: none;
            padding: 4px 8px;
            /* Allow text wrapping */
            white-space: normal !important; /* Ensure normal wrapping behavior */
            overflow: visible !important; /* Ensure content is visible */
            text-overflow: clip !important; /* Prevent ellipsis */
            /* Ensure content aligns */
            display: flex;
            align-items: center;
        }
        #t-basic .resume-table tr:not(.info) th {
            color: #606266;
            font-weight: normal;
            background: none;
            justify-content: flex-start;
        }
        #t-basic .resume-table tr:not(.info) td {
            font-weight: 500;
            color: #303133;
            justify-content: flex-start;
        }
    }
    
    /* 基础表格样式 */
    .resume-table {
        width: 100%;
        margin-bottom: 0;
    }
    .resume-table th, 
    .resume-table td { 
        vertical-align: middle!important;
        padding: 12px 8px;
        word-break: break-all;
    }
    
    /* 响应式表格样式 */
    @media screen and (max-width: 768px) {
        .resume-table {
            display: block;
            width: 100%;
        }
        
        /* 两列表格样式 */
        .resume-table tr:not(.info):not(.four-columns) {
            display: block;
            border-bottom: 1px solid #eee;
            padding: 8px 0;
        }
        .resume-table tr:not(.info):not(.four-columns) th,
        .resume-table tr:not(.info):not(.four-columns) td {
            border: none;
            padding: 4px 8px;
            display: block;
            width: 100%;
            /* Ensure text wraps */
            white-space: normal !important;
            overflow: visible !important;
            text-overflow: clip !important;
        }
        .resume-table tr:not(.info):not(.four-columns) th {
            color: #606266;
            font-weight: normal;
            background: none;
        }
        .resume-table tr:not(.info):not(.four-columns) td {
            font-weight: 500;
            color: #303133;
        }
        
        /* 四列表格样式 */
        .resume-table tr.four-columns {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            padding: 8px;
            background: #fff;
            border-radius: 4px;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .resume-table tr.four-columns th,
        .resume-table tr.four-columns td {
            width: 100%;
            padding: 4px 8px;
            border: none;
            background: none;
            /* Ensure text wraps */
            white-space: normal !important;
            overflow: visible !important;
            text-overflow: clip !important;
        }
        .resume-table tr.four-columns th {
            color: #909399;
            font-size: 13px;
        }
        .resume-table tr.four-columns td {
            color: #303133;
            font-weight: 500;
        }
        
        /* 调整基本信息布局 */
        .row {
            margin: 0;
        }
        .col-md-3 {
            padding: 15px;
            text-align: center;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 15px;
        }
        .col-md-9 {
            padding: 0;
        }
        .profile-user-img {
            width: 120px !important;
            height: 120px !important;
            margin: 0 auto !important;
            overflow: hidden !important;
            border-radius: 50% !important;
            border: 2px solid #eee !important;
        }
        .profile-user-img img {
            width: 100% !important;
            height: 100% !important;
            object-fit: cover !important;
        }
    }
    
    /* 头像可点击样式 */
    .profile-user-img a {
        display: block;
        width: 100%;
        height: 100%;
        cursor: zoom-in;
        transition: all 0.3s ease;
    }
    
    .profile-user-img a:hover img {
        transform: scale(1.05);
    }
    
    /* 时间轴基础样式 */
    .timeline {
        position: relative;
        margin: 30px 0;
        padding: 20px 0;
    }
    .timeline:before {
        content: '';
        position: absolute;
        top: 0;
        left: 20px;
        height: 100%;
        width: 2px;
        background: #e4e7ed;
    }
    
    /* 时间轴项目样式 */
    .timeline-item {
        position: relative;
        margin-bottom: 30px;
        padding-left: 50px;
    }
    .timeline-item:last-child {
        margin-bottom: 0;
    }
    
    /* 时间轴点样式 */
    .timeline-point {
        position: absolute;
        left: 12px;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        text-align: center;
        line-height: 18px;
        font-size: 12px;
        color: #fff;
        z-index: 1;
        box-shadow: 0 0 0 4px #fff;
    }
    .timeline-point:before {
        content: '';
        position: absolute;
        top: -4px;
        left: -4px;
        right: -4px;
        bottom: -4px;
        border-radius: 50%;
        background: inherit;
        opacity: 0.2;
    }
    .timeline-point-info { background: #409eff; }
    .timeline-point-success { background: #67c23a; }
    .timeline-point-warning { background: #e6a23c; }
    
    /* 时间轴内容样式 */
    .timeline-event {
        position: relative;
        background: #fff;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        transition: all 0.3s ease;
    }
    .timeline-event:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px 0 rgba(0,0,0,.15);
    }
    
    .timeline-heading {
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
    }
    .timeline-heading h4 {
        margin: 0;
        color: #303133;
        font-size: 16px;
        font-weight: 600;
    }
    .timeline-heading .company-info {
        display: flex;
        flex-direction: column;
        gap: 4px;
    }
    
    .timeline-body {
        color: #606266;
        font-size: 14px;
        line-height: 1.6;
    }
    .timeline-body p {
        margin: 8px 0;
    }
    .timeline-body strong {
        color: #303133;
        font-weight: 600;
        margin-right: 8px;
    }
    
    /* 标签样式 */
    .timeline-tag {
        display: inline-block;
        padding: 2px 8px;
        background: #f0f2f5;
        border-radius: 4px;
        color: #606266;
        font-size: 12px;
        margin-right: 8px;
        margin-bottom: 8px;
    }
    
    /* 工作岗位标签样式 */
    .position-tag {
        display: inline-block;
        margin-left: 8px;
        color: #409eff;
        font-size: 14px;
        font-weight: 500;
        transition: all 0.3s ease;
    }
    .position-tag:hover {
        color: #66b1ff;
    }
    .position-tag i {
        margin-right: 6px;
        font-size: 13px;
    }
    
    /* 空状态提示样式 */
    .alert-info {
        background-color: #f4f4f5;
        border-color: #e9e9eb;
        color: #909399;
        padding: 20px;
        border-radius: 8px;
        text-align: center;
        margin: 20px 0;
    }
    .alert-info i {
        margin-right: 8px;
        font-size: 16px;
    }
    
    /* 照片资料样式 */
    .photos-container {
        padding: 20px;
    }
    
    .photo-item {
        margin-bottom: 30px;
    }
    
    .photo-card {
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
        transition: all 0.3s ease;
    }
    
    .photo-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px 0 rgba(0,0,0,.15);
    }
    
    .photo-header {
        padding: 12px 15px;
        background: #f8f9fa;
        border-bottom: 1px solid #ebeef5;
    }
    
    .photo-header h4 {
        margin: 0;
        font-size: 15px;
        color: #303133;
        font-weight: 500;
    }
    
    .photo-header h4 i {
        margin-right: 8px;
        color: #409eff;
    }
    
    .photo-body {
        padding: 0;
        background: #fff;
        text-align: center;
        position: relative;
        overflow: hidden;
        height: 220px;
    }
    
    .photo-preview {
        display: block;
        height: 100%;
        position: relative;
        cursor: zoom-in; /* 添加放大镜光标 */
    }
    
    .photo-body img {
        width: 100%;
        height: 100%;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .photo-preview:hover img {
        transform: scale(1.05);
    }
    
    /* 图片预览弹窗样式 */
    .layui-layer-content {
        overflow: hidden !important;
    }
    
    .image-preview-toolbar {
        padding: 10px;
        background-color: rgba(0, 0, 0, 0.5);
        border-radius: 30px;
        margin: 0 auto;
        width: fit-content;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    }
    
    .image-preview-toolbar button {
        margin: 0 3px;
        border-radius: 4px;
        min-width: 36px;
        opacity: 0.9;
        transition: all 0.2s ease;
    }
    
    .image-preview-toolbar button:hover {
        opacity: 1;
        transform: translateY(-2px);
    }
    
    /* 图片查看器容器样式 */
    .image-viewer-container {
        background-color: rgba(0, 0, 0, 0.7) !important;
        border-radius: 4px;
        padding: 20px;
    }
    
    .image-viewer-container img {
        user-select: none;
        -webkit-user-drag: none;
        cursor: move !important;
        /* 添加轻微的阴影，使图片在半透明背景上更加突出 */
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
        border-radius: 2px;
        object-fit: contain;
    }
    
    /* 自定义Layer样式 */
    .layui-layer-photo-transparent {
        background-color: transparent !important;
        box-shadow: none !important;
        border-radius: 8px;
    }
    
    .layui-layer-photo-transparent .layui-layer-content {
        background-color: transparent !important;
        overflow: hidden !important;
        border-radius: 8px;
    }
    
    /* 修改Layer遮罩层样式 */
    .layui-layer-shade {
        background-color: rgba(0, 0, 0, 0.3) !important; /* 半透明黑色 */
        opacity: 1 !important;
    }
    
    .layui-layer-close {
        color: #fff !important;
        font-size: 24px !important;
        background-color: rgba(0, 0, 0, 0.5) !important; /* 半透明背景 */
        border-radius: 50%;
        padding: 5px;
        width: 30px !important;
        height: 30px !important;
        line-height: 30px !important;
        text-align: center;
        right: -15px !important;
        top: -15px !important;
    }
    
    /* 移动端样式优化 */
    @media (max-width: 768px) {
        .image-preview-toolbar {
            bottom: 10px !important;
            padding: 8px;
            border-radius: 20px;
            max-width: 90%;
        }
        
        .image-preview-toolbar button {
            padding: 3px 6px;
            font-size: 12px;
            margin: 0 2px;
            min-width: 32px;
        }
        
        /* 移动端照片样式 */
        .photos-container {
            padding: 10px;
        }
        
        .photo-item {
            margin-bottom: 15px;
        }
        
        .photo-body {
            height: 180px;
        }
        
        .section-title {
            margin: 20px 0 15px;
            font-size: 16px;
        }
    }
    
    .section-title {
        margin: 30px 0 20px;
        padding-bottom: 10px;
        border-bottom: 1px solid #ebeef5;
        color: #303133;
        font-size: 18px;
        font-weight: 500;
    }
    
    .section-title i {
        margin-right: 10px;
        color: #409eff;
    }
</style> 

<!-- JavaScript for dragging -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    var draggableBtn = document.getElementById('draggable-back-btn');
    var isDragging = false;
    var offsetX, offsetY;
    var startX, startY; // Used to differentiate click from drag

    draggableBtn.addEventListener('mousedown', function(e) {
        isDragging = true;
        startX = e.clientX;
        startY = e.clientY;
        offsetX = e.clientX - draggableBtn.getBoundingClientRect().left;
        offsetY = e.clientY - draggableBtn.getBoundingClientRect().top;

        // Prevent default drag behavior
        e.preventDefault();
    });

    document.addEventListener('mousemove', function(e) {
        if (!isDragging) return;

        // Calculate new position, considering scroll offset
        var newX = e.clientX - offsetX;
        var newY = e.clientY - offsetY;

        // Update element position using fixed positioning
        draggableBtn.style.left = newX + 'px';
        draggableBtn.style.top = newY + 'px';
        draggableBtn.style.right = 'auto';
        draggableBtn.style.bottom = 'auto';
    });

    document.addEventListener('mouseup', function(e) {
        if (!isDragging) return;
        isDragging = false;

        // Check if it was a click or a drag
        var movedX = Math.abs(e.clientX - startX);
        var movedY = Math.abs(e.clientY - startY);

        if (movedX < 5 && movedY < 5) { // If moved less than 5 pixels, treat as tap/click
            // This was a tap/click, allow the click listener to handle it
        } else {
            // This was a drag, prevent default click action and propagation
            e.stopPropagation();
            e.preventDefault(); // Also prevent default action
        }
    });

    // Add touch event listeners for dragging on mobile devices
    draggableBtn.addEventListener('touchstart', function(e) {
        isDragging = true;
        var touch = e.touches[0];
        startX = touch.clientX;
        startY = touch.clientY;
        offsetX = touch.clientX - draggableBtn.getBoundingClientRect().left;
        offsetY = touch.clientY - draggableBtn.getBoundingClientRect().top;

        // Prevent default touch behavior (like scrolling)
        e.preventDefault();
    });

    document.addEventListener('touchmove', function(e) {
        if (!isDragging) return;

        var touch = e.touches[0];
        var newX = touch.clientX - offsetX;
        var newY = touch.clientY - offsetY;

        draggableBtn.style.left = newX + 'px';
        draggableBtn.style.top = newY + 'px';
        draggableBtn.style.right = 'auto';
        draggableBtn.style.bottom = 'auto';
    });

    document.addEventListener('touchend', function(e) {
        if (!isDragging) return;
        isDragging = false;

        // Use the last touch position from the touchend event
        // Note: touchend event does not have touches array, so use changedTouches
        var touch = e.changedTouches[0];
        var movedX = Math.abs(touch.clientX - startX);
        var movedY = Math.abs(touch.clientY - startY);

        if (movedX < 10 && movedY < 10) { // If moved less than 10 pixels, treat as tap
            // This was a tap, trigger the back action directly
            history.back();
        } else {
             // This was a drag, prevent the default action
             e.stopPropagation();
             e.preventDefault(); // Also prevent default action
        }
    });

    // Add click listener for the back action
    draggableBtn.addEventListener('click', function(e) {
        // This click listener will only fire if stopPropagation was not called by mouseup or touchend.
        // In case of a simple click (not drag), mouseup/touchend did not stop propagation.
        // In case of a tap on H5, history.back() is already called in touchend.
        // For completeness and mouse clicks, call history.back() here.
        history.back();
    });
});

// 添加图片预览功能
$(document).on('click', '.photo-preview', function() {
    var src = $(this).data('src');
    if (src) {
        // 使用Layer弹出图片预览
        layer.open({
            type: 1,
            title: false,
            closeBtn: 1,
            shadeClose: true,
            skin: 'layui-layer-photo-transparent',
            area: ['90%', '90%'],
            offset: 'auto',
            maxmin: false,
            moveOut: true,
            resize: false,
            scrollbar: false,
            content: '<div class="image-viewer-container" style="width:100%;height:100%;display:flex;justify-content:center;align-items:center;background:rgba(0,0,0,0.7);overflow:hidden;border-radius:4px;"><img src="' + src + '" style="max-width:96%;max-height:96%;object-fit:contain;" /></div>',
            success: function(layero, index) {
                // 调整弹出层样式为半透明
                $('.layui-layer').css({
                    'background-color': 'transparent',
                    'box-shadow': 'none'
                });
                
                // 调整遮罩层为半透明
                $('.layui-layer-shade').css({
                    'background-color': 'rgba(0,0,0,0.3)',
                    'opacity': '1'
                });
                
                // 在弹出层中添加图片缩放控制
                var $img = $(layero).find('img');
                var $container = $(layero).find('.image-viewer-container');
                
                // 初始状态
                var scale = 1;
                var rotate = 0;
                var posX = 0;
                var posY = 0;
                var isDragging = false;
                var startX, startY;
                
                // 添加缩放和旋转按钮
                var $toolbar = $('<div class="image-preview-toolbar"></div>');
                $toolbar.css({
                    'position': 'absolute',
                    'bottom': '20px',
                    'left': '0',
                    'right': '0',
                    'text-align': 'center',
                    'z-index': '19891015',
                    'background-color': 'rgba(0,0,0,0.6)',
                    'border-radius': '30px',
                    'padding': '8px 15px',
                    'box-shadow': '0 2px 10px rgba(0,0,0,0.2)',
                    'width': 'auto',
                    'max-width': '80%',
                    'margin': '0 auto'
                });
                
                var $zoomIn = $('<button class="btn btn-sm btn-primary" title="放大"><i class="fa fa-search-plus"></i></button>');
                var $zoomOut = $('<button class="btn btn-sm btn-primary" title="缩小"><i class="fa fa-search-minus"></i></button>');
                var $rotateLeft = $('<button class="btn btn-sm btn-info" title="向左旋转"><i class="fa fa-rotate-left"></i></button>');
                var $rotateRight = $('<button class="btn btn-sm btn-info" title="向右旋转"><i class="fa fa-rotate-right"></i></button>');
                var $reset = $('<button class="btn btn-sm btn-default" title="重置"><i class="fa fa-refresh"></i></button>');
                
                $toolbar.append($zoomOut).append(' ').append($zoomIn).append(' ')
                        .append($rotateLeft).append(' ').append($rotateRight).append(' ')
                        .append($reset);
                
                $(layero).append($toolbar);
                
                // 缩放功能
                $zoomIn.on('click', function() {
                    scale += 0.1;
                    updateTransform();
                });
                
                $zoomOut.on('click', function() {
                    if (scale > 0.2) {
                        scale -= 0.1;
                        updateTransform();
                    }
                });
                
                // 旋转功能
                $rotateLeft.on('click', function() {
                    rotate -= 90;
                    updateTransform();
                });
                
                $rotateRight.on('click', function() {
                    rotate += 90;
                    updateTransform();
                });
                
                // 重置
                $reset.on('click', function() {
                    scale = 1;
                    rotate = 0;
                    posX = 0;
                    posY = 0;
                    updateTransform();
                });
                
                // 更新变换
                function updateTransform() {
                    $img.css('transform', 'translate(' + posX + 'px, ' + posY + 'px) scale(' + scale + ') rotate(' + rotate + 'deg)');
                }
                
                // 添加拖动功能
                $img.css('cursor', 'move');
                
                // 鼠标按下事件
                $img.on('mousedown', function(e) {
                    e.preventDefault();
                    isDragging = true;
                    startX = e.clientX;
                    startY = e.clientY;
                });
                
                // 鼠标移动事件
                $(document).on('mousemove.imageDrag', function(e) {
                    if (!isDragging) return;
                    var dx = e.clientX - startX;
                    var dy = e.clientY - startY;
                    posX += dx;
                    posY += dy;
                    updateTransform();
                    startX = e.clientX;
                    startY = e.clientY;
                });
                
                // 鼠标松开事件
                $(document).on('mouseup.imageDrag', function() {
                    isDragging = false;
                });
                
                // 触摸支持
                $img.on('touchstart', function(e) {
                    e.preventDefault();
                    isDragging = true;
                    startX = e.originalEvent.touches[0].clientX;
                    startY = e.originalEvent.touches[0].clientY;
                });
                
                $(document).on('touchmove.imageDrag', function(e) {
                    if (!isDragging) return;
                    var dx = e.originalEvent.touches[0].clientX - startX;
                    var dy = e.originalEvent.touches[0].clientY - startY;
                    posX += dx;
                    posY += dy;
                    updateTransform();
                    startX = e.originalEvent.touches[0].clientX;
                    startY = e.originalEvent.touches[0].clientY;
                });
                
                $(document).on('touchend.imageDrag', function() {
                    isDragging = false;
                });
                
                // 点击空白区域关闭图片查看器
                $container.on('click', function(e) {
                    // 确保点击的是容器而不是图片
                    if (e.target === this) {
                        layer.close(index);
                    }
                });
                
                // 阻止图片上的点击事件冒泡到容器
                $img.on('click', function(e) {
                    e.stopPropagation();
                });
                
                // 阻止工具栏上的点击事件冒泡到弹出层（防止点击工具栏时关闭弹出层）
                $toolbar.on('click', function(e) {
                    e.stopPropagation();
                });
                
                // 图层关闭时清除事件
                $(document).on('click', '.layui-layer-close', function() {
                    $(document).off('mousemove.imageDrag mouseup.imageDrag touchmove.imageDrag touchend.imageDrag');
                });
                
                // 鼠标滚轮缩放
                $img.on('wheel', function(e) {
                    e.preventDefault();
                    if (e.originalEvent.deltaY < 0) {
                        // 向上滚动，放大
                        scale += 0.1;
                    } else {
                        // 向下滚动，缩小
                        if (scale > 0.2) {
                            scale -= 0.1;
                        }
                    }
                    updateTransform();
                });
                
                // 样式调整
                $img.css({
                    'transition': 'none', // 移除过渡效果，使拖动更流畅
                    'max-width': '100%',
                    'max-height': 'calc(100vh - 100px)',
                    'transform-origin': 'center'
                });
            }
        });
    }
});
</script> 