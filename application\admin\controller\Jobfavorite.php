<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\model\Jobfavorite as JobfavoriteModel;

/**
 * 职位收藏管理
 *
 * @icon fa fa-heart
 * @remark 用户收藏的职位管理
 */
class Jobfavorite extends Backend
{
    
    /**
     * Jobfavorite模型对象
     * @var \app\common\model\Jobfavorite
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new JobfavoriteModel;
    }
    
    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自己控制这部分逻辑
     * 需要将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */
    
    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        
        // 获取job_id参数
        $job_id = $this->request->get('job_id');
        
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            
            try {
                // 创建查询构建器
                $query = $this->model->with(['user', 'job']);
                
                // 如果有job_id参数，添加筛选条件
                if ($job_id) {
                    $query->where('job_id', $job_id);
                }
                
                // 获取排序参数
                $sort = $this->request->get('sort', 'id');
                $order = $this->request->get('order', 'desc');
                
                // 应用排序
                $query->order($sort, $order);
                
                // 执行查询
                $list = $query->paginate(10);
                
                $result = array("total" => $list->total(), "rows" => $list->items());
                return json($result);
            } catch (\Exception $e) {
                return json(['total' => 0, 'rows' => [], 'error' => $e->getMessage()]);
            }
        }
        
        // 如果有job_id参数，设置页面标题
        if ($job_id) {
            $job = \app\common\model\Jobs::get($job_id);
            if ($job) {
                $this->view->assign('title', '岗位收藏列表 - ' . $job->job_name);
                // 将job_id传递给前端JS
                $this->assignconfig('job_id', $job_id);
            }
        }
        
        return $this->view->fetch();
    }
    
    /**
     * 添加菜单
     */
    public function menu()
    {
        $menu = [
            [
                'name' => 'jobfavorite',
                'title' => '职位收藏管理',
                'icon' => 'fa fa-heart',
                'remark' => '用户收藏的职位管理',
                'sublist' => [
                    ['name' => 'jobfavorite/index', 'title' => '收藏列表'],
                    ['name' => 'jobfavorite/add', 'title' => '添加收藏'],
                    ['name' => 'jobfavorite/edit', 'title' => '编辑收藏'],
                    ['name' => 'jobfavorite/del', 'title' => '删除收藏'],
                    ['name' => 'jobfavorite/multi', 'title' => '批量更新'],
                ],
                'weigh' => 100
            ]
        ];
        
        $this->success('菜单创建成功', null, $menu);
    }
} 