<?php
/**
 * PhpWord 加载测试
 */

// 包含autoload文件
require_once __DIR__ . '/../vendor/autoload.php';

echo "<h1>PhpWord 加载测试</h1>";

// 测试PhpWord类是否存在
if (class_exists('\PhpOffice\PhpWord\PhpWord')) {
    echo "<p style='color: green;'>✅ PhpWord 类加载成功！</p>";
    
    try {
        // 创建PhpWord对象
        $phpWord = new \PhpOffice\PhpWord\PhpWord();
        echo "<p style='color: green;'>✅ PhpWord 对象创建成功！</p>";
        
        // 创建一个简单的文档
        $section = $phpWord->addSection();
        $section->addText('Hello World! PhpWord 工作正常。');
        
        echo "<p style='color: green;'>✅ PhpWord 功能测试成功！</p>";
        echo "<p>PhpWord 已经可以正常使用了。</p>";
        
        // 显示一些基本信息
        echo "<h2>PhpWord 信息</h2>";
        echo "<p>PhpWord 版本: " . (defined('\PhpOffice\PhpWord\PhpWord::VERSION') ? \PhpOffice\PhpWord\PhpWord::VERSION : '未知') . "</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ PhpWord 对象创建失败: " . $e->getMessage() . "</p>";
        echo "<p>错误详情: " . $e->getTraceAsString() . "</p>";
    }
} else {
    echo "<p style='color: red;'>❌ PhpWord 类无法加载</p>";
    
    // 显示调试信息
    echo "<h2>调试信息</h2>";
    echo "<p>PHP版本: " . PHP_VERSION . "</p>";
    
    // 检查必需的扩展
    $requiredExtensions = ['dom', 'xml', 'json'];
    echo "<h3>必需扩展检查:</h3>";
    foreach ($requiredExtensions as $ext) {
        if (extension_loaded($ext)) {
            echo "<p style='color: green;'>✅ $ext</p>";
        } else {
            echo "<p style='color: red;'>❌ $ext (缺失)</p>";
        }
    }
    
    // 检查PhpWord文件是否存在
    $phpwordFile = __DIR__ . '/../vendor/phpoffice/phpword/src/PhpWord/PhpWord.php';
    if (file_exists($phpwordFile)) {
        echo "<p style='color: green;'>✅ PhpWord 文件存在: $phpwordFile</p>";
    } else {
        echo "<p style='color: red;'>❌ PhpWord 文件不存在: $phpwordFile</p>";
    }
    
    // 检查autoload配置
    $autoloadFile = __DIR__ . '/../vendor/composer/autoload_psr4.php';
    if (file_exists($autoloadFile)) {
        $psr4Config = include $autoloadFile;
        if (isset($psr4Config['PhpOffice\\PhpWord\\'])) {
            echo "<p style='color: green;'>✅ PhpWord autoload 配置存在</p>";
        } else {
            echo "<p style='color: red;'>❌ PhpWord autoload 配置缺失</p>";
        }
    }
}

echo "<hr>";
echo "<p><small>测试完成时间: " . date('Y-m-d H:i:s') . "</small></p>";
?>
