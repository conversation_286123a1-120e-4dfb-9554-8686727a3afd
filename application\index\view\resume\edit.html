<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>编辑简历</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="https://cdn.staticfile.org/bootstrap/5.1.3/css/bootstrap.min.css" as="style">
    <link rel="preload" href="https://cdn.staticfile.org/font-awesome/6.0.0/css/all.min.css" as="style">
    <link rel="preload" href="https://cdn.staticfile.org/sweetalert2/11.7.32/sweetalert2.min.css" as="style">
    <link rel="preload" href="__CDN__/assets/css/index.css" as="style">
    
    <!-- 使用国内CDN -->
    <link href="https://cdn.staticfile.org/bootstrap/5.1.3/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.staticfile.org/sweetalert2/11.7.32/sweetalert2.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">

    <!-- 预加载JavaScript -->
    <link rel="preload" href="https://cdn.staticfile.org/jquery/3.6.0/jquery.min.js" as="script">
    <link rel="preload" href="https://cdn.staticfile.org/bootstrap/5.1.3/js/bootstrap.bundle.min.js" as="script">
    <link rel="preload" href="https://cdn.staticfile.org/sweetalert2/11.7.32/sweetalert2.all.min.js" as="script">

    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --accent-color: #e74c3c;
            --light-bg: #f8f9fa;
            --dark-bg: #2c3e50;
            --success-color: #2ecc71;
            --warning-color: #f1c40f;
            --border-radius: 12px;
            --box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            --transition: all 0.3s ease;
        }
        
        body {
            background-color: var(--light-bg);
            color: var(--primary-color);
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        }

        .navbar {
        background-color: rgba(255, 255, 255, 0.95) !important;
        backdrop-filter: blur(10px);
        box-shadow: var(--box-shadow);
        padding: 1rem 0;
        transition: var(--transition);
    }

    .navbar.scrolled {
        padding: 0.5rem 0;
    }

    .navbar-brand {
        font-weight: 700;
        color: var(--primary-color) !important;
        font-size: 1.5rem;
        display: flex;
        align-items: center;
        letter-spacing: -0.5px;
    }

    .navbar-brand i {
        color: var(--primary-color);
        margin-right: 0.5rem;
        font-size: 1.8rem;
    }

    .nav-link {
        color: var(--text-primary) !important;
        font-weight: 500;
        padding: 0.75rem 1.25rem !important;
        margin: 0 0.25rem;
        border-radius: var(--border-radius);
        transition: var(--transition);
        position: relative;
    }

    .nav-link::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 2px;
        background-color: var(--primary-color);
        transition: var(--transition);
    }

    .nav-link:hover::after {
        width: 80%;
    }

    .nav-link:hover {
        background-color: rgba(26, 115, 232, 0.08);
        color: var(--primary-color) !important;
    }

    .nav-link.active {
        background-color: var(--primary-color) !important;
        color: white !important;
    }

    .nav-link.active::after {
        display: none;
    }

    @media (max-width: 991.98px) {
        .navbar-collapse {
            background-color: rgba(255, 255, 255, 0.98);
            padding: 1rem;
            border-radius: var(--border-radius);
            box-shadow: var(--box-shadow);
            margin-top: 1rem;
            backdrop-filter: blur(10px);
        }

        .nav-link {
            padding: 1rem !important;
            margin: 0.5rem 0;
        }

        .navbar-buttons {
            margin-top: 1rem;
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .navbar-buttons .btn {
            width: 100%;
        }
    }

        .card {
            border: none;
            box-shadow: var(--box-shadow);
            border-radius: var(--border-radius);
            transition: var(--transition);
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0,0,0,0.15);
        }

        .card-header {
            background-color: white;
            border-bottom: 1px solid rgba(0,0,0,0.05);
            padding: 1.5rem;
            border-radius: var(--border-radius) var(--border-radius) 0 0 !important;
        }

        .card-body {
            padding: 2rem;
        }

        .section-title {
            color: var(--primary-color);
            font-weight: 600;
            margin-bottom: 1.5rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--secondary-color);
            display: flex;
            align-items: center;
        }

        .section-title i {
            margin-right: 0.75rem;
            color: var(--secondary-color);
        }

        .form-label {
            font-weight: 500;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .form-control, .form-select {
            border-radius: var(--border-radius);
            border: 1px solid #e0e0e0;
            padding: 0.75rem;
            transition: var(--transition);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-color);
            box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
        }

        .btn {
            border-radius: var(--border-radius);
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            transition: var(--transition);
        }

        .btn-primary {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-primary:hover {
            background-color: #2980b9;
            border-color: #2980b9;
            transform: translateY(-1px);
        }

        .btn-outline-primary {
            color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--secondary-color);
            color: white;
            transform: translateY(-1px);
        }

        .btn-danger {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
        }

        .btn-danger:hover {
            background-color: #c0392b;
            border-color: #c0392b;
        }

        .file-upload {
            position: relative;
            overflow: hidden;
            border-radius: var(--border-radius);
        }

        .file-upload input[type="file"] {
            position: absolute;
            top: 0;
            right: 0;
            min-width: 100%;
            min-height: 100%;
            font-size: 100px;
            text-align: right;
            filter: alpha(opacity=0);
            opacity: 0;
            outline: none;
            cursor: pointer;
            display: block;
        }

        .file-upload-label {
            display: block;
            padding: 1rem;
            background-color: var(--light-bg);
            border: 2px dashed #dee2e6;
            border-radius: var(--border-radius);
            text-align: center;
            cursor: pointer;
            transition: var(--transition);
        }

        .file-upload-label:hover {
            background-color: #e9ecef;
            border-color: var(--secondary-color);
        }

        .preview-container {
            position: relative;
            display: inline-block;
            margin-top: 10px;
        }

        .preview-container img {
            max-width: 100%;
            max-height: 200px;
            border-radius: var(--border-radius);
        }

        .delete-image {
            position: absolute;
            top: -10px;
            right: -10px;
            background-color: var(--accent-color);
            color: white;
            border: none;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 1;
            transition: opacity 0.3s ease;
            z-index: 1;
        }

        .preview-container:hover .delete-image {
            opacity: 1;
        }

        .delete-image:hover {
            background-color: #c0392b;
        }

        .progress-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 3rem;
            position: relative;
            padding: 0 2rem;
            overflow-x: auto;
            -webkit-overflow-scrolling: touch;
            scrollbar-width: none; /* Firefox */
            -ms-overflow-style: none; /* IE and Edge */
        }

        .progress-steps::-webkit-scrollbar {
            display: none; /* Chrome, Safari, Opera */
        }

        .progress-step {
            flex: 0 0 auto;
            text-align: center;
            position: relative;
            z-index: 1;
            min-width: 80px;
            padding: 0 10px;
        }

        .progress-step::before {
            content: '';
            width: 32px;
            height: 32px;
            background-color: var(--light-bg);
            border: 2px solid #dee2e6;
            border-radius: 50%;
            display: block;
            margin: 0 auto 0.5rem;
            line-height: 28px;
            color: white;
            transition: var(--transition);
        }

        .progress-step.active::before {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .progress-step span {
            font-weight: 500;
            color: var(--primary-color);
            font-size: 0.9rem;
            white-space: nowrap;
        }

        .progress-line {
            position: absolute;
            top: 16px;
            left: 0;
            right: 0;
            height: 2px;
            background-color: #dee2e6;
            z-index: 0;
        }

        .education-item, .work-item, .contact-item {
            margin-bottom: 1.5rem;
            border: 1px solid rgba(0,0,0,0.05);
        }

        .education-item .card-body, .work-item .card-body, .contact-item .card-body {
            padding: 1.5rem;
        }

        .required-field::after {
            content: ' *';
            color: var(--accent-color);
        }

        .form-control.is-invalid,
        .form-select.is-invalid {
            border-color: var(--accent-color);
            background-color: rgba(231, 76, 60, 0.05);
        }

        .form-control.is-invalid:focus,
        .form-select.is-invalid:focus {
            border-color: var(--accent-color);
            box-shadow: 0 0 0 0.2rem rgba(231, 76, 60, 0.25);
        }

        .invalid-feedback {
            display: none;
            color: var(--accent-color);
            font-size: 0.875rem;
            margin-top: 0.25rem;
        }

        .is-invalid ~ .invalid-feedback {
            display: block;
        }

        .required-field.is-invalid::after {
            content: ' *';
            color: var(--accent-color);
        }

        @media (max-width: 768px) {
            .card-body {
                padding: 1.5rem;
            }

            .progress-steps {
                padding: 0 1rem;
                margin-bottom: 2rem;
            }

            .progress-step {
                min-width: 70px;
            }

            .progress-step::before {
                width: 28px;
                height: 28px;
                margin-bottom: 0.3rem;
            }

            .progress-step span {
                font-size: 0.8rem;
            }

            .progress-line {
                top: 14px;
            }
        }

        @media (max-width: 576px) {
            .progress-steps {
                padding: 0 0.5rem;
            }

            .progress-step {
                min-width: 60px;
            }

            .progress-step::before {
                width: 24px;
                height: 24px;
                margin-bottom: 0.2rem;
            }

            .progress-step span {
                font-size: 0.75rem;
            }

            .progress-line {
                top: 12px;
            }
        }
    </style>
    {php}
    // 处理联系人、教育背景、工作经历的数据，用于JavaScript初始化
    $contact_names = explode('|', $resume['contact_name'] ?? '');
    $contactCount = count(array_filter($contact_names)) ?: 1;
    
    $education_schools = explode('|', $resume['education_school'] ?? '');
    $educationCount = count(array_filter($education_schools)) ?: 1;
    
    $job_companies = explode('|', $resume['job_company'] ?? '');
    $workCount = count(array_filter($job_companies)) ?: 1;
    {/php}
    <script>
        // 预定义变量
        var contactCount = {$contactCount};
        var educationCount = {$educationCount};
        var workCount = {$workCount};
    </script>
</head>
<body>
    <!-- 顶部导航栏 -->
<nav class="navbar navbar-expand-lg navbar-light fixed-top">
    <div class="container">
        <a class="navbar-brand" href="{:url('index/index/index')}">
            <i class="fas fa-briefcase"></i>{$site.name|htmlentities}
        </a>
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='Index'}active{/eq}" href="{:url('index/index/index')}">
                        <i class="fas fa-home"></i>首页
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='Job'}active{/eq}" href="{:url('index/job/list')}">
                        <i class="fas fa-list me-1"></i>职位列表
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {eq name='Think.url.controller' value='News'}active{/eq}" href="{:url('index/resume/list')}">
                        <i class="fas fa-newspaper me-1"></i>我的简历
                    </a>
                </li>
            </ul>
            <div class="navbar-buttons">
                {if $user}
                <!-- 已登录状态 -->
                <a href="{:url('index/user/index')}" class="btn btn-outline-primary me-2">
                    <i class="fas fa-user me-1"></i>个人中心
                </a>
                <a href="javascript:;" class="btn btn-outline-danger" id="btn-logout">
                    <i class="fas fa-sign-out-alt me-1"></i>退出登录
                </a>
                <!-- 退出登录表单 -->
                <form id="logout-form" action="{:url('index/user/logout')}" method="post" style="display: none;">
                    {:token()}
                </form>
                {else}
                <!-- 未登录状态 -->
                <a href="{:url('index/user/login')}" class="btn btn-primary">
                    <i class="fas fa-sign-in-alt me-1"></i>登录/注册
                </a>
                {/if}
            </div>
        </div>
    </div>
</nav>

    <!-- 主要内容 -->
    <div class="container mt-5 pt-5">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header">
                        <h4 class="mb-0">
                            <i class="fas fa-edit me-2"></i>编辑简历
                        </h4>
                    </div>
                    <div class="card-body">
                        <!-- 进度步骤 -->
                        <div class="progress-steps">
                            <div class="progress-line"></div>
                            <div class="progress-step active">
                                <span>基本信息</span>
                            </div>
                            <div class="progress-step">
                                <span>语言能力</span>
                            </div>
                            <div class="progress-step">
                                <span>联系人信息</span>
                            </div>
                            <div class="progress-step">
                                <span>教育背景</span>
                            </div>
                            <div class="progress-step">
                                <span>工作经历</span>
                            </div>
                            <div class="progress-step">
                                <span>证件信息</span>
                            </div>
                            <div class="progress-step">
                                <span>照片上传</span>
                            </div>
                        </div>

                        <form action="{:url('index/resume/edit', ['id'=>$resume.id])}" method="post" enctype="multipart/form-data">
                            <!-- 基本信息 -->
                            <div class="form-section" id="section1">
                                <h5 class="section-title">
                                    <i class="fas fa-user"></i>基本信息
                                </h5>
                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <label class="form-label required-field">姓名</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control {if $ocr_enabled != '1'}rounded-end{/if}" name="name" value="{$resume.name}" required>
                                            {if $ocr_enabled == '1'}
                                            <button type="button" class="btn btn-outline-primary" onclick="scanIdCard('front')" style="border-top-left-radius: 0; border-bottom-left-radius: 0;">
                                                <i class="fas fa-camera me-1"></i>扫描证件
                                            </button>
                                            {/if}
                                        </div>
                                        <div class="invalid-feedback">请输入姓名</div>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">身份证号</label>
                                        <input type="text" class="form-control" name="id_card" value="{$resume.id_card}" required>
                                        <div class="invalid-feedback">请输入身份证号</div>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">性别</label>
                                        <select class="form-select" name="gender" required>
                                            <option value="">请选择</option>
                                            <option value="男" {if condition="$resume.gender eq '男'"}selected{/if}>男</option>
                                            <option value="女" {if condition="$resume.gender eq '女'"}selected{/if}>女</option>
                                        </select>
                                        <div class="invalid-feedback">请选择性别</div>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <label class="form-label required-field">年龄</label>
                                        <input type="number" class="form-control" name="age" value="{$resume.age}" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">民族</label>
                                        <input type="text" class="form-control" name="ethnicity" value="{$resume.ethnicity}" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">身高(cm)</label>
                                        <input type="number" step="0.01" class="form-control" name="height" value="{$resume.height}" required>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <label class="form-label required-field">体重(kg)</label>
                                        <input type="number" step="0.01" class="form-control" name="weight" value="{$resume.weight}" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">出生日期</label>
                                        <input type="date" class="form-control" name="birth_date" value="{$resume.birth_date}" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">手机号码</label>
                                        <input type="tel" class="form-control" name="phone" value="{$resume.phone}" required>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <label class="form-label required-field">婚姻状况</label>
                                        <select class="form-select" name="marital_status" required>
                                            <option value="">请选择</option>
                                            <option value="未婚" {if condition="$resume.marital_status eq '未婚'"}selected{/if}>未婚</option>
                                            <option value="已婚" {if condition="$resume.marital_status eq '已婚'"}selected{/if}>已婚</option>
                                            <option value="离异" {if condition="$resume.marital_status eq '离异'"}selected{/if}>离异</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">意向职位</label>
                                        <input type="text" class="form-control" name="intended_position" value="{$resume.intended_position}" required>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">应聘职位</label>
                                        <input type="text" class="form-control" name="applied_position" value="{$resume.applied_position}" required>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <label class="form-label">特殊职业资格证</label>
                                    <textarea type="text" class="form-control" name="special_certificate" value="{$resume.special_certificate}"></textarea>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <label class="form-label required-field">自我评价</label>
                                        {if condition="config('site.openai_enabled') eq '1'"}
                                        <div class="input-group mb-2">
                                            <select class="form-select" style="max-width: 150px;" id="selfEvaluationWordCount">
                                                <option value="50">50字</option>
                                                <option value="100">100字</option>
                                                <option value="150">150字</option>
                                                <option value="200">200字</option>
                                                <option value="300">300字</option>
                                            </select>
                                            <button type="button" class="btn btn-primary" onclick="generateSelfEvaluation()">
                                                <i class="fas fa-magic me-1"></i>AI润色
                                            </button>
                                        </div>
                                        {/if}
                                        <textarea class="form-control" name="self_evaluation" rows="3" required>{$resume.self_evaluation}</textarea>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-12">
                                        <label class="form-label required-field">兴趣爱好</label>
                                        {if condition="config('site.openai_enabled') eq '1'"}
                                        <div class="input-group mb-2">
                                            <select class="form-select" style="max-width: 150px;" id="hobbiesWordCount">
                                                <option value="30">30字</option>
                                                <option value="50">50字</option>
                                                <option value="80">80字</option>
                                                <option value="100">100字</option>
                                                <option value="150">150字</option>
                                            </select>
                                            <button type="button" class="btn btn-primary" onclick="generateHobbies()">
                                                <i class="fas fa-magic me-1"></i>AI润色
                                            </button>
                                        </div>
                                        {/if}
                                        <textarea class="form-control" name="hobbies" rows="2" required>{$resume.hobbies}</textarea>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label class="form-label required-field">户口所在地</label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <select class="form-select" name="hukou_province" id="hukouProvince" value="{$resume.hukou_province}" required>
                                                    <option value="">请选择省份</option>
                                                </select>
                                            </div>
                                            <div class="col-md-6">
                                                <select class="form-select" name="hukou_city" id="hukouCity" value="{$resume.hukou_city}" required>
                                                    <option value="">请选择城市</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label required-field">常住地址</label>
                                        <div class="row">
                                            <div class="col-md-4">
                                                <select class="form-select" name="residence_province" id="residenceProvince" value="{$resume.residence_province}" required>
                                                    <option value="">请选择省份</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <select class="form-select" name="residence_city" id="residenceCity" value="{$resume.residence_city}" required>
                                                    <option value="">请选择城市</option>
                                                </select>
                                            </div>
                                            <div class="col-md-4">
                                                <select class="form-select" name="residence_district" id="residenceDistrict" value="{$resume.residence_district}" required>
                                                    <option value="">请选择区县</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end mt-4">
                                    <button type="button" class="btn btn-primary" onclick="nextSection(1)">
                                        <i class="fas fa-arrow-right me-2"></i>下一步
                                    </button>
                                </div>
                            </div>

                            <!-- 语言能力 -->
                            <div class="form-section" id="section2" style="display: none;">
                                <h5 class="section-title">
                                    <i class="fas fa-language"></i>语言能力
                                </h5>
                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <label class="form-label required-field">粤语水平</label>
                                        <select class="form-select" name="cantonese_level" required>
                                            <option value="">请选择</option>
                                            <option value="不会" {if condition="$resume.cantonese_level eq '不会'"}selected{/if}>不会</option>
                                            <option value="一般" {if condition="$resume.cantonese_level eq '一般'"}selected{/if}>一般</option>
                                            <option value="熟练" {if condition="$resume.cantonese_level eq '熟练'"}selected{/if}>熟练</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">国语水平</label>
                                        <select class="form-select" name="mandarin_level" required>
                                            <option value="">请选择</option>
                                            <option value="不会" {if condition="$resume.mandarin_level eq '不会'"}selected{/if}>不会</option>
                                            <option value="一般" {if condition="$resume.mandarin_level eq '一般'"}selected{/if}>一般</option>
                                            <option value="熟练" {if condition="$resume.mandarin_level eq '熟练'"}selected{/if}>熟练</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">英语水平</label>
                                        <select class="form-select" name="english_level" required>
                                            <option value="">请选择</option>
                                            <option value="不会" {if condition="$resume.english_level eq '不会'"}selected{/if}>不会</option>
                                            <option value="一般" {if condition="$resume.english_level eq '一般'"}selected{/if}>一般</option>
                                            <option value="熟练" {if condition="$resume.english_level eq '熟练'"}selected{/if}>熟练</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="text-end mt-4">
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="prevSection(2)">
                                        <i class="fas fa-arrow-left me-2"></i>上一步
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="nextSection(2)">
                                        <i class="fas fa-arrow-right me-2"></i>下一步
                                    </button>
                                </div>
                            </div>

                            <!-- 联系人信息 -->
                            <div class="form-section" id="section3" style="display: none;">
                                <h5 class="section-title">
                                    <i class="fas fa-address-book"></i>联系人信息
                                </h5>
                                <div id="contactList">
                                    {php}
                                        $contact_names = explode('|', $resume['contact_name'] ?? '');
                                        $contact_relations = explode('|', $resume['contact_relation'] ?? '');
                                        $contact_ages = explode('|', $resume['contact_age'] ?? '');
                                        $contact_jobs = explode('|', $resume['contact_job'] ?? '');
                                        $contactCount = count($contact_names);
                                    {/php}
                                    
                                    {if condition="$contactCount > 0"}
                                        {volist name="contact_names" id="name" key="i"}
                                        <div class="contact-item card">
                                            <div class="card-body">
                                                {if condition="$i > 1"}
                                                <div class="d-flex justify-content-between align-items-center mb-3">
                                                    <h6 class="mb-0">联系人 #{$i}</h6>
                                                    <button type="button" class="btn btn-danger btn-sm" onclick="removeContact(this)">
                                                        <i class="fas fa-times me-1"></i>删除
                                                    </button>
                                                </div>
                                                {/if}
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <label class="form-label {if condition="$i eq 1"}required-field{/if}">联系人姓名</label>
                                                        <input type="text" class="form-control" name="contact_name[]" value="{$name}" {if condition="$i eq 1"}required{/if}>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label {if condition="$i eq 1"}required-field{/if}">与本人关系</label>
                                                        <select class="form-select" name="contact_relation[]" {if condition="$i eq 1"}required{/if}>
                                                            <option value="父母" {if condition="isset($contact_relations[$i-1]) && $contact_relations[$i-1] eq '父母'"}selected{/if}>父母</option>
                                                            <option value="配偶" {if condition="isset($contact_relations[$i-1]) && $contact_relations[$i-1] eq '配偶'"}selected{/if}>配偶</option>
                                                            <option value="子女" {if condition="isset($contact_relations[$i-1]) && $contact_relations[$i-1] eq '子女'"}selected{/if}>子女</option>
                                                            <option value="兄弟姐妹" {if condition="isset($contact_relations[$i-1]) && $contact_relations[$i-1] eq '兄弟姐妹'"}selected{/if}>兄弟姐妹</option>
                                                            <option value="其他亲属" {if condition="isset($contact_relations[$i-1]) && $contact_relations[$i-1] eq '其他亲属'"}selected{/if}>其他亲属</option>
                                                            <option value="朋友" {if condition="isset($contact_relations[$i-1]) && $contact_relations[$i-1] eq '朋友'"}selected{/if}>朋友</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-md-6">
                                                        <label class="form-label {if condition="$i eq 1"}required-field{/if}">联系人年龄</label>
                                                        <input type="number" class="form-control" name="contact_age[]" value="{$contact_ages[$i-1]|default=''}" {if condition="$i eq 1"}required{/if}>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label {if condition="$i eq 1"}required-field{/if}">联系人工作</label>
                                                        <input type="text" class="form-control" name="contact_job[]" value="{$contact_jobs[$i-1]|default=''}" {if condition="$i eq 1"}required{/if}>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {/volist}
                                    {else}
                                        <div class="contact-item card">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <label class="form-label required-field">联系人姓名</label>
                                                        <input type="text" class="form-control" name="contact_name[]" required>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label required-field">与本人关系</label>
                                                        <select class="form-select" name="contact_relation[]" required>
                                                            <option value="父母">父母</option>
                                                            <option value="配偶">配偶</option>
                                                            <option value="子女">子女</option>
                                                            <option value="兄弟姐妹">兄弟姐妹</option>
                                                            <option value="其他亲属">其他亲属</option>
                                                            <option value="朋友">朋友</option>
                                                        </select>
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-md-6">
                                                        <label class="form-label">联系人年龄</label>
                                                        <input type="number" class="form-control" name="contact_age[]">
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">联系人工作</label>
                                                        <input type="text" class="form-control" name="contact_job[]">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {/if}
                                </div>
                                <div class="text-center mb-4">
                                    <button type="button" class="btn btn-outline-primary" onclick="addContact()">
                                        <i class="fas fa-plus me-2"></i>添加联系人
                                    </button>
                                </div>
                                <div class="text-end mt-4">
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="prevSection(3)">
                                        <i class="fas fa-arrow-left me-2"></i>上一步
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="nextSection(3)">
                                        <i class="fas fa-arrow-right me-2"></i>下一步
                                    </button>
                                </div>
                            </div>

                            <!-- 教育背景 -->
                            <div class="form-section" id="section4" style="display: none;">
                                <h5 class="section-title">
                                    <i class="fas fa-graduation-cap"></i>教育背景
                                </h5>
                                <div id="educationList">
                                    {php}
                                        $education_starts = explode('|', $resume['education_start'] ?? '');
                                        $education_ends = explode('|', $resume['education_end'] ?? '');
                                        $education_schools = explode('|', $resume['education_school'] ?? '');
                                        $education_majors = explode('|', $resume['education_major'] ?? '');
                                        $graduation_educations = explode('|', $resume['graduation_education'] ?? '');
                                        $educationCount = count($education_schools);
                                    {/php}
                                    
                                    {if condition="$educationCount > 0"}
                                        {volist name="education_schools" id="school" key="i"}
                                        <div class="education-item card">
                                            <div class="card-body">
                                                {if condition="$i > 1"}
                                                <div class="d-flex justify-content-between align-items-center mb-3">
                                                    <h6 class="mb-0">教育经历 #{$i}</h6>
                                                    <button type="button" class="btn btn-danger btn-sm" onclick="removeEducation(this)">
                                                        <i class="fas fa-times me-1"></i>删除
                                                    </button>
                                                </div>
                                                {/if}
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <label class="form-label {if condition="$i eq 1"}required-field{/if}">学历</label>
                                                        <select class="form-select" name="graduation_education[]" {if condition="$i eq 1"}required{/if}>
                                                            <option value="小学" {if condition="isset($graduation_educations[$i-1]) && $graduation_educations[$i-1] eq '小学'"}selected{/if}>小学</option>
                                                            <option value="初中" {if condition="isset($graduation_educations[$i-1]) && $graduation_educations[$i-1] eq '初中'"}selected{/if}>初中</option>
                                                            <option value="高中" {if condition="isset($graduation_educations[$i-1]) && $graduation_educations[$i-1] eq '高中'"}selected{/if}>高中</option>
                                                            <option value="中专" {if condition="isset($graduation_educations[$i-1]) && $graduation_educations[$i-1] eq '中专'"}selected{/if}>中专</option>
                                                            <option value="大专" {if condition="isset($graduation_educations[$i-1]) && $graduation_educations[$i-1] eq '大专'"}selected{/if}>大专</option>
                                                            <option value="本科" {if condition="isset($graduation_educations[$i-1]) && $graduation_educations[$i-1] eq '本科'"}selected{/if}>本科</option>
                                                            <option value="硕士" {if condition="isset($graduation_educations[$i-1]) && $graduation_educations[$i-1] eq '硕士'"}selected{/if}>硕士</option>
                                                            <option value="博士" {if condition="isset($graduation_educations[$i-1]) && $graduation_educations[$i-1] eq '博士'"}selected{/if}>博士</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label class="form-label {if condition="$i eq 1"}required-field{/if}">学校名称</label>
                                                        <input type="text" class="form-control" name="education_school[]" value="{$school}" {if condition="$i eq 1"}required{/if}>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label class="form-label">专业</label>
                                                        <input type="text" class="form-control" name="education_major[]" value="{$education_majors[$i-1]|default=''}">
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-md-6">
                                                        <label class="form-label {if condition="$i eq 1"}required-field{/if}">入学时间</label>
                                                        <input type="month" class="form-control" name="education_start[]" value="{$education_starts[$i-1]|default=''}" {if condition="$i eq 1"}required{/if}>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label {if condition="$i eq 1"}required-field{/if}">毕业时间</label>
                                                        <input type="month" class="form-control" name="education_end[]" value="{$education_ends[$i-1]|default=''}" {if condition="$i eq 1"}required{/if}>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {/volist}
                                    {else}
                                        <div class="education-item card">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-4">
                                                        <label class="form-label required-field">学历</label>
                                                        <select class="form-select" name="graduation_education[]" required>
                                                            <option value="">请选择</option>
                                                            <option value="小学">小学</option>
                                                            <option value="初中">初中</option>
                                                            <option value="高中">高中</option>
                                                            <option value="中专">中专</option>
                                                            <option value="大专">大专</option>
                                                            <option value="本科">本科</option>
                                                            <option value="硕士">硕士</option>
                                                            <option value="博士">博士</option>
                                                        </select>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label class="form-label required-field">学校名称</label>
                                                        <input type="text" class="form-control" name="education_school[]" required>
                                                    </div>
                                                    <div class="col-md-4">
                                                        <label class="form-label">专业</label>
                                                        <input type="text" class="form-control" name="education_major[]">
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-md-6">
                                                        <label class="form-label">入学时间</label>
                                                        <input type="month" class="form-control" name="education_start[]">
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">毕业时间</label>
                                                        <input type="month" class="form-control" name="education_end[]">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {/if}
                                </div>
                                <div class="text-center mb-4">
                                    <button type="button" class="btn btn-outline-primary" onclick="addEducation()">
                                        <i class="fas fa-plus me-2"></i>添加教育经历
                                    </button>
                                </div>
                                <div class="text-end mt-4">
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="prevSection(4)">
                                        <i class="fas fa-arrow-left me-2"></i>上一步
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="nextSection(4)">
                                        <i class="fas fa-arrow-right me-2"></i>下一步
                                    </button>
                                </div>
                            </div>

                            <!-- 工作经历 -->
                            <div class="form-section" id="section5" style="display: none;">
                                <h5 class="section-title">
                                    <i class="fas fa-briefcase"></i>工作经历
                                </h5>
                                <div id="workList">
                                    {php}
                                        $job_starts = explode('|', $resume['job_start'] ?? '');
                                        $job_ends = explode('|', $resume['job_end'] ?? '');
                                        $job_companies = explode('|', $resume['job_company'] ?? '');
                                        $job_positions = explode('|', $resume['job_position'] ?? '');
                                        $job_descriptions = explode('|', $resume['job_description'] ?? '');
                                        $workCount = count($job_companies);
                                    {/php}
                                    
                                    {if condition="$workCount > 0"}
                                        {volist name="job_companies" id="company" key="i"}
                                        <div class="work-item card">
                                            <div class="card-body">
                                                {if condition="$i > 1"}
                                                <div class="d-flex justify-content-between align-items-center mb-3">
                                                    <h6 class="mb-0">工作经历 #{$i}</h6>
                                                    <button type="button" class="btn btn-danger btn-sm" onclick="removeWork(this)">
                                                        <i class="fas fa-times me-1"></i>删除
                                                    </button>
                                                </div>
                                                {/if}
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <label class="form-label {if condition="$i eq 1"}required-field{/if}">工作单位</label>
                                                        <input type="text" class="form-control" name="job_company[]" value="{$company}" {if condition="$i eq 1"}required{/if}>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label {if condition="$i eq 1"}required-field{/if}">工作岗位</label>
                                                        <input type="text" class="form-control" name="job_position[]" value="{$job_positions[$i-1]|default=''}" {if condition="$i eq 1"}required{/if}>
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-md-6">
                                                        <label class="form-label {if condition="$i eq 1"}required-field{/if}">入职时间</label>
                                                        <input type="month" class="form-control" name="job_start[]" value="{$job_starts[$i-1]|default=''}" {if condition="$i eq 1"}required{/if}>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label {if condition="$i eq 1"}required-field{/if}">离职时间</label>
                                                        <input type="month" class="form-control" name="job_end[]" value="{$job_ends[$i-1]|default=''}" {if condition="$i eq 1"}required{/if}>
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-md-12">
                                                        <label class="form-label {if condition="$i eq 1"}required-field{/if}">工作内容</label>
                                                        {if condition="config('site.openai_enabled') eq '1'"}
                                                        <div class="input-group mb-2">
                                                            <select class="form-select" style="max-width: 150px;" data-index="{$i}">
                                                                <option value="20">20字</option>
                                                                <option value="30">30字</option>
                                                                <option value="50">50字</option>
                                                                <option value="80">80字</option>
                                                                <option value="100">100字</option>
                                                                <option value="120">120字</option>
                                                                <option value="150">150字</option>
                                                            </select>
                                                            <button type="button" class="btn btn-primary" onclick="generateJobDescription({$i})">
                                                                <i class="fas fa-magic me-1"></i>AI生成
                                                            </button>
                                                        </div>
                                                        {/if}
                                                        <textarea class="form-control" name="job_description[]" rows="3" {if condition="$i eq 1"}required{/if}>{$job_descriptions[$i-1]|default=''}</textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {/volist}
                                    {else}
                                        <div class="work-item card">
                                            <div class="card-body">
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <label class="form-label required-field">工作单位</label>
                                                        <input type="text" class="form-control" name="job_company[]" required>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label required-field">工作岗位</label>
                                                        <input type="text" class="form-control" name="job_position[]" required>
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-md-6">
                                                        <label class="form-label">入职时间</label>
                                                        <input type="month" class="form-control" name="job_start[]">
                                                    </div>
                                                    <div class="col-md-6">
                                                        <label class="form-label">离职时间</label>
                                                        <input type="month" class="form-control" name="job_end[]">
                                                    </div>
                                                </div>
                                                <div class="row mt-3">
                                                    <div class="col-md-12">
                                                        <label class="form-label">工作内容</label>
                                                        <div class="input-group mb-2">
                                                            <select class="form-select" style="max-width: 150px;" data-index="${workCount + 1}">
                                                                <option value="20">20字</option>
                                                                <option value="30">30字</option>
                                                                <option value="50">50字</option>
                                                                <option value="80">80字</option>
                                                                <option value="100">100字</option>
                                                                <option value="120">120字</option>
                                                                <option value="150">150字</option>
                                                            </select>
                                                            <button type="button" class="btn btn-primary" onclick="generateJobDescription(${workCount + 1})">
                                                                <i class="fas fa-magic me-1"></i>AI生成
                                                            </button>
                                                        </div>
                                                        <textarea class="form-control" name="job_description[]" rows="3"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    {/if}
                                </div>
                                <div class="text-center mb-4">
                                    <button type="button" class="btn btn-outline-primary" onclick="addWork()">
                                        <i class="fas fa-plus me-2"></i>添加工作经历
                                    </button>
                                </div>
                                <div class="text-end mt-4">
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="prevSection(5)">
                                        <i class="fas fa-arrow-left me-2"></i>上一步
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="nextSection(5)">
                                        <i class="fas fa-arrow-right me-2"></i>下一步
                                    </button>
                                </div>
                            </div>

                            <!-- 证件信息 -->
                            <div class="form-section" id="section6" style="display: none;">
                                <h5 class="section-title">
                                    <i class="fas fa-id-card"></i>证件信息
                                </h5>
                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label class="form-label">港澳通行证编号</label>
                                        <input type="text" class="form-control" name="hk_macau_passport" value="{$resume.hk_macau_passport}">
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">港澳通行证到期时间</label>
                                        <input type="date" class="form-control" name="hk_macau_passport_expiry" value="{$resume.hk_macau_passport_expiry}">
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label class="form-label required-field">身份证正面</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload me-2"></i>点击上传
                                            </label>
                                            <input type="file" name="id_card_front" accept="image/*" required onchange="previewIdCard(this, 'front')">
                                            <input type="hidden" name="old_id_card_front" value="{$resume.id_card_front}">
                                        </div>
                                        <div id="idCardFrontPreview" class="preview-container" style="display: none;">
                                            <img src="{$resume.id_card_front}" alt="身份证正面预览">
                                            <button type="button" class="delete-image" onclick="deleteImage('idCardFrontPreview', 'id_card_front')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label required-field">身份证反面</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload me-2"></i>点击上传
                                            </label>
                                            <input type="file" name="id_card_back" accept="image/*" required onchange="previewIdCard(this, 'back')">
                                            <input type="hidden" name="old_id_card_back" value="{$resume.id_card_back}">
                                        </div>
                                        <div id="idCardBackPreview" class="preview-container" style="display: none;">
                                            <img src="{$resume.id_card_back}" alt="身份证反面预览">
                                            <button type="button" class="delete-image" onclick="deleteImage('idCardBackPreview', 'id_card_back')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="row mb-4">
                                    <div class="col-md-6">
                                        <label class="form-label">港澳通行证正面</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload me-2"></i>点击上传
                                            </label>
                                            <input type="file" name="hk_macau_passport_front" accept="image/*" onchange="previewImage(this, 'hkMacauPassportFrontPreview')">
                                            <input type="hidden" name="old_hk_macau_passport_front" value="{$resume.hk_macau_passport_front}">
                                        </div>
                                        <div id="hkMacauPassportFrontPreview" class="preview-container" style="display: none;">
                                            <img src="{$resume.hk_macau_passport_front}" alt="港澳通行证正面预览">
                                            <button type="button" class="delete-image" onclick="deleteImage('hkMacauPassportFrontPreview', 'hk_macau_passport_front')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">港澳通行证反面</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload me-2"></i>点击上传
                                            </label>
                                            <input type="file" name="hk_macau_passport_back" accept="image/*" onchange="previewImage(this, 'hkMacauPassportBackPreview')">
                                            <input type="hidden" name="old_hk_macau_passport_back" value="{$resume.hk_macau_passport_back}">
                                        </div>
                                        <div id="hkMacauPassportBackPreview" class="preview-container" style="display: none;">
                                            <img src="{$resume.hk_macau_passport_back}" alt="港澳通行证反面预览">
                                            <button type="button" class="delete-image" onclick="deleteImage('hkMacauPassportBackPreview', 'hk_macau_passport_back')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end mt-4">
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="prevSection(6)">
                                        <i class="fas fa-arrow-left me-2"></i>上一步
                                    </button>
                                    <button type="button" class="btn btn-primary" onclick="nextSection(6)">
                                        <i class="fas fa-arrow-right me-2"></i>下一步
                                    </button>
                                </div>
                            </div>

                            <!-- 照片上传 -->
                            <div class="form-section" id="section7" style="display: none;">
                                <h5 class="section-title">
                                    <i class="fas fa-camera"></i>照片上传
                                </h5>
                                <div class="row mb-4">
                                    <div class="col-md-4">
                                        <label class="form-label required-field">头像</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload me-2"></i>点击上传
                                            </label>
                                            <input type="file" name="avatar" accept="image/*" required onchange="previewImage(this, 'avatarPreview')">
                                            <input type="hidden" name="old_avatar" value="{$resume.avatar}">
                                        </div>
                                        <div id="avatarPreview" class="preview-container" style="display: none;">
                                            <img src="{$resume.avatar}" alt="头像预览">
                                            <button type="button" class="delete-image" onclick="deleteImage('avatarPreview', 'avatar')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label required-field">全身照</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload me-2"></i>点击上传
                                            </label>
                                            <input type="file" name="full_body_photo" accept="image/*" required onchange="previewImage(this, 'fullBodyPhotoPreview')">
                                            <input type="hidden" name="old_full_body_photo" value="{$resume.full_body_photo}">
                                        </div>
                                        <div id="fullBodyPhotoPreview" class="preview-container" style="display: none;">
                                            <img src="{$resume.full_body_photo}" alt="全身照预览">
                                            <button type="button" class="delete-image" onclick="deleteImage('fullBodyPhotoPreview', 'full_body_photo')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <label class="form-label">补充照片</label>
                                        <div class="file-upload">
                                            <label class="file-upload-label">
                                                <i class="fas fa-upload me-2"></i>点击上传
                                            </label>
                                            <input type="file" name="additional_photos[]" accept="image/*" multiple onchange="previewAdditionalPhotos(this)">
                                            <input type="hidden" name="old_additional_photos" value="{$resume.additional_photos}">
                                        </div>
                                        <div id="additionalPhotosPreview" class="preview-container" style="display: none;">
                                            <img src="{$resume.additional_photos}" alt="补充照片预览">
                                            <button type="button" class="delete-image" onclick="deleteImage('additionalPhotosPreview', 'additional_photos')">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="text-end mt-4">
                                    <button type="button" class="btn btn-outline-primary me-2" onclick="prevSection(7)">
                                        <i class="fas fa-arrow-left me-2"></i>上一步
                                    </button>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-paper-plane me-2"></i>提交申请
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 将JavaScript移到body结束标签前 -->
    <script src="https://cdn.staticfile.org/jquery/3.6.0/jquery.min.js"></script>
    <script src="https://cdn.staticfile.org/bootstrap/5.1.3/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.staticfile.org/sweetalert2/11.7.32/sweetalert2.all.min.js"></script>
    <script>
        // 延迟加载非关键JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化基本功能
            document.getElementById('section1').style.display = 'block';
            updateProgress(1);
            
            // 延迟加载其他功能
            setTimeout(() => {
                checkLoginStatus();
                getAreaData();
            }, 100);
        });

        // 地区数据
        let areaData = null;

        // 获取地区数据
        async function getAreaData() {
            try {
                const response = await fetch('{:url("index/area/getAreaData")}');
                areaData = await response.json();
                initAreaSelectors();
                // 初始化已保存的地址
                initSavedAddresses();
            } catch (error) {
                console.error('获取地区数据失败:', error);
            }
        }

        // 初始化已保存的地址
        function initSavedAddresses() {
            // 初始化户口所在地
            if ('{$resume.hukou_location}') {
                const hukouParts = '{$resume.hukou_location}'.split('/');
                if (hukouParts.length >= 2) {
                    // 查找并选择省份
                    const province = areaData.find(item => item.name === hukouParts[0] && item.level === 1);
                    if (province) {
                        const provinceSelect = document.getElementById('hukouProvince');
                        provinceSelect.value = province.id;
                        // 触发change事件以加载城市
                        provinceSelect.dispatchEvent(new Event('change'));
                        
                        // 查找并选择城市
                        setTimeout(() => {
                            const city = areaData.find(item => item.name === hukouParts[1] && item.pid === province.id);
                            if (city) {
                                document.getElementById('hukouCity').value = city.id;
                            }
                        }, 100);
                    }
                }
            }

            // 初始化常住地址
            if ('{$resume.residence_address}') {
                const residenceParts = '{$resume.residence_address}'.split('/');
                if (residenceParts.length >= 3) {
                    // 查找并选择省份
                    const province = areaData.find(item => item.name === residenceParts[0] && item.level === 1);
                    if (province) {
                        const provinceSelect = document.getElementById('residenceProvince');
                        provinceSelect.value = province.id;
                        // 触发change事件以加载城市
                        provinceSelect.dispatchEvent(new Event('change'));
                        
                        // 查找并选择城市和区县
                        setTimeout(() => {
                            const city = areaData.find(item => item.name === residenceParts[1] && item.pid === province.id);
                            if (city) {
                                const citySelect = document.getElementById('residenceCity');
                                citySelect.value = city.id;
                                // 触发change事件以加载区县
                                citySelect.dispatchEvent(new Event('change'));
                                
                                // 查找并选择区县
                                setTimeout(() => {
                                    const district = areaData.find(item => item.name === residenceParts[2] && item.pid === city.id);
                                    if (district) {
                                        document.getElementById('residenceDistrict').value = district.id;
                                    }
                                }, 100);
                            }
                        }, 100);
                    }
                }
            }
        }

        // 初始化地区选择器
        function initAreaSelectors() {
            // 初始化户口所在地选择器
            initHukouSelector();
            // 初始化常住地址选择器
            initResidenceSelector();
        }

        // 初始化户口所在地选择器
        function initHukouSelector() {
            const provinceSelect = document.getElementById('hukouProvince');
            const citySelect = document.getElementById('hukouCity');

            // 加载省份数据
            const provinces = areaData.filter(item => item.level === 1);
            provinces.forEach(province => {
                const option = new Option(province.name, province.id);
                provinceSelect.add(option);
            });

            // 省份选择事件
            provinceSelect.addEventListener('change', function() {
                const provinceId = this.value;
                citySelect.innerHTML = '<option value="">请选择城市</option>';
                
                if (provinceId) {
                    const cities = areaData.filter(item => item.pid === parseInt(provinceId));
                    cities.forEach(city => {
                        const option = new Option(city.name, city.id);
                        citySelect.add(option);
                    });
                }
            });
        }

        // 初始化常住地址选择器
        function initResidenceSelector() {
            const provinceSelect = document.getElementById('residenceProvince');
            const citySelect = document.getElementById('residenceCity');
            const districtSelect = document.getElementById('residenceDistrict');

            // 加载省份数据
            const provinces = areaData.filter(item => item.level === 1);
            provinces.forEach(province => {
                const option = new Option(province.name, province.id);
                provinceSelect.add(option);
            });

            // 省份选择事件
            provinceSelect.addEventListener('change', function() {
                const provinceId = this.value;
                citySelect.innerHTML = '<option value="">请选择城市</option>';
                districtSelect.innerHTML = '<option value="">请选择区县</option>';
                
                if (provinceId) {
                    const cities = areaData.filter(item => item.pid === parseInt(provinceId));
                    cities.forEach(city => {
                        const option = new Option(city.name, city.id);
                        citySelect.add(option);
                    });
                }
            });

            // 城市选择事件
            citySelect.addEventListener('change', function() {
                const cityId = this.value;
                districtSelect.innerHTML = '<option value="">请选择区县</option>';
                
                if (cityId) {
                    const districts = areaData.filter(item => item.pid === parseInt(cityId));
                    districts.forEach(district => {
                        const option = new Option(district.name, district.id);
                        districtSelect.add(option);
                    });
                }
            });
        }

        // 页面加载完成后获取地区数据
        document.addEventListener('DOMContentLoaded', getAreaData);

        // 身份证号自动识别
        document.querySelector('input[name="id_card"]').addEventListener('input', function(e) {
            const idCard = e.target.value;
            if (idCard.length === 18) {
                // 获取性别
                const gender = parseInt(idCard.substr(16, 1)) % 2 === 0 ? '女' : '男';
                document.querySelector('select[name="gender"]').value = gender;

                // 获取出生日期
                const year = idCard.substr(6, 4);
                const month = idCard.substr(10, 2);
                const day = idCard.substr(12, 2);
                const birthDate = `${year}-${month}-${day}`;
                document.querySelector('input[name="birth_date"]').value = birthDate;

                // 计算年龄
                const today = new Date();
                const birth = new Date(birthDate);
                let age = today.getFullYear() - birth.getFullYear();
                const monthDiff = today.getMonth() - birth.getMonth();
                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                    age--;
                }
                document.querySelector('input[name="age"]').value = age;
            }
        });

        // 文件上传预览
        document.querySelectorAll('input[type="file"]').forEach(input => {
            input.addEventListener('change', function(e) {
                const fileUpload = this.closest('.file-upload');
                const label = this.previousElementSibling;
                if (this.files && this.files[0]) {
                    label.innerHTML = `<i class="fas fa-check me-2"></i>${this.files[0].name}`;
                    fileUpload.classList.remove('is-invalid');
                    // 如果有已保存的图片，移除required属性
                    const oldField = fileUpload.querySelector(`input[name="old_${this.name}"]`);
                    if (oldField && oldField.value) {
                        this.removeAttribute('required');
                    }
                } else {
                    label.innerHTML = `<i class="fas fa-upload me-2"></i>点击上传`;
                    if (this.hasAttribute('required')) {
                        const oldField = fileUpload.querySelector(`input[name="old_${this.name}"]`);
                        if (!oldField || !oldField.value) {
                            fileUpload.classList.add('is-invalid');
                        }
                    }
                }
            });

            // 初始化时检查是否有已保存的图片
            const fileUpload = input.closest('.file-upload');
            const oldField = fileUpload.querySelector(`input[name="old_${input.name}"]`);
            if (oldField && oldField.value) {
                const label = input.previousElementSibling;
                label.innerHTML = `<i class="fas fa-check me-2"></i>已上传图片`;
                fileUpload.classList.remove('is-invalid');
                // 如果有已保存的图片，移除required属性
                input.removeAttribute('required');
            }
        });

        // 联系人相关
        function addContact() {
            const template = `
                <div class="contact-item card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">联系人 #${contactCount + 1}</h6>
                            <button type="button" class="btn btn-danger btn-sm" onclick="removeContact(this)">
                                <i class="fas fa-times me-1"></i>删除
                            </button>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">联系人姓名</label>
                                <input type="text" class="form-control" name="contact_name[]">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">与本人关系</label>
                                <select class="form-select" name="contact_relation[]">
                                    <option value="父母">父母</option>
                                    <option value="配偶">配偶</option>
                                    <option value="子女">子女</option>
                                    <option value="兄弟姐妹">兄弟姐妹</option>
                                    <option value="其他亲属">其他亲属</option>
                                    <option value="朋友">朋友</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">联系人年龄</label>
                                <input type="number" class="form-control" name="contact_age[]">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">联系人工作</label>
                                <input type="text" class="form-control" name="contact_job[]">
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('contactList').insertAdjacentHTML('beforeend', template);
            contactCount++;
        }

        function removeContact(button) {
            const contactItem = button.closest('.contact-item');
            contactItem.remove();
        }

        // 教育经历相关
        function addEducation() {
            const template = `
                <div class="education-item card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">教育经历 #${educationCount + 1}</h6>
                            <button type="button" class="btn btn-danger btn-sm" onclick="removeEducation(this)">
                                <i class="fas fa-times me-1"></i>删除
                            </button>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <label class="form-label">学历</label>
                                <select class="form-select" name="graduation_education[]">
                                    <option value="">请选择</option>
                                    <option value="小学">小学</option>
                                    <option value="初中">初中</option>
                                    <option value="高中">高中</option>
                                    <option value="中专">中专</option>
                                    <option value="大专">大专</option>
                                    <option value="本科">本科</option>
                                    <option value="硕士">硕士</option>
                                    <option value="博士">博士</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">学校名称</label>
                                <input type="text" class="form-control" name="education_school[]">
                            </div>
                            <div class="col-md-4">
                                <label class="form-label">专业</label>
                                <input type="text" class="form-control" name="education_major[]">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">入学时间</label>
                                <input type="month" class="form-control" name="education_start[]">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">毕业时间</label>
                                <input type="month" class="form-control" name="education_end[]">
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('educationList').insertAdjacentHTML('beforeend', template);
            educationCount++;
        }

        function removeEducation(button) {
            const educationItem = button.closest('.education-item');
            educationItem.remove();
        }

        // 工作经历相关
        function addWork() {
            const template = `
                <div class="work-item card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">工作经历 #${workCount + 1}</h6>
                            <button type="button" class="btn btn-danger btn-sm" onclick="removeWork(this)">
                                <i class="fas fa-times me-1"></i>删除
                            </button>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">工作单位</label>
                                <input type="text" class="form-control" name="job_company[]">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">工作岗位</label>
                                <input type="text" class="form-control" name="job_position[]">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-6">
                                <label class="form-label">入职时间</label>
                                <input type="month" class="form-control" name="job_start[]">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">离职时间</label>
                                <input type="month" class="form-control" name="job_end[]">
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <label class="form-label">工作内容</label>
                                <div class="input-group mb-2">
                                    <select class="form-select" style="max-width: 150px;" data-index="${workCount + 1}">
                                        <option value="20">20字</option>
                                        <option value="30">30字</option>
                                        <option value="50">50字</option>
                                        <option value="80">80字</option>
                                        <option value="100">100字</option>
                                        <option value="120">120字</option>
                                        <option value="150">150字</option>
                                    </select>
                                    <button type="button" class="btn btn-primary" onclick="generateJobDescription(${workCount + 1})">
                                        <i class="fas fa-magic me-1"></i>AI生成
                                    </button>
                                </div>
                                <textarea class="form-control" name="job_description[]" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('workList').insertAdjacentHTML('beforeend', template);
            workCount++;
        }

        function removeWork(button) {
            const workItem = button.closest('.work-item');
            workItem.remove();
        }

        // 分步表单控制
        function nextSection(currentSection) {
            // 验证当前部分
            const currentSectionElement = document.getElementById('section' + currentSection);
            const requiredFields = currentSectionElement.querySelectorAll('[required]');
            let isValid = true;
            let firstInvalidField = null;

            requiredFields.forEach(field => {
                if (field.type === 'file') {
                    const fileUpload = field.closest('.file-upload');
                    const oldField = fileUpload.querySelector(`input[name="old_${field.name}"]`);
                    if (!field.files.length && (!oldField || !oldField.value)) {
                        isValid = false;
                        fileUpload.classList.add('is-invalid');
                        if (!firstInvalidField) {
                            firstInvalidField = fileUpload;
                        }
                    } else {
                        fileUpload.classList.remove('is-invalid');
                    }
                } else if (!field.value) {
                    isValid = false;
                    field.classList.add('is-invalid');
                    if (!firstInvalidField) {
                        firstInvalidField = field;
                    }
                } else {
                    field.classList.remove('is-invalid');
                }
            });

            if (!isValid) {
                Swal.fire({
                    title: '提示',
                    text: '请填写所有必填项',
                    icon: 'warning',
                    confirmButtonText: '确定',
                    confirmButtonColor: '#1a73e8'
                }).then(() => {
                    if (firstInvalidField) {
                        firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        const input = firstInvalidField.querySelector('input, select, textarea');
                        if (input) {
                            input.focus();
                        }
                    }
                });
                return;
            }

            // 隐藏当前部分
            currentSectionElement.style.display = 'none';
            // 显示下一部分
            document.getElementById('section' + (currentSection + 1)).style.display = 'block';
            // 更新进度条
            updateProgress(currentSection + 1);
        }

        function prevSection(currentSection) {
            // 隐藏当前部分
            document.getElementById('section' + currentSection).style.display = 'none';
            // 显示上一部分
            document.getElementById('section' + (currentSection - 1)).style.display = 'block';
            // 更新进度条
            updateProgress(currentSection - 1);
        }

        function updateProgress(currentSection) {
            const steps = document.querySelectorAll('.progress-step');
            steps.forEach((step, index) => {
                if (index < currentSection) {
                    step.classList.add('active');
                } else {
                    step.classList.remove('active');
                }
            });
        }

        // 检查登录状态
        function checkLoginStatus() {
            fetch('{:url("index/user/check")}', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('网络请求失败');
                }
                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('服务器返回格式错误');
                }
                return response.json();
            })
            .then(res => {
                    if (!res.code) {
                        Swal.fire({
                            title: '提示',
                            text: '当前未注册/登录账号，是否继续填写？未注册/登录账号，后续将无法管理填写的简历。',
                            icon: 'warning',
                            showCancelButton: true,
                            confirmButtonText: '继续填写',
                            cancelButtonText: '去登录',
                            confirmButtonColor: '#1a73e8',
                            cancelButtonColor: '#5f6368'
                        }).then((result) => {
                            if (result.isDismissed) {
                                // 如果用户点击"去登录"，跳转到登录页面
                                window.location.href = '{:url("index/user/login")}';
                            }
                        });
                    }
            })
            .catch(error => {
                console.error('检查登录状态失败:', error);
                // 发生错误时，默认允许继续填写
                console.log('允许继续填写简历');
        });
        }

        // 添加输入验证
        document.querySelectorAll('input, select, textarea').forEach(field => {
            field.addEventListener('input', function() {
                if (this.hasAttribute('required')) {
                    if (this.value) {
                        this.classList.remove('is-invalid');
                    } else {
                        this.classList.add('is-invalid');
                    }
                }
            });

            field.addEventListener('blur', function() {
                if (this.hasAttribute('required')) {
                    if (this.value) {
                        this.classList.remove('is-invalid');
                    } else {
                        this.classList.add('is-invalid');
                    }
                }
            });
        });

        // 表单提交前处理
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 检查所有必填字段
            const requiredFields = this.querySelectorAll('[required]');
            let isValid = true;
            let firstInvalidField = null;

            requiredFields.forEach(field => {
                if (field.type === 'file') {
                    const fileUpload = field.closest('.file-upload');
                    const oldField = fileUpload.querySelector(`input[name="old_${field.name}"]`);
                    
                    // 如果旧图片路径为空，说明图片被删除，需要上传新图片
                    if (!oldField || !oldField.value) {
                        if (!field.files.length) {
                        isValid = false;
                        fileUpload.classList.add('is-invalid');
                        if (!firstInvalidField) {
                            firstInvalidField = fileUpload;
                            }
                        }
                    }
                } else if (!field.value) {
                    isValid = false;
                    field.classList.add('is-invalid');
                    if (!firstInvalidField) {
                        firstInvalidField = field;
                    }
                }
            });

            if (!isValid) {
                Swal.fire({
                    title: '提示',
                    text: '请填写所有必填项',
                    icon: 'warning',
                    confirmButtonText: '确定',
                    confirmButtonColor: '#1a73e8'
                }).then(() => {
                    if (firstInvalidField) {
                        firstInvalidField.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        const input = firstInvalidField.querySelector('input, select, textarea');
                        if (input) {
                            input.focus();
                        }
                    }
                });
                return;
            }

            // 如果验证通过，提交表单
            this.submit();
        });

        // 预览身份证图片
        function previewIdCard(input, type) {
            if (input.files && input.files[0]) {
                const file = input.files[0];
                const reader = new FileReader();
                
                reader.onload = function(e) {
                    const previewId = type === 'front' ? 'idCardFrontPreview' : 'idCardBackPreview';
                    const preview = document.getElementById(previewId);
                    if (preview) {
                        const img = preview.querySelector('img');
                        if (img) {
                            img.src = e.target.result;
                            preview.style.display = 'block';
                        }
                    }
                };
                
                reader.readAsDataURL(file);
            }
        }

        // 扫描身份证
        function scanIdCard(type) {
            // 创建文件输入元素
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.capture = 'environment'; // 使用后置摄像头
            
            input.onchange = async function(e) {
                if (e.target.files && e.target.files[0]) {
                    const file = e.target.files[0];
                    
                    // 显示预览
                    previewIdCard(e.target, type);
                    
                    // 创建 FormData
                    const formData = new FormData();
                    formData.append('id_card', file);
                    formData.append('type', type);
                    
                    try {
                        // 显示加载提示
                        Swal.fire({
                            title: '正在识别...',
                            text: '请稍候',
                            allowOutsideClick: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });
                        
                        // 发送到后端进行识别
                        const response = await fetch('{:url("index/resume/scanIdCard")}', {
                            method: 'POST',
                            body: formData
                        });
                        
                        if (!response.ok) {
                            throw new Error('网络请求失败');
                        }
                        
                        const contentType = response.headers.get('content-type');
                        if (!contentType || !contentType.includes('application/json')) {
                            throw new Error('服务器返回格式错误');
                        }
                        
                        const result = await response.json();
                
                        if (result.code === 1) {
                            // 识别成功，填充表单
                            const data = result.data;
                            if (type === 'front') {
                                document.querySelector('input[name="name"]').value = data.name || '';
                                document.querySelector('select[name="gender"]').value = data.gender || '';
                                document.querySelector('input[name="age"]').value = data.age || '';
                                document.querySelector('input[name="id_card"]').value = data.id_card || '';
                                
                                // 处理出生日期
                                if (data.id_card && data.id_card.length === 18) {
                                    const year = data.id_card.substr(6, 4);
                                    const month = data.id_card.substr(10, 2);
                                    const day = data.id_card.substr(12, 2);
                                    const birthDate = `${year}-${month}-${day}`;
                                    document.querySelector('input[name="birth_date"]').value = birthDate;
                                }

                                // 自动填充身份证照片
                                const idCardFrontInput = document.querySelector('input[name="id_card_front"]');
                                if (idCardFrontInput) {
                                    // 直接触发文件选择
                                    const fileInput = document.createElement('input');
                                    fileInput.type = 'file';
                                    fileInput.accept = 'image/*';
                                    fileInput.style.display = 'none';
                                    document.body.appendChild(fileInput);
                                    
                                    // 创建一个新的 FileList 对象
                                    const dataTransfer = new ClipboardEvent('').clipboardData || new DataTransfer();
                                    dataTransfer.items.add(file);
                                    fileInput.files = dataTransfer.files;
                                    
                                    // 触发文件选择
                                    idCardFrontInput.files = fileInput.files;
                                    
                                    // 更新预览
                                    const preview = document.getElementById('idCardFrontPreview');
                                    const previewImg = preview.querySelector('img');
                                    const reader = new FileReader();
                                    reader.onload = function(e) {
                                        previewImg.src = e.target.result;
                                        preview.style.display = 'block';
                                    };
                                    reader.readAsDataURL(file);
                                    
                                    // 更新上传按钮文本
                                    const label = idCardFrontInput.previousElementSibling;
                                    if (label) {
                                        label.innerHTML = `<i class="fas fa-check me-2"></i>${file.name}`;
                                    }
                                    
                                    // 清理临时元素
                                    document.body.removeChild(fileInput);
                                }
                            }
                            
                            Swal.fire({
                                title: '识别成功',
                                icon: 'success',
                                timer: 1500
                            });
                        } else {
                            throw new Error(result.msg || '识别失败');
                        }
                    } catch (error) {
                        console.error('身份证识别错误:', error);
                        Swal.fire({
                            title: '识别失败',
                            text: error.message || '请稍后重试',
                            icon: 'error'
                        });
                    }
                }
            };
            
            input.click();
        }

        // 预览图片
        function previewImage(input, previewId) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                const preview = document.getElementById(previewId);
                
                reader.onload = function(e) {
                    const img = preview.querySelector('img');
                    img.src = e.target.result;
                    preview.style.display = 'block';
                };
                
                reader.readAsDataURL(input.files[0]);
            }
        }

        // 删除图片
        function deleteImage(previewId, inputName) {
            const preview = document.getElementById(previewId);
            const input = document.querySelector(`input[name="${inputName}"]`);
            const oldInput = document.querySelector(`input[name="old_${inputName}"]`);
            const label = input.previousElementSibling;
            
            // 清除预览
            preview.style.display = 'none';
            preview.querySelector('img').src = '';
            
            // 清除文件输入
            input.value = '';
            
            // 清除旧图片路径，设置为空字符串表示删除
            if (oldInput) {
                oldInput.value = '';
            }
            
            // 重置上传按钮文本
            label.innerHTML = `<i class="fas fa-upload me-2"></i>点击上传`;
            
            // 如果是必填字段，添加验证
            if (input.hasAttribute('required')) {
                input.closest('.file-upload').classList.add('is-invalid');
            }
        }

        // 预览补充照片
        function previewAdditionalPhotos(input) {
            if (input.files && input.files.length > 0) {
                const preview = document.getElementById('additionalPhotosPreview');
                const img = preview.querySelector('img');
                
                // 只显示第一张图片的预览
                const reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                    preview.style.display = 'block';
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // 页面加载时初始化已有图片
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化所有文件上传字段
            document.querySelectorAll('input[type="file"]').forEach(input => {
                const oldInput = document.querySelector(`input[name="old_${input.name}"]`);
                if (oldInput && oldInput.value) {
                    const label = input.previousElementSibling;
                    label.innerHTML = `<i class="fas fa-check me-2"></i>已上传图片`;
                    
                    // 显示预览
                    let previewId;
                    if (input.name === 'id_card_front') {
                        previewId = 'idCardFrontPreview';
                    } else if (input.name === 'id_card_back') {
                        previewId = 'idCardBackPreview';
                    } else if (input.name === 'hk_macau_passport_front') {
                        previewId = 'hkMacauPassportFrontPreview';
                    } else if (input.name === 'hk_macau_passport_back') {
                        previewId = 'hkMacauPassportBackPreview';
                    } else if (input.name === 'avatar') {
                        previewId = 'avatarPreview';
                    } else if (input.name === 'full_body_photo') {
                        previewId = 'fullBodyPhotoPreview';
                    } else if (input.name === 'additional_photos[]') {
                        previewId = 'additionalPhotosPreview';
                    }

                    if (previewId) {
                        const preview = document.getElementById(previewId);
                        if (preview) {
                            const img = preview.querySelector('img');
                            if (img) {
                                img.src = oldInput.value;
                                preview.style.display = 'block';
                            }
                        }
                    }
                    
                    // 如果有已保存的图片，移除required属性
                    if (input.hasAttribute('required')) {
                        input.removeAttribute('required');
                    }
                }
            });
        });

        // 更新字数选择
        function updateWordCount(select, index) {
            select.setAttribute('data-index', index);
        }

        // 生成工作内容
        async function generateJobDescription(index) {
            // 获取所有工作经历项
            const workItems = document.querySelectorAll('.work-item');
            
            // 获取对应的工作经历项
            const workItem = workItems[index - 1];
            
            if (!workItem) {
                Swal.fire({
                    title: '提示',
                    text: '未找到对应的工作经历',
                    icon: 'error',
                    confirmButtonText: '确定'
                });
                return;
            }

            // 获取工作单位和岗位
            const companyInput = workItem.querySelector('input[name="job_company[]"]');
            const positionInput = workItem.querySelector('input[name="job_position[]"]');
            const wordCountSelect = workItem.querySelector('select[data-index="' + index + '"]');

            if (!companyInput || !positionInput || !wordCountSelect) {
                Swal.fire({
                    title: '提示',
                    text: '表单元素未找到',
                    icon: 'error',
                    confirmButtonText: '确定'
                });
                return;
            }

            const company = companyInput.value;
            const position = positionInput.value;
            const wordCount = wordCountSelect.value;
            
            if (!company || !position) {
                Swal.fire({
                    title: '提示',
                    text: '请先填写工作单位和岗位',
                    icon: 'warning',
                    confirmButtonText: '确定'
                });
                return;
            }

            try {
                // 显示加载提示
                Swal.fire({
                    title: '正在生成...',
                    text: '请稍候',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // 调用后端接口
                const response = await fetch('{:url("index/resume/generateJobDescription")}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        company: company,
                        position: position,
                        word_count: wordCount
                    })
                });

                const result = await response.json();
                
                if (result.code === 1) {
                    // 生成成功，填充内容
                    const textarea = workItem.querySelector('textarea[name="job_description[]"]');
                    if (textarea) {
                        textarea.value = result.data;
                        Swal.fire({
                            title: '生成成功',
                            icon: 'success',
                            timer: 1500
                        });
                    } else {
                        throw new Error('未找到工作内容文本框');
                    }
                } else {
                    throw new Error(result.msg || '生成失败');
                }
            } catch (error) {
                console.error('生成工作内容失败:', error);
                Swal.fire({
                    title: '生成失败',
                    text: error.message || '请稍后重试',
                    icon: 'error'
                });
            }
        }

        // 生成自我评价
        async function generateSelfEvaluation() {
            const textarea = document.querySelector('textarea[name="self_evaluation"]');
            const wordCount = document.getElementById('selfEvaluationWordCount').value;
            
            if (!textarea.value) {
                Swal.fire({
                    title: '提示',
                    text: '请先输入自我评价内容',
                    icon: 'warning',
                    confirmButtonText: '确定'
                });
                return;
            }

            try {
                // 显示加载提示
                Swal.fire({
                    title: '正在润色...',
                    text: '请稍候',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // 调用后端接口
                const response = await fetch('{:url("index/resume/generateSelfEvaluation")}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: textarea.value,
                        word_count: wordCount
                    })
                });

                const result = await response.json();
                
                if (result.code === 1) {
                    // 生成成功，填充内容
                    textarea.value = result.data;
                    Swal.fire({
                        title: '润色成功',
                        icon: 'success',
                        timer: 1500
                    });
                } else {
                    throw new Error(result.msg || '润色失败');
                }
            } catch (error) {
                console.error('润色自我评价失败:', error);
                Swal.fire({
                    title: '润色失败',
                    text: error.message || '请稍后重试',
                    icon: 'error'
                });
            }
        }

        // 生成兴趣爱好
        async function generateHobbies() {
            const textarea = document.querySelector('textarea[name="hobbies"]');
            const wordCount = document.getElementById('hobbiesWordCount').value;
            
            if (!textarea.value) {
                Swal.fire({
                    title: '提示',
                    text: '请先输入兴趣爱好内容',
                    icon: 'warning',
                    confirmButtonText: '确定'
                });
                return;
            }

            try {
                // 显示加载提示
                Swal.fire({
                    title: '正在润色...',
                    text: '请稍候',
                    allowOutsideClick: false,
                    didOpen: () => {
                        Swal.showLoading();
                    }
                });

                // 调用后端接口
                const response = await fetch('{:url("index/resume/generateHobbies")}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        content: textarea.value,
                        word_count: wordCount
                    })
                });

                const result = await response.json();
                
                if (result.code === 1) {
                    // 生成成功，填充内容
                    textarea.value = result.data;
                    Swal.fire({
                        title: '润色成功',
                        icon: 'success',
                        timer: 1500
                    });
                } else {
                    throw new Error(result.msg || '润色失败');
                }
            } catch (error) {
                console.error('润色兴趣爱好失败:', error);
                Swal.fire({
                    title: '润色失败',
                    text: error.message || '请稍后重试',
                    icon: 'error'
                });
            }
        }
    </script>
    <script>
        // 导航栏滚动效果
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });
        
        // 退出登录按钮点击事件
        document.addEventListener('DOMContentLoaded', function() {
            const logoutBtn = document.getElementById('btn-logout');
            if (logoutBtn) {
                logoutBtn.addEventListener('click', function() {
                    document.getElementById('logout-form').submit();
                });
            }
        });
    </script> 
</body>
</html> 