import { get, post } from './request';

/**
 * 获取首页热门职位
 */
export const getHotJobs = (params = {}) => {
  return get('/job/hot', params);
};

/**
 * 获取最新职位
 */
export const getLatestJobs = (params = {}) => {
  return get('/job/latest', params);
};

/**
 * 获取职位列表
 * @param {Object} params - 查询参数
 * @param {string} params.keyword - 搜索关键词
 * @param {string} params.category - 职位分类
 * @param {string} params.education - 学历要求
 * @param {string} params.age - 年龄要求
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 */
export const getJobList = (params = {}) => {
  return get('/job/list', params);
};

/**
 * 获取职位详情
 * @param {number} id - 职位ID
 */
export const getJobDetail = (id) => {
  if (!id) {
    return Promise.reject({ code: 0, message: '职位ID不能为空' });
  }
  return get('/job/detail', { id });
};

/**
 * 获取相似职位
 * @param {number} id - 职位ID
 */
export const getSimilarJobs = (id) => {
  if (!id) {
    return Promise.reject({ code: 0, message: '职位ID不能为空' });
  }
  return get('/job/similar', { id });
};

/**
 * 获取职位分类列表
 */
export const getJobCategories = () => {
  return get('/job/categories');
};

/**
 * 搜索职位
 * @param {Object} params - 搜索参数
 * @param {string} params.keyword - 搜索关键词
 * @param {string} params.category - 职位分类
 * @param {string} params.education - 学历要求
 * @param {string} params.experience - 工作经验
 * @param {number} params.page - 页码
 * @param {number} params.size - 每页数量
 */
export const searchJobs = (params = {}) => {
  return get('/job/search', params);
};

/**
 * 投递简历
 * @param {Object} data - 投递数据
 * @param {number} data.job_id - 职位ID
 * @param {number} data.resume_id - 简历ID
 */
export const applyJob = (data) => {
  return post('/resume/apply', data, { auth: true });
};

/**
 * 收藏职位
 * @param {number} jobId - 职位ID
 */
export const favoriteJob = (jobId) => {
  return post('/job/favorite', { job_id: jobId }, { auth: true });
};

/**
 * 取消收藏职位
 * @param {number} jobId - 职位ID
 */
export const unfavoriteJob = (jobId) => {
  return post('/job/unfavorite', { job_id: jobId }, { auth: true });
};

/**
 * 检查职位是否已收藏
 * @param {number} jobId - 职位ID
 */
export const checkFavorite = (jobId) => {
  if (!jobId) {
    return Promise.reject({ code: 0, message: '职位ID不能为空' });
  }
  return get(`/job/checkfavorite`, { job_id: jobId }, { auth: true });
};

/**
 * 获取我的收藏职位列表
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 */
export const getMyFavorites = (params = {}) => {
  return get('/job/myfavorites', params, { auth: true });
};

export default {
  getHotJobs,
  getLatestJobs,
  getJobList,
  getJobDetail,
  getSimilarJobs,
  getJobCategories,
  searchJobs,
  applyJob,
  favoriteJob,
  unfavoriteJob,
  checkFavorite,
  getMyFavorites
}; 