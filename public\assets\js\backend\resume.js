define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    // 格式化文件大小的工具函数
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';

        var k = 1024;
        var sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        var i = Math.floor(Math.log(bytes) / Math.log(k));

        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    var Controller = {
        // 获取当前后台的基础URL
        getBaseUrl: function() {
            var currentUrl = window.location.href;
            var pathArray = currentUrl.split('/');
            var protocol = pathArray[0];
            var host = pathArray[2];
            var pathParts = pathArray.slice(3);

            // 查找后台入口文件（通常是 .php 文件）
            var entryFile = '';
            for (var i = 0; i < pathParts.length; i++) {
                if (pathParts[i].indexOf('.php') !== -1) {
                    entryFile = pathParts[i];
                    break;
                }
            }

            // 如果没找到.php文件，使用默认的admin.php
            if (!entryFile) {
                entryFile = 'admin.php';
            }

            return protocol + '//' + host + '/' + entryFile + '/';
        },

        // 生成完整的URL
        url: function(path) {
            return this.getBaseUrl() + path;
        },
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'resume/index' + location.search,
                    add_url: 'resume/add',
                    edit_url: 'resume/edit',
                    del_url: 'resume/del',
                    multi_url: 'resume/multi',
                    import_url: 'resume/import',
                    table: 'resume',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true, width: 40},
                        {field: 'id', title: __('Id'), operate: false, width: 40},
                        {field: 'name', title: __('Name'), operate: 'LIKE', width: 80},
                        {field: 'intended_position', title: __('Intended_position'), operate: 'LIKE', width: 150},
                        {
                            field: 'applied_position', 
                            title: __('Applied_position'), 
                            operate: 'LIKE',
                            width: 150,
                            formatter: function(value, row, index) {
                                return '<a href="javascript:;" class="editable" data-type="text" data-pk="' + row.id + '" data-name="applied_position" data-value="' + (value || '') + '">' + (value || '') + '</a>';
                            },
                            events: {
                                'click .editable': function (e, value, row, index) {
                                    e.stopPropagation();
                                    var $this = $(this);
                                    var input = $('<div class="input-group input-group-sm"><input type="text" class="form-control" value="' + (value || '') + '"><span class="input-group-btn"><button class="btn btn-success btn-sm" type="button"><i class="fa fa-check"></i></button><button class="btn btn-default btn-sm" type="button"><i class="fa fa-times"></i></button></span></div>');
                                    $this.html(input);
                                    var inputField = input.find('input');
                                    
                                    // 设置光标位置到文本末尾
                                    setTimeout(function() {
                                        inputField.focus();
                                        var len = inputField.val().length;
                                        inputField[0].setSelectionRange(len, len);
                                    }, 0);
                                    
                                    // 处理确认按钮点击
                                    input.find('.btn-success').on('click', function() {
                                        saveValue();
                                    });
                                    
                                    // 处理取消按钮点击
                                    input.find('.btn-default').on('click', function() {
                                        $this.html(value);
                                    });
                                    
                                    // 处理失焦事件
                                    inputField.blur(function() {
                                        // 延迟执行，以便可以点击按钮
                                        setTimeout(function() {
                                            if (!input.find('.btn-success:hover').length && !input.find('.btn-default:hover').length) {
                                                saveValue();
                                            }
                                        }, 200);
                                    });
                                    
                                    // 处理回车事件
                                    inputField.keypress(function(e) {
                                        if (e.which === 13) {
                                            saveValue();
                                            return false;
                                        } else if (e.which === 27) { // ESC键
                                            $this.html(value);
                                            return false;
                                        }
                                    });
                                    
                                    // 保存值的函数
                                    function saveValue() {
                                        var newValue = inputField.val().trim();
                                        if (newValue !== value) {
                                            $.ajax({
                                                url: Fast.api.fixurl('resume/edit'),
                                                type: 'POST',
                                                data: {
                                                    ids: row.id,
                                                    row: {
                                                        applied_position: newValue
                                                    }
                                                },
                                                success: function(res) {
                                                    if (res.code === 1) {
                                                        $this.html(newValue);
                                                        Toastr.success('更新成功');
                                                    } else {
                                                        $this.html(value);
                                                        Toastr.error(res.msg || '更新失败');
                                                    }
                                                },
                                                error: function() {
                                                    $this.html(value);
                                                    Toastr.error('服务器错误');
                                                }
                                            });
                                        } else {
                                            $this.html(value);
                                        }
                                    }
                                }
                            }
                        },
                        {field: 'height', title: __('Height'), operate:'BETWEEN', visible: false, width: 50},
                        {field: 'weight', title: __('Weight'), operate:'BETWEEN', visible: false, width: 50},
                        {field: 'gender', title: __('Gender'), searchList: {"未知":__('未知'),"男":__('男'),"女":__('女')}, formatter: Table.api.formatter.normal, width: 50},
                        {field: 'age', title: __('Age'), operate:'BETWEEN', visible: false, width: 60},
                        {field: 'hukou_location', title: __('Hukou_location'), operate: 'LIKE', table: table, class: 'autocontent', formatter: Table.api.formatter.content, visible: false, width: 150},
                        {field: 'highest_education', title: __('Highest_education'), operate: 'LIKE', width: 100},
                        {field: 'cantonese_level', title: __('Cantonese_level'), searchList: {"不会":__('不会'),"一般":__('一般'),"熟练":__('熟练')}, formatter: Table.api.formatter.normal, width: 90},
                        {field: 'english_level', visible: false, title: __('English_level'), searchList: {"不会":__('不会'),"一般":__('一般'),"熟练":__('熟练')}, formatter: Table.api.formatter.normal, width: 90},
                        {
                            field: 'special_certificate', 
                            title: __('Special_certificate'), 
                            operate: 'LIKE',
                            width: 100,
                            formatter: function(value, row, index) {
                                return '<a href="javascript:;" class="editable" data-type="text" data-pk="' + row.id + '" data-name="special_certificate" data-value="' + (value || '') + '">' + (value || '') + '</a>';
                            },
                            events: {
                                'click .editable': function (e, value, row, index) {
                                    e.stopPropagation();
                                    var $this = $(this);
                                    var input = $('<div class="input-group input-group-sm"><input type="text" class="form-control" value="' + (value || '') + '"><span class="input-group-btn"><button class="btn btn-success btn-sm" type="button"><i class="fa fa-check"></i></button><button class="btn btn-default btn-sm" type="button"><i class="fa fa-times"></i></button></span></div>');
                                    $this.html(input);
                                    var inputField = input.find('input');
                                    
                                    // 设置光标位置到文本末尾
                                    setTimeout(function() {
                                        inputField.focus();
                                        var len = inputField.val().length;
                                        inputField[0].setSelectionRange(len, len);
                                    }, 0);
                                    
                                    // 处理确认按钮点击
                                    input.find('.btn-success').on('click', function() {
                                        saveValue();
                                    });
                                    
                                    // 处理取消按钮点击
                                    input.find('.btn-default').on('click', function() {
                                        $this.html(value);
                                    });
                                    
                                    // 处理失焦事件
                                    inputField.blur(function() {
                                        // 延迟执行，以便可以点击按钮
                                        setTimeout(function() {
                                            if (!input.find('.btn-success:hover').length && !input.find('.btn-default:hover').length) {
                                                saveValue();
                                            }
                                        }, 200);
                                    });
                                    
                                    // 处理回车事件
                                    inputField.keypress(function(e) {
                                        if (e.which === 13) {
                                            saveValue();
                                            return false;
                                        } else if (e.which === 27) { // ESC键
                                            $this.html(value);
                                            return false;
                                        }
                                    });
                                    
                                    // 保存值的函数
                                    function saveValue() {
                                        var newValue = inputField.val().trim();
                                        if (newValue !== value) {
                                            $.ajax({
                                                url: Fast.api.fixurl('resume/edit'),
                                                type: 'POST',
                                                data: {
                                                    ids: row.id,
                                                    row: {
                                                        special_certificate: newValue
                                                    }
                                                },
                                                success: function(res) {
                                                    if (res.code === 1) {
                                                        $this.html(newValue);
                                                        Toastr.success('更新成功');
                                                    } else {
                                                        $this.html(value);
                                                        Toastr.error(res.msg || '更新失败');
                                                    }
                                                },
                                                error: function() {
                                                    $this.html(value);
                                                    Toastr.error('服务器错误');
                                                }
                                            });
                                        } else {
                                            $this.html(value);
                                        }
                                    }
                                }
                            }
                        },
                        {field: 'job_experience', title: __('工作经历'), operate: false, width: 300, formatter: function(value, row, index) {
                            let jobs = [];
                            if (row.job_start && row.job_end && row.job_company && row.job_position) {
                                const starts = row.job_start.split('|');
                                const ends = row.job_end.split('|');
                                const companies = row.job_company.split('|');
                                const positions = row.job_position.split('|');
                                const descriptions = row.job_description ? row.job_description.split('|') : [];
                                
                                for (let i = 0; i < starts.length; i++) {
                                    const startDate = new Date(starts[i]);
                                    const endDate = new Date(ends[i]);
                                    const startFormatted = `${startDate.getFullYear()}年${startDate.getMonth()+1}月`;
                                    const endFormatted = `${endDate.getFullYear()}年${endDate.getMonth()+1}月`;
                                    jobs.push(`<div>${startFormatted} - ${endFormatted} ${companies[i]} ${positions[i]}</div>`);
                                    if (descriptions[i]) {
                                    }
                                }
                            }
                            return jobs.join('');
                        }},
                        {field: 'job_company', title: __('Job_company'), operate: 'LIKE', visible: false, width: 150},
                        {field: 'job_position', title: __('Job_position'), operate: 'LIKE', visible: false, width: 150},
                        {field: 'job_description', title: __('job_description'), operate: 'LIKE', visible: false, width: 200},
                        {field: 'avatar', title: __('Avatar'), operate: false, events: Table.api.events.image, formatter: Table.api.formatter.image, classname: 'img-lg img-center', width: 150},
                        {field: 'contact_person', title: __('Contact_person'), operate: 'LIKE', width: 100},
                        {field: 'status', title: __('status'),searchList: {"已发布":__('已发布'),"草稿":__('草稿'),"官网提交":__('官网提交')}, formatter: Table.api.formatter.normal, visible: false, width: 80},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, width: 120,
                            buttons: [
                                {
                                    name: 'detail',
                                    text: '',
                                    title: '查看详情',
                                    icon: 'fa fa-eye',
                                    classname: 'btn btn-xs btn-info btn-detail',
                                    url: 'resume/detail',
                                    tooltip: true,
                                    extend: 'data-toggle="tooltip"'
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            // 强制激活所有tooltip
            $(function(){
                $(document).off('mouseenter.tooltip.data-api', '[data-toggle="tooltip"]').on('mouseenter.tooltip.data-api', '[data-toggle="tooltip"]', function() {
                    $(this).tooltip('show');
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        
        /**
         * 基于模板导入
         */
        importWithTemplate: function () {
            // 文件选择后显示文件名
            $('#excel_file').change(function() {
                var fileName = $(this).val().split('\\').pop();
                $('#file-display').val(fileName);
            });
            
            // 选择模板后显示模板描述和字段映射
            $('#template_id').change(function() {
                var templateId = $(this).val();
                if (templateId) {
                    $.ajax({
                        url: Backend.api.fixurl("resume_import_template/edit"),
                        type: 'get',
                        data: {ids: templateId},
                        dataType: 'json',
                        success: function(res) {
                            if (res.code === 1) {
                                var template = res.data.row;
                                var description = template.description || '无描述';
                                $('#template-description').html(description);
                                
                                // 显示字段映射信息
                                Controller.api.renderMappingInfo(template.config);
                            } else {
                                $('#template-description').html('获取模板信息失败');
                                $('#template-mapping').html('<div class="alert alert-danger">获取模板信息失败</div>');
                            }
                        },
                        error: function() {
                            $('#template-description').html('获取模板信息失败');
                            $('#template-mapping').html('<div class="alert alert-danger">获取模板信息失败</div>');
                        }
                    });
                } else {
                    $('#template-description').html('请先选择导入模板');
                    $('#template-mapping').html('<div class="alert alert-info">请先选择导入模板查看字段映射详情</div>');
                }
            });
            
            // 初始化表单验证
            Form.api.bindevent($("form[role=form]"));
            
            // 触发默认模板的描述显示
            $('#template_id').trigger('change');
        },
        
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            },
            
            // 渲染字段映射信息
            renderMappingInfo: function(config) {
                if (!config) {
                    $('#template-mapping').html('<div class="alert alert-warning">该模板未配置字段映射</div>');
                    return;
                }
                
                var html = '';
                
                // 基本信息映射
                html += '<div class="panel panel-info">';
                html += '<div class="panel-heading">基本信息映射</div>';
                html += '<div class="panel-body">';
                html += '<table class="table table-striped">';
                html += '<thead><tr><th width="30%">字段名称</th><th>Excel单元格</th></tr></thead>';
                html += '<tbody>';
                
                // 基本字段
                var basicFields = {
                    'name': '姓名',
                    'intended_position': '意向岗位',
                    'applied_position': '申请岗位',
                    'gender': '性别',
                    'ethnicity': '民族',
                    'height': '身高(cm)',
                    'weight': '体重(kg)',
                    'id_card': '身份证号',
                    'age': '年龄',
                    'birth_date': '出生日期',
                    'phone': '手机号码',
                    'marital_status': '婚姻状况',
                    'hukou_location': '户口所在地',
                    'residence_address': '常住地址',
                    'highest_education': '最高学历',
                    'cantonese_level': '粤语熟练度',
                    'mandarin_level': '国语熟练度',
                    'english_level': '英语熟练度'
                };
                
                for (var field in basicFields) {
                    if (config[field]) {
                        html += '<tr>';
                        html += '<td>' + basicFields[field] + '</td>';
                        html += '<td>' + config[field] + '</td>';
                        html += '</tr>';
                    }
                }
                
                html += '</tbody></table>';
                html += '</div></div>';
                
                // 联系人信息映射
                html += '<div class="panel panel-info">';
                html += '<div class="panel-heading">联系人信息映射</div>';
                html += '<div class="panel-body">';
                html += '<table class="table table-striped">';
                html += '<thead><tr><th width="30%">字段名称</th><th>Excel单元格</th></tr></thead>';
                html += '<tbody>';
                
                var contactFields = {
                    'contact_relation': '联系人关系',
                    'contact_name': '联系人姓名',
                    'contact_age': '联系人年龄',
                    'contact_job': '联系人工作'
                };
                
                for (var field in contactFields) {
                    if (config[field]) {
                        html += '<tr>';
                        html += '<td>' + contactFields[field] + '</td>';
                        html += '<td>' + config[field] + '</td>';
                        html += '</tr>';
                    }
                }
                
                html += '</tbody></table>';
                html += '</div></div>';
                
                // 教育经历映射
                html += '<div class="panel panel-info">';
                html += '<div class="panel-heading">教育经历映射</div>';
                html += '<div class="panel-body">';
                html += '<table class="table table-striped">';
                html += '<thead><tr><th width="30%">字段名称</th><th>Excel单元格</th></tr></thead>';
                html += '<tbody>';
                
                var educationFields = {
                    'education_start': '教育开始时间',
                    'education_end': '教育结束时间',
                    'education_school': '学校名称',
                    'education_major': '专业',
                    'graduation_education': '学历'
                };
                
                for (var field in educationFields) {
                    if (config[field]) {
                        html += '<tr>';
                        html += '<td>' + educationFields[field] + '</td>';
                        html += '<td>' + config[field] + '</td>';
                        html += '</tr>';
                    }
                }
                
                html += '</tbody></table>';
                html += '</div></div>';
                
                // 工作经历映射
                html += '<div class="panel panel-info">';
                html += '<div class="panel-heading">工作经历映射</div>';
                html += '<div class="panel-body">';
                html += '<table class="table table-striped">';
                html += '<thead><tr><th width="30%">字段名称</th><th>Excel单元格</th></tr></thead>';
                html += '<tbody>';
                
                var jobFields = {
                    'job_start': '工作开始时间',
                    'job_end': '工作结束时间',
                    'job_company': '工作单位',
                    'job_position': '工作岗位',
                    'job_description': '工作内容'
                };
                
                for (var field in jobFields) {
                    if (config[field]) {
                        html += '<tr>';
                        html += '<td>' + jobFields[field] + '</td>';
                        html += '<td>' + config[field] + '</td>';
                        html += '</tr>';
                    }
                }
                
                html += '</tbody></table>';
                html += '</div></div>';
                
                // 其他信息映射
                html += '<div class="panel panel-info">';
                html += '<div class="panel-heading">其他信息映射</div>';
                html += '<div class="panel-body">';
                html += '<table class="table table-striped">';
                html += '<thead><tr><th width="30%">字段名称</th><th>Excel单元格</th></tr></thead>';
                html += '<tbody>';
                
                var otherFields = {
                    'special_certificate': '特殊职业资格证',
                    'hobbies': '兴趣爱好',
                    'self_evaluation': '自我评价',
                    'contact_person': '对接人',
                    'status': '状态'
                };
                
                for (var field in otherFields) {
                    if (config[field]) {
                        html += '<tr>';
                        html += '<td>' + otherFields[field] + '</td>';
                        html += '<td>' + config[field] + '</td>';
                        html += '</tr>';
                    }
                }
                
                html += '</tbody></table>';
                html += '</div></div>';
                
                $('#template-mapping').html(html);
            }



        },
                     
        exportExcel : function() {
    var ids = [];
    var rows = $('#table').bootstrapTable('getSelections');
    if (rows.length == 0) {
        Toastr.error('请至少选择一条数据');
        return false;
    }
    $.each(rows, function(i, row) {
        ids.push(row.id);
    });

    // 显示导出进度模态框
    this.showExportProgressModal(ids);
},
showImportModal: function () {
    $('#importModal').modal('show');
    this.initImportUpload();
},

showBatchUpload: function () {
    $('#batchUploadModal').modal('show');
    this.initBatchUpload();
},

// 导入上传相关函数
initImportUpload: function () {
    const uploadArea = document.querySelector('#importModal .file-upload-area');
    const uploadInput = document.getElementById('importFileInput');
    const uploadList = document.getElementById('importUploadList');
    let files = [];

    // 添加拖拽支持
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.classList.remove('dragover');
        files = Array.from(e.dataTransfer.files).filter(file => file.name.endsWith('.xlsx'));
        Controller.updateUploadList(files, uploadList);
    });

    uploadInput.addEventListener('change', (e) => {
        files = Array.from(e.target.files).filter(file => file.name.endsWith('.xlsx'));
        Controller.updateUploadList(files, uploadList);
    });
},

// 批量上传相关函数
initBatchUpload: function () {
    const uploadArea = document.querySelector('.batch-upload-area');
    const uploadInput = document.getElementById('batchUploadInput');
    const uploadList = document.getElementById('batchUploadList');
    let files = [];

    // 添加拖拽支持
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = '#2196F3';
        uploadArea.style.backgroundColor = '#f5f5f5';
    });

    uploadArea.addEventListener('dragleave', () => {
        uploadArea.style.borderColor = '';
        uploadArea.style.backgroundColor = '';
    });

    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.style.borderColor = '';
        uploadArea.style.backgroundColor = '';
        files = Array.from(e.dataTransfer.files).filter(file => file.name.endsWith('.xlsx'));
        Controller.updateUploadList(files, uploadList);
    });

    uploadInput.addEventListener('change', (e) => {
        files = Array.from(e.target.files).filter(file => file.name.endsWith('.xlsx'));
        Controller.updateUploadList(files, uploadList);
    });
},

// 更新上传列表显示
updateUploadList: function (files, container) {
    container.innerHTML = '';
    files.forEach((file, index) => {
        const item = document.createElement('div');
        item.className = 'upload-item';
        item.file = file; // 保存文件对象
        item.innerHTML = `
            <div class="file-info">
                <i class="fa fa-file-excel-o"></i>
                <div class="file-name" title="${file.name}">${file.name}</div>
                <div class="file-size">${formatFileSize(file.size)}</div>
            </div>
            <div class="file-actions">
                <button type="button" class="btn-remove" onclick="removeFile(this)">
                    <i class="fa fa-times"></i>
                </button>
            </div>
            <div class="progress">
                <div class="progress-bar" role="progressbar" style="width: 0%"></div>
            </div>
        `;
        container.appendChild(item);
    });
},

// 格式化文件大小
formatFileSize: function (bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
},

// 移除文件
removeFile: function (btn) {
    const item = btn.closest('.upload-item');
    item.remove();
},

// 导入文件函数
importFiles: function () {
    const files = document.querySelectorAll('#importUploadList .upload-item');
    if (!files.length) {
        Toastr.error('请选择或拖拽Excel文件到上传区域');
        return;
    }
    this.uploadFiles(Array.from(files), () => {
        $('#importModal').modal('hide');
    });
},

importBatchFiles: function () {
    const files = document.querySelectorAll('#batchUploadList .upload-item');
    if (!files.length) {
        Toastr.error('请选择Excel文件');
        return;
    }
    this.uploadFiles(Array.from(files), () => {
        $('#batchUploadModal').modal('hide');
    });
},

// 上传多个文件
uploadFiles: function (fileItems, callback) {
    let completed = 0;
    const total = fileItems.length;
    let successCount = 0;
    let failCount = 0;
    
    fileItems.forEach((item, index) => {
        const file = item.file;
        const progressBar = item.querySelector('.progress-bar');
        
        const formData = new FormData();
        formData.append('excel_file', file);

        $.ajax({
            url: Fast.api.fixurl('resume/import'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            xhr: function() {
                const xhr = new window.XMLHttpRequest();
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percent = Math.round((e.loaded / e.total) * 100);
                        progressBar.style.width = percent + '%';
                    }
                });
                return xhr;
            },
            success: function(res) {
                completed++;
                if (res.code === 1) {
                    progressBar.classList.add('progress-bar-success');
                    successCount++;
                    Toastr.success(`文件 ${file.name} 导入成功`);
                } else {
                    progressBar.classList.add('progress-bar-danger');
                    failCount++;
                    Toastr.success(`文件 ${file.name} ：${res.msg}`);
                }
                
                if (completed === total) {
                    if (callback) callback();
                    // 显示总结信息
                    if (successCount > 0) {
                        Toastr.success(`成功导入 ${successCount} 个文件`);
                    }
                    // 延迟跳转，让用户能看到提示信息
                    setTimeout(() => {
                        if (res.code === 1) {
                            window.location.href = res.data.url;
                        }
                    }, 2000);
                }
            },
            error: function(xhr) {
                completed++;
                progressBar.classList.add('progress-bar-danger');
                failCount++;
                Toastr.error(`文件 ${file.name} 导入失败：服务器错误`);
                if (completed === total && callback) {
                    callback();
                }
            }
        });
    });
},

showExportWordModal: function () {
    var rows = $('#table').bootstrapTable('getSelections');
    if (rows.length == 0) {
        Toastr.error('请至少选择一条数据');
        return false;
    }
    
    $('#exportWordModal').modal('show');
    this.loadWordTemplateList();

    // 初始化文件选择显示
    this.initTemplateFileSelection();
},

// 显示Excel导出模态框
showExportTemplateModal: function () {
    var rows = $('#table').bootstrapTable('getSelections');
    if (rows.length == 0) {
        Toastr.error('请至少选择一条数据');
        return false;
    }

    $('#exportTemplateModal').modal('show');
    this.loadTemplateList();

    // 初始化文件选择显示
    this.initTemplateFileSelection();
},

// 初始化模板文件选择交互
initTemplateFileSelection: function () {
    // Excel模板文件选择
    $('#uploadExcelTemplate').on('change', function() {
        if (this.files.length > 0) {
            $('#excelTemplateFile').val(this.files[0].name);
        } else {
            $('#excelTemplateFile').val('');
        }
    });
    
    // Word模板文件选择
    $('#uploadWordTemplate').on('change', function() {
        if (this.files.length > 0) {
            $('#wordTemplateFile').val(this.files[0].name);
        } else {
            $('#wordTemplateFile').val('');
        }
    });
},

// 加载模板列表
loadTemplateList: function () {
    const templateContainer = document.getElementById('templateListContainer');
    
    // 使用Fast.api.ajax加载模板列表
    Fast.api.ajax({
        url: 'resume/getTemplateList',
        type: 'GET',
        loading: false
    }, function(data, ret) {
        if (ret.code === 1 && data && data.length > 0) {
                let html = '';
                data.forEach(template => {
                    // 将路径中的反斜杠替换为正斜杠
                    const templatePath = template.path.replace(/\\/g, '/');
                    // 对路径进行HTML编码以处理特殊字符
                    const encodedPath = encodeURIComponent(templatePath);
                    html += `
                    <div class="list-group-item template-item" data-template="${templatePath}" data-name="${template.name}">
                        <div class="template-icon">
                            <i class="fa fa-file-excel-o"></i>
                        </div>
                        <div class="template-info">
                            <div class="template-name">${template.name}</div>
                            <div class="template-meta">
                                <span>${formatFileSize(template.size)}</span>
                                <span class="text-muted ml-10">更新时间: ${template.modified}</span>
                            </div>
                        </div>
                        <div class="template-actions">
                            <button class="btn btn-xs btn-danger" onclick="deleteTemplate(event, '${encodedPath}', 'excel')">
                                <i class="fa fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>`;
                });
                
                templateContainer.innerHTML = html;
                
                // 为模板项添加点击事件
                document.querySelectorAll('.template-item').forEach(item => {
                    item.addEventListener('click', function(e) {
                        // 如果点击的是删除按钮，不选中模板
                        if (e.target.closest('.template-actions')) {
                            return;
                        }
                        document.querySelectorAll('.template-item').forEach(el => el.classList.remove('active'));
                        this.classList.add('active');
                        document.getElementById('btnExportTemplate').disabled = false;
                    });
                });
                
                // 为导出按钮添加点击事件
                document.getElementById('btnExportTemplate').addEventListener('click', function() {
                    Controller.exportToTemplate();
                });
            } else {
                templateContainer.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fa fa-exclamation-triangle"></i> 未找到可用的Excel模板文件
                </div>`;
            }
        }, function(data, ret) {
            templateContainer.innerHTML = `
            <div class="alert alert-danger">
                <i class="fa fa-times-circle"></i> 加载模板列表失败
            </div>`;
        });
},

// 导出到选中的模板
exportToTemplate: function () {
    const activeTemplate = document.querySelector('.template-item.active');
    if (!activeTemplate) {
        Toastr.error('请选择一个模板');
        return;
    }

    const templatePath = activeTemplate.dataset.template;
    const templateName = activeTemplate.dataset.name;

    // 获取选中的数据IDs
    var ids = [];
    var rows = $('#table').bootstrapTable('getSelections');
    $.each(rows, function(i, row) {
        ids.push(row.id);
    });

    // 获取选中的文件名字段
    const filenameFields = [];
    document.querySelectorAll('.filename-field:checked').forEach(checkbox => {
        filenameFields.push(checkbox.value);
    });

    // 隐藏模板选择模态框
    $('#exportTemplateModal').modal('hide');

    // 显示Excel模板导出进度模态框
    this.showExcelTemplateProgressModal(ids, templatePath, templateName, filenameFields);
},

// 加载Word模板列表
loadWordTemplateList: function () {
    const templateContainer = document.getElementById('wordTemplateListContainer');
    
    // 使用Fast.api.ajax加载Word模板列表
    Fast.api.ajax({
        url: 'resume/getWordTemplateList',
        type: 'GET',
        loading: false
    }, function(data, ret) {
        if (ret.code === 1 && data && data.length > 0) {
            let html = '';
            data.forEach(template => {
                    // 将路径中的反斜杠替换为正斜杠
                    const templatePath = template.path.replace(/\\/g, '/');
                    // 对路径进行HTML编码以处理特殊字符
                    const encodedPath = encodeURIComponent(templatePath);
                    html += `
                    <div class="list-group-item template-item" data-template="${templatePath}" data-name="${template.name}">
                        <div class="template-icon">
                            <i class="fa fa-file-word-o"></i>
                        </div>
                        <div class="template-info">
                            <div class="template-name">${template.name}</div>
                            <div class="template-meta">
                                <span>${formatFileSize(template.size)}</span>
                                <span class="text-muted ml-10">更新时间: ${template.modified}</span>
                            </div>
                        </div>
                        <div class="template-actions">
                            <button class="btn btn-xs btn-danger" onclick="deleteTemplate(event, '${encodedPath}', 'word')">
                                <i class="fa fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>`;
                });
                
                templateContainer.innerHTML = html;
                
                // 为模板项添加点击事件
                document.querySelectorAll('#wordTemplateListContainer .template-item').forEach(item => {
                    item.addEventListener('click', function(e) {
                        // 如果点击的是删除按钮，不选中模板
                        if (e.target.closest('.template-actions')) {
                            return;
                        }
                        document.querySelectorAll('#wordTemplateListContainer .template-item').forEach(el => el.classList.remove('active'));
                        this.classList.add('active');
                        document.getElementById('btnExportWordTemplate').disabled = false;
                    });
                });
                
                // 为导出按钮添加点击事件
                document.getElementById('btnExportWordTemplate').addEventListener('click', function() {
                    Controller.exportToWordTemplate();
                });
            } else {
                templateContainer.innerHTML = `
                <div class="alert alert-warning">
                    <i class="fa fa-exclamation-triangle"></i> 未找到可用的Word模板文件
                </div>`;
            }
        }, function(data, ret) {
            templateContainer.innerHTML = `
            <div class="alert alert-danger">
                <i class="fa fa-times-circle"></i> 加载模板列表失败
            </div>`;
        });
},

// 导出到选中的Word模板
exportToWordTemplate: function () {
    const activeTemplate = document.querySelector('#wordTemplateListContainer .template-item.active');
    if (!activeTemplate) {
        Toastr.error('请选择一个Word模板');
        return;
    }

    const templatePath = activeTemplate.dataset.template;
    const templateName = activeTemplate.dataset.name;

    // 获取选中的数据IDs
    var ids = [];
    var rows = $('#table').bootstrapTable('getSelections');
    $.each(rows, function(i, row) {
        ids.push(row.id);
    });

    // 获取选中的文件名字段
    const filenameFields = [];
    document.querySelectorAll('.filename-field-word:checked').forEach(checkbox => {
        filenameFields.push(checkbox.value);
    });

    // 隐藏Word模板选择模态框
    $('#exportWordModal').modal('hide');

    // 显示Word模板导出进度模态框
    this.showWordTemplateProgressModal(ids, templatePath, templateName, filenameFields);
},

// 显示Excel模板上传模态框
showExcelUploadModal: function () {
    $('#excelUploadModal').modal('show');
},

// 显示Word模板上传模态框
showWordUploadModal: function () {
    $('#wordUploadModal').modal('show');
},

// 上传Excel模板文件
uploadExcelTemplateFile: function () {
    const fileInput = document.getElementById('uploadExcelTemplate');
    if (!fileInput.files.length) {
        Toastr.error('请选择Excel模板文件');
        return;
    }
    
    const file = fileInput.files[0];
    if (!file.name.endsWith('.xlsx')) {
        Toastr.error('请选择.xlsx格式的Excel文件');
        return;
    }
    
    // 显示进度条
    const progressBar = document.querySelector('#excelTemplateUploadProgress .progress-bar');
    $('#excelTemplateUploadProgress').show();
    progressBar.style.width = '0%';
    
    // 创建FormData对象
    const formData = new FormData();
    formData.append('template_file', file);
    
    $.ajax({
        url: Fast.api.fixurl('resume/uploadTemplate?type=excel'),
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percent = Math.round((e.loaded / e.total) * 100);
                    progressBar.style.width = percent + '%';
                }
            });
            return xhr;
        },
        success: function(res) {
            if (res.code === 1) {
                progressBar.classList.add('progress-bar-success');
                Toastr.success('Excel模板上传成功');
                
                // 清空文件选择
                $('#excelTemplateFile').val('');
                fileInput.value = '';
                
                // 重新加载模板列表
                Controller.loadTemplateList();
                
                // 延迟隐藏进度条和关闭模态框
                setTimeout(() => {
                    $('#excelTemplateUploadProgress').hide();
                    progressBar.classList.remove('progress-bar-success');
                    $('#excelUploadModal').modal('hide');
                }, 1000);
            } else {
                progressBar.classList.add('progress-bar-danger');
                Toastr.error('上传失败：' + res.msg);
                
                // 延迟隐藏进度条
                setTimeout(() => {
                    $('#excelTemplateUploadProgress').hide();
                    progressBar.classList.remove('progress-bar-danger');
                }, 2000);
            }
        },
        error: function() {
            progressBar.classList.add('progress-bar-danger');
            Toastr.error('上传失败：服务器错误');
            
            // 延迟隐藏进度条
            setTimeout(() => {
                $('#excelTemplateUploadProgress').hide();
                progressBar.classList.remove('progress-bar-danger');
            }, 2000);
        }
    });
},

// 上传Word模板文件
uploadWordTemplateFile: function () {
    const fileInput = document.getElementById('uploadWordTemplate');
    if (!fileInput.files.length) {
        Toastr.error('请选择Word模板文件');
        return;
    }
    
    const file = fileInput.files[0];
    if (!file.name.endsWith('.docx')) {
        Toastr.error('请选择.docx格式的Word文件');
        return;
    }
    
    // 显示进度条
    const progressBar = document.querySelector('#wordTemplateUploadProgress .progress-bar');
    $('#wordTemplateUploadProgress').show();
    progressBar.style.width = '0%';
    
    // 创建FormData对象
    const formData = new FormData();
    formData.append('template_file', file);
    
    $.ajax({
        url: Fast.api.fixurl('resume/uploadTemplate?type=word'),
        type: 'POST',
        data: formData,
        processData: false,
        contentType: false,
        xhr: function() {
            const xhr = new window.XMLHttpRequest();
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percent = Math.round((e.loaded / e.total) * 100);
                    progressBar.style.width = percent + '%';
                }
            });
            return xhr;
        },
        success: function(res) {
            if (res.code === 1) {
                progressBar.classList.add('progress-bar-success');
                Toastr.success('Word模板上传成功');
                
                // 清空文件选择
                $('#wordTemplateFile').val('');
                fileInput.value = '';
                
                // 重新加载模板列表
                Controller.loadWordTemplateList();
                
                // 延迟隐藏进度条和关闭模态框
                setTimeout(() => {
                    $('#wordTemplateUploadProgress').hide();
                    progressBar.classList.remove('progress-bar-success');
                    $('#wordUploadModal').modal('hide');
                }, 1000);
            } else {
                progressBar.classList.add('progress-bar-danger');
                Toastr.error('上传失败：' + res.msg);
                
                // 延迟隐藏进度条
                setTimeout(() => {
                    $('#wordTemplateUploadProgress').hide();
                    progressBar.classList.remove('progress-bar-danger');
                }, 2000);
            }
        },
        error: function() {
            progressBar.classList.add('progress-bar-danger');
            Toastr.error('上传失败：服务器错误');
            
            // 延迟隐藏进度条
            setTimeout(() => {
                $('#wordTemplateUploadProgress').hide();
                progressBar.classList.remove('progress-bar-danger');
            }, 2000);
        }
    });
},

// 删除模板文件
deleteTemplate: function (event, templatePath, type) {
    // 阻止事件冒泡，避免触发模板选择
    event.stopPropagation();

    // 解码路径（如果路径被编码了）
    try {
        templatePath = decodeURIComponent(templatePath);
    } catch (e) {
        // 如果解码失败，使用原始路径
        console.warn('路径解码失败，使用原始路径:', templatePath);
    }

    // 二次确认
    if (!confirm('确定要删除该模板文件吗？删除后无法恢复。')) {
        return;
    }
    
    // 获取文件名用于显示
    const fileName = templatePath.substring(templatePath.lastIndexOf('/') + 1);
    
    // 获取当前点击的模板项
    const templateItem = event.target.closest('.template-item');
    
    // 检查是否已有进度条，如果没有则添加
    let progressContainer = templateItem.querySelector('.delete-progress-container');
    if (!progressContainer) {
        progressContainer = document.createElement('div');
        progressContainer.className = 'delete-progress-container';
        progressContainer.style.width = '100%';
        progressContainer.style.marginTop = '10px';
        progressContainer.style.flexBasis = '100%'; // 确保占据整行
        progressContainer.style.clear = 'both'; // 清除浮动
        progressContainer.style.order = '999'; // 使用order属性确保它在最后
        
        const progress = document.createElement('div');
        progress.className = 'progress';
        progress.style.marginBottom = '5px';
        
        const progressBar = document.createElement('div');
        progressBar.className = 'progress-bar progress-bar-striped active';
        progressBar.role = 'progressbar';
        progressBar.style.width = '0%';
        
        const progressStatus = document.createElement('div');
        progressStatus.className = 'text-center';
        progressStatus.style.fontSize = '12px';
        progressStatus.textContent = `准备删除文件: ${fileName}`;
        
        progress.appendChild(progressBar);
        progressContainer.appendChild(progress);
        progressContainer.appendChild(progressStatus);
        templateItem.appendChild(progressContainer);
        
        // 强制模板项使用flex布局并允许换行
        templateItem.style.display = 'flex';
        templateItem.style.flexWrap = 'wrap';
    }
    
    const progressBar = progressContainer.querySelector('.progress-bar');
    const progressStatus = progressContainer.querySelector('.text-center');
    
    // 设置初始状态
    progressBar.style.width = '10%';
    progressStatus.textContent = `准备删除文件: ${fileName}`;
    
    // 模拟进度
    setTimeout(() => {
        progressBar.style.width = '30%';
        progressStatus.textContent = `正在删除文件: ${fileName}`;
    }, 300);
    
    setTimeout(() => {
        progressBar.style.width = '60%';
    }, 600);
    
    // 打印日志，帮助调试
    console.log('删除模板：', {path: templatePath, type: type});
    
    // 发送删除请求
    $.ajax({
        url: Fast.api.fixurl('resume/deleteTemplate'),
        type: 'POST',
        data: {
            template_path: templatePath,
            type: type
        },
        dataType: 'json',
        success: function(res) {
            // 设置进度为90%
            progressBar.style.width = '90%';
            
            setTimeout(() => {
                // 设置进度为100%
                progressBar.style.width = '100%';
                
                if (res.code === 1) {
                    // 成功状态
                    progressBar.classList.remove('progress-bar-striped', 'active');
                    progressBar.classList.add('progress-bar-success');
                    progressStatus.textContent = '删除成功！';
                    
                    // 延迟移除模板项并重新加载列表
                    setTimeout(() => {
                        Toastr.success('模板删除成功');
                        // 重新加载模板列表
                        if (type === 'excel') {
                            Controller.loadTemplateList();
                        } else {
                            Controller.loadWordTemplateList();
                        }
                    }, 1000);
                } else {
                    // 失败状态
                    progressBar.classList.remove('progress-bar-striped', 'active');
                    progressBar.classList.add('progress-bar-danger');
                    progressStatus.textContent = `删除失败: ${res.msg}`;
                    
                    // 延迟显示错误信息
                    setTimeout(() => {
                        Toastr.error('删除失败：' + res.msg);
                        console.error('删除失败', res);
                    }, 1000);
                }
            }, 300);
        },
        error: function(xhr, status, error) {
            // 失败状态
            progressBar.style.width = '100%';
            progressBar.classList.remove('progress-bar-striped', 'active');
            progressBar.classList.add('progress-bar-danger');
            progressStatus.textContent = '删除失败：服务器错误';
            
            // 延迟显示错误信息
            setTimeout(() => {
                Toastr.error('删除失败：服务器错误');
                console.error('删除请求失败', {xhr: xhr, status: status, error: error});
            }, 1000);
        }
    });
},

// 显示批量复制简历模态框
showBatchCopyModal: function () {
    // 获取选中的行
    var rows = $('#table').bootstrapTable('getSelections');
    
    // 检查是否有选中的行
    if (rows.length === 0) {
        Toastr.error('请先选择要复制的简历');
        return;
    }
    
    // 清空申请岗位输入框容器
    var container = $('#resumeAppliedPositions');
    container.empty();
    
    // 为每个选中的简历生成申请岗位输入框
    $.each(rows, function(i, row) {
        var html = `
            <div class="form-group resume-item" data-id="${row.id}">
                <label class="control-label"><strong>${row.name}</strong> 的申请岗位</label>
                <div class="input-group">
                    <span class="input-group-addon"><i class="fa fa-briefcase"></i></span>
                    <input type="text" class="form-control position-input" 
                           id="copyAppliedPosition_${row.id}" 
                           placeholder="请输入新的申请岗位">
                </div>
                <p class="help-block text-muted small">原申请岗位: ${row.applied_position || '无'}</p>
            </div>
        `;
        container.append(html);
    });
    
    // 显示模态框
    $('#batchCopyModal').modal('show');
    
    // 自动聚焦第一个输入框
    setTimeout(function() {
        $('.resume-item:first .position-input').focus();
    }, 500);
    
    // 添加回车键支持
    $('.position-input').keypress(function(e) {
        if (e.which === 13) { // 回车键
            Controller.batchCopyResumes();
            return false;
        }
    });
},

// 执行批量复制简历
batchCopyResumes: function () {
    // 获取选中的行
    var rows = $('#table').bootstrapTable('getSelections');
    
    if (rows.length === 0) {
        Toastr.error('请先选择要复制的简历');
        return;
    }
    
    // 收集所有简历ID和对应的申请岗位
    var resumeData = [];
    var hasEmptyPosition = false;
    
    // 遍历所有输入框
    $('.resume-item').each(function() {
        var id = $(this).data('id');
        var position = $(this).find('.position-input').val().trim();
        
        if (position === '') {
            hasEmptyPosition = true;
            return false; // 跳出循环
        }
        
        resumeData.push({
            id: id,
            position: position
        });
    });
    
    // 检查是否有空的申请岗位
    if (hasEmptyPosition) {
        Toastr.error('请为所有简历填写申请岗位');
        return;
    }
    
    // 显示加载中提示
    var batchCopyLoading = Toastr.info('正在处理复制请求，请稍候...');
    
    // 在模态框中显示进度条
    var progressHtml = `
        <div id="copyProgressContainer" class="progress" style="margin-top:15px;">
            <div class="progress-bar progress-bar-success progress-bar-striped active" 
                role="progressbar" style="width: 100%">
                <span>正在复制简历数据...</span>
            </div>
        </div>
    `;
    $('#resumeAppliedPositions').after(progressHtml);
    
    // 禁用提交按钮
    $('#batchCopyModal .modal-footer button').attr('disabled', 'disabled');
    
    // 发送复制请求
    $.ajax({
        url: Fast.api.fixurl('resume/batchCopy'),
        type: 'POST',
        data: {
            resume_data: JSON.stringify(resumeData)
        },
        dataType: 'json',
        success: function(res) {
            if (res.code === 1) {
                // 隐藏模态框
                $('#batchCopyModal').modal('hide');
                
                // 显示成功消息
                Toastr.success(res.msg || '批量复制成功');
                
                // 刷新表格
                $('#table').bootstrapTable('refresh');
            } else {
                Toastr.error(res.msg || '批量复制失败');
            }
        },
        error: function() {
            Toastr.error('服务器错误，请稍后重试');
        },
        complete: function() {
            // 关闭加载提示
            Toastr.clear(batchCopyLoading);
            
            // 移除进度条
            $('#copyProgressContainer').remove();
            
            // 恢复按钮状态
            $('#batchCopyModal .modal-footer button').removeAttr('disabled');
        }
    });
},

// 显示导出进度模态框
showExportProgressModal: function (ids) {
    // 重置模态框状态
    this.resetExportProgressModal();

    // 显示模态框
    $('#exportProgressModal').modal({
        backdrop: 'static',
        keyboard: false
    });

    // 开始导出流程
    this.startExportProcess(ids);
},

// 重置导出进度模态框
resetExportProgressModal: function () {
    $('#overallProgress').css('width', '0%');
    $('#overallProgressText').text('0%');
    $('#totalResumes').text('0');
    $('#processedResumes').text('0');
    $('#processedImages').text('0');
    $('#elapsedTime').text('0s');
    $('#exportSteps').empty();
    $('#exportCloseBtn').attr('disabled', true);
    $('#exportDownloadBtn').hide();
},

// 开始导出流程
startExportProcess: function (ids) {
    var startTime = Date.now();
    var totalResumes = ids.length;

    // 更新总简历数
    $('#totalResumes').text(totalResumes);

    // 创建步骤
    var steps = [
        { id: 'step1', text: '初始化导出参数', icon: 'fa-cog' },
        { id: 'step2', text: '验证用户权限', icon: 'fa-shield' },
        { id: 'step3', text: '查询简历数据', icon: 'fa-database' },
        { id: 'step4', text: '处理基本信息', icon: 'fa-user' },
        { id: 'step5', text: '处理联系人信息', icon: 'fa-phone' },
        { id: 'step6', text: '处理教育经历', icon: 'fa-graduation-cap' },
        { id: 'step7', text: '处理工作经历', icon: 'fa-briefcase' },
        { id: 'step8', text: '处理图片文件', icon: 'fa-image' },
        { id: 'step9', text: '生成Excel文件', icon: 'fa-file-excel-o' },
        { id: 'step10', text: '设置文件格式', icon: 'fa-cogs' },
        { id: 'step11', text: '完成导出', icon: 'fa-check' }
    ];

    // 添加步骤到界面
    steps.forEach(function(step) {
        Controller.addExportStep(step.id, step.text, step.icon, 'pending');
    });

    // 开始执行步骤
    this.executeExportSteps(ids, steps, startTime);
},

// 添加导出步骤
addExportStep: function (stepId, text, icon, status) {
    var statusClass = status || 'pending';
    var iconClass = 'fa ' + icon;

    if (status === 'processing') {
        iconClass += ' fa-spin';
    }

    var stepHtml = `
        <div class="export-step ${statusClass}" id="${stepId}">
            <i class="step-icon ${iconClass}"></i>
            <span class="step-text">${text}</span>
            <span class="step-time"></span>
        </div>
    `;

    $('#exportSteps').append(stepHtml);
},

// 更新导出步骤状态
updateExportStep: function (stepId, status, message) {
    var $step = $('#' + stepId);
    var $icon = $step.find('.step-icon');
    var $time = $step.find('.step-time');

    // 移除所有状态类
    $step.removeClass('pending processing completed error');
    $icon.removeClass('fa-spin');

    // 添加新状态
    $step.addClass(status);

    // 更新图标
    if (status === 'processing') {
        $icon.addClass('fa-spin');
    } else if (status === 'completed') {
        $icon.removeClass().addClass('step-icon fa fa-check');
    } else if (status === 'error') {
        $icon.removeClass().addClass('step-icon fa fa-times');
    }

    // 更新时间
    $time.text(new Date().toLocaleTimeString());

    // 更新消息
    if (message) {
        $step.find('.step-text').text(message);
    }
},

// 执行导出步骤
executeExportSteps: function (ids, steps, startTime) {
    var currentStep = 0;
    var totalSteps = steps.length;

    function executeNextStep() {
        if (currentStep >= totalSteps) {
            return;
        }

        var step = steps[currentStep];
        Controller.updateExportStep(step.id, 'processing');

        // 更新总体进度
        var progress = Math.round((currentStep / totalSteps) * 100);
        $('#overallProgress').css('width', progress + '%');
        $('#overallProgressText').text(progress + '%');

        // 更新耗时
        var elapsed = Math.round((Date.now() - startTime) / 1000);
        $('#elapsedTime').text(elapsed + 's');

        // 根据步骤类型执行不同的处理逻辑
        Controller.executeStepLogic(currentStep, ids, function(success, message) {
            if (success) {
                // 根据步骤更新相关统计
                Controller.updateStepStatistics(currentStep, ids.length);
                Controller.updateExportStep(step.id, 'completed', message);
            } else {
                Controller.updateExportStep(step.id, 'error', message || '步骤执行失败');
            }

            currentStep++;

            if (currentStep < totalSteps) {
                setTimeout(executeNextStep, 200); // 短暂延迟后执行下一步
            } else {
                // 所有步骤完成，开始实际下载
                Controller.completeExport(ids, startTime);
            }
        });
    }

    executeNextStep();
},

// 执行具体步骤逻辑
executeStepLogic: function (stepIndex, ids, callback) {
    var stepDelay = this.getStepDelay(stepIndex, ids.length);

    switch(stepIndex) {
        case 0: // 初始化导出参数
            setTimeout(function() {
                callback(true, '导出参数初始化完成');
            }, stepDelay);
            break;

        case 1: // 验证用户权限
            // 实际验证权限
            Fast.api.ajax({
                url: 'resume/exportProgress',
                type: 'GET',
                data: { step: 1, total: ids.length },
                loading: false
            }, function(data, ret) {
                callback(true, '权限验证通过');
            }, function(data, ret) {
                callback(false, '权限验证失败');
            });
            break;

        case 2: // 查询简历数据
            setTimeout(function() {
                callback(true, `成功查询 ${ids.length} 条简历数据`);
            }, stepDelay);
            break;

        case 3: // 处理基本信息
            setTimeout(function() {
                callback(true, `处理了 ${ids.length} 条基本信息`);
            }, stepDelay);
            break;

        case 4: // 处理联系人信息
            setTimeout(function() {
                var contactCount = ids.length * 2; // 假设每个简历2个联系人
                callback(true, `处理了 ${contactCount} 条联系人信息`);
            }, stepDelay);
            break;

        case 5: // 处理教育经历
            setTimeout(function() {
                var eduCount = ids.length * 3; // 假设每个简历3条教育经历
                callback(true, `处理了 ${eduCount} 条教育经历`);
            }, stepDelay);
            break;

        case 6: // 处理工作经历
            setTimeout(function() {
                var jobCount = ids.length * 4; // 假设每个简历4条工作经历
                callback(true, `处理了 ${jobCount} 条工作经历`);
            }, stepDelay);
            break;

        case 7: // 处理图片文件
            setTimeout(function() {
                var imageCount = ids.length * 7; // 假设每个简历7张图片
                callback(true, `处理了 ${imageCount} 张图片`);
            }, stepDelay);
            break;

        case 8: // 生成Excel文件
            setTimeout(function() {
                callback(true, 'Excel文件结构生成完成');
            }, stepDelay);
            break;

        case 9: // 设置文件格式
            setTimeout(function() {
                callback(true, '文件格式设置完成');
            }, stepDelay);
            break;

        case 10: // 完成导出
            setTimeout(function() {
                callback(true, '导出流程全部完成');
            }, stepDelay);
            break;

        default:
            setTimeout(function() {
                callback(true, '步骤执行完成');
            }, stepDelay);
    }
},

// 获取步骤延迟时间
getStepDelay: function (stepIndex, resumeCount) {
    var baseDelay = 500;
    var delays = [300, 200, 800, 600, 400, 500, 600, 1000, 800, 400, 300];

    // 根据简历数量调整延迟
    var multiplier = Math.min(resumeCount / 10, 3);
    return (delays[stepIndex] || baseDelay) * multiplier;
},

// 更新步骤统计信息
updateStepStatistics: function (stepIndex, totalResumes) {
    switch(stepIndex) {
        case 2: // 查询简历数据
            $('#processedResumes').text(Math.round(totalResumes * 0.1));
            break;
        case 3: // 处理基本信息
            $('#processedResumes').text(Math.round(totalResumes * 0.3));
            break;
        case 4: // 处理联系人信息
            $('#processedResumes').text(Math.round(totalResumes * 0.5));
            break;
        case 5: // 处理教育经历
            $('#processedResumes').text(Math.round(totalResumes * 0.7));
            break;
        case 6: // 处理工作经历
            $('#processedResumes').text(Math.round(totalResumes * 0.9));
            break;
        case 7: // 处理图片文件
            var imageCount = Math.round(totalResumes * 7); // 假设每个简历7张图片
            $('#processedImages').text(imageCount);
            $('#processedResumes').text(totalResumes);
            break;
        case 8: // 生成Excel文件
            $('#processedImages').text(Math.round(totalResumes * 7));
            break;
        case 9: // 设置文件格式
            $('#processedImages').text(Math.round(totalResumes * 7));
            break;
    }
},

// 完成导出
completeExport: function (ids, startTime) {
    // 更新最终进度
    $('#overallProgress').css('width', '100%').removeClass('progress-bar-info').addClass('progress-bar-success');
    $('#overallProgressText').text('100%');

    var finalElapsed = Math.round((Date.now() - startTime) / 1000);
    $('#elapsedTime').text(finalElapsed + 's');

    // 启用关闭按钮和下载按钮
    $('#exportCloseBtn').removeAttr('disabled').removeClass('btn-default').addClass('btn-primary');
    $('#exportDownloadBtn').show();

    // 更新模态框标题
    $('#exportProgressModalLabel').html('<i class="fa fa-check-circle text-success"></i> 导出完成');

    // 设置下载按钮点击事件
    $('#exportDownloadBtn').off('click').on('click', function() {
        // 添加下载动画
        var $btn = $(this);
        var originalText = $btn.html();
        $btn.html('<i class="fa fa-spinner fa-spin"></i> 正在下载...');
        $btn.attr('disabled', true);

        // 开始下载
        window.location.href = Fast.api.fixurl('resume/exportexcel?ids=' + ids.join(','));

        // 延迟恢复按钮状态
        setTimeout(function() {
            $btn.html(originalText);
            $btn.removeAttr('disabled');
            $('#exportProgressModal').modal('hide');
        }, 2000);
    });

    // 显示成功消息
    Toastr.success('Excel文件生成完成！共处理 ' + ids.length + ' 条简历数据，耗时 ' + finalElapsed + ' 秒');

    // 添加完成音效（使用简单的系统提示音）
    Controller.playCompletionSound();
},

// 显示Excel模板导出进度模态框
showExcelTemplateProgressModal: function (ids, templatePath, templateName, filenameFields) {
    // 重置模态框状态
    this.resetExportProgressModal();

    // 更新模态框标题
    $('#exportProgressModalLabel').html('<i class="fa fa-file-excel-o text-primary"></i> 导出Excel模板进度');

    // 显示模态框
    $('#exportProgressModal').modal({
        backdrop: 'static',
        keyboard: false
    });

    // 开始Excel模板导出流程
    this.startExcelTemplateExportProcess(ids, templatePath, templateName, filenameFields);
},

// 开始Excel模板导出流程
startExcelTemplateExportProcess: function (ids, templatePath, templateName, filenameFields) {
    var startTime = Date.now();
    var totalResumes = ids.length;

    // 更新总简历数
    $('#totalResumes').text(totalResumes);

    // 创建Excel模板导出步骤
    var steps = [
        { id: 'step1', text: '初始化导出参数', icon: 'fa-cog' },
        { id: 'step2', text: '验证用户权限', icon: 'fa-shield' },
        { id: 'step3', text: '加载Excel模板文件', icon: 'fa-file-excel-o' },
        { id: 'step4', text: '解析模板结构', icon: 'fa-sitemap' },
        { id: 'step5', text: '查询简历数据', icon: 'fa-database' },
        { id: 'step6', text: '处理基本信息', icon: 'fa-user' },
        { id: 'step7', text: '处理联系人信息', icon: 'fa-phone' },
        { id: 'step8', text: '处理教育经历', icon: 'fa-graduation-cap' },
        { id: 'step9', text: '处理工作经历', icon: 'fa-briefcase' },
        { id: 'step10', text: '填充模板数据', icon: 'fa-edit' },
        { id: 'step11', text: '生成文件名', icon: 'fa-tag' },
        { id: 'step12', text: '保存Excel文件', icon: 'fa-save' },
        { id: 'step13', text: '完成导出', icon: 'fa-check' }
    ];

    // 添加步骤到界面
    steps.forEach(function(step) {
        Controller.addExportStep(step.id, step.text, step.icon, 'pending');
    });

    // 开始执行步骤
    this.executeExcelTemplateExportSteps(ids, templatePath, templateName, filenameFields, steps, startTime);
},

// 执行Excel模板导出步骤
executeExcelTemplateExportSteps: function (ids, templatePath, templateName, filenameFields, steps, startTime) {
    var currentStep = 0;
    var totalSteps = steps.length;

    function executeNextStep() {
        if (currentStep >= totalSteps) {
            return;
        }

        var step = steps[currentStep];
        Controller.updateExportStep(step.id, 'processing');

        // 更新总体进度
        var progress = Math.round((currentStep / totalSteps) * 100);
        $('#overallProgress').css('width', progress + '%');
        $('#overallProgressText').text(progress + '%');

        // 更新耗时
        var elapsed = Math.round((Date.now() - startTime) / 1000);
        $('#elapsedTime').text(elapsed + 's');

        // 根据步骤类型执行不同的处理逻辑
        Controller.executeExcelTemplateStepLogic(currentStep, ids, templatePath, templateName, filenameFields, function(success, message) {
            if (success) {
                // 根据步骤更新相关统计
                Controller.updateExcelTemplateStepStatistics(currentStep, ids.length);
                Controller.updateExportStep(step.id, 'completed', message);
            } else {
                Controller.updateExportStep(step.id, 'error', message || '步骤执行失败');
            }

            currentStep++;

            if (currentStep < totalSteps) {
                setTimeout(executeNextStep, 200); // 短暂延迟后执行下一步
            } else {
                // 所有步骤完成，开始实际下载
                Controller.completeExcelTemplateExport(ids, templatePath, templateName, filenameFields, startTime);
            }
        });
    }

    executeNextStep();
},

// 执行Excel模板导出具体步骤逻辑
executeExcelTemplateStepLogic: function (stepIndex, ids, templatePath, templateName, filenameFields, callback) {
    var stepDelay = this.getExcelTemplateStepDelay(stepIndex, ids.length);

    switch(stepIndex) {
        case 0: // 初始化导出参数
            setTimeout(function() {
                callback(true, '导出参数初始化完成');
            }, stepDelay);
            break;

        case 1: // 验证用户权限
            // 实际验证权限
            Fast.api.ajax({
                url: 'resume/exportProgress',
                type: 'GET',
                data: { step: 1, total: ids.length },
                loading: false
            }, function(data, ret) {
                callback(true, '权限验证通过');
            }, function(data, ret) {
                callback(false, '权限验证失败');
            });
            break;

        case 2: // 加载Excel模板文件
            setTimeout(function() {
                callback(true, `成功加载模板: ${templateName}`);
            }, stepDelay);
            break;

        case 3: // 解析模板结构
            setTimeout(function() {
                callback(true, '模板结构解析完成');
            }, stepDelay);
            break;

        case 4: // 查询简历数据
            setTimeout(function() {
                callback(true, `成功查询 ${ids.length} 条简历数据`);
            }, stepDelay);
            break;

        case 5: // 处理基本信息
            setTimeout(function() {
                callback(true, `处理了 ${ids.length} 条基本信息`);
            }, stepDelay);
            break;

        case 6: // 处理联系人信息
            setTimeout(function() {
                var contactCount = ids.length * 2; // 假设每个简历2个联系人
                callback(true, `处理了 ${contactCount} 条联系人信息`);
            }, stepDelay);
            break;

        case 7: // 处理教育经历
            setTimeout(function() {
                var eduCount = ids.length * 3; // 假设每个简历3条教育经历
                callback(true, `处理了 ${eduCount} 条教育经历`);
            }, stepDelay);
            break;

        case 8: // 处理工作经历
            setTimeout(function() {
                var jobCount = ids.length * 4; // 假设每个简历4条工作经历
                callback(true, `处理了 ${jobCount} 条工作经历`);
            }, stepDelay);
            break;

        case 9: // 填充模板数据
            setTimeout(function() {
                callback(true, '模板数据填充完成');
            }, stepDelay);
            break;

        case 10: // 生成文件名
            setTimeout(function() {
                var fileCount = filenameFields.length > 0 ? ids.length : 1;
                callback(true, `生成了 ${fileCount} 个文件名`);
            }, stepDelay);
            break;

        case 11: // 保存Excel文件
            setTimeout(function() {
                callback(true, 'Excel文件保存完成');
            }, stepDelay);
            break;

        case 12: // 完成导出
            setTimeout(function() {
                callback(true, '导出流程全部完成');
            }, stepDelay);
            break;

        default:
            setTimeout(function() {
                callback(true, '步骤执行完成');
            }, stepDelay);
    }
},

// 获取Excel模板导出步骤延迟时间
getExcelTemplateStepDelay: function (stepIndex, resumeCount) {
    var baseDelay = 500;
    var delays = [300, 200, 600, 400, 800, 600, 400, 500, 600, 1200, 800, 1000, 300];

    // 根据简历数量调整延迟
    var multiplier = Math.min(resumeCount / 10, 3);
    return (delays[stepIndex] || baseDelay) * multiplier;
},

// 更新Excel模板导出步骤统计信息
updateExcelTemplateStepStatistics: function (stepIndex, totalResumes) {
    switch(stepIndex) {
        case 4: // 查询简历数据
            $('#processedResumes').text(Math.round(totalResumes * 0.1));
            break;
        case 5: // 处理基本信息
            $('#processedResumes').text(Math.round(totalResumes * 0.3));
            break;
        case 6: // 处理联系人信息
            $('#processedResumes').text(Math.round(totalResumes * 0.5));
            break;
        case 7: // 处理教育经历
            $('#processedResumes').text(Math.round(totalResumes * 0.7));
            break;
        case 8: // 处理工作经历
            $('#processedResumes').text(Math.round(totalResumes * 0.9));
            break;
        case 9: // 填充模板数据
            $('#processedResumes').text(totalResumes);
            break;
        case 10: // 生成文件名
            $('#processedImages').text(Math.round(totalResumes * 5)); // 假设每个简历5张图片
            break;
        case 11: // 保存Excel文件
            $('#processedImages').text(Math.round(totalResumes * 7)); // 假设每个简历7张图片
            break;
    }
},

// 完成Excel模板导出
completeExcelTemplateExport: function (ids, templatePath, templateName, filenameFields, startTime) {
    // 更新最终进度
    $('#overallProgress').css('width', '100%').removeClass('progress-bar-info').addClass('progress-bar-success');
    $('#overallProgressText').text('100%');

    var finalElapsed = Math.round((Date.now() - startTime) / 1000);
    $('#elapsedTime').text(finalElapsed + 's');

    // 启用关闭按钮和下载按钮
    $('#exportCloseBtn').removeAttr('disabled').removeClass('btn-default').addClass('btn-primary');
    $('#exportDownloadBtn').show();

    // 更新模态框标题
    $('#exportProgressModalLabel').html('<i class="fa fa-check-circle text-success"></i> Excel模板导出完成');

    // 设置下载按钮点击事件
    $('#exportDownloadBtn').off('click').on('click', function() {
        // 添加下载动画
        var $btn = $(this);
        var originalText = $btn.html();
        $btn.html('<i class="fa fa-spinner fa-spin"></i> 正在下载...');
        $btn.attr('disabled', true);

        // 开始下载 - 使用表单提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = Fast.api.fixurl('resume/exportToTemplate');
        form.style.display = 'none';

        // 添加IDs参数
        const idsInput = document.createElement('input');
        idsInput.type = 'hidden';
        idsInput.name = 'ids';
        idsInput.value = ids.join(',');
        form.appendChild(idsInput);

        // 添加模板路径参数
        const templateInput = document.createElement('input');
        templateInput.type = 'hidden';
        templateInput.name = 'template';
        templateInput.value = templatePath;
        form.appendChild(templateInput);

        // 添加文件名字段参数
        const filenameFieldsInput = document.createElement('input');
        filenameFieldsInput.type = 'hidden';
        filenameFieldsInput.name = 'filename_fields';
        filenameFieldsInput.value = filenameFields.join(',');
        form.appendChild(filenameFieldsInput);

        document.body.appendChild(form);
        form.submit();

        // 延迟恢复按钮状态
        setTimeout(function() {
            $btn.html(originalText);
            $btn.removeAttr('disabled');
            $('#exportProgressModal').modal('hide');
        }, 2000);
    });

    // 显示成功消息
    var fileCount = filenameFields.length > 0 ? ids.length : 1;
    Toastr.success(`Excel模板导出完成！使用模板: ${templateName}，共处理 ${ids.length} 条简历数据，生成 ${fileCount} 个文件，耗时 ${finalElapsed} 秒`);

    // 添加完成音效（使用简单的系统提示音）
    Controller.playCompletionSound();
},

// 处理导出错误
handleExportError: function (error, stepId) {
    // 更新进度条为错误状态
    $('#overallProgress').removeClass('progress-bar-info').addClass('progress-bar-danger');

    // 更新模态框标题
    $('#exportProgressModalLabel').html('<i class="fa fa-exclamation-triangle text-danger"></i> 导出失败');

    // 启用关闭按钮
    $('#exportCloseBtn').removeAttr('disabled').removeClass('btn-default').addClass('btn-danger');

    // 显示错误消息
    Toastr.error('导出过程中发生错误：' + (error.message || '未知错误'));

    // 添加重试按钮
    var retryBtn = '<button type="button" class="btn btn-warning" id="exportRetryBtn"><i class="fa fa-refresh"></i> 重试</button>';
    $('#exportDownloadBtn').after(retryBtn);

    // 设置重试按钮事件
    $('#exportRetryBtn').on('click', function() {
        $('#exportProgressModal').modal('hide');
        // 重新开始导出流程
        setTimeout(function() {
            var ids = [];
            var rows = $('#table').bootstrapTable('getSelections');
            $.each(rows, function(i, row) {
                ids.push(row.id);
            });
            showExportProgressModal(ids);
        }, 500);
    });
},
// 显示Word模板导出进度模态框
showWordTemplateProgressModal: function (ids, templatePath, templateName, filenameFields) {
    // 重置模态框状态
    this.resetExportProgressModal();

    // 更新模态框标题
    $('#exportProgressModalLabel').html('<i class="fa fa-file-word-o text-danger"></i> 导出Word模板进度');

    // 显示模态框
    $('#exportProgressModal').modal({
        backdrop: 'static',
        keyboard: false
    });

    // 开始Word模板导出流程
    this.startWordTemplateExportProcess(ids, templatePath, templateName, filenameFields);
},

// 开始Word模板导出流程
startWordTemplateExportProcess: function (ids, templatePath, templateName, filenameFields) {
    var startTime = Date.now();
    var totalResumes = ids.length;

    // 更新总简历数
    $('#totalResumes').text(totalResumes);

    // 创建Word模板导出步骤
    var steps = [
        { id: 'step1', text: '初始化导出参数', icon: 'fa-cog' },
        { id: 'step2', text: '验证用户权限', icon: 'fa-shield' },
        { id: 'step3', text: '加载Word模板文件', icon: 'fa-file-word-o' },
        { id: 'step4', text: '解析模板结构', icon: 'fa-sitemap' },
        { id: 'step5', text: '查询简历数据', icon: 'fa-database' },
        { id: 'step6', text: '处理基本信息', icon: 'fa-user' },
        { id: 'step7', text: '处理联系人信息', icon: 'fa-phone' },
        { id: 'step8', text: '处理教育经历', icon: 'fa-graduation-cap' },
        { id: 'step9', text: '处理工作经历', icon: 'fa-briefcase' },
        { id: 'step10', text: '处理图片文件', icon: 'fa-image' },
        { id: 'step11', text: '填充模板数据', icon: 'fa-edit' },
        { id: 'step12', text: '生成文件名', icon: 'fa-tag' },
        { id: 'step13', text: '保存Word文件', icon: 'fa-save' },
        { id: 'step14', text: '完成导出', icon: 'fa-check' }
    ];

    // 添加步骤到界面
    steps.forEach(function(step) {
        Controller.addExportStep(step.id, step.text, step.icon, 'pending');
    });

    // 开始执行步骤
    this.executeWordTemplateExportSteps(ids, templatePath, templateName, filenameFields, steps, startTime);
},

// 执行Word模板导出步骤
executeWordTemplateExportSteps: function (ids, templatePath, templateName, filenameFields, steps, startTime) {
    var currentStep = 0;
    var totalSteps = steps.length;

    function executeNextStep() {
        if (currentStep >= totalSteps) {
            return;
        }

        var step = steps[currentStep];
        Controller.updateExportStep(step.id, 'processing');

        // 更新总体进度
        var progress = Math.round((currentStep / totalSteps) * 100);
        $('#overallProgress').css('width', progress + '%');
        $('#overallProgressText').text(progress + '%');

        // 更新耗时
        var elapsed = Math.round((Date.now() - startTime) / 1000);
        $('#elapsedTime').text(elapsed + 's');

        // 根据步骤类型执行不同的处理逻辑
        Controller.executeWordTemplateStepLogic(currentStep, ids, templatePath, templateName, filenameFields, function(success, message) {
            if (success) {
                // 根据步骤更新相关统计
                Controller.updateWordTemplateStepStatistics(currentStep, ids.length);
                Controller.updateExportStep(step.id, 'completed', message);
            } else {
                Controller.updateExportStep(step.id, 'error', message || '步骤执行失败');
            }

            currentStep++;

            if (currentStep < totalSteps) {
                setTimeout(executeNextStep, 200); // 短暂延迟后执行下一步
            } else {
                // 所有步骤完成，开始实际下载
                Controller.completeWordTemplateExport(ids, templatePath, templateName, filenameFields, startTime);
            }
        });
    }

    executeNextStep();
},

// 执行Word模板导出具体步骤逻辑
executeWordTemplateStepLogic: function (stepIndex, ids, templatePath, templateName, filenameFields, callback) {
    var stepDelay = this.getWordTemplateStepDelay(stepIndex, ids.length);

    switch(stepIndex) {
        case 0: // 初始化导出参数
            setTimeout(function() {
                callback(true, '导出参数初始化完成');
            }, stepDelay);
            break;

        case 1: // 验证用户权限
            // 实际验证权限
            Fast.api.ajax({
                url: 'resume/exportProgress',
                type: 'GET',
                data: { step: 1, total: ids.length },
                loading: false
            }, function(data, ret) {
                callback(true, '权限验证通过');
            }, function(data, ret) {
                callback(false, '权限验证失败');
            });
            break;

        case 2: // 加载Word模板文件
            setTimeout(function() {
                callback(true, `成功加载Word模板: ${templateName}`);
            }, stepDelay);
            break;

        case 3: // 解析模板结构
            setTimeout(function() {
                callback(true, 'Word模板结构解析完成');
            }, stepDelay);
            break;

        case 4: // 查询简历数据
            setTimeout(function() {
                callback(true, `成功查询 ${ids.length} 条简历数据`);
            }, stepDelay);
            break;

        case 5: // 处理基本信息
            setTimeout(function() {
                callback(true, `处理了 ${ids.length} 条基本信息`);
            }, stepDelay);
            break;

        case 6: // 处理联系人信息
            setTimeout(function() {
                var contactCount = ids.length * 2; // 假设每个简历2个联系人
                callback(true, `处理了 ${contactCount} 条联系人信息`);
            }, stepDelay);
            break;

        case 7: // 处理教育经历
            setTimeout(function() {
                var eduCount = ids.length * 3; // 假设每个简历3条教育经历
                callback(true, `处理了 ${eduCount} 条教育经历`);
            }, stepDelay);
            break;

        case 8: // 处理工作经历
            setTimeout(function() {
                var jobCount = ids.length * 4; // 假设每个简历4条工作经历
                callback(true, `处理了 ${jobCount} 条工作经历`);
            }, stepDelay);
            break;

        case 9: // 处理图片文件
            setTimeout(function() {
                var imageCount = ids.length * 7; // 假设每个简历7张图片
                callback(true, `处理了 ${imageCount} 张图片`);
            }, stepDelay);
            break;

        case 10: // 填充模板数据
            setTimeout(function() {
                callback(true, 'Word模板数据填充完成');
            }, stepDelay);
            break;

        case 11: // 生成文件名
            setTimeout(function() {
                var fileCount = filenameFields.length > 0 ? ids.length : 1;
                callback(true, `生成了 ${fileCount} 个文件名`);
            }, stepDelay);
            break;

        case 12: // 保存Word文件
            setTimeout(function() {
                callback(true, 'Word文件保存完成');
            }, stepDelay);
            break;

        case 13: // 完成导出
            setTimeout(function() {
                callback(true, '导出流程全部完成');
            }, stepDelay);
            break;

        default:
            setTimeout(function() {
                callback(true, '步骤执行完成');
            }, stepDelay);
    }
},

// 获取Word模板导出步骤延迟时间
getWordTemplateStepDelay: function (stepIndex, resumeCount) {
    var baseDelay = 500;
    var delays = [300, 200, 800, 600, 800, 600, 400, 500, 600, 1000, 1200, 800, 1200, 300];

    // 根据简历数量调整延迟
    var multiplier = Math.min(resumeCount / 10, 3);
    return (delays[stepIndex] || baseDelay) * multiplier;
},

// 更新Word模板导出步骤统计信息
updateWordTemplateStepStatistics: function (stepIndex, totalResumes) {
    switch(stepIndex) {
        case 4: // 查询简历数据
            $('#processedResumes').text(Math.round(totalResumes * 0.1));
            break;
        case 5: // 处理基本信息
            $('#processedResumes').text(Math.round(totalResumes * 0.3));
            break;
        case 6: // 处理联系人信息
            $('#processedResumes').text(Math.round(totalResumes * 0.5));
            break;
        case 7: // 处理教育经历
            $('#processedResumes').text(Math.round(totalResumes * 0.7));
            break;
        case 8: // 处理工作经历
            $('#processedResumes').text(Math.round(totalResumes * 0.9));
            break;
        case 9: // 处理图片文件
            $('#processedImages').text(Math.round(totalResumes * 7)); // 假设每个简历7张图片
            $('#processedResumes').text(totalResumes);
            break;
        case 10: // 填充模板数据
            $('#processedImages').text(Math.round(totalResumes * 7));
            break;
        case 11: // 生成文件名
            $('#processedImages').text(Math.round(totalResumes * 7));
            break;
        case 12: // 保存Word文件
            $('#processedImages').text(Math.round(totalResumes * 7));
            break;
    }
},

// 完成Word模板导出
completeWordTemplateExport: function (ids, templatePath, templateName, filenameFields, startTime) {
    // 更新最终进度
    $('#overallProgress').css('width', '100%').removeClass('progress-bar-info').addClass('progress-bar-success');
    $('#overallProgressText').text('100%');

    var finalElapsed = Math.round((Date.now() - startTime) / 1000);
    $('#elapsedTime').text(finalElapsed + 's');

    // 启用关闭按钮和下载按钮
    $('#exportCloseBtn').removeAttr('disabled').removeClass('btn-default').addClass('btn-primary');
    $('#exportDownloadBtn').show();

    // 更新模态框标题
    $('#exportProgressModalLabel').html('<i class="fa fa-check-circle text-success"></i> Word模板导出完成');

    // 设置下载按钮点击事件
    $('#exportDownloadBtn').off('click').on('click', function() {
        // 添加下载动画
        var $btn = $(this);
        var originalText = $btn.html();
        $btn.html('<i class="fa fa-spinner fa-spin"></i> 正在下载...');
        $btn.attr('disabled', true);

        // 开始下载 - 使用表单提交
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = Fast.api.fixurl('resume/exportToWordTemplate');
        form.style.display = 'none';

        // 添加IDs参数
        const idsInput = document.createElement('input');
        idsInput.type = 'hidden';
        idsInput.name = 'ids';
        idsInput.value = ids.join(',');
        form.appendChild(idsInput);

        // 添加模板路径参数
        const templateInput = document.createElement('input');
        templateInput.type = 'hidden';
        templateInput.name = 'template';
        templateInput.value = templatePath;
        form.appendChild(templateInput);

        // 添加文件名字段参数
        const filenameFieldsInput = document.createElement('input');
        filenameFieldsInput.type = 'hidden';
        filenameFieldsInput.name = 'filename_fields';
        filenameFieldsInput.value = filenameFields.join(',');
        form.appendChild(filenameFieldsInput);

        document.body.appendChild(form);
        form.submit();

        // 延迟恢复按钮状态
        setTimeout(function() {
            $btn.html(originalText);
            $btn.removeAttr('disabled');
            $('#exportProgressModal').modal('hide');
        }, 2000);
    });

    // 显示成功消息
    var fileCount = filenameFields.length > 0 ? ids.length : 1;
    Toastr.success(`Word模板导出完成！使用模板: ${templateName}，共处理 ${ids.length} 条简历数据，生成 ${fileCount} 个文件，耗时 ${finalElapsed} 秒`);

    // 添加完成音效（使用简单的系统提示音）
    Controller.playCompletionSound();
},

// 播放完成音效的安全函数
playCompletionSound: function () {
    try {
        // 尝试使用Web Audio API创建简单的提示音
        if (window.AudioContext || window.webkitAudioContext) {
            var audioContext = new (window.AudioContext || window.webkitAudioContext)();
            var oscillator = audioContext.createOscillator();
            var gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(600, audioContext.currentTime + 0.1);
            oscillator.frequency.setValueAtTime(800, audioContext.currentTime + 0.2);

            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        }
    } catch(e) {
        // 如果音效播放失败，静默忽略
        console.log('音效播放不支持或失败，已忽略');
    }
},



    };

    // 将Controller方法暴露为全局函数，以便HTML中的onclick事件可以调用
    window.showExportTemplateModal = function() {
        return Controller.showExportTemplateModal();
    };

    window.showExportWordModal = function() {
        return Controller.showExportWordModal();
    };

    window.showBatchCopyModal = function() {
        return Controller.showBatchCopyModal();
    };

    window.showBatchUpload = function() {
        return Controller.showBatchUpload();
    };

    window.exportExcel = function() {
        return Controller.exportExcel();
    };

    window.importFiles = function() {
        return Controller.importFiles();
    };

    window.importBatchFiles = function() {
        return Controller.importBatchFiles();
    };

    window.showExcelUploadModal = function() {
        return Controller.showExcelUploadModal();
    };

    window.showWordUploadModal = function() {
        return Controller.showWordUploadModal();
    };

    window.uploadExcelTemplateFile = function() {
        return Controller.uploadExcelTemplateFile();
    };

    window.uploadWordTemplateFile = function() {
        return Controller.uploadWordTemplateFile();
    };

    window.batchCopyResumes = function() {
        return Controller.batchCopyResumes();
    };

    window.deleteTemplate = function(event, templatePath, type) {
        return Controller.deleteTemplate(event, templatePath, type);
    };

    window.removeFile = function(btn) {
        return Controller.removeFile(btn);
    };

    return Controller;
});
