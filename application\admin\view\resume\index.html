<div class="panel panel-default panel-intro">
    
    <div class="panel-heading">
        {:build_heading(null,FALSE)}
        <ul class="nav nav-tabs" data-field="status">
            
            {foreach name="statusList" item="vo"}
            <li class="{:$Think.get.status === (string)$key ? 'active' : ''}"><a href="#t-{$key}" data-value="{$key}" data-toggle="tab">{$vo}</a></li>
            {/foreach}
            <li class="{:$Think.get.status === null ? 'active' : ''}"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
        </ul>
    </div>


    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {php}
                        $showBackButton = isset($_GET['from']) && $_GET['from'] === 'jobinterview';
                        {/php}
                        {if $showBackButton}
                        <a href="javascript:history.back();" class="btn btn-primary btn-bback" title="{:__('Back')}"><i class="fa fa-reply"></i> 返回面试管理</a>
                        {/if}
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}" ><i class="fa fa-refresh"></i> </a>
                        <a href="javascript:;" class="btn btn-success btn-add {:$auth->check('resume/add')?'':'hide'}" title="{:__('Add')}" ><i class="fa fa-plus"></i> {:__('Add')}</a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled {:$auth->check('resume/edit')?'':'hide'}" title="{:__('Edit')}" ><i class="fa fa-pencil"></i> {:__('Edit')}</a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled {:$auth->check('resume/del')?'':'hide'}" title="{:__('Delete')}" ><i class="fa fa-trash"></i> {:__('Delete')}</a>
                        <a href="javascript:;" class="btn btn-primary btn-disabled disabled {:$auth->check('resume/showExportTemplateModal')?'':'hide'}" title="导出Excel简历" onclick="showExportTemplateModal()"><i class="fa fa-file-excel-o"></i> <span class="hidden-xs">导出Excel简历</span></a>
                        <a href="javascript:;" class="btn btn-danger btn-disabled disabled {:$auth->check('resume/showExportWordModal')?'':'hide'}" title="导出Word简历" onclick="showExportWordModal()"><i class="fa fa-file-word-o"></i> <span class="hidden-xs">导出Word简历</span></a>
                        <a href="javascript:;" class="btn btn-success btn-disabled disabled {:$auth->check('resume/batchCopy')?'':'hide'}" title="复制简历" onclick="showBatchCopyModal()"><i class="fa fa-copy"></i> <span class="hidden-xs">复制简历</span></a>
                        <a href="javascript:;" class="btn btn-info {:$auth->check('resume/import')?'':'hide'}" title="Excel导入" onclick="showBatchUpload()"><i class="fa fa-upload"></i> <span class="hidden-xs">Excel导入</span></a>
                        <a href="{:url('resume/importWithTemplate')}" class="btn btn-success {:$auth->check('resume/importWithTemplate')?'':'hide'}" title="模板导入"><i class="fa fa-file-excel-o"></i> <span class="hidden-xs">模板导入</span></a>
                        <a href="javascript:;" class="btn btn-warning btn-disabled disabled {:$auth->check('resume/exportexcel')?'':'hide'}" title="导出Excel汇总" onclick="exportExcel()"><i class="fa fa-file-excel-o"></i> <span class="hidden-xs">导出Excel汇总</span></a>


                        <div class="dropdown btn-group {:$auth->check('jobcategory/multi')?'':'hide'}">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown"><i class="fa fa-cog"></i> {:__('More')}</a>
                            <ul class="dropdown-menu text-left" role="menu">
                                {foreach name="statusList" item="vo"}
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:" data-params="status={$key}">{:__('设置为' . $key)}</a></li>
                                {/foreach}
                            </ul>
                        </div>
                        
                        
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover"
                           data-operate-edit="{:$auth->check('resume/edit')}"
                           data-operate-del="{:$auth->check('resume/del')}"
                           width="100%">
                    </table>
                </div>
            </div>

        </div>
    </div>
</div>

<!-- 导入模态框 -->
<div class="modal fade" id="importModal" tabindex="-1" role="dialog" aria-labelledby="importModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title" id="importModalLabel">单个Excel导入</h4>
            </div>
            <div class="modal-body">
                <div class="file-upload-area">
                    <label class="custom-file-upload">
                        <i class="fa fa-file-excel-o"></i> 选择Excel文件
                        <input type="file" id="importFileInput" accept=".xlsx" multiple>
                    </label>
                    <p class="help-block">支持选择.xlsx格式的Excel文件，也可以将文件拖拽到此处</p>
                </div>
                <div id="importUploadList" class="upload-list"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="importFiles()">开始导入</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量导入模态框 -->
<div class="modal fade" id="batchUploadModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <div class="header-title-container" style="display:flex; align-items:center; width:90%;">
                    <h4 class="modal-title">批量Excel导入</h4>
                    <a href="{:url('admin/resume/downloadTemplate')}" class="btn btn-info btn-sm" style="margin-left:20px;">
                        <i class="fa fa-download"></i> 下载导入模板
                    </a>
                </div>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> 请先下载导入模板，按照模板格式填写数据后再进行导入
                </div>
                <div class="file-upload-area batch-upload-area">
                    <label class="custom-file-upload">
                        <i class="fa fa-folder-open"></i> 选择Excel文件
                        <input type="file" id="batchUploadInput" accept=".xlsx" multiple>
                    </label>
                    <p class="help-block">支持选择多个.xlsx格式的Excel文件，也可以将文件拖拽到此处</p>
                </div>
                <div id="batchUploadList" class="upload-list"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" onclick="importBatchFiles()">开始导入</button>
            </div>
        </div>
    </div>
</div>

<!-- 导出到Excel模板选择模态框 -->
<div class="modal fade" id="exportTemplateModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <div class="header-title-container" style="display:flex; align-items:center; width:90%;">
                    <h4 class="modal-title">选择导出模板</h4>
                    <button class="btn btn-success btn-sm" onclick="showExcelUploadModal()" style="margin-left:20px;">
                        <i class="fa fa-upload"></i> 上传新模板
                    </button>
                </div>
            </div>
            <div class="modal-body">
                <div class="template-list">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> 请选择要导出的Excel模板
                    </div>
                    
                    <div id="templateListContainer" class="list-group">
                        <!-- 模板列表将通过JavaScript动态加载 -->
                        <div class="text-center mt-20 mb-20">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-10">正在加载模板列表...</p>
                        </div>
                    </div>
                </div>
                
                <!-- Excel导出模态框中的文件名选项 -->
                <div class="filename-options mt-20">
                    <h5><strong>文件名选项</strong></h5>
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> 请选择文件命名中包含的字段，按选择顺序组合
                    </div>
                    <div class="checkbox-list">
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field" value="name" checked> 姓名</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field" value="age" checked> 年龄</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field" value="applied_position" checked> 申请岗位</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field" value="gender"> 性别</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field" value="height"> 身高</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field" value="weight"> 体重</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field" value="ethnicity"> 民族</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field" value="highest_education"> 最高学历</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field" value="special_certificate"> 特殊证书</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field" value="marital_status"> 婚姻情况</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field" value="hukou_location"> 户口所在地</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="btnExportTemplate" disabled>导出到选中模板</button>
            </div>
        </div>
    </div>
</div>

<!-- 导出到Word模板选择模态框 -->
<div class="modal fade" id="exportWordModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <div class="header-title-container" style="display:flex; align-items:center; width:90%;">
                    <h4 class="modal-title">选择导出Word模板</h4>
                    <button class="btn btn-success btn-sm" onclick="showWordUploadModal()" style="margin-left:20px;">
                        <i class="fa fa-upload"></i> 上传新模板
                    </button>
                </div>
            </div>
            <div class="modal-body">
                <div class="template-list">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> 请选择要导出的Word模板
                    </div>
                    
                    <div id="wordTemplateListContainer" class="list-group">
                        <!-- Word模板列表将通过JavaScript动态加载 -->
                        <div class="text-center mt-20 mb-20">
                            <i class="fa fa-spinner fa-spin fa-2x"></i>
                            <p class="mt-10">正在加载模板列表...</p>
                        </div>
                    </div>
                </div>
                
                <!-- 文件名自定义选项 -->
                <div class="filename-options mt-20">
                    <h5><strong>文件名选项</strong></h5>
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle"></i> 请选择文件命名中包含的字段，按选择顺序组合
                    </div>
                    <div class="checkbox-list">
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field-word" value="name" checked> 姓名</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field-word" value="age" checked> 年龄</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field-word" value="applied_position" checked> 申请岗位</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field-word" value="gender"> 性别</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field-word" value="height"> 身高</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field-word" value="weight"> 体重</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field-word" value="ethnicity"> 民族</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field-word" value="highest_education"> 最高学历</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field-word" value="special_certificate"> 特殊证书</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field-word" value="marital_status"> 婚姻情况</label>
                        </div>
                        <div class="checkbox">
                            <label><input type="checkbox" class="filename-field-word" value="hukou_location"> 户口所在地</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="btnExportWordTemplate" disabled>导出到选中模板</button>
            </div>
        </div>
    </div>
</div>

<!-- 添加Excel模板上传模态框 -->
<div class="modal fade" id="excelUploadModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">上传Excel模板</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="input-group">
                            <input type="text" class="form-control" id="excelTemplateFile" readonly placeholder="请选择Excel模板文件">
                            <span class="input-group-btn">
                                <label class="btn btn-primary" for="uploadExcelTemplate">
                                    <i class="fa fa-folder-open"></i> 浏览
                                </label>
                            </span>
                        </div>
                        <input type="file" id="uploadExcelTemplate" accept=".xlsx" style="display:none">
                        <p class="help-block">支持.xlsx格式的Excel文件</p>
                    </div>
                </div>
                <div id="excelTemplateUploadProgress" class="progress" style="display:none; margin-top:10px;">
                    <div class="progress-bar progress-bar-success" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="uploadExcelTemplateFile()">
                    <i class="fa fa-cloud-upload"></i> 上传模板
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 添加Word模板上传模态框 -->
<div class="modal fade" id="wordUploadModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">上传Word模板</h4>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-xs-12">
                        <div class="input-group">
                            <input type="text" class="form-control" id="wordTemplateFile" readonly placeholder="请选择Word模板文件">
                            <span class="input-group-btn">
                                <label class="btn btn-primary" for="uploadWordTemplate">
                                    <i class="fa fa-folder-open"></i> 浏览
                                </label>
                            </span>
                        </div>
                        <input type="file" id="uploadWordTemplate" accept=".docx" style="display:none">
                        <p class="help-block">支持.docx格式的Word文件</p>
                    </div>
                </div>
                <div id="wordTemplateUploadProgress" class="progress" style="display:none; margin-top:10px;">
                    <div class="progress-bar progress-bar-success" role="progressbar" style="width: 0%"></div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="uploadWordTemplateFile()">
                    <i class="fa fa-cloud-upload"></i> 上传模板
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 批量复制简历模态框 -->
<div class="modal fade" id="batchCopyModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                <h4 class="modal-title">批量复制简历</h4>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fa fa-info-circle"></i> 您可以批量复制选中的简历记录，并为每个简历指定新的申请岗位
                </div>
                
                <div id="resumeAppliedPositions" class="resume-list">
                    <!-- 这里将动态生成选中简历的申请岗位输入框 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                <button type="button" class="btn btn-success" onclick="batchCopyResumes()">
                    <i class="fa fa-copy"></i> 确认复制
                </button>
            </div>
        </div>
    </div>
</div>


<!-- 导出进度模态框 -->
<div class="modal fade" id="exportProgressModal" tabindex="-1" role="dialog" aria-labelledby="exportProgressModalLabel">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="exportProgressModalLabel">
                    <i class="fa fa-file-excel-o text-success"></i> 导出Excel汇总进度
                </h4>
            </div>
            <div class="modal-body">
                <!-- 总体进度 -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h5 class="panel-title">
                                    <i class="fa fa-tasks"></i> 总体进度
                                </h5>
                            </div>
                            <div class="panel-body">
                                <div class="progress progress-striped active">
                                    <div id="overallProgress" class="progress-bar progress-bar-info" role="progressbar"
                                         style="width: 0%">
                                        <span id="overallProgressText">0%</span>
                                    </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-md-3">
                                        <div class="info-box bg-blue">
                                            <span class="info-box-icon"><i class="fa fa-users"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">选中简历</span>
                                                <span class="info-box-number" id="totalResumes">0</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="info-box bg-green">
                                            <span class="info-box-icon"><i class="fa fa-check"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">已处理</span>
                                                <span class="info-box-number" id="processedResumes">0</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="info-box bg-yellow">
                                            <span class="info-box-icon"><i class="fa fa-image"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">图片处理</span>
                                                <span class="info-box-number" id="processedImages">0</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="info-box bg-red">
                                            <span class="info-box-icon"><i class="fa fa-clock-o"></i></span>
                                            <div class="info-box-content">
                                                <span class="info-box-text">耗时</span>
                                                <span class="info-box-number" id="elapsedTime">0s</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 详细步骤 -->
                <div class="row">
                    <div class="col-md-12">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h5 class="panel-title">
                                    <i class="fa fa-list-ol"></i> 详细步骤
                                </h5>
                            </div>
                            <div class="panel-body" style="max-height: 300px; overflow-y: auto;">
                                <div id="exportSteps">
                                    <!-- 步骤将动态添加到这里 -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal" id="exportCloseBtn" disabled>
                    <i class="fa fa-times"></i> 关闭
                </button>
                <button type="button" class="btn btn-success" id="exportDownloadBtn" style="display: none;">
                    <i class="fa fa-download"></i> 下载文件
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* 导出进度样式 */
.export-step {
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 4px;
    border-left: 4px solid #ddd;
    background: #f9f9f9;
    transition: all 0.3s ease;
}

.export-step.pending {
    border-left-color: #ddd;
    background: #f9f9f9;
    color: #666;
}

.export-step.processing {
    border-left-color: #3c8dbc;
    background: #e8f4fd;
    color: #3c8dbc;
    animation: pulse 1.5s infinite;
}

.export-step.completed {
    border-left-color: #00a65a;
    background: #e8f5e8;
    color: #00a65a;
}

.export-step.error {
    border-left-color: #dd4b39;
    background: #fdf2f2;
    color: #dd4b39;
}

.export-step .step-icon {
    margin-right: 8px;
    width: 16px;
    text-align: center;
}

.export-step .step-time {
    float: right;
    font-size: 12px;
    opacity: 0.7;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

.info-box {
    display: block;
    min-height: 70px;
    background: #fff;
    width: 100%;
    box-shadow: 0 1px 1px rgba(0,0,0,0.1);
    border-radius: 2px;
    margin-bottom: 15px;
}

.info-box-icon {
    border-top-left-radius: 2px;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 2px;
    display: block;
    float: left;
    height: 70px;
    width: 70px;
    text-align: center;
    font-size: 45px;
    line-height: 70px;
    background: rgba(0,0,0,0.2);
}

.info-box-content {
    padding: 5px 10px;
    margin-left: 70px;
}

.info-box-text {
    text-transform: uppercase;
    font-weight: bold;
    font-size: 12px;
}

.info-box-number {
    display: block;
    font-weight: bold;
    font-size: 18px;
}

.bg-blue { background-color: #3c8dbc !important; color: #fff; }
.bg-green { background-color: #00a65a !important; color: #fff; }
.bg-yellow { background-color: #f39c12 !important; color: #fff; }
.bg-red { background-color: #dd4b39 !important; color: #fff; }

/* 进度条动画 */
.progress-striped .progress-bar {
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent);
    background-size: 40px 40px;
}

.progress.active .progress-bar {
    animation: progress-bar-stripes 2s linear infinite;
}

@keyframes progress-bar-stripes {
    from { background-position: 40px 0; }
    to { background-position: 0 0; }
}

/* 模态框增强样式 */
#exportProgressModal .modal-content {
    border-radius: 6px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

#exportProgressModal .modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 6px 6px 0 0;
}

#exportProgressModal .modal-header .close {
    color: white;
    opacity: 0.8;
}

#exportProgressModal .modal-header .close:hover {
    opacity: 1;
}

/* 信息框悬停效果 */
.info-box {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.info-box:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 步骤容器滚动条样式 */
#exportSteps::-webkit-scrollbar {
    width: 6px;
}

#exportSteps::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#exportSteps::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

#exportSteps::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 按钮动画效果 */
.btn {
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* 面板标题样式 */
.panel-title {
    font-weight: 600;
    color: #333;
}

.panel-title i {
    margin-right: 8px;
    color: #666;
}

/* 统一的上传区域样式 */
.file-upload-area {
    border: 2px dashed #ccc;
    padding: 30px;
    text-align: center;
    background: #fafafa;
    border-radius: 6px;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    margin-bottom: 20px;
}

.file-upload-area:hover {
    border-color: #2196F3;
    background-color: #f5f5f5;
}

.file-upload-area i {
    font-size: 48px;
    color: #2196F3;
    margin-bottom: 15px;
    display: block;
}

.file-upload-area p {
    margin: 10px 0;
    color: #666;
}

.file-upload-area .text-muted {
    font-size: 12px;
    color: #999;
}

/* 自定义文件上传按钮样式 */
.custom-file-upload {
    display: inline-block;
    padding: 12px 24px;
    background: #ffffff;
    color: #020000;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;
    font-size: 14px;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.custom-file-upload:hover {
    background: #f4fffe;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-1px);
}

.custom-file-upload:active {
    transform: translateY(0);
    box-shadow: 0 2px 3px rgba(0,0,0,0.1);
}

.custom-file-upload i {
    font-size: 18px;
    margin-right: 8px;
    vertical-align: middle;
}

.custom-file-upload input[type="file"] {
    display: none;
}

/* 拖拽上传区域特殊样式 */
.drag-upload-area.dragover {
    border-color: #2196F3;
    background-color: #e3f2fd;
    transform: scale(1.02);
}

/* 上传列表样式优化 */
.upload-list {
    margin-top: 20px;
}

.upload-item {
    padding: 15px;
    margin-bottom: 10px;
    background: #fff;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
    flex-wrap: wrap;
}

.upload-item:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.upload-item .file-info {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
    margin-right: 10px;
}

.upload-item .file-info i {
    font-size: 24px;
    color: #2196F3;
    margin-right: 12px;
    flex-shrink: 0;
}

.upload-item .file-name {
    flex: 1;
    margin-right: 12px;
    color: #333;
    font-weight: 500;
    word-break: break-word;
    min-width: 100px;
}

.upload-item .file-size {
    color: #999;
    font-size: 12px;
    white-space: nowrap;
    flex-shrink: 0;
    margin-left: 8px;
}

.upload-item .progress {
    width: 100%;
    height: 4px;
    margin: 10px 0 0;
    background-color: #f5f5f5;
    border-radius: 2px;
    overflow: hidden;
    flex-basis: 100%;
}

.upload-item .progress-bar {
    background-color: #2196F3;
    transition: width 0.3s ease;
}

.upload-item .progress-bar-success {
    background-color: #4CAF50;
}

.upload-item .progress-bar-danger {
    background-color: #f44336;
}

.upload-item .file-actions {
    margin-left: 12px;
    display: flex;
    align-items: center;
    flex-shrink: 0;
}

.upload-item .btn-remove {
    color: #f44336;
    background: none;
    border: none;
    padding: 4px 8px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.3s;
}

.upload-item .btn-remove:hover {
    background-color: #ffebee;
    color: #d32f2f;
}

/* 模态框样式优化 */
.modal-content {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.modal-header {
    border-bottom: 1px solid #eee;
    padding: 15px 20px;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    border-top: 1px solid #eee;
    padding: 15px 20px;
}

.modal-title {
    font-weight: 500;
    color: #333;
}

.help-block {
    margin-top: 12px;
    color: #666;
    font-size: 13px;
}

/* 模板选择样式 */
.template-list {
    max-height: 350px;
    overflow-y: auto;
}

.template-item {
    display: flex;
    align-items: center;
    padding: 10px;
    cursor: pointer;
    transition: all 0.2s;
    border-left: 3px solid transparent;
}

.template-item:hover {
    background-color: #f9f9f9;
    border-left-color: #2196F3;
}

.template-item.active {
    background-color: #e3f2fd;
    border-left-color: #2196F3;
}

.template-icon {
    font-size: 20px;
    margin-right: 12px;
    color: #217346; /* Excel绿色 */
}

/* Word模板图标颜色 */
#wordTemplateListContainer .template-icon {
    color: #2b579a; /* Word蓝色 */
}

.template-info {
    flex: 1;
}

.template-name {
    font-weight: 500;
    margin-bottom: 3px;
    font-size: 13px;
}

.template-meta {
    font-size: 11px;
    color: #777;
}

.template-actions {
    margin-left: 10px;
}

/* 文件名选项样式 */
.filename-options {
    border-top: 1px solid #eee;
    padding-top: 20px;
}

.filename-options h5 {
    margin-bottom: 15px;
    color: #333;
}

.checkbox-list {
    display: flex;
    flex-wrap: wrap;
}

.checkbox-list .checkbox {
    width: 33.33%;
    margin-bottom: 10px;
    margin-top: 0;
    position: relative;
    height: 22px;
}

.checkbox-list .checkbox label {
    display: flex;
    align-items: center;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    margin: 0;
}

.checkbox-list .checkbox input {
    margin: 0;
    margin-right: 5px;
    position: relative;
    top: 0;
}

.mt-10 {
    margin-top: 10px;
}

.mt-20 {
    margin-top: 20px;
}

.mb-20 {
    margin-bottom: 20px;
}

/* 简历列表样式 */
.resume-list {
    max-height: 500px;
    overflow-y: auto;
    padding-right: 5px;
}

.resume-list .form-group:not(:last-child) {
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    margin-bottom: 15px;
}
</style>



