<!-- 底部信息 -->
<footer class="footer bg-dark text-light py-4">
    <div class="container">
        <div class="row">
            <div class="col-md-4">
                <h5>联系我们</h5>
                <p>
                    <i class="fas fa-phone"></i> {$site.phone}<br>
                    <i class="fas fa-envelope"></i> {$site.email}<br>
                    <i class="fas fa-map-marker-alt"></i> {$site.address}
                </p>
            </div>
            <div class="col-md-4">
                <h5>快速链接</h5>
                <ul class="list-unstyled">
                    <li><a href="{:url('index/about/index')}" class="text-light">关于我们</a></li>
                    <li><a href="{:url('index/help/index')}" class="text-light">帮助中心</a></li>
                    <li><a href="{:url('index/privacy/index')}" class="text-light">隐私政策</a></li>
                </ul>
            </div>
            <div class="col-md-4">
                <h5>关注我们</h5>
                <div class="social-links">
                    <a href="javascript:;" class="text-light me-2" data-bs-toggle="modal" data-bs-target="#wechatModal"><i class="fab fa-weixin fa-2x"></i></a>
                    <a href="javascript:;" class="text-light me-2" data-bs-toggle="modal" data-bs-target="#weiboModal"><i class="fab fa-weibo fa-2x"></i></a>
                </div>
            </div>
        </div>
        <hr class="mt-4">
        <div class="text-center">
            <p>Copyright © {$site.name|htmlentities} {:date('Y',time())} 版权所有 
                <a href="https://beian.miit.gov.cn" target="_blank" class="text-light">{$site.beian|htmlentities}</a>
            </p>
        </div>
    </div>
</footer>

<!-- 微信弹窗 -->
<div class="modal fade" id="wechatModal" tabindex="-1" aria-labelledby="wechatModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="wechatModalLabel">{$site.wechat_title|default='微信公众号'}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img src="{$site.wechat_image}" alt="微信二维码" class="img-fluid mb-3" style="max-width: 200px;">
                <p class="mb-0">{$site.wechat_desc|default='扫描上方二维码关注我们的微信公众号，获取最新招聘信息和职场资讯。'}</p>
            </div>
        </div>
    </div>
</div>

<!-- 微博弹窗 -->
<div class="modal fade" id="weiboModal" tabindex="-1" aria-labelledby="weiboModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="weiboModalLabel">{$site.weibo_title|default='官方微博'}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{$site.weibo_desc|default='关注我们的官方微博，了解最新动态和招聘信息。'}</p>
                <div class="d-grid gap-2">
                    <a href="{$site.weibo_url}" target="_blank" class="btn btn-danger">
                        <i class="fab fa-weibo me-2"></i>访问官方微博
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 底部样式 -->
<style>
    .footer {
        background-color: var(--dark-bg);
        color: white;
        padding: 5rem 0 2rem;
        position: relative;
        overflow: hidden;
    }

    .footer::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient-primary);
    }

    .footer h5 {
        color: white;
        font-weight: 600;
        margin-bottom: 1.5rem;
        font-size: 1.25rem;
    }

    .footer a {
        color: rgba(255,255,255,0.8);
        text-decoration: none;
        transition: var(--transition);
        display: inline-block;
        margin-bottom: 0.5rem;
    }

    .footer a:hover {
        color: white;
        transform: translateX(5px);
    }

    .social-links a {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background: rgba(255,255,255,0.1);
        margin-right: 1rem;
        transition: var(--transition);
        cursor: pointer;
    }

    .social-links a:hover {
        background: var(--primary-color);
        transform: translateY(-3px);
    }
</style> 