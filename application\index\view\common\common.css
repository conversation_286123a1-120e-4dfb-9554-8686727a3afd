/* 全局变量 */
:root {
    --primary-color: #1a73e8;
    --secondary-color: #34a853;
    --accent-color: #ea4335;
    --light-bg: #f8f9fa;
    --dark-bg: #202124;
    --text-primary: #202124;
    --text-secondary: #5f6368;
    --border-radius: 16px;
    --box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --gradient-primary: linear-gradient(135deg, #1a73e8, #4285f4);
    --gradient-success: linear-gradient(135deg, #34a853, #0f9d58);
}

/* 基础样式 */
body {
    background-color: var(--light-bg);
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON>o, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
}

/* 按钮样式 */
.btn {
    border-radius: var(--border-radius);
    padding: 0.75rem 1.5rem;
    font-weight: 500;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.btn:hover::after {
    width: 300px;
    height: 300px;
}

.btn-primary {
    background: var(--gradient-primary);
    border: none;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(26, 115, 232, 0.3);
}

.btn-outline-primary {
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
    background: transparent;
}

.btn-outline-primary:hover {
    background: var(--gradient-primary);
    color: white;
    border-color: transparent;
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(26, 115, 232, 0.3);
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    background: white;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.card-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 1rem;
    font-size: 1.25rem;
}

.card-title a {
    color: var(--text-primary);
    text-decoration: none;
    transition: var(--transition);
    position: relative;
}

.card-title a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background-color: var(--primary-color);
    transition: var(--transition);
}

.card-title a:hover {
    color: var(--primary-color);
}

.card-title a:hover::after {
    width: 100%;
}

/* 标签样式 */
.badge {
    padding: 0.5em 1em;
    font-weight: 500;
    border-radius: 30px;
    font-size: 0.85rem;
    letter-spacing: 0.3px;
}

.badge.bg-primary {
    background: var(--gradient-primary) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #4285f4, #5c9ce6) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #5f6368, #80868b) !important;
}

/* 标题样式 */
.section-title {
    color: var(--text-primary);
    font-weight: 700;
    margin-bottom: 2.5rem;
    position: relative;
    padding-bottom: 1rem;
    text-align: center;
    font-size: 2rem;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: var(--gradient-primary);
    border-radius: 2px;
}

/* 响应式调整 */
@media (max-width: 991.98px) {
    .section-title {
        font-size: 1.75rem;
    }
    
    .btn {
        padding: 0.6rem 1.2rem;
    }
} 