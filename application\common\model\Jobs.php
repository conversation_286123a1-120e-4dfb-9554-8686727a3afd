<?php

namespace app\common\model;

use think\Model;


class Jobs extends Model
{

    

    

    // 表名
    protected $name = 'jobs';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = true;

    // 定义时间戳字段名
    protected $createTime = 'create_time';
    protected $updateTime = 'update_time';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text'
    ];
    

    
    public function getStatusList()
    {
        return ['草稿' => __('草稿'), '上架' => __('上架'), '暂停' => __('暂停'), '下架' => __('下架')];
    }


    public function getStatusTextAttr($value, $data)
    {
        $value = $value ?: ($data['status'] ?? '');
        $list = $this->getStatusList();
        return $list[$value] ?? '';
    }

    /**
     * 获取面试人数
     * @param string $job_code 职位编号
     * @param string $job_name 职位名称
     * @return int
     */
    public function getInterviewCount($job_code, $job_name)
    {
        $interviewModel = new \app\common\model\job\Interview;
        $interviews = $interviewModel->where([
            'job_code' => $job_code,
            'job' => $job_name
        ])->column('interviewee');
        
        $totalCount = 0;
        foreach ($interviews as $interviewee) {
            if (!empty($interviewee)) {
                $names = array_filter(explode(',', $interviewee));
                $totalCount += count($names);
            }
        }
        return $totalCount;
    }

    /**
     * 获取简历数量
     * @param mixed $value
     * @param array $data
     * @return int
     */
    public function getResumeCountAttr($value, $data)
    {
        if (isset($data['job_code']) && isset($data['job_name'])) {
            return $this->getInterviewCount($data['job_code'], $data['job_name']);
        }
        return 0;
    }

    /**
     * 获取收藏数量
     */
    public function getFavoriteCount()
    {
        return \app\common\model\Jobfavorite::where('job_id', $this->id)->count();
    }

    /**
     * 获取简历投递数量
     */
    public function getResumeApplyCount()
    {
        return \app\common\model\ResumeApply::where('job_id', $this->id)->count();
    }

}
